using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Data;
using System.Runtime.Serialization;
using System.Reflection;

namespace NextBroker.Pages.Polisi
{
    public class RequiredIfAttribute : ValidationAttribute
    {
        private readonly string _dependentProperty;
        private readonly object _targetValue;

        public RequiredIfAttribute(string dependentProperty, object targetValue)
        {
            _dependentProperty = dependentProperty;
            _targetValue = targetValue;
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var dependentPropertyValue = validationContext.ObjectInstance.GetType().GetProperty(_dependentProperty).GetValue(validationContext.ObjectInstance);

            if ((dependentPropertyValue == null && _targetValue == null) || (dependentPropertyValue?.Equals(_targetValue) ?? false))
            {
                if (value == null)
                {
                    return new ValidationResult(ErrorMessage);
                }
            }

            return ValidationResult.Success;
        }
    }

    public enum TipNaFakturaenumKlasa19
    {
        [Display(Name = "Влезна фактура кон клиент")]
        [EnumMember(Value = "Влезна фактура кон клиент")]
        VleznaFakturaKonKlient,
        [Display(Name = "Влезна фактура кон брокер")]
        [EnumMember(Value = "Влезна фактура кон брокер")]
        VleznaFakturaKonBroker
    }

    [IgnoreAntiforgeryToken(Order = 1001)]
    public class DodajPolisaKlasa19Model : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public bool HasAdminAccess { get; private set; }

        public IEnumerable<SelectListItem> Osiguriteli { get; set; }
        public IEnumerable<SelectListItem> KlasiOsiguruvanje { get; set; }
        public IEnumerable<SelectListItem> Produkti { get; set; }
        public IEnumerable<SelectListItem> PriciniZaStorno { get; set; }
        public IEnumerable<SelectListItem> Valuti { get; set; }
        public IEnumerable<SelectListItem> NaciniNaPlakanje { get; set; }
        public IEnumerable<SelectListItem> TipoviNaPlakanje { get; set; }
        public IEnumerable<SelectListItem> Banki { get; set; }
        public IEnumerable<SelectListItem> ListaDejnosti { get; set; }
        public IEnumerable<SelectListItem> TipoviNaFaktura { get; set; }

        [BindProperty]
        public PolisaInputModel Input { get; set; } = new();

        public DodajPolisaKlasa19Model(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public async Task<IActionResult> OnGetAsync()
        {
            if (!await HasPageAccess("DodajPolisaKlasa19"))
            {
                return RedirectToAccessDenied();
            }

            // Check admin access
            HasAdminAccess = await HasPageAccess("DodajPolisaKlasa19Admin");

            await LoadOsiguriteli();
            await LoadKlasiOsiguruvanje();
            await LoadProdukti();
            await LoadPriciniZaStorno();
            await LoadValuti();
            await LoadNaciniNaPlakanje();
            await LoadTipoviNaPlakanje();
            await LoadBanki();
            await LoadListaDejnosti();
            LoadTipoviNaFaktura();

            return Page();
        }

        private async Task LoadOsiguriteli()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Naziv 
                    FROM Klienti 
                    WHERE Osiguritel = 1 
                    AND ZivotNezivot = N'Живот'
                    AND DogovorVaziDo > Convert(date,GETDATE())
                    ORDER BY Naziv", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Naziv"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Osiguriteli = items;
                }
            }
        }

        private async Task LoadKlasiOsiguruvanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, CONCAT(KlasaBroj, ' - ', KlasaIme) as DisplayName 
                    FROM KlasiOsiguruvanje 
                    WHERE (Disabled = 0 OR Disabled IS NULL)
                    AND Id = '19'
                    ORDER BY KlasaBroj", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayName"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    KlasiOsiguruvanje = items;
                }
            }
        }

        private async Task LoadProdukti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Ime 
                    FROM Produkti 
                    WHERE KlasaOsiguruvanjeId = '19'
                    ORDER BY Ime", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Ime"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Produkti = items;
                }
            }
        }

        private async Task LoadPriciniZaStorno()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, PricinaZaStorno 
                    FROM SifrarnikPricinaZaStorno 
                    ORDER BY PricinaZaStorno", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["PricinaZaStorno"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    PriciniZaStorno = items;
                }
            }
        }

        private async Task LoadValuti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Valuta 
                    FROM SifrarnikValuti 
                    ORDER BY Valuta", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Valuta"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Valuti = items;
                }
            }
        }

        private async Task LoadNaciniNaPlakanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, NacinNaPlakanje 
                    FROM SifrarnikNacinNaPlakanje 
                    ORDER BY NacinNaPlakanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["NacinNaPlakanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    NaciniNaPlakanje = items;
                }
            }
        }

        private async Task LoadTipoviNaPlakanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, TipNaPlakanje 
                    FROM SifrarnikTipNaPlakanje 
                    ORDER BY TipNaPlakanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["TipNaPlakanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    TipoviNaPlakanje = items;
                }
            }
        }

        private async Task LoadBanki()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Banka 
                    FROM SifrarnikBanki 
                    ORDER BY Banka", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Banka"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Banki = items;
                }
            }
        }

        private async Task LoadListaDejnosti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, CONCAT(SifraDejnost, ' - ', NazivDejnost) as DisplayName 
                    FROM ListaDejnosti 
                    ORDER BY SifraDejnost", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayName"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    ListaDejnosti = items;
                }
            }
        }

        private void LoadTipoviNaFaktura()
        {
            TipoviNaFaktura = Enum.GetValues(typeof(TipNaFakturaenumKlasa19))
                .Cast<TipNaFakturaenumKlasa19>()
                .Select(e => new SelectListItem
                {
                    Text = e.GetType()
                        .GetMember(e.ToString())
                        .First()
                        .GetCustomAttribute<DisplayAttribute>()
                        ?.Name ?? e.ToString(),
                    Value = e.GetType()
                        .GetMember(e.ToString())
                        .First()
                        .GetCustomAttribute<EnumMemberAttribute>()
                        ?.Value ?? e.ToString()
                })
                .ToList();
        }

        public class PolisaInputModel
        {
            [Required(ErrorMessage = "Осигурител е задолжително поле")]
            [Display(Name = "Осигурител")]
            public long KlientiIdOsiguritel { get; set; }

            [Required(ErrorMessage = "Класа на осигурување е задолжително поле")]
            [Display(Name = "Класа на осигурување")]
            public int KlasiOsiguruvanjeIdKlasa { get; set; }

            [Display(Name = "Продукт")]
            public int? ProduktiIdProizvod { get; set; }

            [Display(Name = "Дејност")]
            public int? ListaDejnostiId { get; set; }

            [RequiredIf("BrojNaPonuda", null, ErrorMessage = "Број на полиса е задолжително поле кога Број на понуда е празно")]
            [Display(Name = "Број на полиса")]
            public string? BrojNaPolisa { get; set; }

            [Display(Name = "Број на понуда")]
            public long? BrojNaPonuda { get; set; }

            [Display(Name = "Колективна")]
            public bool Kolektivna { get; set; }

            [Required(ErrorMessage = "Договорувач е задолжително поле")]
            [Display(Name = "Договорувач")]
            public long KlientiIdDogovoruvac { get; set; }

            [Display(Name = "Осигуреник")]
            public long? KlientiIdOsigurenik { get; set; }

            [Display(Name = "Соработник")]
            public long? KlientiIdSorabotnik { get; set; }

            [Display(Name = "Факторинг")]
            public bool Faktoring { get; set; }

            [Display(Name = "Генерирана излезна фактура")]
            public bool GeneriranaFakturaIzlezna { get; set; }

            [Display(Name = "Сторно")]
            public bool Storno { get; set; }

            [Display(Name = "Причина за сторно")]
            public int? SifrarnikPricinaZaStornoId { get; set; }

            [Required(ErrorMessage = "Важи од е задолжително поле")]
            [Display(Name = "Важи од")]
            public DateTime? VaziOd { get; set; }

            [Required(ErrorMessage = "Важи до е задолжително поле")]
            [Display(Name = "Важи до")]
            public DateTime? VaziDo { get; set; }

            [Required(ErrorMessage = "Датум на издавање е задолжително поле")]
            [Display(Name = "Датум на издавање")]
            public DateTime? DatumNaIzdavanje { get; set; }

            [Required(ErrorMessage = "Валута е задолжително поле")]
            [Display(Name = "Валута")]
            public int ValutiId { get; set; }

            [Required(ErrorMessage = "Начин на плаќање е задолжително поле")]
            [Display(Name = "Начин на плаќање")]
            public int NaciniNaPlakanjeId { get; set; }

            [Required(ErrorMessage = "Тип на плаќање е задолжително поле")]
            [Display(Name = "Тип на плаќање")]
            public int TipoviNaPlakanjeId { get; set; }

            [Display(Name = "Банка")]
            public int? BankiId { get; set; }

            [Display(Name = "Тип на фактура")]
            public string TipNaFaktura { get; set; }

            [Display(Name = "Број на влезна фактура")]
            [DisplayFormat(NullDisplayText = "")]
            public string? BrojNaVleznaFaktura { get; set; }

            [Display(Name = "Број на излезна фактура")]
            public string? BrojNaIzleznaFaktura { get; set; }

            [Display(Name = "Датум на влезна фактура")]
            public DateTime? DatumNaVleznaFaktura { get; set; }

            [Display(Name = "Датум на излезна фактура")]
            public DateTime? DatumNaIzleznaFaktura { get; set; }

            [Display(Name = "Рок на плаќање излезна фактура")]
            public DateTime? RokNaPlakanjeIzleznaFaktura { get; set; }

            [Display(Name = "Рок на плаќање влезна фактура")]
            public DateTime? RokNaPlakanjeVleznaFaktura { get; set; }

            [Display(Name = "Валута за франшиза")]
            public long? SifrarnikValutiIdFranshizaValuta { get; set; }

            [Display(Name = "Процент франшиза")]
            public decimal? ProcentFransiza { get; set; }

            [Display(Name = "Франшиза износ")]            
            public decimal? FranshizaIznos { get; set; }

            [Display(Name = "Корегирана стапка на провизија")]
            public decimal? KoregiranaStapkaNaProvizija { get; set; }

            // Life Insurance specific fields
            [Display(Name = "Број на осигуреници")]
            public int? BrojNaOsigurenici { get; set; }

            [Display(Name = "Премија за едно лице")]
            public decimal? PremijaZaEdnoLice { get; set; }

            [Display(Name = "Осигурена сума за доживување")]
            public decimal? OsigurenaSumaZaDozhivuvanje { get; set; }

            [Display(Name = "Осигурена сума за смрт од болест")]
            public decimal? OsigurenaSumaZaSmrtOdBolest { get; set; }

            [Display(Name = "Осигурена сума за смрт од незгода")]
            public decimal? OsigurenaSumaZaSmrtOdNezgoda { get; set; }

            [Display(Name = "Привремена осигурителна заштита")]
            public decimal? PrivremenaOsiguritelnaZastita { get; set; }

            [Display(Name = "Премија годишна еднократна")]
            public decimal? PremijaGodishnaEdnokratna { get; set; }

            [Display(Name = "Осигурена сума за 100% траен инвалидитет")]
            public decimal? OsigurenaSumaZa100PercentTraenInvaliditet { get; set; }

            [Display(Name = "Осигурена сума за траен инвалидитет")]
            public decimal? OsigurenaSumaZaTraenInvaliditet { get; set; }

            [Display(Name = "Дневен надомест")]
            public decimal? DnevenNadomest { get; set; }

            [Display(Name = "Премија незгода годишна")]
            public decimal? PremijaNezgodaGodishna { get; set; }

            [Display(Name = "Осигурена сума за тешко болни состојби")]
            public decimal? OsigurenaSumaZaTeskoBolniSostojbi { get; set; }

            [Display(Name = "Премија тешко болни состојби годишна")]
            public decimal? PremijaTeshkoBolniSostojbiGodishna { get; set; }

            [Display(Name = "Осигурена сума за операции")]
            public decimal? OsigurenaSumaZaOperacii { get; set; }

            [Display(Name = "Премија за операции годишна")]
            public decimal? PremijaZaOperaciiGodishna { get; set; }

            [Display(Name = "Осигурена сума за трајна неспособност")]
            public decimal? OsigurenaSumazaTrajnaNesposobnost { get; set; }

            [Display(Name = "Премија за трајна неспособност годишна")]
            public decimal? PremijaZaTrajnaNesposobnostGodishna { get; set; }

            [Display(Name = "Осигурена сума за хируршки интервенции")]
            public decimal? OsigurenaSumaZaHirushkiIntervencii { get; set; }

            [Display(Name = "Премија за хируршки интервенции годишна")]
            public decimal? PremijaZaHirushkiIntervenciiGodishna { get; set; }

            [Display(Name = "Вкупна премија годишна")]
            public decimal? VkupnaPremijaGodishna { get; set; }

            [Display(Name = "Вкупна премија еднократна")]
            public decimal? VkupnaPremijaEdnokratna { get; set; }

            [Display(Name = "Доплатоци за подгодишно плаќање")]
            public decimal? DoplatociZaPodgodishnoPlakjanje { get; set; }

            [Display(Name = "Процент на попуст за фактура во рок")]
            public decimal? ProcentNaPopustZaFakturaVoRok { get; set; }

            [Display(Name = "Износ за плаќање во рок")]
            public decimal? IznosZaPlakjanjeVoRok { get; set; }

            [Display(Name = "Процент комерцијален попуст")]
            public decimal? ProcentKomercijalenPopust { get; set; }

            [Display(Name = "Процент финансиски")]
            public decimal? ProcentFinansiski { get; set; }

            [Display(Name = "Премија за наплата")]
            public decimal? PremijaZaNaplata { get; set; }

            [Display(Name = "Вкупна премија")]
            public decimal? VkupnaPremija { get; set; }
        }

        public async Task<IActionResult> OnGetSearchKlientiAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE MB LIKE @Search + '%'
                       OR EDB LIKE @Search + '%'
                       OR EMBG LIKE @Search + '%'
                       OR Naziv LIKE '%' + @Search + '%'
                       OR Ime LIKE '%' + @Search + '%'
                       OR Prezime LIKE '%' + @Search + '%'
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<IActionResult> OnGetSearchSorabotniciAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE (KlientSorabotnik = 1 or KlientVraboten = 1)
                        AND (MB LIKE @Search + '%'
                        OR EDB LIKE @Search + '%'
                        OR EMBG LIKE @Search + '%'
                        OR Naziv LIKE '%' + @Search + '%'
                        OR Ime LIKE '%' + @Search + '%'
                        OR Prezime LIKE '%' + @Search + '%')
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // Get username from session
                        var username = HttpContext.Session.GetString("Username");
                        long polisaId;

                        // Insert into Polisi table and get the ID
                        using (SqlCommand cmd = new SqlCommand(@"
                            INSERT INTO Polisi (
                                UsernameCreated,
                                KlientiIdOsiguritel,
                                KlasiOsiguruvanjeIdKlasa,
                                ProduktiIdProizvod,
                                BrojNaPolisa,
                                BrojNaPonuda,
                                KlientiIdDogovoruvac,
                                KlientiIdOsigurenik,
                                Kolektivna,
                                DatumVaziOd,
                                DatumVaziDo,
                                DatumNaIzdavanje,
                                SifrarnikValutiIdValuta,
                                KlientiIdSorabotnik,
                                Faktoring,
                                SifrarnikValutiIdFranshizaValuta,
                                ProcentFranshiza,
                                FranshizaIznos,
                                KoregiranaStapkaNaProvizija,
                                SifrarnikNacinNaPlakjanjeId,
                                TipNaFaktura,
                                BrojNaFakturaVlezna,
                                DatumNaFakturaVlezna,
                                RokNaPlakjanjeFakturaVlezna,
                                SifrarnikTipNaPlakanjeId,
                                SifrarnikBankiIdBanka,
                                GeneriranaFakturaIzlezna,
                                BrojNaFakturaIzlezna,
                                DatumNaIzleznaFaktura,
                                RokNaPlakjanjeFakturaIzlezna,
                                Storno,
                                PricinaZaStorno
                            ) VALUES (
                                @UsernameCreated,
                                @KlientiIdOsiguritel,
                                @KlasiOsiguruvanjeIdKlasa,
                                @ProduktiIdProizvod,
                                @BrojNaPolisa,
                                @BrojNaPonuda,
                                @KlientiIdDogovoruvac,
                                @KlientiIdOsigurenik,
                                @Kolektivna,
                                @DatumVaziOd,
                                @DatumVaziDo,
                                @DatumNaIzdavanje,
                                @SifrarnikValutiIdValuta,
                                @KlientiIdSorabotnik,
                                @Faktoring,
                                @SifrarnikValutiIdFranshizaValuta,
                                @ProcentFranshiza,
                                @FranshizaIznos,
                                @KoregiranaStapkaNaProvizija,
                                @SifrarnikNacinNaPlakjanjeId,
                                @TipNaFaktura,
                                @BrojNaVleznaFaktura,
                                @DatumNaVleznaFaktura,
                                @RokNaPlakanjeVleznaFaktura,
                                @SifrarnikTipNaPlakanjeId,
                                @SifrarnikBankiIdBanka,
                                @GeneriranaFakturaIzlezna,
                                @BrojNaIzleznaFaktura,
                                @DatumNaIzleznaFaktura,
                                @RokNaPlakanjeIzleznaFaktura,
                                @Storno,
                                @PricinaZaStorno
                            );
                            SELECT SCOPE_IDENTITY();", connection, transaction))
                        {
                            cmd.Parameters.AddWithValue("@UsernameCreated", username);
                            cmd.Parameters.AddWithValue("@KlientiIdOsiguritel", Input.KlientiIdOsiguritel);
                            cmd.Parameters.AddWithValue("@KlasiOsiguruvanjeIdKlasa", Input.KlasiOsiguruvanjeIdKlasa);
                            cmd.Parameters.AddWithValue("@ProduktiIdProizvod", (object)Input.ProduktiIdProizvod ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@BrojNaPolisa", (object)Input.BrojNaPolisa ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@BrojNaPonuda", (object)Input.BrojNaPonuda ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@KlientiIdDogovoruvac", Input.KlientiIdDogovoruvac);
                            cmd.Parameters.AddWithValue("@KlientiIdOsigurenik", (object)Input.KlientiIdOsigurenik ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@Kolektivna", Input.Kolektivna);
                            cmd.Parameters.AddWithValue("@DatumVaziOd", Input.VaziOd);
                            cmd.Parameters.AddWithValue("@DatumVaziDo", Input.VaziDo);
                            cmd.Parameters.AddWithValue("@DatumNaIzdavanje", Input.DatumNaIzdavanje);
                            cmd.Parameters.AddWithValue("@SifrarnikValutiIdValuta", Input.ValutiId);
                            cmd.Parameters.AddWithValue("@KlientiIdSorabotnik", (object)Input.KlientiIdSorabotnik ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@Faktoring", Input.Faktoring);
                            cmd.Parameters.AddWithValue("@SifrarnikValutiIdFranshizaValuta", Input.SifrarnikValutiIdFranshizaValuta ?? (object)DBNull.Value);
                            cmd.Parameters.AddWithValue("@ProcentFranshiza", (object)Input.ProcentFransiza ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@FranshizaIznos", Input.FranshizaIznos.HasValue ? (object)Input.FranshizaIznos.Value : DBNull.Value);
                            cmd.Parameters.AddWithValue("@KoregiranaStapkaNaProvizija", (object)Input.KoregiranaStapkaNaProvizija ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@SifrarnikNacinNaPlakjanjeId", Input.NaciniNaPlakanjeId);
                            cmd.Parameters.AddWithValue("@TipNaFaktura", Input.TipNaFaktura);
                            cmd.Parameters.AddWithValue("@BrojNaVleznaFaktura", (object)Input.BrojNaVleznaFaktura ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@DatumNaVleznaFaktura", (object)Input.DatumNaVleznaFaktura ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@RokNaPlakanjeVleznaFaktura", (object)Input.RokNaPlakanjeVleznaFaktura ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@SifrarnikTipNaPlakanjeId", Input.TipoviNaPlakanjeId);
                            cmd.Parameters.AddWithValue("@SifrarnikBankiIdBanka", (object)Input.BankiId ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@GeneriranaFakturaIzlezna", Input.GeneriranaFakturaIzlezna);
                            cmd.Parameters.AddWithValue("@BrojNaIzleznaFaktura", (object)Input.BrojNaIzleznaFaktura ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@DatumNaIzleznaFaktura", (object)Input.DatumNaIzleznaFaktura ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@RokNaPlakanjeIzleznaFaktura", (object)Input.RokNaPlakanjeIzleznaFaktura ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@Storno", Input.Storno);
                            cmd.Parameters.AddWithValue("@PricinaZaStorno", (object)Input.SifrarnikPricinaZaStornoId ?? DBNull.Value);

                            polisaId = Convert.ToInt64(await cmd.ExecuteScalarAsync());
                        }

                        // Insert into PolisiKlasa19 table
                        using (SqlCommand cmd = new SqlCommand(@"
                            INSERT INTO PolisiKlasa19 (
                                UsernameCreated,
                                PolisaId,
                                ListaDejnostiId,
                                BrojNaOsigurenici,
                                PremijaZaEdnoLice,
                                OsigurenaSumaZaDozhivuvanje,
                                OsigurenaSumaZaSmrtOdBolest,
                                OsigurenaSumaZaSmrtOdNezgoda,
                                PrivremenaOsiguritelnaZastita,
                                PremijaGodishnaEdnokratna,
                                OsigurenaSumaZa100PercentTraenInvaliditet,
                                OsigurenaSumaZaTraenInvaliditet,
                                DnevenNadomest,
                                PremijaNezgodaGodishna,
                                OsigurenaSumaZaTeskoBolniSostojbi,
                                PremijaTeshkoBolniSostojbiGodishna,
                                OsigurenaSumaZaOperacii,
                                PremijaZaOperaciiGodishna,
                                OsigurenaSumazaTrajnaNesposobnost,
                                PremijaZaTrajnaNesposobnostGodishna,
                                OsigurenaSumaZaHirushkiIntervencii,
                                PremijaZaHirushkiIntervenciiGodishna,
                                VkupnaPremijaGodishna,
                                VkupnaPremijaEdnokratna,
                                DoplatociZaPodgodishnoPlakjanje,
                                ProcentNaPopustZaFakturaVoRok,
                                IznosZaPlakjanjeVoRok,
                                ProcentKomercijalenPopust,
                                ProcentFinansiski,
                                PremijaZaNaplata,
                                VkupnaPremija
                            ) VALUES (
                                @UsernameCreated,
                                @PolisaId,
                                @ListaDejnostiId,
                                @BrojNaOsigurenici,
                                @PremijaZaEdnoLice,
                                @OsigurenaSumaZaDozhivuvanje,
                                @OsigurenaSumaZaSmrtOdBolest,
                                @OsigurenaSumaZaSmrtOdNezgoda,
                                @PrivremenaOsiguritelnaZastita,
                                @PremijaGodishnaEdnokratna,
                                @OsigurenaSumaZa100PercentTraenInvaliditet,
                                @OsigurenaSumaZaTraenInvaliditet,
                                @DnevenNadomest,
                                @PremijaNezgodaGodishna,
                                @OsigurenaSumaZaTeskoBolniSostojbi,
                                @PremijaTeshkoBolniSostojbiGodishna,
                                @OsigurenaSumaZaOperacii,
                                @PremijaZaOperaciiGodishna,
                                @OsigurenaSumazaTrajnaNesposobnost,
                                @PremijaZaTrajnaNesposobnostGodishna,
                                @OsigurenaSumaZaHirushkiIntervencii,
                                @PremijaZaHirushkiIntervenciiGodishna,
                                @VkupnaPremijaGodishna,
                                @VkupnaPremijaEdnokratna,
                                @DoplatociZaPodgodishnoPlakjanje,
                                @ProcentNaPopustZaFakturaVoRok,
                                @IznosZaPlakjanjeVoRok,
                                @ProcentKomercijalenPopust,
                                @ProcentFinansiski,
                                @PremijaZaNaplata,
                                @VkupnaPremija
                            )", connection, transaction))
                        {
                            cmd.Parameters.AddWithValue("@UsernameCreated", username);
                            cmd.Parameters.AddWithValue("@PolisaId", polisaId);
                            cmd.Parameters.AddWithValue("@ListaDejnostiId", (object)Input.ListaDejnostiId ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@BrojNaOsigurenici", (object)Input.BrojNaOsigurenici ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@PremijaZaEdnoLice", (object)Input.PremijaZaEdnoLice ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@OsigurenaSumaZaDozhivuvanje", (object)Input.OsigurenaSumaZaDozhivuvanje ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@OsigurenaSumaZaSmrtOdBolest", (object)Input.OsigurenaSumaZaSmrtOdBolest ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@OsigurenaSumaZaSmrtOdNezgoda", (object)Input.OsigurenaSumaZaSmrtOdNezgoda ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@PrivremenaOsiguritelnaZastita", (object)Input.PrivremenaOsiguritelnaZastita ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@PremijaGodishnaEdnokratna", (object)Input.PremijaGodishnaEdnokratna ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@OsigurenaSumaZa100PercentTraenInvaliditet", (object)Input.OsigurenaSumaZa100PercentTraenInvaliditet ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@OsigurenaSumaZaTraenInvaliditet", (object)Input.OsigurenaSumaZaTraenInvaliditet ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@DnevenNadomest", (object)Input.DnevenNadomest ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@PremijaNezgodaGodishna", (object)Input.PremijaNezgodaGodishna ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@OsigurenaSumaZaTeskoBolniSostojbi", (object)Input.OsigurenaSumaZaTeskoBolniSostojbi ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@PremijaTeshkoBolniSostojbiGodishna", (object)Input.PremijaTeshkoBolniSostojbiGodishna ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@OsigurenaSumaZaOperacii", (object)Input.OsigurenaSumaZaOperacii ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@PremijaZaOperaciiGodishna", (object)Input.PremijaZaOperaciiGodishna ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@OsigurenaSumazaTrajnaNesposobnost", (object)Input.OsigurenaSumazaTrajnaNesposobnost ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@PremijaZaTrajnaNesposobnostGodishna", (object)Input.PremijaZaTrajnaNesposobnostGodishna ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@OsigurenaSumaZaHirushkiIntervencii", (object)Input.OsigurenaSumaZaHirushkiIntervencii ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@PremijaZaHirushkiIntervenciiGodishna", (object)Input.PremijaZaHirushkiIntervenciiGodishna ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@VkupnaPremijaGodishna", (object)Input.VkupnaPremijaGodishna ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@VkupnaPremijaEdnokratna", (object)Input.VkupnaPremijaEdnokratna ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@DoplatociZaPodgodishnoPlakjanje", (object)Input.DoplatociZaPodgodishnoPlakjanje ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@ProcentNaPopustZaFakturaVoRok", (object)Input.ProcentNaPopustZaFakturaVoRok ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@IznosZaPlakjanjeVoRok", (object)Input.IznosZaPlakjanjeVoRok ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@ProcentKomercijalenPopust", (object)Input.ProcentKomercijalenPopust ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@ProcentFinansiski", (object)Input.ProcentFinansiski ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@PremijaZaNaplata", (object)Input.PremijaZaNaplata ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@VkupnaPremija", (object)Input.VkupnaPremija ?? DBNull.Value);

                            await cmd.ExecuteNonQueryAsync();
                        }

                        // Call the stored procedure to generate payment installments
                        // Only generate installments if BrojNaPolisa has a value
                        if (!string.IsNullOrEmpty(Input.BrojNaPolisa))
                        {
                            using (SqlCommand cmdRati = new SqlCommand("GenerirajRatiPolisa", connection, transaction))
                            {
                                cmdRati.CommandType = CommandType.StoredProcedure;

                                cmdRati.Parameters.AddWithValue("@PolisaId", polisaId);
                                cmdRati.Parameters.AddWithValue("@SifrarnikValutiId", Input.ValutiId);
                                cmdRati.Parameters.AddWithValue("@DatumNaIzdavanje", Input.DatumNaIzdavanje ?? (object)DBNull.Value);
                                cmdRati.Parameters.AddWithValue("@SifrarnikNacinNaPlakanjeId", Input.NaciniNaPlakanjeId);
                                cmdRati.Parameters.AddWithValue("@Premija", Input.VkupnaPremija ?? (object)DBNull.Value);

                                await cmdRati.ExecuteNonQueryAsync();
                            }
                        }

                        await transaction.CommitAsync();
                        TempData["SuccessMessage"] = "Полисата е успешно зачувана.";
                        return RedirectToPage("/Polisi/ListaPolisi");
                    }
                    catch (Exception ex)
                    {
                        await transaction.RollbackAsync();
                        ModelState.AddModelError("", $"Грешка при зачувување: {ex.Message}");
                        if (ex.InnerException != null)
                        {
                            ModelState.AddModelError("", $"Дополнителни информации: {ex.InnerException.Message}");
                        }
                        return Page();
                    }
                }
            }
        }

        public async Task<JsonResult> OnGetCheckBrojNaPolisaAsync(string brojNaPolisa)
        {
            if (string.IsNullOrWhiteSpace(brojNaPolisa))
            {
                return new JsonResult(0);
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand("SELECT dbo.ProverkaZaPostoenjeNaPolisaBezStorno(@brojNaPolisa)", connection))
                {
                    cmd.Parameters.AddWithValue("@brojNaPolisa", brojNaPolisa);
                    var result = await cmd.ExecuteScalarAsync();
                    return new JsonResult(Convert.ToInt32(result));
                }
            }
        }
    }
} 