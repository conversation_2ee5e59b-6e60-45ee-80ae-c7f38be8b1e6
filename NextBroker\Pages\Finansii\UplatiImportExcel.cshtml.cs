using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using Microsoft.Data.SqlClient;
using System.Data;
using System.Text;
using OfficeOpenXml;

namespace NextBroker.Pages.Finansii
{
    public class UplatiImportExcelModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        [BindProperty]
        public bool HasAdminAccess { get; set; }
        public List<SelectListItem> Osiguriteli { get; set; } = new();
        public long TipNaPlakanjeId { get; private set; } = 6;

        [BindProperty]
        public List<UplataImportModel> PreviewUplati { get; set; } = new();
        
        public bool IsPreview { get; set; }

        public UplatiImportExcelModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
            // Set EPPlus license context
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        public async Task<IActionResult> OnGetAsync()
        {
            if (!await HasPageAccess("Uplati"))
            {
                return RedirectToAccessDenied();
            }

            HasAdminAccess = await HasPageAccess("UplatiAdmin");
            await LoadOsiguriteli();
            return Page();
        }

        private async Task LoadOsiguriteli()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Naziv 
                    FROM Klienti 
                    WHERE Osiguritel = 1 
                    ORDER BY Naziv", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        Osiguriteli.Add(new SelectListItem
                        {
                            Value = reader["Id"].ToString(),
                            Text = reader["Naziv"].ToString()
                        });
                    }
                }
            }
        }

        [BindProperty]
        public IFormFile ExcelFile { get; set; }

        [BindProperty]
        public long OsiguritelId { get; set; }

        public class UplataImportModel
        {
            public DateTime UplataDatum { get; set; }
            public decimal Iznos { get; set; }
            public string PolisaBroj { get; set; }
            public bool Neraspredelena { get; set; }
            public bool PovratNaSredstva { get; set; }
            public bool IsSelected { get; set; } = true;
            public string ValidationMessage { get; set; }
            public bool IsValid { get; set; } = true;
            public string BrojNaFaktura { get; set; }
        }

        private async Task ValidatePolisi(List<UplataImportModel> uplati)
        {
            var polisiBroevi = uplati.Where(u => !string.IsNullOrEmpty(u.PolisaBroj))
                                    .Select(u => u.PolisaBroj)
                                    .Distinct()
                                    .ToList();

            if (!polisiBroevi.Any()) return;

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                
                // Create parameterized query for policy numbers
                var parameters = new List<SqlParameter>();
                var paramPlaceholders = new List<string>();
                
                for (int i = 0; i < polisiBroevi.Count; i++)
                {
                    var paramName = $"@polisa{i}";
                    parameters.Add(new SqlParameter(paramName, polisiBroevi[i]));
                    paramPlaceholders.Add(paramName);
                }

                var sql = $@"
                    SELECT BrojNaPolisa, Storno 
                    FROM Polisi 
                    WHERE BrojNaPolisa IN ({string.Join(",", paramPlaceholders)})";

                using (SqlCommand cmd = new SqlCommand(sql, connection))
                {
                    cmd.Parameters.AddRange(parameters.ToArray());
                    
                    var polisiDict = new Dictionary<string, bool>();
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            polisiDict.Add(
                                reader.GetString(0), // BrojNaPolisa
                                reader.GetBoolean(1) // Storno
                            );
                        }
                    }

                    foreach (var uplata in uplati.Where(u => !string.IsNullOrEmpty(u.PolisaBroj)))
                    {
                        if (!polisiDict.ContainsKey(uplata.PolisaBroj))
                        {
                            uplata.ValidationMessage = "Полисата не постои во систем";
                            uplata.IsValid = false;
                            uplata.IsSelected = false;
                        }
                        else if (polisiDict[uplata.PolisaBroj]) // Storno = true
                        {
                            uplata.ValidationMessage = "Не може да се внесе уплата за сторнирана полиса";
                            uplata.IsValid = false;
                            uplata.IsSelected = false;
                        }
                    }
                }
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!await HasPageAccess("UplatiAdmin"))
                return RedirectToAccessDenied();

            if (ExcelFile == null || ExcelFile.Length == 0)
            {
                ModelState.AddModelError("ExcelFile", "Изберете Excel документ");
                await LoadOsiguriteli();
                return Page();
            }

            var uplati = new List<UplataImportModel>();

            try
            {
                using (var stream = ExcelFile.OpenReadStream())
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets[0];
                    var rowCount = worksheet.Dimension?.Rows ?? 0;
                    var colCount = worksheet.Dimension?.Columns ?? 0;

                    if (rowCount <= 1) // Only header or empty
                    {
                        ModelState.AddModelError("", "Excel документот е празен");
                        await LoadOsiguriteli();
                        return Page();
                    }

                    // Get header row to find columns
                    var headers = new Dictionary<string, int>();
                    for (int col = 1; col <= colCount; col++)
                    {
                        var headerValue = worksheet.Cells[1, col].Text?.Trim();
                        if (!string.IsNullOrEmpty(headerValue))
                        {
                            headers[headerValue] = col;
                        }
                    }

                    // Validate required columns exist
                    var requiredColumns = new[] { "UplataDatum", "Iznos", "BrojNaPolisa", "Neraspredelena", "PovratNaSredstva" };
                    foreach (var column in requiredColumns)
                    {
                        if (!headers.ContainsKey(column))
                        {
                            ModelState.AddModelError("", $"Недостасува колона '{column}' во Excel документот");
                            await LoadOsiguriteli();
                            return Page();
                        }
                    }

                    // Read data rows
                    for (int row = 2; row <= rowCount; row++)
                    {
                        try
                        {
                            var uplataDatumCell = worksheet.Cells[row, headers["UplataDatum"]];
                            var iznosCell = worksheet.Cells[row, headers["Iznos"]].Text;
                            var brojNaPolisaCell = worksheet.Cells[row, headers["BrojNaPolisa"]].Text;
                            var neraspredelenaCell = worksheet.Cells[row, headers["Neraspredelena"]].Text;
                            var povratNaSredstvaCell = worksheet.Cells[row, headers["PovratNaSredstva"]].Text;
                            var brojNaFakturaCell = headers.ContainsKey("BrojNaFaktura") 
                                ? worksheet.Cells[row, headers["BrojNaFaktura"]].Text 
                                : null;

                            if (string.IsNullOrEmpty(uplataDatumCell.Text) && string.IsNullOrEmpty(iznosCell))
                            {
                                continue; // Skip empty rows
                            }

                            DateTime uplataDatum;
                            
                            // First try to get the date directly from Excel's date value
                            if (uplataDatumCell.Value is DateTime)
                            {
                                uplataDatum = (DateTime)uplataDatumCell.Value;
                            }
                            else
                            {
                                var datumStr = uplataDatumCell.Text?.Trim();
                                // Then try parsing the text value
                                if (!DateTime.TryParseExact(datumStr,
                                    new[] { "dd.MM.yyyy", "d.MM.yyyy", "dd.M.yyyy", "d.M.yyyy" },
                                    System.Globalization.CultureInfo.InvariantCulture,
                                    System.Globalization.DateTimeStyles.None,
                                    out uplataDatum))
                                {
                                    if (!DateTime.TryParse(datumStr,
                                        System.Globalization.CultureInfo.CurrentCulture,
                                        System.Globalization.DateTimeStyles.None,
                                        out uplataDatum))
                                    {
                                        throw new Exception($"Невалиден формат на датум '{datumStr}'. Користете формат дд.мм.гггг (пример: 31.12.2024)");
                                    }
                                }
                            }

                            uplati.Add(new UplataImportModel
                            {
                                UplataDatum = uplataDatum,
                                Iznos = decimal.Parse(iznosCell),
                                PolisaBroj = !string.IsNullOrEmpty(brojNaPolisaCell) ? brojNaPolisaCell.Trim() : null,
                                Neraspredelena = ParseBoolean(neraspredelenaCell),
                                PovratNaSredstva = ParseBoolean(povratNaSredstvaCell),
                                BrojNaFaktura = !string.IsNullOrEmpty(brojNaFakturaCell) ? brojNaFakturaCell.Trim() : null
                            });
                        }
                        catch (Exception ex)
                        {
                            ModelState.AddModelError("", $"Грешка во ред {row}: {ex.Message}");
                            await LoadOsiguriteli();
                            return Page();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", $"Грешка при читање на Excel документот: {ex.Message}");
                await LoadOsiguriteli();
                return Page();
            }

            if (!uplati.Any())
            {
                ModelState.AddModelError("", "Нема валидни податоци за импорт");
                await LoadOsiguriteli();
                return Page();
            }

            await ValidatePolisi(uplati);
            PreviewUplati = uplati;
            IsPreview = true;
            await LoadOsiguriteli();
            return Page();
        }

        public async Task<IActionResult> OnPostSaveAsync()
        {
            if (!await HasPageAccess("UplatiAdmin"))
                return RedirectToAccessDenied();

            if (PreviewUplati == null || !PreviewUplati.Any())
            {
                ModelState.AddModelError("", "Нема податоци за импорт");
                await LoadOsiguriteli();
                return Page();
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    INSERT INTO Uplati (
                        UplataDatum, 
                        SifrarnikTipNaPlakanjeId,
                        Iznos,
                        PolisaBroj,
                        Neraspredelena,
                        PovratNaSredstva,
                        KlientIdUplakjac,
                        UsernameCreated,
                        BrojNaFaktura
                    ) VALUES (
                        @UplataDatum,
                        @SifrarnikTipNaPlakanjeId,
                        @Iznos,
                        @PolisaBroj,
                        @Neraspredelena,
                        @PovratNaSredstva,
                        @KlientIdUplakjac,
                        @UsernameCreated,
                        @BrojNaFaktura
                    )", connection))
                {
                    foreach (var uplata in PreviewUplati.Where(u => u.IsSelected))
                    {
                        cmd.Parameters.Clear();
                        cmd.Parameters.AddWithValue("@UplataDatum", uplata.UplataDatum);
                        cmd.Parameters.AddWithValue("@SifrarnikTipNaPlakanjeId", TipNaPlakanjeId);
                        cmd.Parameters.AddWithValue("@Iznos", uplata.Iznos);
                        cmd.Parameters.AddWithValue("@PolisaBroj", string.IsNullOrEmpty(uplata.PolisaBroj) ? DBNull.Value : (object)uplata.PolisaBroj);
                        cmd.Parameters.AddWithValue("@Neraspredelena", uplata.Neraspredelena);
                        cmd.Parameters.AddWithValue("@PovratNaSredstva", uplata.PovratNaSredstva);
                        cmd.Parameters.AddWithValue("@KlientIdUplakjac", OsiguritelId);
                        cmd.Parameters.AddWithValue("@UsernameCreated", HttpContext.Session.GetString("Username"));
                        cmd.Parameters.AddWithValue("@BrojNaFaktura", string.IsNullOrEmpty(uplata.BrojNaFaktura) ? DBNull.Value : (object)uplata.BrojNaFaktura);

                        await cmd.ExecuteNonQueryAsync();
                    }
                }
            }

            return RedirectToPage("/Finansii/Uplati");
        }

        private bool ParseBoolean(string value)
        {
            if (string.IsNullOrWhiteSpace(value)) return false;
            
            // Handle "0"/"1" format
            if (value == "0") return false;
            if (value == "1") return true;
            
            // Handle "true"/"false" format
            if (bool.TryParse(value, out bool result))
                return result;
        
            return false; // Default value if parsing fails
        }
    }
} 