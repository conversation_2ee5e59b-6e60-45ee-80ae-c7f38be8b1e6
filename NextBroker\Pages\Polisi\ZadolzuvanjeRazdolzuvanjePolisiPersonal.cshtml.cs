using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Antiforgery;
using System.Text.Json;
using System.Text;
using System.Web;
using iText.Html2pdf;
using iText.Kernel.Pdf;
using iText.Layout;
using System.IO;
using OfficeOpenXml;
using System.Drawing;
using OfficeOpenXml.Style;

namespace NextBroker.Pages.Polisi
{
    public class ZadolzuvanjeRazdolzuvanjePolisiPersonalModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        private readonly IAntiforgery _antiforgery;

        public ZadolzuvanjeRazdolzuvanjePolisiPersonalModel(IConfiguration configuration, IAntiforgery antiforgery)
            : base(configuration)
        {
            _configuration = configuration;
            _antiforgery = antiforgery;
        }

        [BindProperty]
        public FilterModel Filter { get; set; } = new();
        public List<ZadolzuvanjeRazdolzuvanjeViewModel> Results { get; set; }
        public IEnumerable<SelectListItem> Osiguriteli { get; set; }
        public IEnumerable<SelectListItem> KlasiOsiguruvanje { get; set; }
        public IEnumerable<SelectListItem> Produkti { get; set; }
        public IEnumerable<SelectListItem> Zadolzeni { get; set; }
        public IEnumerable<SelectListItem> OsnoviZaRazdolzuvanje { get; set; }

        // Property to check if any results have BrojNaPersonalnaRazdolznica
        public bool HasAnyRazdolznicaNumbers => Results?.Any(r => !string.IsNullOrWhiteSpace(r.BrojNaPersonalnaRazdolznica)) ?? false;

        public class FilterModel
        {
            public DateTime? DateCreatedFrom { get; set; }
            public DateTime? DateCreatedTo { get; set; }
            public DateTime? DateModifiedFrom { get; set; }
            public DateTime? DateModifiedTo { get; set; }
            public List<long> KlientiIdOsiguritel { get; set; } = new List<long>();
            public int? KlasiOsiguruvanjeIdKlasa { get; set; }
            public int? ProduktiIdProdukt { get; set; }
            public string BrojNaPolisa { get; set; }
            public string BrojNaPolisaTo { get; set; }
            public long? BrojNaPonuda { get; set; }
            public long? BrojNaPonudaTo { get; set; }
            public long? KlientiIdZadolzen { get; set; }
            public DateTime? DatumNaZadolzuvanje { get; set; }
            public long? SifrarnikOsnovZaRazdolzuvanjeId { get; set; }
            public bool? PotvrdenoRazdolzuvanjeKajBroker { get; set; }
            public DateTime? DatumNaRazdolzuvanjeKajBrokerFrom { get; set; }
            public DateTime? DatumNaRazdolzuvanjeKajBrokerTo { get; set; }
            public bool? PotvrdenoRazdolzuvanjeKajOsiguritel { get; set; }
            public DateTime? DatumNaRazdolzuvanjeKajOsiguritelFrom { get; set; }
            public DateTime? DatumNaRazdolzuvanjeKajOsiguritelTo { get; set; }
            public string ExcludeBrojNaPolisa { get; set; }
            public string BrojNaPersonalnaRazdolznica { get; set; }
            public DateTime? DatumNaIzdavanjeFrom { get; set; }
            public DateTime? DatumNaIzdavanjeTo { get; set; }
        }

        public class ZadolzuvanjeRazdolzuvanjeViewModel
        {
            public long Id { get; set; }
            public DateTime? DateCreated { get; set; }
            public string UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string UsernameModified { get; set; }
            public string OsiguritelNaziv { get; set; }
            public long? KlientiIdOsiguritel { get; set; }
            public string KlasaIme { get; set; }
            public int? KlasiOsiguruvanjeIdKlasa { get; set; }
            public string ProduktIme { get; set; }
            public int? ProduktiIdProdukt { get; set; }
            public string BrojNaPolisa { get; set; }
            public long? BrojNaPonuda { get; set; }
            public string ZadolzenIme { get; set; }
            public DateTime? DatumNaZadolzuvanje { get; set; }
            public string OsnovZaRazdolzuvanje { get; set; }
            public bool PotvrdenoRazdolzuvanjeKajBroker { get; set; }
            public DateTime? DatumNaRazdolzuvanjeKajBroker { get; set; }
            public bool PotvrdenoRazdolzuvanjeKajOsiguritel { get; set; }
            public DateTime? DatumNaRazdolzuvanjeKajOsiguritel { get; set; }
            public DateTime? DatumNaIzdavanje { get; set; }
            public string Status { get; set; }
            public bool IsStornirana { get; set; }
            public string BrojNaPersonalnaRazdolznica { get; set; }
        }

        public async Task<IActionResult> OnGetAsync()
        {
            if (!await HasPageAccess("ZadolzuvanjeRazdolzuvanjePolisiPersonal"))
            {
                return RedirectToAccessDenied();
            }

            await LoadDropdowns();
            return Page();
        }

        public async Task<JsonResult> OnGetProdukti(int klasaId)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            var produkti = new List<object>();

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Ime
                    FROM Produkti                    
                    ORDER BY Ime", connection))
                {
                    cmd.Parameters.AddWithValue("@KlasaId", klasaId);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        produkti.Add(new
                        {
                            id = reader["Id"].ToString(),
                            text = reader["Ime"].ToString()
                        });
                    }
                }
            }

            return new JsonResult(produkti);
        }

        private async Task LoadDropdowns()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // Load Osiguriteli
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Naziv 
                    FROM Klienti 
                    WHERE Osiguritel = 1 
                    ORDER BY Naziv", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Naziv"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Osiguriteli = items;
                }

                // Load KlasiOsiguruvanje
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, KlasaIme 
                    FROM KlasiOsiguruvanje 
                    WHERE Id IN (1,2,3,8,9,10,18,19)
                    ORDER BY KlasaIme", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["KlasaIme"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    KlasiOsiguruvanje = items;
                }

                // Load Zadolzeni
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, 
                           CASE 
                               WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                               ELSE CONCAT(Ime, ' ', Prezime)
                           END as DisplayName
                    FROM Klienti 
                    WHERE EMBG IN (SELECT EMB FROM Users WHERE Username = @Username)
                        OR MB IN (SELECT EMB FROM Users WHERE Username = @Username)
                    ORDER BY DisplayName", connection))
                {
                    cmd.Parameters.AddWithValue("@Username", HttpContext.Session.GetString("Username"));
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayName"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Zadolzeni = items;
                }

                // Load OsnoviZaRazdolzuvanje
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, OsnovZaRazdolzuvanje
                    FROM SifrarnikOsnovZaRazdolzuvanje
                    ORDER BY OsnovZaRazdolzuvanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["OsnovZaRazdolzuvanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    OsnoviZaRazdolzuvanje = items;
                }
            }
        }

        // Add a static property to store the filtered IDs
        private static List<long> CurrentFilteredIds { get; set; } = new List<long>();
        private static string CurrentWhereClause { get; set; } = "";

        public async Task<IActionResult> OnPostAsync()
        {
            if (!await HasPageAccess("ZadolzuvanjeRazdolzuvanjePolisiPersonal"))
            {
                return RedirectToAccessDenied();
            }

            await LoadDropdowns();
            var (whereClause, parameters) = BuildWhereClause();
            CurrentWhereClause = whereClause;
            CurrentFilteredIds = await GetFilteredIds(whereClause, parameters);
            await LoadResults();
            return Page();
        }

        private async Task<List<long>> GetFilteredIds(string whereClause, List<SqlParameter> parameters)
        {
            var filteredIds = new List<long>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                var selectQuery = $@"
                    SELECT p.Id
                    FROM PolisiZadolzuvanjeRazdolzuvanje p
                    LEFT JOIN Klienti ko ON p.KlientiIdOsiguritel = ko.Id
                    LEFT JOIN KlasiOsiguruvanje kl ON p.KlasiOsiguruvanjeIdKlasa = kl.Id
                    LEFT JOIN Produkti pr ON p.ProduktiIdProdukt = pr.Id
                    LEFT JOIN Klienti kz ON p.KlientiIdZadolzen = kz.Id
                    LEFT JOIN SifrarnikOsnovZaRazdolzuvanje s ON p.SifrarnikOsnovZaRazdolzuvanjeId = s.Id
                    WHERE 1=1 {whereClause}";

                using (SqlCommand cmd = new SqlCommand(selectQuery, connection))
                {
                    cmd.Parameters.AddRange(parameters.ToArray());
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            filteredIds.Add(reader.GetInt64(0));
                        }
                    }
                }
            }
            return filteredIds;
        }

        private async Task LoadResults()
        {
            Results = new List<ZadolzuvanjeRazdolzuvanjeViewModel>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                var query = @"
                    SELECT 
                        z.*,
                        ko.Naziv as OsiguritelNaziv,
                        kl.KlasaIme,
                        p.Ime as ProduktIme,
                        CASE 
                            WHEN kz.KlientFizickoPravnoLice = 'P' THEN kz.Naziv
                            ELSE CONCAT(kz.Ime, ' ', kz.Prezime)
                        END as ZadolzenIme,
                        s.OsnovZaRazdolzuvanje,
                        dbo.ZadolzuvanjeRazdolzuvanjeVratiPolisaDatumNaIzdavanje(z.BrojNaPolisa) as DatumNaIzdavanje,
                        dbo.VratiDaliImaStorniranaBrojNaPolisa(z.BrojNaPolisa) as IsStornirana,
                        CASE 
                            WHEN dbo.VratiDaliImaStorniranaBrojNaPolisa(z.BrojNaPolisa) = 1 THEN 'Сторнирана'
                            ELSE ''
                        END as Status,
                        z.BrojNaPersonalnaRazdolznica
                    FROM PolisiZadolzuvanjeRazdolzuvanje z
                    LEFT JOIN Klienti ko ON z.KlientiIdOsiguritel = ko.Id
                    LEFT JOIN KlasiOsiguruvanje kl ON z.KlasiOsiguruvanjeIdKlasa = kl.Id
                    LEFT JOIN Produkti p ON z.ProduktiIdProdukt = p.Id
                    LEFT JOIN Klienti kz ON z.KlientiIdZadolzen = kz.Id
                    LEFT JOIN SifrarnikOsnovZaRazdolzuvanje s ON z.SifrarnikOsnovZaRazdolzuvanjeId = s.Id
                    WHERE 1=1";

                var parameters = new List<SqlParameter>();

                if (Filter.DateCreatedFrom.HasValue)
                {
                    query += " AND z.DateCreated >= @DateCreatedFrom";
                    parameters.Add(new SqlParameter("@DateCreatedFrom", Filter.DateCreatedFrom));
                }

                if (Filter.DateCreatedTo.HasValue)
                {
                    query += " AND z.DateCreated <= @DateCreatedTo";
                    parameters.Add(new SqlParameter("@DateCreatedTo", Filter.DateCreatedTo));
                }

                if (Filter.DateModifiedFrom.HasValue)
                {
                    query += " AND z.DateModified >= @DateModifiedFrom";
                    parameters.Add(new SqlParameter("@DateModifiedFrom", Filter.DateModifiedFrom));
                }

                if (Filter.DateModifiedTo.HasValue)
                {
                    query += " AND z.DateModified <= @DateModifiedTo";
                    parameters.Add(new SqlParameter("@DateModifiedTo", Filter.DateModifiedTo));
                }

                if (Filter.KlientiIdOsiguritel != null && Filter.KlientiIdOsiguritel.Any())
                {
                    var paramNames = new List<string>();
                    for (int i = 0; i < Filter.KlientiIdOsiguritel.Count; i++)
                    {
                        var paramName = $"@KlientiIdOsiguritel{i}";
                        paramNames.Add(paramName);
                        parameters.Add(new SqlParameter(paramName, Filter.KlientiIdOsiguritel[i]));
                    }
                    query += " AND z.KlientiIdOsiguritel IN (" + string.Join(",", paramNames) + ")";
                }

                if (Filter.KlasiOsiguruvanjeIdKlasa.HasValue)
                {
                    query += " AND z.KlasiOsiguruvanjeIdKlasa = @KlasiOsiguruvanjeIdKlasa";
                    parameters.Add(new SqlParameter("@KlasiOsiguruvanjeIdKlasa", Filter.KlasiOsiguruvanjeIdKlasa));
                }

                if (Filter.ProduktiIdProdukt.HasValue)
                {
                    query += " AND z.ProduktiIdProdukt = @ProduktiIdProdukt";
                    parameters.Add(new SqlParameter("@ProduktiIdProdukt", Filter.ProduktiIdProdukt));
                }

                if (!string.IsNullOrWhiteSpace(Filter.BrojNaPolisa))
                {
                    if (!string.IsNullOrWhiteSpace(Filter.BrojNaPolisaTo))
                    {
                        query += " AND z.BrojNaPolisa BETWEEN @BrojNaPolisaFrom AND @BrojNaPolisaTo";
                        parameters.Add(new SqlParameter("@BrojNaPolisaFrom", Filter.BrojNaPolisa));
                        parameters.Add(new SqlParameter("@BrojNaPolisaTo", Filter.BrojNaPolisaTo));
                    }
                    else
                    {
                        query += " AND z.BrojNaPolisa = @BrojNaPolisa";
                        parameters.Add(new SqlParameter("@BrojNaPolisa", Filter.BrojNaPolisa));
                    }
                }
                else if (!string.IsNullOrWhiteSpace(Filter.ExcludeBrojNaPolisa))
                {
                    var policyNumbers = Filter.ExcludeBrojNaPolisa.Split(',')
                        .Select(p => p.Trim())
                        .Where(p => !string.IsNullOrWhiteSpace(p))
                        .ToList();

                    if (policyNumbers.Any())
                    {
                        var parameterNames = new List<string>();
                        
                        for (int i = 0; i < policyNumbers.Count; i++)
                        {
                            string paramName = $"@ExcludePolisa{i}";
                            parameterNames.Add(paramName);
                            parameters.Add(new SqlParameter(paramName, policyNumbers[i]));
                        }
                        
                        query += " AND (z.BrojNaPolisa IS NULL OR (z.BrojNaPolisa IS NOT NULL AND z.BrojNaPolisa NOT IN (" + string.Join(",", parameterNames) + ")))";
                    }
                }



                if (Filter.BrojNaPonuda.HasValue)
                {
                    if (Filter.BrojNaPonudaTo.HasValue)
                    {
                        query += " AND z.BrojNaPonuda BETWEEN @BrojNaPonudaFrom AND @BrojNaPonudaTo";
                        parameters.Add(new SqlParameter("@BrojNaPonudaFrom", Filter.BrojNaPonuda));
                        parameters.Add(new SqlParameter("@BrojNaPonudaTo", Filter.BrojNaPonudaTo));
                    }
                    else
                    {
                        query += " AND z.BrojNaPonuda = @BrojNaPonuda";
                        parameters.Add(new SqlParameter("@BrojNaPonuda", Filter.BrojNaPonuda));
                    }
                }

                if (Filter.KlientiIdZadolzen.HasValue)
                {
                    if (Filter.KlientiIdZadolzen == -1)
                    {
                        query += " AND z.KlientiIdZadolzen IS NULL";
                    }
                    else
                    {
                        query += " AND z.KlientiIdZadolzen = @KlientiIdZadolzen";
                        parameters.Add(new SqlParameter("@KlientiIdZadolzen", Filter.KlientiIdZadolzen));
                    }
                }

                if (Filter.DatumNaZadolzuvanje.HasValue)
                {
                    query += " AND z.DatumNaZadolzuvanje = @DatumNaZadolzuvanje";
                    parameters.Add(new SqlParameter("@DatumNaZadolzuvanje", Filter.DatumNaZadolzuvanje));
                }

                if (Filter.SifrarnikOsnovZaRazdolzuvanjeId.HasValue)
                {
                    if (Filter.SifrarnikOsnovZaRazdolzuvanjeId == -1)
                    {
                        query += " AND z.SifrarnikOsnovZaRazdolzuvanjeId IS NULL";
                    }
                    else
                    {
                        query += " AND z.SifrarnikOsnovZaRazdolzuvanjeId = @SifrarnikOsnovZaRazdolzuvanjeId";
                        parameters.Add(new SqlParameter("@SifrarnikOsnovZaRazdolzuvanjeId", Filter.SifrarnikOsnovZaRazdolzuvanjeId));
                    }
                }

                if (Filter.PotvrdenoRazdolzuvanjeKajBroker.HasValue)
                {
                    query += " AND z.PotvrdenoRazdolzuvanjeKajBroker = @PotvrdenoRazdolzuvanjeKajBroker";
                    parameters.Add(new SqlParameter("@PotvrdenoRazdolzuvanjeKajBroker", Filter.PotvrdenoRazdolzuvanjeKajBroker));
                }

                if (Filter.DatumNaRazdolzuvanjeKajBrokerFrom.HasValue)
                {
                    query += " AND z.DatumNaRazdolzuvanjeKajBroker >= @DatumNaRazdolzuvanjeKajBrokerFrom";
                    parameters.Add(new SqlParameter("@DatumNaRazdolzuvanjeKajBrokerFrom", Filter.DatumNaRazdolzuvanjeKajBrokerFrom));
                }

                if (Filter.DatumNaRazdolzuvanjeKajBrokerTo.HasValue)
                {
                    query += " AND z.DatumNaRazdolzuvanjeKajBroker <= @DatumNaRazdolzuvanjeKajBrokerTo";
                    parameters.Add(new SqlParameter("@DatumNaRazdolzuvanjeKajBrokerTo", Filter.DatumNaRazdolzuvanjeKajBrokerTo));
                }

                if (Filter.PotvrdenoRazdolzuvanjeKajOsiguritel.HasValue)
                {
                    query += " AND z.PotvrdenoRazdolzuvanjeKajOsiguritel = @PotvrdenoRazdolzuvanjeKajOsiguritel";
                    parameters.Add(new SqlParameter("@PotvrdenoRazdolzuvanjeKajOsiguritel", Filter.PotvrdenoRazdolzuvanjeKajOsiguritel));
                }

                if (Filter.DatumNaRazdolzuvanjeKajOsiguritelFrom.HasValue)
                {
                    query += " AND z.DatumNaRazdolzuvanjeKajOsiguritel >= @DatumNaRazdolzuvanjeKajOsiguritelFrom";
                    parameters.Add(new SqlParameter("@DatumNaRazdolzuvanjeKajOsiguritelFrom", Filter.DatumNaRazdolzuvanjeKajOsiguritelFrom));
                }

                if (Filter.DatumNaRazdolzuvanjeKajOsiguritelTo.HasValue)
                {
                    query += " AND z.DatumNaRazdolzuvanjeKajOsiguritel <= @DatumNaRazdolzuvanjeKajOsiguritelTo";
                    parameters.Add(new SqlParameter("@DatumNaRazdolzuvanjeKajOsiguritelTo", Filter.DatumNaRazdolzuvanjeKajOsiguritelTo));
                }

                if (!string.IsNullOrWhiteSpace(Filter.BrojNaPersonalnaRazdolznica))
                {
                    var razdolznicaNumbers = Filter.BrojNaPersonalnaRazdolznica.Split(',')
                        .Select(r => r.Trim())
                        .Where(r => !string.IsNullOrWhiteSpace(r))
                        .ToList();

                    if (razdolznicaNumbers.Any())
                    {
                        var parameterNames = new List<string>();
                        
                        for (int i = 0; i < razdolznicaNumbers.Count; i++)
                        {
                            string paramName = $"@BrojNaPersonalnaRazdolznica{i}";
                            parameterNames.Add(paramName);
                            parameters.Add(new SqlParameter(paramName, razdolznicaNumbers[i]));
                        }
                        
                        query += " AND z.BrojNaPersonalnaRazdolznica IN (" + string.Join(",", parameterNames) + ")";
                    }
                }

                if (Filter.DatumNaIzdavanjeFrom.HasValue)
                {
                    query += " AND dbo.ZadolzuvanjeRazdolzuvanjeVratiPolisaDatumNaIzdavanje(z.BrojNaPolisa) >= @DatumNaIzdavanjeFrom";
                    parameters.Add(new SqlParameter("@DatumNaIzdavanjeFrom", Filter.DatumNaIzdavanjeFrom));
                }

                if (Filter.DatumNaIzdavanjeTo.HasValue)
                {
                    query += " AND dbo.ZadolzuvanjeRazdolzuvanjeVratiPolisaDatumNaIzdavanje(z.BrojNaPolisa) <= @DatumNaIzdavanjeTo";
                    parameters.Add(new SqlParameter("@DatumNaIzdavanjeTo", Filter.DatumNaIzdavanjeTo));
                }

                query += @" ORDER BY 
                    z.KlientiIdOsiguritel,
                    z.KlasiOsiguruvanjeIdKlasa,
                    z.ProduktiIdProdukt,
                    dbo.VratiDaliImaStorniranaBrojNaPolisa(z.BrojNaPolisa),
                    dbo.ZadolzuvanjeRazdolzuvanjeVratiPolisaDatumNaIzdavanje(z.BrojNaPolisa)";

                using (SqlCommand cmd = new SqlCommand(query, connection))
                {
                    cmd.Parameters.AddRange(parameters.ToArray());
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        Results.Add(new ZadolzuvanjeRazdolzuvanjeViewModel
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            DateModified = reader["DateModified"] as DateTime?,
                            UsernameModified = reader["UsernameModified"] as string,
                            OsiguritelNaziv = reader["OsiguritelNaziv"] as string,
                            KlientiIdOsiguritel = reader["KlientiIdOsiguritel"] as long?,
                            KlasaIme = reader["KlasaIme"] as string,
                            KlasiOsiguruvanjeIdKlasa = reader["KlasiOsiguruvanjeIdKlasa"] as int?,
                            ProduktIme = reader["ProduktIme"] as string,
                            ProduktiIdProdukt = reader["ProduktiIdProdukt"] as int?,
                            BrojNaPolisa = reader["BrojNaPolisa"] as string,
                            BrojNaPonuda = reader["BrojNaPonuda"] as long?,
                            ZadolzenIme = reader["ZadolzenIme"] as string,
                            DatumNaZadolzuvanje = reader["DatumNaZadolzuvanje"] as DateTime?,
                            OsnovZaRazdolzuvanje = reader["OsnovZaRazdolzuvanje"] as string,
                            PotvrdenoRazdolzuvanjeKajBroker = reader["PotvrdenoRazdolzuvanjeKajBroker"] as bool? ?? false,
                            DatumNaRazdolzuvanjeKajBroker = reader["DatumNaRazdolzuvanjeKajBroker"] as DateTime?,
                            PotvrdenoRazdolzuvanjeKajOsiguritel = reader["PotvrdenoRazdolzuvanjeKajOsiguritel"] as bool? ?? false,
                            DatumNaRazdolzuvanjeKajOsiguritel = reader["DatumNaRazdolzuvanjeKajOsiguritel"] as DateTime?,
                            DatumNaIzdavanje = reader["DatumNaIzdavanje"] as DateTime?,
                            IsStornirana = Convert.ToInt32(reader["IsStornirana"]) == 1,
                            Status = reader["Status"] as string,
                            BrojNaPersonalnaRazdolznica = reader["BrojNaPersonalnaRazdolznica"] as string
                        });
                    }
                }
            }
        }

        [ValidateAntiForgeryToken]
        public async Task<IActionResult> OnPostSaveRowAsync([FromBody] SaveRowModel model)
        {
            if (model == null)
            {
                return new JsonResult(new { success = false, message = "Invalid data" });
            }

            if (!await HasPageAccess("ZadolzuvanjeRazdolzuvanjePolisiPersonal"))
            {
                return RedirectToAccessDenied();
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    UPDATE PolisiZadolzuvanjeRazdolzuvanje
                    SET DateModified = GETDATE(),
                        UsernameModified = @UsernameModified,
                        KlientiIdOsiguritel = @KlientiIdOsiguritel,
                        KlasiOsiguruvanjeIdKlasa = @KlasiOsiguruvanjeIdKlasa,
                        ProduktiIdProdukt = @ProduktiIdProdukt,
                        BrojNaPolisa = @BrojNaPolisa,
                        BrojNaPonuda = @BrojNaPonuda,
                        KlientiIdZadolzen = @KlientiIdZadolzen,
                        DatumNaZadolzuvanje = @DatumNaZadolzuvanje,
                        SifrarnikOsnovZaRazdolzuvanjeId = @SifrarnikOsnovZaRazdolzuvanjeId,
                        PotvrdenoRazdolzuvanjeKajBroker = @PotvrdenoRazdolzuvanjeKajBroker,
                        DatumNaRazdolzuvanjeKajBroker = @DatumNaRazdolzuvanjeKajBroker,
                        PotvrdenoRazdolzuvanjeKajOsiguritel = @PotvrdenoRazdolzuvanjeKajOsiguritel,
                        DatumNaRazdolzuvanjeKajOsiguritel = @DatumNaRazdolzuvanjeKajOsiguritel,
                        DatumNaIzdavanje = @DatumNaIzdavanje
                    WHERE Id = @Id", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", model.Id);
                    cmd.Parameters.AddWithValue("@UsernameModified", HttpContext.Session.GetString("Username"));
                    cmd.Parameters.AddWithValue("@KlientiIdOsiguritel", model.KlientiIdOsiguritel ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@KlasiOsiguruvanjeIdKlasa", model.KlasiOsiguruvanjeIdKlasa ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@ProduktiIdProdukt", model.ProduktiIdProdukt ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@BrojNaPolisa", model.BrojNaPolisa ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@BrojNaPonuda", model.BrojNaPonuda ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@KlientiIdZadolzen", model.KlientiIdZadolzen ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@DatumNaZadolzuvanje", model.DatumNaZadolzuvanje ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@SifrarnikOsnovZaRazdolzuvanjeId", model.SifrarnikOsnovZaRazdolzuvanjeId ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PotvrdenoRazdolzuvanjeKajBroker", model.PotvrdenoRazdolzuvanjeKajBroker ?? false);
                    cmd.Parameters.AddWithValue("@DatumNaRazdolzuvanjeKajBroker", model.DatumNaRazdolzuvanjeKajBroker ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PotvrdenoRazdolzuvanjeKajOsiguritel", model.PotvrdenoRazdolzuvanjeKajOsiguritel ?? false);
                    cmd.Parameters.AddWithValue("@DatumNaRazdolzuvanjeKajOsiguritel", model.DatumNaRazdolzuvanjeKajOsiguritel ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@DatumNaIzdavanje", model.DatumNaIzdavanje ?? (object)DBNull.Value);

                    await cmd.ExecuteNonQueryAsync();
                }
            }

            return new JsonResult(new { success = true });
        }

        public class SaveRowModel
        {
            public long Id { get; set; }
            public long? KlientiIdOsiguritel { get; set; }
            public int? KlasiOsiguruvanjeIdKlasa { get; set; }
            public int? ProduktiIdProdukt { get; set; }
            public string BrojNaPolisa { get; set; }
            public long? BrojNaPonuda { get; set; }
            public long? KlientiIdZadolzen { get; set; }
            public DateTime? DatumNaZadolzuvanje { get; set; }
            public long? SifrarnikOsnovZaRazdolzuvanjeId { get; set; }
            public bool? PotvrdenoRazdolzuvanjeKajBroker { get; set; }
            public DateTime? DatumNaRazdolzuvanjeKajBroker { get; set; }
            public bool? PotvrdenoRazdolzuvanjeKajOsiguritel { get; set; }
            public DateTime? DatumNaRazdolzuvanjeKajOsiguritel { get; set; }
            public DateTime? DatumNaIzdavanje { get; set; }
        }

        public class BulkEditModel
        {
            public string Column { get; set; }
            public string Value { get; set; }
        }

        public async Task<IActionResult> OnPostBulkEditAsync([FromBody] BulkEditModel model)
        {
            if (!await HasPageAccess("ZadolzuvanjeRazdolzuvanjePolisiPersonal"))
            {
                return RedirectToAccessDenied();
            }

            try
            {
                // Validate inputs
                if (model == null)
                {
                    return new JsonResult(new { success = false, message = "Model is null" });
                }

                if (string.IsNullOrEmpty(model.Column))
                {
                    return new JsonResult(new { success = false, message = "Column name is required" });
                }

                if (string.IsNullOrEmpty(model.Value))
                {
                    return new JsonResult(new { success = false, message = "Value is required" });
                }

                if (CurrentFilteredIds == null || CurrentFilteredIds.Count == 0)
                {
                    return new JsonResult(new { success = false, message = "No records selected" });
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    var parameters = new List<SqlParameter>();
                    
                    // Handle boolean values for specific columns
                    if (model.Column == "PotvrdenoRazdolzuvanjeKajBroker" || 
                        model.Column == "PotvrdenoRazdolzuvanjeKajOsiguritel")
                    {
                        var boolValue = model.Value.ToLower() == "true" ? 1 : 0;
                        parameters.Add(new SqlParameter("@NewValue", boolValue));
                    }
                    else
                    {
                        parameters.Add(new SqlParameter("@NewValue", model.Value));
                    }
                    parameters.Add(new SqlParameter("@Username", HttpContext.Session.GetString("Username")));

                    var query = $@"
                        UPDATE p
                        SET {model.Column} = @NewValue,
                            DateModified = GETDATE(),
                            UsernameModified = @Username
                        FROM PolisiZadolzuvanjeRazdolzuvanje p
                        WHERE p.Id IN (" + string.Join(",", CurrentFilteredIds) + @")";

                    using (SqlCommand cmd = new SqlCommand(query, connection))
                    {
                        cmd.Parameters.AddRange(parameters.ToArray());
                        await cmd.ExecuteNonQueryAsync();
                    }
                }

                return new JsonResult(new { 
                    success = true,
                    whereClause = CurrentWhereClause,
                    affectedIds = CurrentFilteredIds
                });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { 
                    success = false, 
                    message = $"Error: {ex.Message}",
                    details = ex.ToString()
                });
            }
        }

        private (string whereClause, List<SqlParameter> parameters) BuildWhereClause()
        {
            var whereClause = new System.Text.StringBuilder();
            var parameters = new List<SqlParameter>();

            if (Filter.DateCreatedFrom.HasValue)
            {
                whereClause.Append(" AND p.DateCreated >= @DateCreatedFrom");
                parameters.Add(new SqlParameter("@DateCreatedFrom", Filter.DateCreatedFrom));
            }

            if (Filter.DateCreatedTo.HasValue)
            {
                whereClause.Append(" AND p.DateCreated <= @DateCreatedTo");
                parameters.Add(new SqlParameter("@DateCreatedTo", Filter.DateCreatedTo));
            }

            if (Filter.DateModifiedFrom.HasValue)
            {
                whereClause.Append(" AND p.DateModified >= @DateModifiedFrom");
                parameters.Add(new SqlParameter("@DateModifiedFrom", Filter.DateModifiedFrom));
            }

            if (Filter.DateModifiedTo.HasValue)
            {
                whereClause.Append(" AND p.DateModified <= @DateModifiedTo");
                parameters.Add(new SqlParameter("@DateModifiedTo", Filter.DateModifiedTo));
            }

            if (Filter.KlientiIdOsiguritel != null && Filter.KlientiIdOsiguritel.Any())
            {
                var paramNames = new List<string>();
                for (int i = 0; i < Filter.KlientiIdOsiguritel.Count; i++)
                {
                    var paramName = $"@KlientiIdOsiguritel{i}";
                    paramNames.Add(paramName);
                    parameters.Add(new SqlParameter(paramName, Filter.KlientiIdOsiguritel[i]));
                }
                whereClause.Append(" AND p.KlientiIdOsiguritel IN (" + string.Join(",", paramNames) + ")");
            }

            if (Filter.KlasiOsiguruvanjeIdKlasa.HasValue)
            {
                whereClause.Append(" AND p.KlasiOsiguruvanjeIdKlasa = @KlasiOsiguruvanjeIdKlasa");
                parameters.Add(new SqlParameter("@KlasiOsiguruvanjeIdKlasa", Filter.KlasiOsiguruvanjeIdKlasa));
            }

            if (Filter.ProduktiIdProdukt.HasValue)
            {
                whereClause.Append(" AND p.ProduktiIdProdukt = @ProduktiIdProdukt");
                parameters.Add(new SqlParameter("@ProduktiIdProdukt", Filter.ProduktiIdProdukt));
            }

            if (!string.IsNullOrEmpty(Filter.BrojNaPolisa))
            {
                if (!string.IsNullOrEmpty(Filter.BrojNaPolisaTo))
                {
                    whereClause.Append(" AND p.BrojNaPolisa BETWEEN @BrojNaPolisaFrom AND @BrojNaPolisaTo");
                    parameters.Add(new SqlParameter("@BrojNaPolisaFrom", Filter.BrojNaPolisa));
                    parameters.Add(new SqlParameter("@BrojNaPolisaTo", Filter.BrojNaPolisaTo));
                }
                else
                {
                    whereClause.Append(" AND p.BrojNaPolisa = @BrojNaPolisa");
                    parameters.Add(new SqlParameter("@BrojNaPolisa", Filter.BrojNaPolisa));
                }
            }

            if (!string.IsNullOrWhiteSpace(Filter.ExcludeBrojNaPolisa))
            {
                var policyNumbers = Filter.ExcludeBrojNaPolisa.Split(',')
                    .Select(p => p.Trim())
                    .Where(p => !string.IsNullOrWhiteSpace(p))
                    .ToList();

                if (policyNumbers.Any())
                {
                    var parameterNames = new List<string>();
                    
                    for (int i = 0; i < policyNumbers.Count; i++)
                    {
                        string paramName = $"@ExcludePolisa{i}";
                        parameterNames.Add(paramName);
                        parameters.Add(new SqlParameter(paramName, policyNumbers[i]));
                    }
                    
                    whereClause.Append(" AND (p.BrojNaPolisa IS NULL OR (p.BrojNaPolisa IS NOT NULL AND p.BrojNaPolisa NOT IN (" + string.Join(",", parameterNames) + ")))");
                }
            }

            if (Filter.BrojNaPonuda.HasValue)
            {
                if (Filter.BrojNaPonudaTo.HasValue)
                {
                    whereClause.Append(" AND p.BrojNaPonuda BETWEEN @BrojNaPonudaFrom AND @BrojNaPonudaTo");
                    parameters.Add(new SqlParameter("@BrojNaPonudaFrom", Filter.BrojNaPonuda));
                    parameters.Add(new SqlParameter("@BrojNaPonudaTo", Filter.BrojNaPonudaTo));
                }
                else
                {
                    whereClause.Append(" AND p.BrojNaPonuda = @BrojNaPonuda");
                    parameters.Add(new SqlParameter("@BrojNaPonuda", Filter.BrojNaPonuda));
                }
            }

            if (Filter.KlientiIdZadolzen.HasValue)
            {
                if (Filter.KlientiIdZadolzen == -1)
                {
                    whereClause.Append(" AND p.KlientiIdZadolzen IS NULL");
                }
                else
                {
                    whereClause.Append(" AND p.KlientiIdZadolzen = @KlientiIdZadolzen");
                    parameters.Add(new SqlParameter("@KlientiIdZadolzen", Filter.KlientiIdZadolzen));
                }
            }

            if (Filter.DatumNaZadolzuvanje.HasValue)
            {
                whereClause.Append(" AND p.DatumNaZadolzuvanje = @DatumNaZadolzuvanje");
                parameters.Add(new SqlParameter("@DatumNaZadolzuvanje", Filter.DatumNaZadolzuvanje));
            }

            if (Filter.SifrarnikOsnovZaRazdolzuvanjeId.HasValue)
            {
                if (Filter.SifrarnikOsnovZaRazdolzuvanjeId == -1)
                {
                    whereClause.Append(" AND p.SifrarnikOsnovZaRazdolzuvanjeId IS NULL");
                }
                else
                {
                    whereClause.Append(" AND p.SifrarnikOsnovZaRazdolzuvanjeId = @SifrarnikOsnovZaRazdolzuvanjeId");
                    parameters.Add(new SqlParameter("@SifrarnikOsnovZaRazdolzuvanjeId", Filter.SifrarnikOsnovZaRazdolzuvanjeId));
                }
            }

            if (Filter.PotvrdenoRazdolzuvanjeKajBroker.HasValue)
            {
                whereClause.Append(" AND p.PotvrdenoRazdolzuvanjeKajBroker = @PotvrdenoRazdolzuvanjeKajBroker");
                parameters.Add(new SqlParameter("@PotvrdenoRazdolzuvanjeKajBroker", Filter.PotvrdenoRazdolzuvanjeKajBroker));
            }

            if (Filter.DatumNaRazdolzuvanjeKajBrokerFrom.HasValue)
            {
                whereClause.Append(" AND p.DatumNaRazdolzuvanjeKajBroker >= @DatumNaRazdolzuvanjeKajBrokerFrom");
                parameters.Add(new SqlParameter("@DatumNaRazdolzuvanjeKajBrokerFrom", Filter.DatumNaRazdolzuvanjeKajBrokerFrom));
            }

            if (Filter.DatumNaRazdolzuvanjeKajBrokerTo.HasValue)
            {
                whereClause.Append(" AND p.DatumNaRazdolzuvanjeKajBroker <= @DatumNaRazdolzuvanjeKajBrokerTo");
                parameters.Add(new SqlParameter("@DatumNaRazdolzuvanjeKajBrokerTo", Filter.DatumNaRazdolzuvanjeKajBrokerTo));
            }

            if (Filter.PotvrdenoRazdolzuvanjeKajOsiguritel.HasValue)
            {
                whereClause.Append(" AND p.PotvrdenoRazdolzuvanjeKajOsiguritel = @PotvrdenoRazdolzuvanjeKajOsiguritel");
                parameters.Add(new SqlParameter("@PotvrdenoRazdolzuvanjeKajOsiguritel", Filter.PotvrdenoRazdolzuvanjeKajOsiguritel));
            }

            if (Filter.DatumNaRazdolzuvanjeKajOsiguritelFrom.HasValue)
            {
                whereClause.Append(" AND p.DatumNaRazdolzuvanjeKajOsiguritel >= @DatumNaRazdolzuvanjeKajOsiguritelFrom");
                parameters.Add(new SqlParameter("@DatumNaRazdolzuvanjeKajOsiguritelFrom", Filter.DatumNaRazdolzuvanjeKajOsiguritelFrom));
            }

            if (Filter.DatumNaRazdolzuvanjeKajOsiguritelTo.HasValue)
            {
                whereClause.Append(" AND p.DatumNaRazdolzuvanjeKajOsiguritel <= @DatumNaRazdolzuvanjeKajOsiguritelTo");
                parameters.Add(new SqlParameter("@DatumNaRazdolzuvanjeKajOsiguritelTo", Filter.DatumNaRazdolzuvanjeKajOsiguritelTo));
            }

            if (!string.IsNullOrWhiteSpace(Filter.BrojNaPersonalnaRazdolznica))
            {
                var razdolznicaNumbers = Filter.BrojNaPersonalnaRazdolznica.Split(',')
                    .Select(r => r.Trim())
                    .Where(r => !string.IsNullOrWhiteSpace(r))
                    .ToList();

                if (razdolznicaNumbers.Any())
                {
                    var parameterNames = new List<string>();
                    
                    for (int i = 0; i < razdolznicaNumbers.Count; i++)
                    {
                        string paramName = $"@BrojNaPersonalnaRazdolznica{i}";
                        parameterNames.Add(paramName);
                        parameters.Add(new SqlParameter(paramName, razdolznicaNumbers[i]));
                    }
                    
                    whereClause.Append(" AND p.BrojNaPersonalnaRazdolznica IN (" + string.Join(",", parameterNames) + ")");
                }
            }

            if (Filter.DatumNaIzdavanjeFrom.HasValue)
            {
                whereClause.Append(" AND dbo.ZadolzuvanjeRazdolzuvanjeVratiPolisaDatumNaIzdavanje(p.BrojNaPolisa) >= @DatumNaIzdavanjeFrom");
                parameters.Add(new SqlParameter("@DatumNaIzdavanjeFrom", Filter.DatumNaIzdavanjeFrom));
            }

            if (Filter.DatumNaIzdavanjeTo.HasValue)
            {
                whereClause.Append(" AND dbo.ZadolzuvanjeRazdolzuvanjeVratiPolisaDatumNaIzdavanje(p.BrojNaPolisa) <= @DatumNaIzdavanjeTo");
                parameters.Add(new SqlParameter("@DatumNaIzdavanjeTo", Filter.DatumNaIzdavanjeTo));
            }

            return (whereClause.ToString(), parameters);
        }

        public JsonResult OnGetOsiguriteli()
        {
            var items = new List<object>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Naziv 
                    FROM Klienti 
                    WHERE Osiguritel = 1 
                    ORDER BY Naziv", connection))
                {
                    using SqlDataReader reader = cmd.ExecuteReader();
                    while (reader.Read())
                    {
                        items.Add(new
                        {
                            id = reader["Id"].ToString(),
                            text = reader["Naziv"].ToString()
                        });
                    }
                }
            }
            return new JsonResult(items);
        }

        public JsonResult OnGetKlasiOsiguruvanje()
        {
            var items = new List<object>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, KlasaIme 
                    FROM KlasiOsiguruvanje 
                    WHERE Id IN (1,2,3,8,9,10,18,19)
                    ORDER BY KlasaIme", connection))
                {
                    using SqlDataReader reader = cmd.ExecuteReader();
                    while (reader.Read())
                    {
                        items.Add(new
                        {
                            id = reader["Id"].ToString(),
                            text = reader["KlasaIme"].ToString()
                        });
                    }
                }
            }
            return new JsonResult(items);
        }

        public async Task<JsonResult> OnGetZadolzeni()
        {
            var items = new List<object>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, 
                           CASE 
                               WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                               ELSE CONCAT(Ime, ' ', Prezime)
                           END as DisplayName
                    FROM Klienti 
                    WHERE EMBG IN (SELECT EMB FROM Users WHERE Username = @Username)
                        OR MB IN (SELECT EMB FROM Users WHERE Username = @Username)
                    ORDER BY DisplayName", connection))
                {
                    cmd.Parameters.AddWithValue("@Username", HttpContext.Session.GetString("Username"));
                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            items.Add(new
                            {
                                id = reader["Id"].ToString(),
                                text = reader["DisplayName"].ToString()
                            });
                        }
                    }
                }
            }
            return new JsonResult(items);
        }

        public JsonResult OnGetOsnoviZaRazdolzuvanje()
        {
            var items = new List<object>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, OsnovZaRazdolzuvanje
                    FROM SifrarnikOsnovZaRazdolzuvanje
                    ORDER BY OsnovZaRazdolzuvanje", connection))
                {
                    using SqlDataReader reader = cmd.ExecuteReader();
                    while (reader.Read())
                    {
                        items.Add(new
                        {
                            id = reader["Id"].ToString(),
                            text = reader["OsnovZaRazdolzuvanje"].ToString()
                        });
                    }
                }
            }
            return new JsonResult(items);
        }

        public async Task<IActionResult> OnGetGenerateDocumentAsync()
        {
            if (!await HasPageAccess("ZadolzuvanjeRazdolzuvanjePolisiPersonal"))
            {
                return RedirectToAccessDenied();
            }

            await LoadDropdowns();

            // Parse filter from query string
            var filterJson = HttpContext.Request.Query["filter"].ToString();
            if (!string.IsNullOrEmpty(filterJson))
            {
                var filterData = JsonSerializer.Deserialize<Dictionary<string, string>>(filterJson);
                Filter = new FilterModel
                {
                    DateCreatedFrom = !string.IsNullOrEmpty(filterData["Filter.DateCreatedFrom"]) ? DateTime.Parse(filterData["Filter.DateCreatedFrom"]) : null,
                    DateCreatedTo = !string.IsNullOrEmpty(filterData["Filter.DateCreatedTo"]) ? DateTime.Parse(filterData["Filter.DateCreatedTo"]) : null,
                    DateModifiedFrom = !string.IsNullOrEmpty(filterData["Filter.DateModifiedFrom"]) ? DateTime.Parse(filterData["Filter.DateModifiedFrom"]) : null,
                    DateModifiedTo = !string.IsNullOrEmpty(filterData["Filter.DateModifiedTo"]) ? DateTime.Parse(filterData["Filter.DateModifiedTo"]) : null,
                    KlientiIdOsiguritel = filterData
                        .Where(x => x.Key.StartsWith("Filter.KlientiIdOsiguritel[") && x.Key.EndsWith("]"))
                        .Select(x => long.Parse(x.Value))
                        .ToList(),
                    KlasiOsiguruvanjeIdKlasa = !string.IsNullOrEmpty(filterData["Filter.KlasiOsiguruvanjeIdKlasa"]) ? int.Parse(filterData["Filter.KlasiOsiguruvanjeIdKlasa"]) : null,
                    ProduktiIdProdukt = !string.IsNullOrEmpty(filterData["Filter.ProduktiIdProdukt"]) ? int.Parse(filterData["Filter.ProduktiIdProdukt"]) : null,
                    BrojNaPolisa = !string.IsNullOrEmpty(filterData["Filter.BrojNaPolisa"]) ? filterData["Filter.BrojNaPolisa"] : null,
                    BrojNaPolisaTo = !string.IsNullOrEmpty(filterData["Filter.BrojNaPolisaTo"]) ? filterData["Filter.BrojNaPolisaTo"] : null,
                    BrojNaPonuda = !string.IsNullOrEmpty(filterData["Filter.BrojNaPonuda"]) ? long.Parse(filterData["Filter.BrojNaPonuda"]) : null,
                    BrojNaPonudaTo = !string.IsNullOrEmpty(filterData["Filter.BrojNaPonudaTo"]) ? long.Parse(filterData["Filter.BrojNaPonudaTo"]) : null,
                    KlientiIdZadolzen = !string.IsNullOrEmpty(filterData["Filter.KlientiIdZadolzen"]) ? long.Parse(filterData["Filter.KlientiIdZadolzen"]) : null,
                    DatumNaZadolzuvanje = !string.IsNullOrEmpty(filterData["Filter.DatumNaZadolzuvanje"]) ? DateTime.Parse(filterData["Filter.DatumNaZadolzuvanje"]) : null,
                    SifrarnikOsnovZaRazdolzuvanjeId = !string.IsNullOrEmpty(filterData["Filter.SifrarnikOsnovZaRazdolzuvanjeId"]) ? long.Parse(filterData["Filter.SifrarnikOsnovZaRazdolzuvanjeId"]) : null,
                    PotvrdenoRazdolzuvanjeKajBroker = !string.IsNullOrEmpty(filterData["Filter.PotvrdenoRazdolzuvanjeKajBroker"]) ? bool.Parse(filterData["Filter.PotvrdenoRazdolzuvanjeKajBroker"]) : null,
                    DatumNaRazdolzuvanjeKajBrokerFrom = !string.IsNullOrEmpty(filterData["Filter.DatumNaRazdolzuvanjeKajBrokerFrom"]) ? DateTime.Parse(filterData["Filter.DatumNaRazdolzuvanjeKajBrokerFrom"]) : null,
                    DatumNaRazdolzuvanjeKajBrokerTo = !string.IsNullOrEmpty(filterData["Filter.DatumNaRazdolzuvanjeKajBrokerTo"]) ? DateTime.Parse(filterData["Filter.DatumNaRazdolzuvanjeKajBrokerTo"]) : null,
                    PotvrdenoRazdolzuvanjeKajOsiguritel = !string.IsNullOrEmpty(filterData["Filter.PotvrdenoRazdolzuvanjeKajOsiguritel"]) ? bool.Parse(filterData["Filter.PotvrdenoRazdolzuvanjeKajOsiguritel"]) : null,
                    DatumNaRazdolzuvanjeKajOsiguritelFrom = !string.IsNullOrEmpty(filterData["Filter.DatumNaRazdolzuvanjeKajOsiguritelFrom"]) ? DateTime.Parse(filterData["Filter.DatumNaRazdolzuvanjeKajOsiguritelFrom"]) : null,
                    DatumNaRazdolzuvanjeKajOsiguritelTo = !string.IsNullOrEmpty(filterData["Filter.DatumNaRazdolzuvanjeKajOsiguritelTo"]) ? DateTime.Parse(filterData["Filter.DatumNaRazdolzuvanjeKajOsiguritelTo"]) : null,
                    ExcludeBrojNaPolisa = filterData.GetValueOrDefault("Filter.ExcludeBrojNaPolisa", ""),
                    BrojNaPersonalnaRazdolznica = filterData.GetValueOrDefault("Filter.BrojNaPersonalnaRazdolznica", ""),
                    DatumNaIzdavanjeFrom = !string.IsNullOrEmpty(filterData["Filter.DatumNaIzdavanjeFrom"]) ? DateTime.Parse(filterData["Filter.DatumNaIzdavanjeFrom"]) : null,
                    DatumNaIzdavanjeTo = !string.IsNullOrEmpty(filterData["Filter.DatumNaIzdavanjeTo"]) ? DateTime.Parse(filterData["Filter.DatumNaIzdavanjeTo"]) : null
                };
            }

            var currentUsername = HttpContext.Session.GetString("Username");
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            
            // Get user info
            string userFullName = "";
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                await conn.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 1 CONCAT(Ime, ' ', Prezime, ' ', Naziv) 
                    FROM Klienti 
                    WHERE EMBG = (SELECT EMB FROM Users WHERE Username = @Username)", conn))
                {
                    cmd.Parameters.AddWithValue("@Username", currentUsername);
                    var result = await cmd.ExecuteScalarAsync();
                    userFullName = result?.ToString() ?? "";
                }
            }

            // Build where clause using the filter
            var (whereClause, parameters) = BuildWhereClause();
            CurrentWhereClause = whereClause;
            CurrentFilteredIds = new List<long>();

            var filteredResults = new List<ZadolzuvanjeRazdolzuvanjeViewModel>();
            int nextRazdolznicaNumber = 1; // Initialize with default value
            string completeRazdolznicaNumber = "";

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // First, get the next razdolznica number - this calls the sequence only once
                        using (SqlCommand cmd = new SqlCommand("SELECT NEXT VALUE FOR VratiSledenBrojPersonalnaRazdolznica", connection, transaction))
                        {
                            var result = await cmd.ExecuteScalarAsync();
                            if (result != null && result != DBNull.Value)
                            {
                                nextRazdolznicaNumber = Convert.ToInt32(result);
                            }
                        }

                        // Format the complete razdolznica number using the retrieved number
                        completeRazdolznicaNumber = $"{DateTime.Now.ToString("dd.MM.yyyy")}/{Filter.KlientiIdZadolzen}/{nextRazdolznicaNumber}/R";

                        // Get filtered IDs first
                        var selectIdsQuery = $@"
                            SELECT p.Id
                            FROM PolisiZadolzuvanjeRazdolzuvanje p
                            LEFT JOIN Klienti ko ON p.KlientiIdOsiguritel = ko.Id
                            LEFT JOIN KlasiOsiguruvanje kl ON p.KlasiOsiguruvanjeIdKlasa = kl.Id
                            LEFT JOIN Produkti pr ON p.ProduktiIdProdukt = pr.Id
                            LEFT JOIN Klienti kz ON p.KlientiIdZadolzen = kz.Id
                            LEFT JOIN SifrarnikOsnovZaRazdolzuvanje s ON p.SifrarnikOsnovZaRazdolzuvanjeId = s.Id
                            WHERE 1=1 {whereClause}";

                        using (SqlCommand cmd = new SqlCommand(selectIdsQuery, connection, transaction))
                        {
                            // Add parameters for the ID selection query
                            foreach (var param in parameters)
                            {
                                cmd.Parameters.Add(new SqlParameter(param.ParameterName, param.Value));
                            }
                            
                            using (var reader = await cmd.ExecuteReaderAsync())
                            {
                                while (await reader.ReadAsync())
                                {
                                    CurrentFilteredIds.Add(reader.GetInt64(0));
                                }
                            }
                        }

                        // Update BrojNaPersonalnaRazdolznica for all filtered IDs that don't already have one
                        if (CurrentFilteredIds.Any())
                        {
                            StringBuilder paramNames = new StringBuilder();
                            var updateParameters = new List<SqlParameter>();
                            
                            for (int i = 0; i < CurrentFilteredIds.Count; i++)
                            {
                                string paramName = $"@Id{i}";
                                paramNames.Append(i > 0 ? ", " + paramName : paramName);
                                updateParameters.Add(new SqlParameter(paramName, CurrentFilteredIds[i]));
                            }
                            
                            string updateQuery = $@"
                                UPDATE PolisiZadolzuvanjeRazdolzuvanje
                                SET BrojNaPersonalnaRazdolznica = @CompleteRazdolznicaNumber,
                                    DateModified = GETDATE(),
                                    UsernameModified = @UsernameModified
                                WHERE Id IN ({paramNames})
                                AND BrojNaPersonalnaRazdolznica IS NULL";
                            
                            using (SqlCommand cmd = new SqlCommand(updateQuery, connection, transaction))
                            {
                                cmd.Parameters.AddRange(updateParameters.ToArray());
                                cmd.Parameters.AddWithValue("@CompleteRazdolznicaNumber", completeRazdolznicaNumber);
                                cmd.Parameters.AddWithValue("@UsernameModified", currentUsername);
                                
                                await cmd.ExecuteNonQueryAsync();
                            }
                        }

                        // Now query the updated data
                        var dataQuery = @"
                            SELECT 
                                p.Id,
                                p.BrojNaPolisa,
                                p.BrojNaPonuda,
                                ko.KlasaIme,
                                pr.Ime as ProduktIme,
                                kl.Naziv as OsiguritelNaziv,
                                p.KlientiIdOsiguritel,
                                p.KlasiOsiguruvanjeIdKlasa,
                                p.ProduktiIdProdukt,
                                CASE 
                                    WHEN kz.KlientFizickoPravnoLice = 'P' THEN kz.Naziv
                                    ELSE CONCAT(kz.Ime, ' ', kz.Prezime)
                                END as ZadolzenIme,
                                s.OsnovZaRazdolzuvanje,
                                dbo.ZadolzuvanjeRazdolzuvanjeVratiPolisaDatumNaIzdavanje(p.BrojNaPolisa) as DatumNaIzdavanje,
                                dbo.VratiDaliImaStorniranaBrojNaPolisa(p.BrojNaPolisa) as IsStornirana,
                                CASE 
                                    WHEN dbo.VratiDaliImaStorniranaBrojNaPolisa(p.BrojNaPolisa) = 1 THEN 'Сторнирана'
                                    ELSE ''
                                END as Status,
                                p.BrojNaPersonalnaRazdolznica
                            FROM PolisiZadolzuvanjeRazdolzuvanje p
                            LEFT JOIN KlasiOsiguruvanje ko ON p.KlasiOsiguruvanjeIdKlasa = ko.Id
                            LEFT JOIN Produkti pr ON p.ProduktiIdProdukt = pr.Id
                            LEFT JOIN Klienti kl ON p.KlientiIdOsiguritel = kl.Id
                            LEFT JOIN Klienti kz ON p.KlientiIdZadolzen = kz.Id
                            LEFT JOIN SifrarnikOsnovZaRazdolzuvanje s ON p.SifrarnikOsnovZaRazdolzuvanjeId = s.Id
                            WHERE 1=1 " + whereClause + @"
                            ORDER BY 
                                p.KlientiIdOsiguritel,
                                p.KlasiOsiguruvanjeIdKlasa,
                                p.ProduktiIdProdukt,
                                dbo.VratiDaliImaStorniranaBrojNaPolisa(p.BrojNaPolisa),
                                dbo.ZadolzuvanjeRazdolzuvanjeVratiPolisaDatumNaIzdavanje(p.BrojNaPolisa)";

                        using (SqlCommand cmd = new SqlCommand(dataQuery, connection, transaction))
                        {
                            // Add parameters for the data selection query
                            foreach (var param in parameters)
                            {
                                cmd.Parameters.Add(new SqlParameter(param.ParameterName, param.Value));
                            }
                            
                            using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                            {
                                while (await reader.ReadAsync())
                                {
                                    filteredResults.Add(new ZadolzuvanjeRazdolzuvanjeViewModel
                                    {
                                        Id = reader.GetInt64(0),
                                        BrojNaPolisa = reader.IsDBNull(1) ? null : reader.GetString(1),
                                        BrojNaPonuda = reader.IsDBNull(2) ? null : reader.GetInt64(2),
                                        KlasaIme = reader.IsDBNull(3) ? null : reader.GetString(3),
                                        ProduktIme = reader.IsDBNull(4) ? null : reader.GetString(4),
                                        OsiguritelNaziv = reader.IsDBNull(5) ? null : reader.GetString(5),
                                        KlientiIdOsiguritel = reader.IsDBNull(6) ? null : reader.GetInt64(6),
                                        KlasiOsiguruvanjeIdKlasa = reader.IsDBNull(7) ? null : reader.GetInt32(7),
                                        ProduktiIdProdukt = reader.IsDBNull(8) ? null : reader.GetInt32(8),
                                        ZadolzenIme = reader.IsDBNull(9) ? null : reader.GetString(9),
                                        OsnovZaRazdolzuvanje = reader.IsDBNull(10) ? null : reader.GetString(10),
                                        DatumNaIzdavanje = reader.IsDBNull(11) ? null : reader.GetDateTime(11),
                                        IsStornirana = !reader.IsDBNull(12) && Convert.ToInt32(reader.GetValue(12)) == 1,
                                        Status = reader.IsDBNull(13) ? null : reader.GetString(13)
                                    });
                                }
                            }
                        }

                        // Commit the transaction
                        transaction.Commit();
                    }
                    catch (Exception)
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }

            // Generate HTML content
            var sb = new StringBuilder();
            sb.Append(@"
                <html>
                <head>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 15px; font-size: 10px; }
                        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                        th, td { 
                            border: 1px solid black; 
                            padding: 4px; 
                            text-align: left;
                            font-size: 9px;
                        }
                        .header { margin-bottom: 30px; }
                        .signatures { margin-top: 30px; }
                        .signature-box { display: inline-block; width: 45%; }
                        .signature-line { border-top: 1px solid black; margin-top: 30px; width: 150px; }
                        h3 { font-size: 14px; margin: 5px 0; }
                        h4 { font-size: 12px; margin: 10px 0; }
                        p { margin: 3px 0; }
                    </style>
                </head>
                <body>
                    <div class='header'>
                        <h3>ОБД ИНКО АД Скопје</h3>
                        <p>Ул. Бул. Крсте Мисирков бр. 1</p>
                        <p>Скопје</p>
                    </div>");

            sb.AppendFormat(
                @"
                    <h4>ЗАДОЛЖУВАЊЕ БРОЈ: {0}/{2}/{3}/R</h4>
                    
                    <table>
                        <tr>
                            <th>Ред. број</th>
                            <th>Осигурител</th>
                            <th>Класа</th>
                            <th>Производ</th>
                            <th>Број на понуда</th>
                            <th>Број на полиса</th>
                            <th>Основ за раздолжување</th>
                            <th>Датум на издавање</th>
                            <th>Статус</th>
                        </tr>",
                DateTime.Now.ToString("dd.MM.yyyy"), 
                userFullName,
                Filter.KlientiIdZadolzen,
                nextRazdolznicaNumber);

            // Add rows from filtered results
            int rowNum = 1;
            foreach (var item in filteredResults)
            {
                sb.AppendFormat(
                    @"
                        <tr>
                            <td>{0}</td>
                            <td>{1}</td>
                            <td>{2}</td>
                            <td>{3}</td>
                            <td>{4}</td>
                            <td>{5}</td>
                            <td>{6}</td>
                            <td>{7}</td>
                            <td>{8}</td>
                        </tr>",
                    rowNum++,
                    item.OsiguritelNaziv ?? "",
                    item.KlasaIme ?? "",
                    item.ProduktIme ?? "",
                    item.BrojNaPonuda?.ToString() ?? "",
                    item.BrojNaPolisa ?? "",
                    item.OsnovZaRazdolzuvanje ?? "",
                    item.DatumNaIzdavanje?.ToString("dd.MM.yyyy") ?? "",
                    item.Status ?? "");
            }

            sb.AppendFormat(
                @"
                    </table>
                    
                    <p>Датум на задолжување: {0}</p>
                    
                    <div class='signatures'>
                        <div class='signature-box'>
                            <p>Предал:</p>
                            <div class='signature-line'></div>
                            <p>{1}</p>
                        </div>
                        <div class='signature-box' style='float: right;'>
                            <p>Примил:</p>
                            <div class='signature-line'></div>                            
                        </div>
                    </div>
                </body>
                </html>",
                DateTime.Now.ToString("dd.MM.yyyy"),
                userFullName,
                filteredResults.FirstOrDefault()?.ZadolzenIme ?? "");

            // Convert HTML to PDF using iText7
            using (var stream = new MemoryStream())
            {
                using (var writer = new PdfWriter(stream))
                {
                    HtmlConverter.ConvertToPdf(sb.ToString(), writer);
                }

                return File(stream.ToArray(), "application/pdf", $"Zadolzuvanje_{DateTime.Now:yyyyMMdd}.pdf");
            }
        }

        public async Task<IActionResult> OnGetPdfDebugInfoAsync()
        {
            if (!await HasPageAccess("ZadolzuvanjeRazdolzuvanjePolisiPersonal"))
            {
                return RedirectToAccessDenied();
            }

            // First load all the dropdowns as they're needed for filtering
            await LoadDropdowns();

            // Parse filter from query string
            var filterJson = HttpContext.Request.Query["filter"].ToString();
            if (!string.IsNullOrEmpty(filterJson))
            {
                var filterData = JsonSerializer.Deserialize<Dictionary<string, string>>(filterJson);
                Filter = new FilterModel
                {
                    DateCreatedFrom = !string.IsNullOrEmpty(filterData["Filter.DateCreatedFrom"]) ? DateTime.Parse(filterData["Filter.DateCreatedFrom"]) : null,
                    DateCreatedTo = !string.IsNullOrEmpty(filterData["Filter.DateCreatedTo"]) ? DateTime.Parse(filterData["Filter.DateCreatedTo"]) : null,
                    DateModifiedFrom = !string.IsNullOrEmpty(filterData["Filter.DateModifiedFrom"]) ? DateTime.Parse(filterData["Filter.DateModifiedFrom"]) : null,
                    DateModifiedTo = !string.IsNullOrEmpty(filterData["Filter.DateModifiedTo"]) ? DateTime.Parse(filterData["Filter.DateModifiedTo"]) : null,
                    KlientiIdOsiguritel = filterData
                        .Where(x => x.Key.StartsWith("Filter.KlientiIdOsiguritel[") && x.Key.EndsWith("]"))
                        .Select(x => long.Parse(x.Value))
                        .ToList(),
                    KlasiOsiguruvanjeIdKlasa = !string.IsNullOrEmpty(filterData["Filter.KlasiOsiguruvanjeIdKlasa"]) ? int.Parse(filterData["Filter.KlasiOsiguruvanjeIdKlasa"]) : null,
                    ProduktiIdProdukt = !string.IsNullOrEmpty(filterData["Filter.ProduktiIdProdukt"]) ? int.Parse(filterData["Filter.ProduktiIdProdukt"]) : null,
                    BrojNaPolisa = !string.IsNullOrEmpty(filterData["Filter.BrojNaPolisa"]) ? filterData["Filter.BrojNaPolisa"] : null,
                    BrojNaPolisaTo = !string.IsNullOrEmpty(filterData["Filter.BrojNaPolisaTo"]) ? filterData["Filter.BrojNaPolisaTo"] : null,
                    BrojNaPonuda = !string.IsNullOrEmpty(filterData["Filter.BrojNaPonuda"]) ? long.Parse(filterData["Filter.BrojNaPonuda"]) : null,
                    BrojNaPonudaTo = !string.IsNullOrEmpty(filterData["Filter.BrojNaPonudaTo"]) ? long.Parse(filterData["Filter.BrojNaPonudaTo"]) : null,
                    KlientiIdZadolzen = !string.IsNullOrEmpty(filterData["Filter.KlientiIdZadolzen"]) ? long.Parse(filterData["Filter.KlientiIdZadolzen"]) : null,
                    DatumNaZadolzuvanje = !string.IsNullOrEmpty(filterData["Filter.DatumNaZadolzuvanje"]) ? DateTime.Parse(filterData["Filter.DatumNaZadolzuvanje"]) : null,
                    SifrarnikOsnovZaRazdolzuvanjeId = !string.IsNullOrEmpty(filterData["Filter.SifrarnikOsnovZaRazdolzuvanjeId"]) ? long.Parse(filterData["Filter.SifrarnikOsnovZaRazdolzuvanjeId"]) : null,
                    PotvrdenoRazdolzuvanjeKajBroker = !string.IsNullOrEmpty(filterData["Filter.PotvrdenoRazdolzuvanjeKajBroker"]) ? bool.Parse(filterData["Filter.PotvrdenoRazdolzuvanjeKajBroker"]) : null,
                    DatumNaRazdolzuvanjeKajBrokerFrom = !string.IsNullOrEmpty(filterData["Filter.DatumNaRazdolzuvanjeKajBrokerFrom"]) ? DateTime.Parse(filterData["Filter.DatumNaRazdolzuvanjeKajBrokerFrom"]) : null,
                    DatumNaRazdolzuvanjeKajBrokerTo = !string.IsNullOrEmpty(filterData["Filter.DatumNaRazdolzuvanjeKajBrokerTo"]) ? DateTime.Parse(filterData["Filter.DatumNaRazdolzuvanjeKajBrokerTo"]) : null,
                    PotvrdenoRazdolzuvanjeKajOsiguritel = !string.IsNullOrEmpty(filterData["Filter.PotvrdenoRazdolzuvanjeKajOsiguritel"]) ? bool.Parse(filterData["Filter.PotvrdenoRazdolzuvanjeKajOsiguritel"]) : null,
                    DatumNaRazdolzuvanjeKajOsiguritelFrom = !string.IsNullOrEmpty(filterData["Filter.DatumNaRazdolzuvanjeKajOsiguritelFrom"]) ? DateTime.Parse(filterData["Filter.DatumNaRazdolzuvanjeKajOsiguritelFrom"]) : null,
                    DatumNaRazdolzuvanjeKajOsiguritelTo = !string.IsNullOrEmpty(filterData["Filter.DatumNaRazdolzuvanjeKajOsiguritelTo"]) ? DateTime.Parse(filterData["Filter.DatumNaRazdolzuvanjeKajOsiguritelTo"]) : null,
                    ExcludeBrojNaPolisa = filterData.GetValueOrDefault("Filter.ExcludeBrojNaPolisa", ""),
                    BrojNaPersonalnaRazdolznica = filterData.GetValueOrDefault("Filter.BrojNaPersonalnaRazdolznica", ""),
                    DatumNaIzdavanjeFrom = !string.IsNullOrEmpty(filterData["Filter.DatumNaIzdavanjeFrom"]) ? DateTime.Parse(filterData["Filter.DatumNaIzdavanjeFrom"]) : null,
                    DatumNaIzdavanjeTo = !string.IsNullOrEmpty(filterData["Filter.DatumNaIzdavanjeTo"]) ? DateTime.Parse(filterData["Filter.DatumNaIzdavanjeTo"]) : null
                };
            }

            // Add debug info for filter values
            var filterDebug = new StringBuilder();
            filterDebug.AppendLine("\nFilter Values:");
            foreach (var prop in Filter.GetType().GetProperties())
            {
                filterDebug.AppendLine($"{prop.Name}: {prop.GetValue(Filter)}");
            }

            var (whereClause, parameters) = BuildWhereClause();
            CurrentWhereClause = whereClause;
            CurrentFilteredIds = new List<long>();

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                var query = @"SELECT p.Id 
                            FROM PolisiZadolzuvanjeRazdolzuvanje p 
                            WHERE 1=1 " + whereClause;

                using (SqlCommand cmd = new SqlCommand(query, connection))
                {
                    cmd.Parameters.AddRange(parameters.ToArray());
                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            CurrentFilteredIds.Add(reader.GetInt64(0));
                        }
                    }
                }
            }

            return new JsonResult(new { 
                success = true,
                whereClause = CurrentWhereClause,
                filteredIds = CurrentFilteredIds,
                parameterValues = parameters.Select(p => $"{p.ParameterName}: {p.Value}"),
                filterValues = filterDebug.ToString()
            });
        }

        public async Task<IActionResult> OnGetGenerateRazdolzuvanjeDocumentAsync()
        {
            if (!await HasPageAccess("ZadolzuvanjeRazdolzuvanjePolisiPersonal"))
            {
                return RedirectToAccessDenied();
            }

            await LoadDropdowns();

            var filterJson = HttpContext.Request.Query["filter"].ToString();
            if (!string.IsNullOrEmpty(filterJson))
            {
                var filterData = JsonSerializer.Deserialize<Dictionary<string, string>>(filterJson);
                Filter = new FilterModel
                {
                    DateCreatedFrom = !string.IsNullOrEmpty(filterData["Filter.DateCreatedFrom"]) ? DateTime.Parse(filterData["Filter.DateCreatedFrom"]) : null,
                    DateCreatedTo = !string.IsNullOrEmpty(filterData["Filter.DateCreatedTo"]) ? DateTime.Parse(filterData["Filter.DateCreatedTo"]) : null,
                    DateModifiedFrom = !string.IsNullOrEmpty(filterData["Filter.DateModifiedFrom"]) ? DateTime.Parse(filterData["Filter.DateModifiedFrom"]) : null,
                    DateModifiedTo = !string.IsNullOrEmpty(filterData["Filter.DateModifiedTo"]) ? DateTime.Parse(filterData["Filter.DateModifiedTo"]) : null,
                    KlientiIdOsiguritel = filterData
                        .Where(x => x.Key.StartsWith("Filter.KlientiIdOsiguritel[") && x.Key.EndsWith("]"))
                        .Select(x => long.Parse(x.Value))
                        .ToList(),
                    KlasiOsiguruvanjeIdKlasa = !string.IsNullOrEmpty(filterData["Filter.KlasiOsiguruvanjeIdKlasa"]) ? int.Parse(filterData["Filter.KlasiOsiguruvanjeIdKlasa"]) : null,
                    ProduktiIdProdukt = !string.IsNullOrEmpty(filterData["Filter.ProduktiIdProdukt"]) ? int.Parse(filterData["Filter.ProduktiIdProdukt"]) : null,
                    BrojNaPolisa = !string.IsNullOrEmpty(filterData["Filter.BrojNaPolisa"]) ? filterData["Filter.BrojNaPolisa"] : null,
                    BrojNaPolisaTo = !string.IsNullOrEmpty(filterData["Filter.BrojNaPolisaTo"]) ? filterData["Filter.BrojNaPolisaTo"] : null,
                    BrojNaPonuda = !string.IsNullOrEmpty(filterData["Filter.BrojNaPonuda"]) ? long.Parse(filterData["Filter.BrojNaPonuda"]) : null,
                    BrojNaPonudaTo = !string.IsNullOrEmpty(filterData["Filter.BrojNaPonudaTo"]) ? long.Parse(filterData["Filter.BrojNaPonudaTo"]) : null,
                    KlientiIdZadolzen = !string.IsNullOrEmpty(filterData["Filter.KlientiIdZadolzen"]) ? long.Parse(filterData["Filter.KlientiIdZadolzen"]) : null,
                    DatumNaZadolzuvanje = !string.IsNullOrEmpty(filterData["Filter.DatumNaZadolzuvanje"]) ? DateTime.Parse(filterData["Filter.DatumNaZadolzuvanje"]) : null,
                    SifrarnikOsnovZaRazdolzuvanjeId = !string.IsNullOrEmpty(filterData["Filter.SifrarnikOsnovZaRazdolzuvanjeId"]) ? long.Parse(filterData["Filter.SifrarnikOsnovZaRazdolzuvanjeId"]) : null,
                    PotvrdenoRazdolzuvanjeKajBroker = !string.IsNullOrEmpty(filterData["Filter.PotvrdenoRazdolzuvanjeKajBroker"]) ? bool.Parse(filterData["Filter.PotvrdenoRazdolzuvanjeKajBroker"]) : null,
                    DatumNaRazdolzuvanjeKajBrokerFrom = !string.IsNullOrEmpty(filterData["Filter.DatumNaRazdolzuvanjeKajBrokerFrom"]) ? DateTime.Parse(filterData["Filter.DatumNaRazdolzuvanjeKajBrokerFrom"]) : null,
                    DatumNaRazdolzuvanjeKajBrokerTo = !string.IsNullOrEmpty(filterData["Filter.DatumNaRazdolzuvanjeKajBrokerTo"]) ? DateTime.Parse(filterData["Filter.DatumNaRazdolzuvanjeKajBrokerTo"]) : null,
                    PotvrdenoRazdolzuvanjeKajOsiguritel = !string.IsNullOrEmpty(filterData["Filter.PotvrdenoRazdolzuvanjeKajOsiguritel"]) ? bool.Parse(filterData["Filter.PotvrdenoRazdolzuvanjeKajOsiguritel"]) : null,
                    DatumNaRazdolzuvanjeKajOsiguritelFrom = !string.IsNullOrEmpty(filterData["Filter.DatumNaRazdolzuvanjeKajOsiguritelFrom"]) ? DateTime.Parse(filterData["Filter.DatumNaRazdolzuvanjeKajOsiguritelFrom"]) : null,
                    DatumNaRazdolzuvanjeKajOsiguritelTo = !string.IsNullOrEmpty(filterData["Filter.DatumNaRazdolzuvanjeKajOsiguritelTo"]) ? DateTime.Parse(filterData["Filter.DatumNaRazdolzuvanjeKajOsiguritelTo"]) : null,
                    ExcludeBrojNaPolisa = filterData.GetValueOrDefault("Filter.ExcludeBrojNaPolisa", ""),
                    BrojNaPersonalnaRazdolznica = filterData.GetValueOrDefault("Filter.BrojNaPersonalnaRazdolznica", ""),
                    DatumNaIzdavanjeFrom = !string.IsNullOrEmpty(filterData["Filter.DatumNaIzdavanjeFrom"]) ? DateTime.Parse(filterData["Filter.DatumNaIzdavanjeFrom"]) : null,
                    DatumNaIzdavanjeTo = !string.IsNullOrEmpty(filterData["Filter.DatumNaIzdavanjeTo"]) ? DateTime.Parse(filterData["Filter.DatumNaIzdavanjeTo"]) : null
                };
            }

            var currentUsername = HttpContext.Session.GetString("Username");
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            
            // Get user info
            string userFullName = "";
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                await conn.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 1 CONCAT(Ime, ' ', Prezime, ' ', Naziv) 
                    FROM Klienti 
                    WHERE EMBG = (SELECT EMB FROM Users WHERE Username = @Username)", conn))
                {
                    cmd.Parameters.AddWithValue("@Username", currentUsername);
                    var result = await cmd.ExecuteScalarAsync();
                    userFullName = result?.ToString() ?? "";
                }
            }

            // Build where clause using the filter
            var (whereClause, parameters) = BuildWhereClause();
            CurrentWhereClause = whereClause;
            CurrentFilteredIds = new List<long>();

            var filteredResults = new List<ZadolzuvanjeRazdolzuvanjeViewModel>();
            int nextRazdolznicaNumber = 1; // Initialize with default value
            string completeRazdolznicaNumber = "";

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // First, get the next razdolznica number - this calls the sequence only once
                        using (SqlCommand cmd = new SqlCommand("SELECT NEXT VALUE FOR VratiSledenBrojPersonalnaRazdolznica", connection, transaction))
                        {
                            var result = await cmd.ExecuteScalarAsync();
                            if (result != null && result != DBNull.Value)
                            {
                                nextRazdolznicaNumber = Convert.ToInt32(result);
                            }
                        }

                        // Format the complete razdolznica number using the retrieved number
                        completeRazdolznicaNumber = $"{DateTime.Now.ToString("dd.MM.yyyy")}/{Filter.KlientiIdZadolzen}/{nextRazdolznicaNumber}/R";

                        // Get filtered IDs first
                        var selectIdsQuery = $@"
                            SELECT p.Id
                            FROM PolisiZadolzuvanjeRazdolzuvanje p
                            LEFT JOIN Klienti ko ON p.KlientiIdOsiguritel = ko.Id
                            LEFT JOIN KlasiOsiguruvanje kl ON p.KlasiOsiguruvanjeIdKlasa = kl.Id
                            LEFT JOIN Produkti pr ON p.ProduktiIdProdukt = pr.Id
                            LEFT JOIN Klienti kz ON p.KlientiIdZadolzen = kz.Id
                            LEFT JOIN SifrarnikOsnovZaRazdolzuvanje s ON p.SifrarnikOsnovZaRazdolzuvanjeId = s.Id
                            WHERE 1=1 {whereClause}";

                        using (SqlCommand cmd = new SqlCommand(selectIdsQuery, connection, transaction))
                        {
                            // Add parameters for the ID selection query
                            foreach (var param in parameters)
                            {
                                cmd.Parameters.Add(new SqlParameter(param.ParameterName, param.Value));
                            }
                            
                            using (var reader = await cmd.ExecuteReaderAsync())
                            {
                                while (await reader.ReadAsync())
                                {
                                    CurrentFilteredIds.Add(reader.GetInt64(0));
                                }
                            }
                        }

                        // Update BrojNaPersonalnaRazdolznica for all filtered IDs that don't already have one
                        if (CurrentFilteredIds.Any())
                        {
                            StringBuilder paramNames = new StringBuilder();
                            var updateParameters = new List<SqlParameter>();
                            
                            for (int i = 0; i < CurrentFilteredIds.Count; i++)
                            {
                                string paramName = $"@Id{i}";
                                paramNames.Append(i > 0 ? ", " + paramName : paramName);
                                updateParameters.Add(new SqlParameter(paramName, CurrentFilteredIds[i]));
                            }
                            
                            string updateQuery = $@"
                                UPDATE PolisiZadolzuvanjeRazdolzuvanje
                                SET BrojNaPersonalnaRazdolznica = @CompleteRazdolznicaNumber,
                                    DateModified = GETDATE(),
                                    UsernameModified = @UsernameModified
                                WHERE Id IN ({paramNames})
                                AND BrojNaPersonalnaRazdolznica IS NULL";
                            
                            using (SqlCommand cmd = new SqlCommand(updateQuery, connection, transaction))
                            {
                                cmd.Parameters.AddRange(updateParameters.ToArray());
                                cmd.Parameters.AddWithValue("@CompleteRazdolznicaNumber", completeRazdolznicaNumber);
                                cmd.Parameters.AddWithValue("@UsernameModified", currentUsername);
                                
                                await cmd.ExecuteNonQueryAsync();
                            }
                        }

                        // Now query the updated data
                        var dataQuery = @"
                            SELECT 
                                p.Id,
                                p.DateCreated,
                                p.UsernameCreated,
                                p.DateModified,
                                p.UsernameModified,
                                kl.Naziv as OsiguritelNaziv,
                                p.KlientiIdOsiguritel,
                                ko.KlasaIme,
                                p.KlasiOsiguruvanjeIdKlasa,
                                pr.Ime as ProduktIme,
                                p.ProduktiIdProdukt,
                                p.BrojNaPolisa,
                                p.BrojNaPonuda,
                                CASE 
                                    WHEN kz.KlientFizickoPravnoLice = 'P' THEN kz.Naziv
                                    ELSE CONCAT(kz.Ime, ' ', kz.Prezime)
                                END as ZadolzenIme,
                                p.DatumNaZadolzuvanje,
                                s.OsnovZaRazdolzuvanje,
                                p.PotvrdenoRazdolzuvanjeKajBroker,
                                p.DatumNaRazdolzuvanjeKajBroker,
                                p.PotvrdenoRazdolzuvanjeKajOsiguritel,
                                p.DatumNaRazdolzuvanjeKajOsiguritel,
                                dbo.ZadolzuvanjeRazdolzuvanjeVratiPolisaDatumNaIzdavanje(p.BrojNaPolisa) as DatumNaIzdavanje,
                                dbo.VratiDaliImaStorniranaBrojNaPolisa(p.BrojNaPolisa) as IsStornirana,
                                CASE 
                                    WHEN dbo.VratiDaliImaStorniranaBrojNaPolisa(p.BrojNaPolisa) = 1 THEN 'Сторнирана'
                                    ELSE ''
                                END as Status
                            FROM PolisiZadolzuvanjeRazdolzuvanje p
                            LEFT JOIN KlasiOsiguruvanje ko ON p.KlasiOsiguruvanjeIdKlasa = ko.Id
                            LEFT JOIN Produkti pr ON p.ProduktiIdProdukt = pr.Id
                            LEFT JOIN Klienti kl ON p.KlientiIdOsiguritel = kl.Id
                            LEFT JOIN Klienti kz ON p.KlientiIdZadolzen = kz.Id
                            LEFT JOIN SifrarnikOsnovZaRazdolzuvanje s ON p.SifrarnikOsnovZaRazdolzuvanjeId = s.Id
                            WHERE 1=1 " + whereClause + @"
                            ORDER BY 
                                p.KlientiIdOsiguritel,
                                p.KlasiOsiguruvanjeIdKlasa,
                                p.ProduktiIdProdukt,
                                dbo.VratiDaliImaStorniranaBrojNaPolisa(p.BrojNaPolisa),
                                dbo.ZadolzuvanjeRazdolzuvanjeVratiPolisaDatumNaIzdavanje(p.BrojNaPolisa)";

                        using (SqlCommand cmd = new SqlCommand(dataQuery, connection, transaction))
                        {
                            // Add parameters for the data selection query
                            foreach (var param in parameters)
                            {
                                cmd.Parameters.Add(new SqlParameter(param.ParameterName, param.Value));
                            }
                            
                            using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                            {
                                while (await reader.ReadAsync())
                                {
                                    filteredResults.Add(new ZadolzuvanjeRazdolzuvanjeViewModel
                                    {
                                        Id = reader.GetInt64(0),
                                        DateCreated = reader.IsDBNull(1) ? null : reader.GetDateTime(1),
                                        UsernameCreated = reader.IsDBNull(2) ? null : reader.GetString(2),
                                        DateModified = reader.IsDBNull(3) ? null : reader.GetDateTime(3),
                                        UsernameModified = reader.IsDBNull(4) ? null : reader.GetString(4),
                                        OsiguritelNaziv = reader.IsDBNull(5) ? null : reader.GetString(5),
                                        KlientiIdOsiguritel = reader.IsDBNull(6) ? null : reader.GetInt64(6),
                                        KlasaIme = reader.IsDBNull(7) ? null : reader.GetString(7),
                                        KlasiOsiguruvanjeIdKlasa = reader.IsDBNull(8) ? null : reader.GetInt32(8),
                                        ProduktIme = reader.IsDBNull(9) ? null : reader.GetString(9),
                                        ProduktiIdProdukt = reader.IsDBNull(10) ? null : reader.GetInt32(10),
                                        BrojNaPolisa = reader.IsDBNull(11) ? null : reader.GetString(11),
                                        BrojNaPonuda = reader.IsDBNull(12) ? null : reader.GetInt64(12),
                                        ZadolzenIme = reader.IsDBNull(13) ? null : reader.GetString(13),
                                        DatumNaZadolzuvanje = reader.IsDBNull(14) ? null : reader.GetDateTime(14),
                                        OsnovZaRazdolzuvanje = reader.IsDBNull(15) ? null : reader.GetString(15),
                                        PotvrdenoRazdolzuvanjeKajBroker = reader.IsDBNull(16) ? false : reader.GetBoolean(16),
                                        DatumNaRazdolzuvanjeKajBroker = reader.IsDBNull(17) ? null : reader.GetDateTime(17),
                                        PotvrdenoRazdolzuvanjeKajOsiguritel = reader.IsDBNull(18) ? false : reader.GetBoolean(18),
                                        DatumNaRazdolzuvanjeKajOsiguritel = reader.IsDBNull(19) ? null : reader.GetDateTime(19),
                                        DatumNaIzdavanje = reader.IsDBNull(20) ? null : reader.GetDateTime(20),
                                        IsStornirana = !reader.IsDBNull(21) && Convert.ToInt32(reader.GetValue(21)) == 1,
                                        Status = reader.IsDBNull(22) ? null : reader.GetString(22)
                                    });
                                }
                            }
                        }

                        // Commit the transaction
                        transaction.Commit();
                    }
                    catch (Exception)
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }

            // Generate HTML content
            var sb = new StringBuilder();
            sb.Append(@"
                <html>
                <head>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 15px; font-size: 10px; }
                        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                        th, td { 
                            border: 1px solid black; 
                            padding: 4px; 
                            text-align: left;
                            font-size: 9px;
                        }
                        .header { margin-bottom: 30px; }
                        .signatures { margin-top: 30px; }
                        .signature-box { display: inline-block; width: 45%; }
                        .signature-line { border-top: 1px solid black; margin-top: 30px; width: 150px; }
                        h3 { font-size: 14px; margin: 5px 0; }
                        h4 { font-size: 12px; margin: 10px 0; }
                        p { margin: 3px 0; }
                    </style>
                </head>
                <body>
                    <div class='header'>
                        <h3>ОБД ИНКО АД Скопје</h3>
                        <p>Ул. Бул. Крсте Мисирков бр. 1</p>
                        <p>Скопје</p>
                    </div>");

            sb.AppendFormat(
                @"
                    <h4>РАЗДОЛЖУВАЊЕ БРОЈ: {0}/{2}/{3}/R</h4>
                    
                    <table>
                        <tr>
                            <th>Ред. број</th>
                            <th>Осигурител</th>
                            <th>Класа</th>
                            <th>Производ</th>
                            <th>Број на понуда</th>
                            <th>Број на полиса</th>
                            <th>Основ за раздолжување</th>
                            <th>Датум на издавање</th>
                            <th>Статус</th>
                        </tr>",
                DateTime.Now.ToString("dd.MM.yyyy"), 
                userFullName,
                Filter.KlientiIdZadolzen,
                nextRazdolznicaNumber);

            // Add rows from filtered results
            int rowNum = 1;
            foreach (var item in filteredResults)
            {
                sb.AppendFormat(
                    @"
                        <tr>
                            <td>{0}</td>
                            <td>{1}</td>
                            <td>{2}</td>
                            <td>{3}</td>
                            <td>{4}</td>
                            <td>{5}</td>
                            <td>{6}</td>
                            <td>{7}</td>
                            <td>{8}</td>
                        </tr>",
                    rowNum++,
                    item.OsiguritelNaziv ?? "",
                    item.KlasaIme ?? "",
                    item.ProduktIme ?? "",
                    item.BrojNaPonuda?.ToString() ?? "",
                    item.BrojNaPolisa ?? "",
                    item.OsnovZaRazdolzuvanje ?? "",
                    item.DatumNaIzdavanje?.ToString("dd.MM.yyyy") ?? "",
                    item.Status ?? "");
            }

            sb.AppendFormat(
                @"
                    </table>
                    
                    <p>Датум на раздолжување: {0}</p>
                    
                    <div class='signatures'>
                        <div class='signature-box'>
                            <p>Предал:</p>
                            <div class='signature-line'></div>
                            <p>{1}</p>
                        </div>
                        <div class='signature-box' style='float: right;'>
                            <p>Примил:</p>
                            <div class='signature-line'></div>                            
                        </div>
                    </div>
                </body>
                </html>",
                DateTime.Now.ToString("dd.MM.yyyy"),
                userFullName,
                filteredResults.FirstOrDefault()?.ZadolzenIme ?? "",
                filteredResults.FirstOrDefault()?.DatumNaIzdavanje?.ToString("dd.MM.yyyy") ?? "");

            // Convert HTML to PDF using iText7
            using (var stream = new MemoryStream())
            {
                using (var writer = new PdfWriter(stream))
                {
                    HtmlConverter.ConvertToPdf(sb.ToString(), writer);
                }

                return File(stream.ToArray(), "application/pdf", $"Razdolzuvanje_{DateTime.Now:yyyyMMdd}.pdf");
            }
        }

        public async Task<IActionResult> OnGetGenerateExcelAsync()
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;  // Add this line

            if (!await HasPageAccess("ZadolzuvanjeRazdolzuvanjePolisiPersonal"))
            {
                return RedirectToAccessDenied();
            }

            await LoadDropdowns();

            var filterJson = HttpContext.Request.Query["filter"].ToString();
            if (!string.IsNullOrEmpty(filterJson))
            {
                var filterData = JsonSerializer.Deserialize<Dictionary<string, string>>(filterJson);
                Filter = new FilterModel
                {
                    DateCreatedFrom = !string.IsNullOrEmpty(filterData["Filter.DateCreatedFrom"]) ? DateTime.Parse(filterData["Filter.DateCreatedFrom"]) : null,
                    DateCreatedTo = !string.IsNullOrEmpty(filterData["Filter.DateCreatedTo"]) ? DateTime.Parse(filterData["Filter.DateCreatedTo"]) : null,
                    DateModifiedFrom = !string.IsNullOrEmpty(filterData["Filter.DateModifiedFrom"]) ? DateTime.Parse(filterData["Filter.DateModifiedFrom"]) : null,
                    DateModifiedTo = !string.IsNullOrEmpty(filterData["Filter.DateModifiedTo"]) ? DateTime.Parse(filterData["Filter.DateModifiedTo"]) : null,
                    KlientiIdOsiguritel = filterData
                        .Where(x => x.Key.StartsWith("Filter.KlientiIdOsiguritel[") && x.Key.EndsWith("]"))
                        .Select(x => long.Parse(x.Value))
                        .ToList(),
                    KlasiOsiguruvanjeIdKlasa = !string.IsNullOrEmpty(filterData["Filter.KlasiOsiguruvanjeIdKlasa"]) ? int.Parse(filterData["Filter.KlasiOsiguruvanjeIdKlasa"]) : null,
                    ProduktiIdProdukt = !string.IsNullOrEmpty(filterData["Filter.ProduktiIdProdukt"]) ? int.Parse(filterData["Filter.ProduktiIdProdukt"]) : null,
                    BrojNaPolisa = !string.IsNullOrEmpty(filterData["Filter.BrojNaPolisa"]) ? filterData["Filter.BrojNaPolisa"] : null,
                    BrojNaPolisaTo = !string.IsNullOrEmpty(filterData["Filter.BrojNaPolisaTo"]) ? filterData["Filter.BrojNaPolisaTo"] : null,
                    BrojNaPonuda = !string.IsNullOrEmpty(filterData["Filter.BrojNaPonuda"]) ? long.Parse(filterData["Filter.BrojNaPonuda"]) : null,
                    BrojNaPonudaTo = !string.IsNullOrEmpty(filterData["Filter.BrojNaPonudaTo"]) ? long.Parse(filterData["Filter.BrojNaPonudaTo"]) : null,
                    KlientiIdZadolzen = !string.IsNullOrEmpty(filterData["Filter.KlientiIdZadolzen"]) ? long.Parse(filterData["Filter.KlientiIdZadolzen"]) : null,
                    DatumNaZadolzuvanje = !string.IsNullOrEmpty(filterData["Filter.DatumNaZadolzuvanje"]) ? DateTime.Parse(filterData["Filter.DatumNaZadolzuvanje"]) : null,
                    SifrarnikOsnovZaRazdolzuvanjeId = !string.IsNullOrEmpty(filterData["Filter.SifrarnikOsnovZaRazdolzuvanjeId"]) ? long.Parse(filterData["Filter.SifrarnikOsnovZaRazdolzuvanjeId"]) : null,
                    PotvrdenoRazdolzuvanjeKajBroker = !string.IsNullOrEmpty(filterData["Filter.PotvrdenoRazdolzuvanjeKajBroker"]) ? bool.Parse(filterData["Filter.PotvrdenoRazdolzuvanjeKajBroker"]) : null,
                    DatumNaRazdolzuvanjeKajBrokerFrom = !string.IsNullOrEmpty(filterData["Filter.DatumNaRazdolzuvanjeKajBrokerFrom"]) ? DateTime.Parse(filterData["Filter.DatumNaRazdolzuvanjeKajBrokerFrom"]) : null,
                    DatumNaRazdolzuvanjeKajBrokerTo = !string.IsNullOrEmpty(filterData["Filter.DatumNaRazdolzuvanjeKajBrokerTo"]) ? DateTime.Parse(filterData["Filter.DatumNaRazdolzuvanjeKajBrokerTo"]) : null,
                    PotvrdenoRazdolzuvanjeKajOsiguritel = !string.IsNullOrEmpty(filterData["Filter.PotvrdenoRazdolzuvanjeKajOsiguritel"]) ? bool.Parse(filterData["Filter.PotvrdenoRazdolzuvanjeKajOsiguritel"]) : null,
                    DatumNaRazdolzuvanjeKajOsiguritelFrom = !string.IsNullOrEmpty(filterData["Filter.DatumNaRazdolzuvanjeKajOsiguritelFrom"]) ? DateTime.Parse(filterData["Filter.DatumNaRazdolzuvanjeKajOsiguritelFrom"]) : null,
                    DatumNaRazdolzuvanjeKajOsiguritelTo = !string.IsNullOrEmpty(filterData["Filter.DatumNaRazdolzuvanjeKajOsiguritelTo"]) ? DateTime.Parse(filterData["Filter.DatumNaRazdolzuvanjeKajOsiguritelTo"]) : null,
                    ExcludeBrojNaPolisa = filterData.GetValueOrDefault("Filter.ExcludeBrojNaPolisa", ""),
                    BrojNaPersonalnaRazdolznica = filterData.GetValueOrDefault("Filter.BrojNaPersonalnaRazdolznica", ""),
                    DatumNaIzdavanjeFrom = !string.IsNullOrEmpty(filterData["Filter.DatumNaIzdavanjeFrom"]) ? DateTime.Parse(filterData["Filter.DatumNaIzdavanjeFrom"]) : null,
                    DatumNaIzdavanjeTo = !string.IsNullOrEmpty(filterData["Filter.DatumNaIzdavanjeTo"]) ? DateTime.Parse(filterData["Filter.DatumNaIzdavanjeTo"]) : null
                };
            }

            var (whereClause, parameters) = BuildWhereClause();
            var filteredResults = new List<ZadolzuvanjeRazdolzuvanjeViewModel>();

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                var query = @"SELECT 
                                p.Id,
                                p.DateCreated,
                                p.UsernameCreated,
                                p.DateModified,
                                p.UsernameModified,
                                kl.Naziv as OsiguritelNaziv,
                                p.KlientiIdOsiguritel,
                                ko.KlasaIme,
                                p.KlasiOsiguruvanjeIdKlasa,
                                pr.Ime as ProduktIme,
                                p.ProduktiIdProdukt,
                                p.BrojNaPolisa,
                                p.BrojNaPonuda,
                                CASE 
                                    WHEN kz.KlientFizickoPravnoLice = 'P' THEN kz.Naziv
                                    ELSE CONCAT(kz.Ime, ' ', kz.Prezime)
                                END as ZadolzenIme,
                                p.DatumNaZadolzuvanje,
                                s.OsnovZaRazdolzuvanje,
                                p.PotvrdenoRazdolzuvanjeKajBroker,
                                p.DatumNaRazdolzuvanjeKajBroker,
                                p.PotvrdenoRazdolzuvanjeKajOsiguritel,
                                p.DatumNaRazdolzuvanjeKajOsiguritel,
                                dbo.ZadolzuvanjeRazdolzuvanjeVratiPolisaDatumNaIzdavanje(p.BrojNaPolisa) as DatumNaIzdavanje,
                                dbo.VratiDaliImaStorniranaBrojNaPolisa(p.BrojNaPolisa) as IsStornirana,
                                CASE 
                                    WHEN dbo.VratiDaliImaStorniranaBrojNaPolisa(p.BrojNaPolisa) = 1 THEN 'Сторнирана'
                                    ELSE ''
                                END as Status,
                                p.BrojNaPersonalnaRazdolznica
                            FROM PolisiZadolzuvanjeRazdolzuvanje p
                            LEFT JOIN KlasiOsiguruvanje ko ON p.KlasiOsiguruvanjeIdKlasa = ko.Id
                            LEFT JOIN Produkti pr ON p.ProduktiIdProdukt = pr.Id
                            LEFT JOIN Klienti kl ON p.KlientiIdOsiguritel = kl.Id
                            LEFT JOIN Klienti kz ON p.KlientiIdZadolzen = kz.Id
                            LEFT JOIN SifrarnikOsnovZaRazdolzuvanje s ON p.SifrarnikOsnovZaRazdolzuvanjeId = s.Id
                            WHERE 1=1 " + whereClause + @"
                            ORDER BY 
                                p.KlientiIdOsiguritel,
                                p.KlasiOsiguruvanjeIdKlasa,
                                p.ProduktiIdProdukt,
                                dbo.VratiDaliImaStorniranaBrojNaPolisa(p.BrojNaPolisa),
                                dbo.ZadolzuvanjeRazdolzuvanjeVratiPolisaDatumNaIzdavanje(p.BrojNaPolisa)";

                using (SqlCommand cmd = new SqlCommand(query, connection))
                {
                    cmd.Parameters.AddRange(parameters.ToArray());
                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            filteredResults.Add(new ZadolzuvanjeRazdolzuvanjeViewModel
                            {
                                Id = reader.GetInt64(0),
                                DateCreated = reader.IsDBNull(1) ? null : reader.GetDateTime(1),
                                UsernameCreated = reader.IsDBNull(2) ? null : reader.GetString(2),
                                DateModified = reader.IsDBNull(3) ? null : reader.GetDateTime(3),
                                UsernameModified = reader.IsDBNull(4) ? null : reader.GetString(4),
                                OsiguritelNaziv = reader.IsDBNull(5) ? null : reader.GetString(5),
                                KlientiIdOsiguritel = reader.IsDBNull(6) ? null : reader.GetInt64(6),
                                KlasaIme = reader.IsDBNull(7) ? null : reader.GetString(7),
                                KlasiOsiguruvanjeIdKlasa = reader.IsDBNull(8) ? null : reader.GetInt32(8),
                                ProduktIme = reader.IsDBNull(9) ? null : reader.GetString(9),
                                ProduktiIdProdukt = reader.IsDBNull(10) ? null : reader.GetInt32(10),
                                BrojNaPolisa = reader.IsDBNull(11) ? null : reader.GetString(11),
                                BrojNaPonuda = reader.IsDBNull(12) ? null : reader.GetInt64(12),
                                ZadolzenIme = reader.IsDBNull(13) ? null : reader.GetString(13),
                                DatumNaZadolzuvanje = reader.IsDBNull(14) ? null : reader.GetDateTime(14),
                                OsnovZaRazdolzuvanje = reader.IsDBNull(15) ? null : reader.GetString(15),
                                PotvrdenoRazdolzuvanjeKajBroker = reader.IsDBNull(16) ? false : reader.GetBoolean(16),
                                DatumNaRazdolzuvanjeKajBroker = reader.IsDBNull(17) ? null : reader.GetDateTime(17),
                                PotvrdenoRazdolzuvanjeKajOsiguritel = reader.IsDBNull(18) ? false : reader.GetBoolean(18),
                                DatumNaRazdolzuvanjeKajOsiguritel = reader.IsDBNull(19) ? null : reader.GetDateTime(19),
                                DatumNaIzdavanje = reader.IsDBNull(20) ? null : reader.GetDateTime(20),
                                IsStornirana = !reader.IsDBNull(21) && Convert.ToInt32(reader.GetValue(21)) == 1,
                                Status = reader.IsDBNull(22) ? null : reader.GetString(22),
                                BrojNaPersonalnaRazdolznica = reader.IsDBNull(23) ? null : reader.GetString(23)
                            });
                        }
                    }
                }
            }

            // Create Excel Package
            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("Преглед");

                // Add headers
                worksheet.Cells[1, 1].Value = "Датум на креирање";
                worksheet.Cells[1, 2].Value = "Креирал";
                worksheet.Cells[1, 3].Value = "Датум на промена";
                worksheet.Cells[1, 4].Value = "Променил";
                worksheet.Cells[1, 5].Value = "Осигурител";
                worksheet.Cells[1, 6].Value = "Класа";
                worksheet.Cells[1, 7].Value = "Продукт";
                worksheet.Cells[1, 8].Value = "Број на полиса";
                worksheet.Cells[1, 9].Value = "Број на понуда";
                worksheet.Cells[1, 10].Value = "Задолжен";
                worksheet.Cells[1, 11].Value = "Датум на задолжување";
                worksheet.Cells[1, 12].Value = "Основ за раздолжување";
                worksheet.Cells[1, 13].Value = "Потврдено кај брокер";
                worksheet.Cells[1, 14].Value = "Датум на раздолжување кај брокер";
                worksheet.Cells[1, 15].Value = "Потврдено кај осигурител";
                worksheet.Cells[1, 16].Value = "Датум на раздолжување кај осигурител";
                worksheet.Cells[1, 17].Value = "Датум на издавање";
                worksheet.Cells[1, 18].Value = "Статус";
                worksheet.Cells[1, 19].Value = "Број на персонална раздолжница";

                // Style the header
                using (var range = worksheet.Cells[1, 1, 1, 19])
                {
                    range.Style.Font.Bold = true;
                    range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                    range.Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                }

                // Add data
                int row = 2;
                foreach (var item in filteredResults)
                {
                    worksheet.Cells[row, 1].Value = item.DateCreated?.ToString("dd.MM.yyyy");
                    worksheet.Cells[row, 2].Value = item.UsernameCreated;
                    worksheet.Cells[row, 3].Value = item.DateModified?.ToString("dd.MM.yyyy");
                    worksheet.Cells[row, 4].Value = item.UsernameModified;
                    worksheet.Cells[row, 5].Value = item.OsiguritelNaziv;
                    worksheet.Cells[row, 6].Value = item.KlasaIme;
                    worksheet.Cells[row, 7].Value = item.ProduktIme;
                    worksheet.Cells[row, 8].Value = item.BrojNaPolisa;
                    worksheet.Cells[row, 9].Value = item.BrojNaPonuda;
                    worksheet.Cells[row, 10].Value = item.ZadolzenIme;
                    worksheet.Cells[row, 11].Value = item.DatumNaZadolzuvanje?.ToString("dd.MM.yyyy");
                    worksheet.Cells[row, 12].Value = item.OsnovZaRazdolzuvanje;
                    worksheet.Cells[row, 13].Value = item.PotvrdenoRazdolzuvanjeKajBroker ? "Да" : "Не";
                    worksheet.Cells[row, 14].Value = item.DatumNaRazdolzuvanjeKajBroker?.ToString("dd.MM.yyyy");
                    worksheet.Cells[row, 15].Value = item.PotvrdenoRazdolzuvanjeKajOsiguritel ? "Да" : "Не";
                    worksheet.Cells[row, 16].Value = item.DatumNaRazdolzuvanjeKajOsiguritel?.ToString("dd.MM.yyyy");
                    worksheet.Cells[row, 17].Value = item.DatumNaIzdavanje?.ToString("dd.MM.yyyy");
                    worksheet.Cells[row, 18].Value = item.Status;
                    worksheet.Cells[row, 19].Value = item.BrojNaPersonalnaRazdolznica;
                    row++;
                }

                // Auto-fit columns
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                // Return the Excel file
                var content = package.GetAsByteArray();
                return File(
                    content,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    $"ZadolzuvanjeRazdolzuvanje_{DateTime.Now:yyyyMMdd}.xlsx"
                );
            }
        }


    }
}
