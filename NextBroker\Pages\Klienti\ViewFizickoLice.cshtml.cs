using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using Renci.SshNet;
using System.IO;

namespace NextBroker.Pages.Klienti
{
    public class ViewFizickoLiceModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public List<KlientiFile> Files { get; set; }

        public class KlientiFile
        {
            public long Id { get; set; }
            public string FileName { get; set; }
            public string FilePath { get; set; }
            public DateTime DateCreated { get; set; }
            public string UsernameCreated { get; set; }
        }

        public ViewFizickoLiceModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public SelectList Opstini { get; set; }
        public SelectList Banki { get; set; }
        public SelectList NivoaNaRizik { get; set; }
        public SelectList RabotniPozicii { get; set; }
        public SelectList OrganizacioniEdinici { get; set; }
        public SelectList EkspozituriIdEkspozitura { get; set; }

        public class InputModel
        {
            public long Id { get; set; }
            public string? Ime { get; set; }
            public string? Prezime { get; set; }
            public string? EMBG { get; set; }
            public int? ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija { get; set; }
            public string? UlicaOdDokumentZaIdentifikacija { get; set; }
            public string? BrojOdDokumentZaIdentifikacija { get; set; }
            public int? ListaOpstiniIdOpstinaZaKomunikacija { get; set; }
            public string? UlicaZaKomunikacija { get; set; }
            public string? BrojZaKomunikacija { get; set; }
            public string? BrojPasosLicnaKarta { get; set; }
            public DateTime? DatumVaziOdPasosLicnaKarta { get; set; }
            public DateTime? DatumVaziDoPasosLicnaKarta { get; set; }
            public string? Email { get; set; }
            public string? Tel { get; set; }
            public string? Webstrana { get; set; }
            public bool SoglasnostZaDirektenMarketing { get; set; }
            public bool SoglasnostZaEmailKomunikacija { get; set; }
            public bool SoglasnostZaTelKomunikacija { get; set; }
            public DateTime? DatumNaPovlecenaSoglasnostZaDirektenMarketing { get; set; }
            public bool NositelNaJF { get; set; }
            public string? OsnovZaNositelNaJF { get; set; }
            public bool ZasilenaAnaliza { get; set; }
            public int? NivoaNaRizikIdNivoNaRizik { get; set; }
            public DateTime? DaumNaDogovor { get; set; }
            public DateTime? DogovorVaziDo { get; set; }
            public string? BrojNaDogovor { get; set; }
            public string? DogovorOpredelenoNeopredeleno { get; set; }
            public string? PlateznaSmetka { get; set; }
            public int? SifrarnikBankiIdBanka { get; set; }
            public long? SifrarnikRabotniPoziciiIdRabotnaPozicija { get; set; }
            public long? SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica { get; set; }
            public long? SifrarnikRabotniPoziciiIdNadreden { get; set; }
            public DateTime? NadredenDo { get; set; }
            public DateTime? NadredenOd { get; set; }
            public bool ZadolzitelnoLicenca { get; set; }
            public string? BrojNaResenieOdASOZaLicenca { get; set; }
            public DateTime? DatumNaResenieOdASOZaLicenca { get; set; }
            public DateTime? DatumNaOdzemenaLicenca { get; set; }
            public string? BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti { get; set; }
            public DateTime? DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti { get; set; }
            public bool KlientVraboten { get; set; }
            public bool KlientSorabotnik { get; set; }
            public string? KlientPol { get; set; }
            public DateTime? DatumNaOvlastuvanje { get; set; }
            public int? RokNaPlakanjeDenovi { get; set; }
            public string? NadredenImePrezime { get; set; }
            public string? Zabeleska { get; set; }
            public int? EkspozituriIdEkspozitura { get; set; }
        }

        [BindProperty]
        public InputModel Input { get; set; }

        public async Task<IActionResult> OnGetAsync(long id)
        {
            if (!await HasPageAccess("ListaKlienti"))
            {
                return RedirectToAccessDenied();
            }

            await LoadDropdownData();
            await LoadClientData(id);
            await LoadFiles(id);

            return Page();
        }

        private async Task LoadDropdownData()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // Load Opstini
                using (SqlCommand cmd = new SqlCommand("SELECT Id, Opstina FROM ListaOpstini", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["Opstina"].ToString(), reader["Id"].ToString()));
                    }
                    Opstini = new SelectList(items, "Value", "Text");
                }

                // Load NivoaNaRizik
                using (SqlCommand cmd = new SqlCommand("SELECT Id, OpisNivoRizik FROM NivoaNaRizik", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["OpisNivoRizik"].ToString(), reader["Id"].ToString()));
                    }
                    NivoaNaRizik = new SelectList(items, "Value", "Text");
                }

                // Load Banki
                using (SqlCommand cmd = new SqlCommand("SELECT Id, Banka FROM SifrarnikBanki", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["Banka"].ToString(), reader["Id"].ToString()));
                    }
                    Banki = new SelectList(items, "Value", "Text");
                }

                // Load RabotniPozicii
                using (SqlCommand cmd = new SqlCommand("SELECT Id, RabotnaPozicija FROM SifrarnikRabotniPozicii", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["RabotnaPozicija"].ToString(), reader["Id"].ToString()));
                    }
                    RabotniPozicii = new SelectList(items, "Value", "Text");
                }

                // Load OrganizacioniEdinici
                using (SqlCommand cmd = new SqlCommand("SELECT Id, OrganizacionaEdinica FROM SifrarnikOrganizacioniEdinici", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["OrganizacionaEdinica"].ToString(), reader["Id"].ToString()));
                    }
                    OrganizacioniEdinici = new SelectList(items, "Value", "Text");
                }

                // Load Ekspozituri
                using (SqlCommand cmd = new SqlCommand("SELECT Id, Ime FROM Ekspozituri", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["Ime"].ToString(), reader["Id"].ToString()));
                    }
                    EkspozituriIdEkspozitura = new SelectList(items, "Value", "Text");
                }            
            }
        }

        private async Task LoadClientData(long id)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                string sql = @"
                    SELECT k.*, 
                           CASE 
                               WHEN n.KlientFizickoPravnoLice = 'P' THEN n.Naziv
                               ELSE CONCAT(ISNULL(n.Ime, ''), ' ', ISNULL(n.Prezime, ''))
                           END as NadredenImePrezime
                    FROM Klienti k
                    LEFT JOIN Klienti n ON k.SifrarnikRabotniPoziciiIdNadreden = n.Id
                    WHERE k.Id = @Id";

                using (SqlCommand cmd = new SqlCommand(sql, connection))
                {
                    cmd.Parameters.AddWithValue("@Id", id);
                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            Input = new InputModel
                            {
                                Id = id,
                                Ime = reader["Ime"].ToString(),
                                Prezime = reader["Prezime"].ToString(),
                                EMBG = reader["EMBG"].ToString(),
                                ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija = reader.IsDBNull(reader.GetOrdinal("ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija")),
                                UlicaOdDokumentZaIdentifikacija = reader["UlicaOdDokumentZaIdentifikacija"].ToString(),
                                BrojOdDokumentZaIdentifikacija = reader["BrojOdDokumentZaIdentifikacija"].ToString(),
                                ListaOpstiniIdOpstinaZaKomunikacija = reader.IsDBNull(reader.GetOrdinal("ListaOpstiniIdOpstinaZaKomunikacija")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("ListaOpstiniIdOpstinaZaKomunikacija")),
                                UlicaZaKomunikacija = reader["UlicaZaKomunikacija"].ToString(),
                                BrojZaKomunikacija = reader["BrojZaKomunikacija"].ToString(),
                                BrojPasosLicnaKarta = reader["BrojPasosLicnaKarta"].ToString(),
                                DatumVaziOdPasosLicnaKarta = reader.IsDBNull(reader.GetOrdinal("DatumVaziOdPasosLicnaKarta")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DatumVaziOdPasosLicnaKarta")),
                                DatumVaziDoPasosLicnaKarta = reader.IsDBNull(reader.GetOrdinal("DatumVaziDoPasosLicnaKarta")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DatumVaziDoPasosLicnaKarta")),
                                Email = reader["Email"].ToString(),
                                Tel = reader["Tel"].ToString(),
                                Webstrana = reader["Webstrana"].ToString(),
                                SoglasnostZaDirektenMarketing = !reader.IsDBNull(reader.GetOrdinal("SoglasnostZaDirektenMarketing")) && reader.GetBoolean(reader.GetOrdinal("SoglasnostZaDirektenMarketing")),
                                SoglasnostZaEmailKomunikacija = !reader.IsDBNull(reader.GetOrdinal("SoglasnostZaEmailKomunikacija")) && reader.GetBoolean(reader.GetOrdinal("SoglasnostZaEmailKomunikacija")),
                                SoglasnostZaTelKomunikacija = !reader.IsDBNull(reader.GetOrdinal("SoglasnostZaTelKomunikacija")) && reader.GetBoolean(reader.GetOrdinal("SoglasnostZaTelKomunikacija")),
                                DatumNaPovlecenaSoglasnostZaDirektenMarketing = reader.IsDBNull(reader.GetOrdinal("DatumNaPovlecenaSoglasnostZaDirektenMarketing")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DatumNaPovlecenaSoglasnostZaDirektenMarketing")),
                                NositelNaJF = !reader.IsDBNull(reader.GetOrdinal("NositelNaJF")) && reader.GetBoolean(reader.GetOrdinal("NositelNaJF")),
                                OsnovZaNositelNaJF = reader["OsnovZaNositelNaJF"].ToString(),
                                ZasilenaAnaliza = !reader.IsDBNull(reader.GetOrdinal("ZasilenaAnaliza")) && reader.GetBoolean(reader.GetOrdinal("ZasilenaAnaliza")),
                                NivoaNaRizikIdNivoNaRizik = reader.IsDBNull(reader.GetOrdinal("NivoaNaRizikIdNivoNaRizik")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("NivoaNaRizikIdNivoNaRizik")),
                                DaumNaDogovor = reader.IsDBNull(reader.GetOrdinal("DaumNaDogovor")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DaumNaDogovor")),
                                DogovorVaziDo = reader.IsDBNull(reader.GetOrdinal("DogovorVaziDo")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DogovorVaziDo")),
                                BrojNaDogovor = reader["BrojNaDogovor"].ToString(),
                                DogovorOpredelenoNeopredeleno = reader["DogovorOpredelenoNeopredeleno"].ToString(),
                                PlateznaSmetka = reader["PlateznaSmetka"].ToString(),
                                SifrarnikBankiIdBanka = reader.IsDBNull(reader.GetOrdinal("SifrarnikBankiIdBanka")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("SifrarnikBankiIdBanka")),
                                SifrarnikRabotniPoziciiIdRabotnaPozicija = reader.IsDBNull(reader.GetOrdinal("SifrarnikRabotniPoziciiIdRabotnaPozicija")) ? null : (long?)reader.GetInt64(reader.GetOrdinal("SifrarnikRabotniPoziciiIdRabotnaPozicija")),
                                SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica = reader.IsDBNull(reader.GetOrdinal("SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica")) ? null : (long?)reader.GetInt64(reader.GetOrdinal("SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica")),
                                SifrarnikRabotniPoziciiIdNadreden = reader.IsDBNull(reader.GetOrdinal("SifrarnikRabotniPoziciiIdNadreden")) ? null : (long?)reader.GetInt64(reader.GetOrdinal("SifrarnikRabotniPoziciiIdNadreden")),
                                NadredenOd = reader.IsDBNull(reader.GetOrdinal("NadredenVaziOd")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("NadredenVaziOd")),
                                NadredenDo = reader.IsDBNull(reader.GetOrdinal("NadredenDo")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("NadredenDo")),
                                ZadolzitelnoLicenca = !reader.IsDBNull(reader.GetOrdinal("ZadolzitelnoLicenca")) && reader.GetBoolean(reader.GetOrdinal("ZadolzitelnoLicenca")),
                                BrojNaResenieOdASOZaLicenca = reader["BrojNaResenieOdASOZaLicenca"].ToString(),
                                DatumNaResenieOdASOZaLicenca = reader.IsDBNull(reader.GetOrdinal("DatumNaResenieOdASOZaLicenca")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DatumNaResenieOdASOZaLicenca")),
                                DatumNaOdzemenaLicenca = reader.IsDBNull(reader.GetOrdinal("DatumNaOdzemenaLicenca")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DatumNaOdzemenaLicenca")),
                                BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti = reader["BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti"].ToString(),
                                DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti = reader.IsDBNull(reader.GetOrdinal("DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti")),
                                KlientVraboten = !reader.IsDBNull(reader.GetOrdinal("KlientVraboten")) && reader.GetBoolean(reader.GetOrdinal("KlientVraboten")),
                                KlientSorabotnik = !reader.IsDBNull(reader.GetOrdinal("KlientSorabotnik")) && reader.GetBoolean(reader.GetOrdinal("KlientSorabotnik")),
                                KlientPol = reader["KlientPol"].ToString(),
                                DatumNaOvlastuvanje = reader.IsDBNull(reader.GetOrdinal("DatumNaOvlastuvanje")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DatumNaOvlastuvanje")),
                                RokNaPlakanjeDenovi = reader.IsDBNull(reader.GetOrdinal("RokNaPlakanjeDenovi")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("RokNaPlakanjeDenovi")),
                                NadredenImePrezime = reader["NadredenImePrezime"].ToString(),
                                Zabeleska = reader["Zabeleska"].ToString(),
                                EkspozituriIdEkspozitura = reader.IsDBNull(reader.GetOrdinal("EkspozituriIdEkspozitura")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("EkspozituriIdEkspozitura"))
                            };
                        }
                    }
                }
            }
        }

        private async Task LoadFiles(long clientId)
        {
            Files = new List<KlientiFile>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                string sql = @"SELECT Id, FileName, FilePath, DateCreated, UsernameCreated 
                             FROM KlientiFileSystem 
                             WHERE KlientId = @KlientId 
                             ORDER BY DateCreated DESC";

                using (SqlCommand cmd = new SqlCommand(sql, connection))
                {
                    cmd.Parameters.AddWithValue("@KlientId", clientId);
                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            Files.Add(new KlientiFile
                            {
                                Id = reader.GetInt64(0),
                                FileName = reader.GetString(1),
                                FilePath = reader.GetString(2),
                                DateCreated = reader.GetDateTime(3),
                                UsernameCreated = reader.GetString(4)
                            });
                        }
                    }
                }
            }
        }

        public async Task<IActionResult> OnGetDownloadFileAsync(long fileId)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            string filePath = "";
            string fileName = "";

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                string sql = "SELECT FilePath, FileName FROM KlientiFileSystem WHERE Id = @FileId";
                using (SqlCommand cmd = new SqlCommand(sql, connection))
                {
                    cmd.Parameters.AddWithValue("@FileId", fileId);
                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            filePath = reader.GetString(0);
                            fileName = reader.GetString(1);
                        }
                        else
                        {
                            return NotFound("File not found");
                        }
                    }
                }
            }

            var sftpConfig = _configuration.GetSection("SftpConfig").Get<Dictionary<string, string>>();
            using (var client = new SftpClient(sftpConfig["Host"], int.Parse(sftpConfig["Port"]), sftpConfig["Username"], sftpConfig["Password"]))
            {
                try
                {
                    client.Connect();
                    if (!client.Exists(filePath))
                    {
                        return NotFound("File not found on SFTP server");
                    }

                    using (var ms = new MemoryStream())
                    {
                        client.DownloadFile(filePath, ms);
                        ms.Position = 0;
                        return File(ms.ToArray(), "application/octet-stream", fileName);
                    }
                }
                catch (Exception ex)
                {
                    return StatusCode(500, $"Error downloading file: {ex.Message}");
                }
                finally
                {
                    if (client.IsConnected)
                    {
                        client.Disconnect();
                    }
                }
            }
        }
    }
} 
