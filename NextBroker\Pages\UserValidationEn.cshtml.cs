using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Data.SqlClient;
using RazorPortal.Services;

namespace RazorPortal.Pages
{
    public class UserValidationEnModel : PageModel
    {
        private readonly IConfiguration _configuration;

        public string Message { get; set; }
        public bool IsConfirmed { get; set; } = false;

        public UserValidationEnModel(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public void OnGet()
        {
            // This can handle any logic needed when the page loads
        }

        public IActionResult OnPost(string validationCode)
        {
            if (string.IsNullOrEmpty(validationCode))
            {
                Message = "Invalid validation code, please try again...";
                return Page();
            }

            var connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (var connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (var command = new SqlCommand("UPDATE users SET locked = 0, resetrequest = NULL WHERE resetrequest = @code", connection))
                {
                    command.Parameters.AddWithValue("@code", validationCode);
                    int rowsAffected = command.ExecuteNonQuery();

                    if (rowsAffected > 0)
                    {
                        Message = "User validated successfully.";
                        IsConfirmed = true;
                    }
                    else
                    {
                        Message = "Invalid validation code, please try again...";
                    }
                }
            }

            return Page();
        }
    }
} 