using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Data.SqlClient;
using RazorPortal.Services;
using System.Collections.Generic;

namespace NextBroker.Pages.AdministrationPages
{
    public class KontrolaPristapModel : SecurePageModel
    {
        public List<string> Users { get; set; } = new();
        public List<PageInfo> AllPages { get; set; } = new();
        public List<UserPageInfo> UserPages { get; set; } = new();
        public string? SelectedUser { get; set; }

        public KontrolaPristapModel(IConfiguration configuration) 
            : base(configuration)
        {
        }

        public async Task<IActionResult> OnGet(string? username = null)
        {
            if (!await HasPageAccess("KontrolaPristap"))
            {
                return RedirectToAccessDenied();
            }

            await LoadUsers();
            await LoadAllPages();
            
            if (!string.IsNullOrEmpty(username))
            {
                SelectedUser = username;
                await LoadUserPages(username);
            }

            return Page();
        }

        private async Task LoadUsers()
        {
            using var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection"));
            await connection.OpenAsync();
            using var command = new SqlCommand(
                "SELECT DISTINCT username FROM Users WHERE username != 'admin' ORDER BY username", 
                connection);
            
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                Users.Add(reader.GetString(0));
            }
        }

        private async Task LoadAllPages()
        {
            using var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection"));
            await connection.OpenAsync();
            using var command = new SqlCommand(
                "SELECT Id, PageName, PageDescription FROM PagesList ORDER BY PageName", 
                connection);
            
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                AllPages.Add(new PageInfo
                {
                    Id = reader.GetInt32(0),
                    PageName = reader.GetString(1),
                    PageDescription = reader.IsDBNull(2) ? null : reader.GetString(2)
                });
            }
        }

        private async Task LoadUserPages(string username)
        {
            using var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection"));
            await connection.OpenAsync();
            using var command = new SqlCommand(@"
                SELECT upa.Id, upa.PageName, upa.DateCreated, pl.PageDescription 
                FROM UserPageAccess upa
                LEFT JOIN PagesList pl ON upa.PageName = pl.PageName
                WHERE Username = @Username 
                ORDER BY upa.PageName", 
                connection);
            command.Parameters.AddWithValue("@Username", username);
            
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                UserPages.Add(new UserPageInfo
                {
                    Id = reader.GetInt32(0),
                    PageName = reader.GetString(1),
                    DateGranted = reader.GetDateTime(2),
                    PageDescription = reader.IsDBNull(3) ? null : reader.GetString(3)
                });
            }
        }

        public async Task<IActionResult> OnPostAddAccessAsync(string username, string pageName)
        {
            if (!await HasPageAccess("KontrolaPristap"))
            {
                return RedirectToAccessDenied();
            }

            using var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection"));
            await connection.OpenAsync();
            using var command = new SqlCommand(
                "INSERT INTO UserPageAccess (PageName, Username) VALUES (@PageName, @Username)", 
                connection);
            command.Parameters.AddWithValue("@PageName", pageName);
            command.Parameters.AddWithValue("@Username", username);
            await command.ExecuteNonQueryAsync();
            
            return RedirectToPage(new { username });
        }

        public async Task<IActionResult> OnPostRemoveAccessAsync(int id, string username)
        {
            if (!await HasPageAccess("KontrolaPristap"))
            {
                return RedirectToAccessDenied();
            }

            using var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection"));
            await connection.OpenAsync();
            using var command = new SqlCommand(
                "DELETE FROM UserPageAccess WHERE Id = @Id", 
                connection);
            command.Parameters.AddWithValue("@Id", id);
            await command.ExecuteNonQueryAsync();
            
            return RedirectToPage(new { username });
        }
    }

    public class PageInfo
    {
        public int Id { get; set; }
        public string PageName { get; set; } = string.Empty;
        public string? PageDescription { get; set; }
    }

    public class UserPageInfo
    {
        public int Id { get; set; }
        public string PageName { get; set; } = string.Empty;
        public string? PageDescription { get; set; }
        public DateTime DateGranted { get; set; }
    }
} 