@page
@model NextBroker.Pages.Pregledi.SostojbaNaDolgPoDogovoruvacModel
@{
    ViewData["Title"] = "Состојба на долг по договорувач";
}

@section Styles {
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" />
}

<div class="container-fluid">
    <h2>@ViewData["Title"]</h2>
</div>

<div class="container mt-4">
    <form method="post">
        <div class="row">
            <div class="col-md-4">
                <div class="input-group input-group-sm">
                    <input type="text" class="form-control" asp-for="MaticenBroj" placeholder="Внесете матичен број">                    
                </div>
                <div class="input-group input-group-sm">
                    <button type="submit" class="btn btn-primary">Генерирај преглед</button>
                </div>
            </div>
        </div>
    </form>

    <!-- Add this right after the form -->
    <div class="row mt-3">
        <div class="col-md-4">
            @if (Model.Results != null && Model.Results.Any())
            {
                <a href="?handler=ExportExcel&maticenBroj=@Model.MaticenBroj" class="btn btn-success btn-sm">
                    <i class="fas fa-file-excel me-1"></i> Генерирај Excel
                </a>
            }
        </div>
    </div>

    <!-- Add this for debug info -->
    @if (!string.IsNullOrEmpty(Model.DebugInfo))
    {
        <div class="alert alert-info mt-3">
            <pre>@Model.DebugInfo</pre>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger mt-3">
            @Model.ErrorMessage
        </div>
    }

    @if (Model.Results != null && Model.Results.Any())
    {
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="resultTable">
                        <thead>
                            <tr>
                                <th>Полиса број</th>
                                <th>Осигурител</th>
                                <th>Класа</th>
                                <th>Производ</th>
                                <th>Вкупна Премија за плаќање</th>
                                <th>Вкупен број на рати</th>
                                <th>Преостаната Должна премија</th>
                                <th>Преостанати рати</th>
                                <th>Вкупен доспеан долг</th>
                                <th>Број на доспеани неплатени рати</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var row in Model.Results)
                            {
                                <tr>
                                    <td>@row.BrojNaPolisa</td>
                                    <td>@row.Osiguritel</td>
                                    <td>@row.Klasa</td>
                                    <td>@row.Proizvod</td>
                                    <td>@row.VkupnaPremija.ToString("N2")</td>
                                    <td>@row.VkupenBrojRati</td>
                                    <td>@row.PreostanataDolznaPremija.ToString("N2")</td>
                                    <td>@row.PreostanatiRati</td>
                                    <td>@row.VkupenDospeanDolg.ToString("N2")</td>
                                    <td>@row.BrojDospeaniNeplateniRati</td>
                                </tr>
                            }
                        </tbody>
                        @if (Model.Totals != null)
                        {
                            <tfoot>
                                <tr class="table-dark fw-bold">
                                    <td colspan="4">ВКУПНО:</td>
                                    <td>@Model.Totals.VkupnaPremija.ToString("N2")</td>
                                    <td>@Model.Totals.VkupenBrojRati</td>
                                    <td>@Model.Totals.PreostanataDolznaPremija.ToString("N2")</td>
                                    <td>@Model.Totals.PreostanatiRati</td>
                                    <td>@Model.Totals.VkupenDospeanDolg.ToString("N2")</td>
                                    <td>@Model.Totals.BrojDospeaniNeplateniRati</td>
                                </tr>
                            </tfoot>
                        }
                    </table>
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    <script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        $(document).ready(function() {
            $('#resultTable').DataTable({
                "order": [[0, "desc"]],
                "pageLength": 25,
                "language": {
                    "lengthMenu": "Прикажи _MENU_ редови",
                    "zeroRecords": "Не се пронајдени записи",
                    "info": "Прикажани _START_ до _END_ од _TOTAL_ записи",
                    "infoEmpty": "Прикажани 0 до 0 од 0 записи",
                    "infoFiltered": "(филтрирано од вкупно _MAX_ записи)",
                    "search": "Пребарај:",
                    "paginate": {
                        "first": "Прва",
                        "last": "Последна",
                        "next": "Следна",
                        "previous": "Претходна"
                    }
                }
            });
        });
    </script>
}


