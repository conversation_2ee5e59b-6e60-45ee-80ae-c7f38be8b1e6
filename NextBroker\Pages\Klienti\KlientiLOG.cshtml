@page "/Klienti/KlientiLOG/{id:long}"
@model NextBroker.Pages.Klienti.KlientiLOGModel
@{
    ViewData["Title"] = "Историјат на промени - " + Model.KlientNaziv;
}

<div class="container-fluid mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1>Историјат на промени</h1>
            <h5 class="text-muted">@Model.KlientNaziv (ID: @Model.KlientId)</h5>
        </div>
        <a href="/Klienti/ListaKlienti" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Назад кон листа
        </a>
    </div>

    @if (!Model.KlientiLOG.Any())
    {
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            Нема пронајдени записи во историјатот за овој клиент.
        </div>
    }
    else
    {
        <div class="card">
            <div class="card-body">
                <div class="alert alert-info mb-3">
                    <i class="fas fa-info-circle"></i>
                    <strong>Легенда:</strong> Табелата покажува историјат на промени и тековната состојба на клиентот. 
                    Записот означен со <span class="badge bg-success">ТЕКОВНО</span> го претставува тековниот запис од главната табела.
                </div>
                <div class="mb-3">
                    <input type="text" id="searchBox" class="form-control" placeholder="Пребарувај во историјатот..." />
                </div>
                <div class="mb-3">
                    <button class="btn btn-secondary w-100 text-start" type="button" data-bs-toggle="collapse" data-bs-target="#columnTogglers" aria-expanded="false" aria-controls="columnTogglers">
                        <i class="fas fa-cog me-2"></i> Додатни информации
                        <i class="fas fa-chevron-down float-end mt-1"></i>
                    </button>
                </div>
                <div class="collapse mb-3" id="columnTogglers">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <strong>Основни информации</strong>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="6" id="toggle-tip">
                                <label class="form-check-label" for="toggle-tip">Тип</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="7" id="toggle-ime-naziv">
                                <label class="form-check-label" for="toggle-ime-naziv">Име/Назив</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="8" id="toggle-prezime">
                                <label class="form-check-label" for="toggle-prezime">Презиме</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="9" id="toggle-edb">
                                <label class="form-check-label" for="toggle-edb">ЕДБ</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="10" id="toggle-mb">
                                <label class="form-check-label" for="toggle-mb">МБ</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="11" id="toggle-embg">
                                <label class="form-check-label" for="toggle-embg">ЕМБГ</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="54" id="toggle-pol">
                                <label class="form-check-label" for="toggle-pol">Пол</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="55" id="toggle-vraboten">
                                <label class="form-check-label" for="toggle-vraboten">Вработен</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="56" id="toggle-sorabotnik">
                                <label class="form-check-label" for="toggle-sorabotnik">Соработник</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="57" id="toggle-brokersko">
                                <label class="form-check-label" for="toggle-brokersko">Брокерско друштво</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="58" id="toggle-osiguritel">
                                <label class="form-check-label" for="toggle-osiguritel">Осигурител</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <strong>Контакт информации</strong>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="23" id="toggle-email">
                                <label class="form-check-label" for="toggle-email">Email</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="24" id="toggle-tel">
                                <label class="form-check-label" for="toggle-tel">Телефон</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="25" id="toggle-webstrana">
                                <label class="form-check-label" for="toggle-webstrana">Веб страна</label>
                            </div>
                            <strong class="mt-3">Документи</strong>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="18" id="toggle-datum-tekovna">
                                <label class="form-check-label" for="toggle-datum-tekovna">Датум на тековна</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="19" id="toggle-pasos">
                                <label class="form-check-label" for="toggle-pasos">Број на пасош/ЛК</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="20" id="toggle-vazi-od">
                                <label class="form-check-label" for="toggle-vazi-od">Важи од</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="21" id="toggle-vazi-do">
                                <label class="form-check-label" for="toggle-vazi-do">Важи до</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="59" id="toggle-datum-ovlastuvanje">
                                <label class="form-check-label" for="toggle-datum-ovlastuvanje">Датум на овластување</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <strong>Адресни податоци</strong>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="12" id="toggle-opstina-id">
                                <label class="form-check-label" for="toggle-opstina-id">Општина (Идент.)</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="13" id="toggle-ulica-id">
                                <label class="form-check-label" for="toggle-ulica-id">Улица (Идент.)</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="14" id="toggle-broj-id">
                                <label class="form-check-label" for="toggle-broj-id">Број (Идент.)</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="15" id="toggle-opstina-kom">
                                <label class="form-check-label" for="toggle-opstina-kom">Општина (Комун.)</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="16" id="toggle-ulica-kom">
                                <label class="form-check-label" for="toggle-ulica-kom">Улица (Комун.)</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="17" id="toggle-broj-kom">
                                <label class="form-check-label" for="toggle-broj-kom">Број (Комун.)</label>
                            </div>
                            <strong class="mt-3">Дејност</strong>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="22" id="toggle-dejnost">
                                <label class="form-check-label" for="toggle-dejnost">Дејност</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <strong>Согласности</strong>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="26" id="toggle-sogl-marketing">
                                <label class="form-check-label" for="toggle-sogl-marketing">Согл. директен маркетинг</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="27" id="toggle-sogl-email">
                                <label class="form-check-label" for="toggle-sogl-email">Согл. email комуникација</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="28" id="toggle-sogl-tel">
                                <label class="form-check-label" for="toggle-sogl-tel">Согл. тел. комуникација</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="29" id="toggle-datum-sogl">
                                <label class="form-check-label" for="toggle-datum-sogl">Датум повлечена согласност</label>
                            </div>
                            <strong class="mt-3">Вистински сопственик</strong>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="30" id="toggle-vistinski-sopstvenik">
                                <label class="form-check-label" for="toggle-vistinski-sopstvenik">Вистински сопственик</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="31" id="toggle-vs-ime">
                                <label class="form-check-label" for="toggle-vs-ime">Име на вист. сопственик</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="32" id="toggle-vs-prezime">
                                <label class="form-check-label" for="toggle-vs-prezime">Презиме на вист. сопственик</label>
                            </div>
                        </div>
                    </div>
                    <div class="row g-3 mt-2">
                        <div class="col-md-3">
                            <strong>Анализа и ризик</strong>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="33" id="toggle-nositel-jf">
                                <label class="form-check-label" for="toggle-nositel-jf">Носител на ЈФ</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="34" id="toggle-osnov-jf">
                                <label class="form-check-label" for="toggle-osnov-jf">Основ за носител на ЈФ</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="35" id="toggle-zasilena">
                                <label class="form-check-label" for="toggle-zasilena">Засилена анализа</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="36" id="toggle-nivo-rizik">
                                <label class="form-check-label" for="toggle-nivo-rizik">Ниво на ризик</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <strong>Договор</strong>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="37" id="toggle-datum-dogovor">
                                <label class="form-check-label" for="toggle-datum-dogovor">Датум на договор</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="38" id="toggle-dogovor-vazi">
                                <label class="form-check-label" for="toggle-dogovor-vazi">Договор важи до</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="39" id="toggle-broj-dogovor">
                                <label class="form-check-label" for="toggle-broj-dogovor">Број на договор</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="40" id="toggle-dogovor-tip">
                                <label class="form-check-label" for="toggle-dogovor-tip">Договор определено/неопределено</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <strong>Банкарски податоци</strong>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="41" id="toggle-platezna-smetka">
                                <label class="form-check-label" for="toggle-platezna-smetka">Платежна сметка</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="42" id="toggle-banka">
                                <label class="form-check-label" for="toggle-banka">Банка</label>
                            </div>
                            <strong class="mt-3">Работна позиција</strong>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="43" id="toggle-rabotna-pozicija">
                                <label class="form-check-label" for="toggle-rabotna-pozicija">Работна позиција</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="44" id="toggle-org-edinica">
                                <label class="form-check-label" for="toggle-org-edinica">Организациона единица</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="45" id="toggle-nadreden">
                                <label class="form-check-label" for="toggle-nadreden">Надреден</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="46" id="toggle-nadreden-od">
                                <label class="form-check-label" for="toggle-nadreden-od">Надреден важи од</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="47" id="toggle-nadreden-do">
                                <label class="form-check-label" for="toggle-nadreden-do">Надреден до</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <strong>Лиценци и дозволи</strong>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="48" id="toggle-zadolzitelno-licenca">
                                <label class="form-check-label" for="toggle-zadolzitelno-licenca">Задолжително лиценца</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="49" id="toggle-broj-resenie">
                                <label class="form-check-label" for="toggle-broj-resenie">Број на решение од АСО</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="50" id="toggle-datum-resenie">
                                <label class="form-check-label" for="toggle-datum-resenie">Датум на решение од АСО</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="51" id="toggle-datum-odzemena">
                                <label class="form-check-label" for="toggle-datum-odzemena">Датум на одземена лиценца</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="52" id="toggle-broj-dozvola">
                                <label class="form-check-label" for="toggle-broj-dozvola">Број на дозвола за брокер</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="53" id="toggle-datum-dozvola">
                                <label class="form-check-label" for="toggle-datum-dozvola">Датум на дозвола за брокер</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input toggle-column" data-column="54" id="toggle-zivot-nezivot">
                                <label class="form-check-label" for="toggle-zivot-nezivot">Живот/Неживот</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table id="logTable" class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>LOG ID</th>
                                <th>Оригинален ID</th>
                                <th>Датум на креирање</th>
                                <th>Креирано од</th>
                                <th>Датум на промена</th>
                                <th>Променето од</th>
                                <th>Тип</th>
                                <th>Име/Назив</th>
                                <th>Презиме</th>
                                <th>ЕДБ</th>
                                <th>МБ</th>
                                <th>ЕМБГ</th>
                                <th>Општина (Идент.)</th>
                                <th>Улица (Идент.)</th>
                                <th>Број (Идент.)</th>
                                <th>Општина (Комун.)</th>
                                <th>Улица (Комун.)</th>
                                <th>Број (Комун.)</th>
                                <th>Датум на тековна</th>
                                <th>Број на пасош/ЛК</th>
                                <th>Важи од</th>
                                <th>Важи до</th>
                                <th>Дејност</th>
                                <th>Email</th>
                                <th>Телефон</th>
                                <th>Веб страна</th>
                                <th>Согл. директен маркетинг</th>
                                <th>Согл. email комуникација</th>
                                <th>Согл. тел. комуникација</th>
                                <th>Датум повлечена согласност</th>
                                <th>Вистински сопственик</th>
                                <th>Име на вист. сопственик</th>
                                <th>Презиме на вист. сопственик</th>
                                <th>Носител на ЈФ</th>
                                <th>Основ за носител на ЈФ</th>
                                <th>Засилена анализа</th>
                                <th>Ниво на ризик</th>
                                <th>Датум на договор</th>
                                <th>Договор важи до</th>
                                <th>Број на договор</th>
                                <th>Договор определено/неопределено</th>
                                <th>Платежна сметка</th>
                                <th>Банка</th>
                                <th>Работна позиција</th>
                                <th>Организациона единица</th>
                                <th>Надреден</th>
                                <th>Надреден важи од</th>
                                <th>Надреден до</th>
                                <th>Задолжително лиценца</th>
                                <th>Број на решение од АСО</th>
                                <th>Датум на решение од АСО</th>
                                <th>Датум на одземена лиценца</th>
                                <th>Број на дозвола за брокер</th>
                                <th>Датум на дозвола за брокер</th>
                                <th>Живот/Неживот</th>
                                <th>Пол</th>
                                <th>Вработен</th>
                                <th>Соработник</th>
                                <th>Брокерско друштво</th>
                                <th>Осигурител</th>  
                                <th>Датум на овластување</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var logEntry in Model.KlientiLOG)
                            {
                                <tr class="@(logEntry.IsCurrentRecord ? "table-success current-record" : "")">
                                    <td>
                                        @if (logEntry.IsCurrentRecord)
                                        {
                                            <span class="badge bg-success">ТЕКОВНО</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-primary">@logEntry.LOGId</span>
                                        }
                                    </td>
                                    <td>@logEntry.Id</td>
                                    <td>@(logEntry.DateCreated?.ToString("dd.MM.yyyy HH:mm"))</td>
                                    <td>@logEntry.UsernameCreated</td>
                                    <td>@(logEntry.DateModified?.ToString("dd.MM.yyyy HH:mm"))</td>
                                    <td>@logEntry.UsernameModified</td>
                                    <td>@logEntry.KlientTip</td>
                                    <td>@(logEntry.KlientTip == "Физичко лице" ? logEntry.Ime : logEntry.Naziv)</td>
                                    <td>@(logEntry.KlientTip == "Физичко лице" ? logEntry.Prezime : "")</td>
                                    <td>@logEntry.EDB</td>
                                    <td>@logEntry.MB</td>
                                    <td>@logEntry.EMBG</td>
                                    <td>@logEntry.ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija</td>
                                    <td>@logEntry.UlicaOdDokumentZaIdentifikacija</td>
                                    <td>@logEntry.BrojOdDokumentZaIdentifikacija</td>
                                    <td>@logEntry.ListaOpstiniIdOpstinaZaKomunikacija</td>
                                    <td>@logEntry.UlicaZaKomunikacija</td>
                                    <td>@logEntry.BrojZaKomunikacija</td>
                                    <td>@(logEntry.DatumNaTekovnaSostojba?.ToString("dd.MM.yyyy"))</td>
                                    <td>@logEntry.BrojPasosLicnaKarta</td>
                                    <td>@(logEntry.DatumVaziOdPasosLicnaKarta?.ToString("dd.MM.yyyy"))</td>
                                    <td>@(logEntry.DatumVaziDoPasosLicnaKarta?.ToString("dd.MM.yyyy"))</td>
                                    <td>@logEntry.ListaDejnostiIdDejnost</td>
                                    <td>@logEntry.Email</td>
                                    <td>@logEntry.Tel</td>
                                    <td>@logEntry.Webstrana</td>
                                    <td>@(logEntry.SoglasnostZaDirektenMarketing ? "Да" : "Не")</td>
                                    <td>@(logEntry.SoglasnostZaEmailKomunikacija ? "Да" : "Не")</td>
                                    <td>@(logEntry.SoglasnostZaTelKomunikacija ? "Да" : "Не")</td>
                                    <td>@(logEntry.DatumNaPovlecenaSoglasnostZaDirektenMarketing?.ToString("dd.MM.yyyy"))</td>
                                    <td>@logEntry.VistinskiSopstvenik</td>
                                    <td>@logEntry.VistinskiSopstvenikIme</td>
                                    <td>@logEntry.VistinskiSopstvenikPrezime</td>
                                    <td>@(logEntry.NositelNaJF ? "Да" : "Не")</td>
                                    <td>@logEntry.OsnovZaNositelNaJF</td>
                                    <td>@(logEntry.ZasilenaAnaliza ? "Да" : "Не")</td>
                                    <td>@logEntry.NivoaNaRizikIdNivoNaRizik</td>
                                    <td>@(logEntry.DaumNaDogovor?.ToString("dd.MM.yyyy"))</td>
                                    <td>@(logEntry.DogovorVaziDo?.ToString("dd.MM.yyyy"))</td>
                                    <td>@logEntry.BrojNaDogovor</td>
                                    <td>@logEntry.DogovorOpredelenoNeopredeleno</td>
                                    <td>@logEntry.PlateznaSmetka</td>
                                    <td>@logEntry.SifrarnikBankiIdBanka</td>
                                    <td>@logEntry.SifrarnikRabotniPoziciiIdRabotnaPozicija</td>
                                    <td>@logEntry.SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica</td>
                                    <td>@logEntry.SifrarnikRabotniPoziciiIdNadreden</td>
                                    <td>@(logEntry.NadredenVaziOd?.ToString("dd.MM.yyyy"))</td>
                                    <td>@(logEntry.NadredenDo?.ToString("dd.MM.yyyy"))</td>
                                    <td>@(logEntry.ZadolzitelnoLicenca ? "Да" : "Не")</td>
                                    <td>@logEntry.BrojNaResenieOdASOZaLicenca</td>
                                    <td>@(logEntry.DatumNaResenieOdASOZaLicenca?.ToString("dd.MM.yyyy"))</td>
                                    <td>@(logEntry.DatumNaOdzemenaLicenca?.ToString("dd.MM.yyyy"))</td>
                                    <td>@logEntry.BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti</td>
                                    <td>@(logEntry.DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti?.ToString("dd.MM.yyyy"))</td>
                                    <td>@logEntry.ZivotNezivot</td>
                                    <td>@logEntry.KlientPol</td>
                                    <td>@(logEntry.KlientVraboten ? "Да" : "Не")</td>
                                    <td>@(logEntry.KlientSorabotnik ? "Да" : "Не")</td>
                                    <td>@(logEntry.BrokerskoDrustvo ? "Да" : "Не")</td>
                                    <td>@(logEntry.Osiguritel ? "Да" : "Не")</td>
                                    <td>@(logEntry.DatumNaOvlastuvanje?.ToString("dd.MM.yyyy"))</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" />
    <style>
        .rotate-180 {
            transform: rotate(180deg);
            transition: transform 0.3s ease;
        }
        .fa-chevron-down {
            transition: transform 0.3s ease;
        }
        #columnTogglers {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.25rem;
        }
        .table-responsive {
            cursor: grab;
            user-select: none;
        }
        
        .table-responsive.grabbing {
            cursor: grabbing;
        }
        
        .badge {
            font-size: 0.85em;
        }
        
        .current-record {
            border-left: 4px solid #198754 !important;
            background-color: rgba(25, 135, 84, 0.05) !important;
        }
        
        .current-record:hover {
            background-color: rgba(25, 135, 84, 0.1) !important;
        }
    </style>

    <script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        $(document).ready(function() {
            // Handle collapse icon rotation
            $('#columnTogglers').on('show.bs.collapse', function () {
                $('.fa-chevron-down').addClass('rotate-180');
            }).on('hide.bs.collapse', function () {
                $('.fa-chevron-down').removeClass('rotate-180');
            });

            var table = $('#logTable').DataTable({
                "dom": "t",
                "language": {
                    "emptyTable": "Нема пронајдени записи во историјатот",
                    "zeroRecords": "Нема пронајдени записи во историјатот"
                },
                "ordering": false,
                "paging": false,
                "pageLength": -1,
                "columnDefs": [
                    // Show only these columns by default - LOG ID, Original ID, dates, usernames, client info
                    { "targets": [0,1,2,3,4,5,6,7,8], "visible": true },
                    // Hide all other columns
                    { "targets": "_all", "visible": false }
                ]
            });

            $('#searchBox').on('keyup', function() {
                table.search(this.value).draw();
            });

            // Handle column visibility toggles
            $('.toggle-column').on('change', function() {
                var column = table.column($(this).attr('data-column'));
                column.visible($(this).is(':checked'));
            });

            // Set initial checkbox states based on visible columns
            table.columns().every(function(index) {
                var isVisible = this.visible();
                $('.toggle-column[data-column="' + index + '"]').prop('checked', isVisible);
            });

            // Add drag scroll functionality
            const tableContainer = document.querySelector('.table-responsive');
            let isDown = false;
            let startX;
            let scrollLeft;

            tableContainer.addEventListener('mousedown', (e) => {
                isDown = true;
                tableContainer.classList.add('grabbing');
                startX = e.pageX - tableContainer.offsetLeft;
                scrollLeft = tableContainer.scrollLeft;
            });

            tableContainer.addEventListener('mouseleave', () => {
                isDown = false;
                tableContainer.classList.remove('grabbing');
            });

            tableContainer.addEventListener('mouseup', () => {
                isDown = false;
                tableContainer.classList.remove('grabbing');
            });

            tableContainer.addEventListener('mousemove', (e) => {
                if (!isDown) return;
                e.preventDefault();
                const x = e.pageX - tableContainer.offsetLeft;
                const walk = (x - startX) * 2; // Adjust scrolling speed
                tableContainer.scrollLeft = scrollLeft - walk;
            });
        });
    </script>
}
