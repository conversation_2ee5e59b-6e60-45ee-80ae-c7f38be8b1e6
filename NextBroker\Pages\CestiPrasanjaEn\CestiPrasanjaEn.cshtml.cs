using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RazorPortal.Services;
using System.Threading.Tasks;

namespace NextBroker.Pages
{
    public class CestiPrasanjaEnModel : PageModel
    {
        private readonly MailerService _mailerService;

        public CestiPrasanjaEnModel(MailerService mailerService)
        {
            _mailerService = mailerService;
        }

        [BindProperty]
        public string Name { get; set; }

        [BindProperty]
        public string Surname { get; set; }

        [BindProperty]
        public string Email { get; set; }

        [BindProperty]
        public string Phone { get; set; }

        [BindProperty]
        public string Question { get; set; }

        [BindProperty]
        public string Description { get; set; }

        public string SuccessMessage { get; private set; }
        public string ErrorMessage { get; private set; }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                ErrorMessage = "All fields are required.";
                return Page();
            }

            var subject = Question;
            var messageBody = $"First Name: {Name}<br>Last Name: {Surname}<br>Contact Email: {Email}<br>Contact Phone: {Phone}<br>Question: {Question}<br>Description: {Description}";

            await _mailerService.SendEmailAsync("<EMAIL>", subject, messageBody);

            SuccessMessage = "Your question has been sent. We will contact you as soon as possible. Thank you!";
            return Page();
        }
    }
} 