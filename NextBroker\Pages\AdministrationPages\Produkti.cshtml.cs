using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.Collections.Generic;

namespace NextBroker.Pages.AdministrationPages
{
    public class Produkt
    {
        public int Id { get; set; }
        public DateTime DateCreated { get; set; }
        public string Ime { get; set; } = string.Empty;
        public string Kategorija { get; set; } = string.Empty;
    }

    public class ProduktiModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public List<Produkt> ProduktiList { get; set; }
        [TempData]
        public bool IsEditing { get; set; }

        public ProduktiModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
            ProduktiList = new List<Produkt>();
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("Produkti"))
            {
                return RedirectToAccessDenied();
            }

            LoadProdukti();
            return Page();
        }

        private void LoadProdukti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(
                    "SELECT Id, DateCreated, Ime, Kategorija " +
                    "FROM Produkti ORDER BY Ime", connection))
                {
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ProduktiList.Add(new Produkt
                            {
                                Id = reader.GetInt32(0),
                                DateCreated = reader.IsDBNull(1) ? DateTime.MinValue : reader.GetDateTime(1),
                                Ime = reader.IsDBNull(2) ? string.Empty : reader.GetString(2),
                                Kategorija = reader.IsDBNull(3) ? string.Empty : reader.GetString(3)
                            });
                        }
                    }
                }
            }
        }

        public IActionResult OnPostToggleEdit()
        {
            IsEditing = !IsEditing;
            LoadProdukti();
            return Page();
        }

        public IActionResult OnPostAddProdukt(string ime, string kategorija)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(
                    "INSERT INTO Produkti (Ime, Kategorija) " +
                    "VALUES (@Ime, @Kategorija)", 
                    connection))
                {
                    command.Parameters.AddWithValue("@Ime", ime);
                    command.Parameters.AddWithValue("@Kategorija", kategorija);
                    command.ExecuteNonQuery();
                }
            }
            
            return RedirectToPage();
        }

        public IActionResult OnPostDeleteProdukt([FromBody] DeleteProduktModel model)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(
                    "DELETE FROM Produkti WHERE Id = @Id", 
                    connection))
                {
                    command.Parameters.AddWithValue("@Id", model.Id);
                    command.ExecuteNonQuery();
                }
            }
            
            return new JsonResult(new { success = true });
        }

        public IActionResult OnPostSaveChanges([FromBody] List<Produkt> updates)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                foreach (var produkt in updates)
                {
                    string currentValues = "SELECT Ime, Kategorija FROM Produkti WHERE Id = @Id";
                    using (SqlCommand getCommand = new SqlCommand(currentValues, connection))
                    {
                        getCommand.Parameters.AddWithValue("@Id", produkt.Id);
                        using (SqlDataReader reader = getCommand.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                string currentIme = reader.IsDBNull(0) ? string.Empty : reader.GetString(0);
                                string currentKategorija = reader.IsDBNull(1) ? string.Empty : reader.GetString(1);

                                if (currentIme != produkt.Ime || 
                                    currentKategorija != produkt.Kategorija)
                                {
                                    reader.Close();
                                    using (SqlCommand command = new SqlCommand(
                                        "UPDATE Produkti SET " +
                                        "Ime = @Ime, " +
                                        "Kategorija = @Kategorija " +
                                        "WHERE Id = @Id", connection))
                                    {
                                        command.Parameters.AddWithValue("@Id", produkt.Id);
                                        command.Parameters.AddWithValue("@Ime", produkt.Ime);
                                        command.Parameters.AddWithValue("@Kategorija", produkt.Kategorija);
                                        command.ExecuteNonQuery();
                                    }
                                }
                            }
                        }
                    }
                }
            }
            IsEditing = false;
            return new JsonResult(new { success = true });
        }
    }

    public class DeleteProduktModel
    {
        public int Id { get; set; }
    }
} 