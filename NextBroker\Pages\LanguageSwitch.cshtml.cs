using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace NextBroker.Pages
{
    public class LanguageSwitchModel : PageModel
    {
        public IActionResult OnGet(string lang)
        {
            HttpContext.Session.SetString("Language", lang);
            
            // Redirect to appropriate Index page based on language
            return RedirectToPage(lang == "En" ? "/IndexEn" : "/Index");
        }
    }
}