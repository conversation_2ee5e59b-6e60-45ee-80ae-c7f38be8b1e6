using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;

namespace NextBroker.Pages.Polisi
{
    public class DodajPolisaKlasa6Model : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public DodajPolisaKlasa6Model(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("DodajPolisaKlasa6"))
            {
                return RedirectToAccessDenied();
            }

            return Page();
        }
    }
}
