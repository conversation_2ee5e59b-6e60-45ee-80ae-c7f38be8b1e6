@page
@model NextBroker.Pages.AdministrationPages.KlasiOsiguruvanjeModel
@{
    ViewData["Title"] = "Класи на осигурување";
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container">
    <div class="mb-3">
        <form method="post" asp-page-handler="ToggleEdit" class="d-flex gap-2">
            <button type="submit" class="btn btn-primary" onclick="return confirmCancel()">
                @(Model.IsEditing ? "Откажи" : "Промени ги податоците")
            </button>
            @if (Model.IsEditing)
            {
                <button type="button" class="btn btn-success" id="saveChanges">Зачувај промени</button>
            }
            <button type="button" class="btn btn-primary" onclick="toggleAddForm()">Додади класа</button>
        </form>
    </div>

    <div id="addForm" style="display: none;" class="mb-3">
        <div class="card">
            <div class="card-body">
                <form method="post" asp-page-handler="AddKlasa">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">Број на класа</label>
                            <input type="number" class="form-control" name="klasaBroj" required />
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Име на класа</label>
                            <input type="text" class="form-control" name="klasaIme" required />
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Опис</label>
                            <input type="text" class="form-control" name="klasaOpis" required />
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="submit" class="btn btn-success">Додади</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="mb-3">
        <input type="text" id="searchBox" class="form-control" placeholder="Пребарувај..." />
    </div>

    <div class="table-responsive">
        <table class="table table-striped" id="klasiTable">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Датум на креирање</th>
                    <th>Датум на промена</th>
                    <th>Број на класа</th>
                    <th>Име на класа</th>
                    <th>Опис</th>
                    <th>Активна</th>
                    @if (Model.IsEditing)
                    {
                        <th>Акции</th>
                    }
                </tr>
            </thead>
            <tbody>
                @foreach (var klasa in Model.KlasiList)
                {
                    <tr data-id="@klasa.Id">
                        <td>@klasa.Id</td>
                        <td>@(klasa.DateCreated == DateTime.MinValue ? "" : klasa.DateCreated.ToString("dd.MM.yyyy HH:mm:ss"))</td>
                        <td>@(klasa.DateModified == DateTime.MinValue ? "" : klasa.DateModified.ToString("dd.MM.yyyy HH:mm:ss"))</td>
                        <td>
                            @if (Model.IsEditing)
                            {
                                <input type="number" class="form-control klasaBroj-edit" value="@klasa.KlasaBroj" />
                            }
                            else
                            {
                                @klasa.KlasaBroj
                            }
                        </td>
                        <td>
                            @if (Model.IsEditing)
                            {
                                <input type="text" class="form-control klasaIme-edit" value="@klasa.KlasaIme" />
                            }
                            else
                            {
                                @klasa.KlasaIme
                            }
                        </td>
                        <td>
                            @if (Model.IsEditing)
                            {
                                <input type="text" class="form-control klasaOpis-edit" value="@klasa.KlasaOpis" />
                            }
                            else
                            {
                                @klasa.KlasaOpis
                            }
                        </td>
                        <td>
                            @if (Model.IsEditing)
                            {
                                <input type="checkbox" class="form-check-input disabled-edit" checked="@(!klasa.Disabled)" />
                            }
                            else
                            {
                                <span>@(!klasa.Disabled ? "Да" : "Не")</span>
                            }
                        </td>
                        @if (Model.IsEditing)
                        {
                            <td>
                                <button type="button" class="btn btn-danger btn-sm delete-klasa" onclick="deleteKlasa(@klasa.Id)">Избриши</button>
                            </td>
                        }
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            var table = $('#klasiTable').DataTable({
                "dom": "t",
                "language": {
                    "emptyTable": "Нема пронајдени записи",
                    "zeroRecords": "Нема пронајдени записи"
                },
                "ordering": false,
                "paging": false,
                "pageLength": -1
            });

            $('#searchBox').on('keyup', function() {
                table.search(this.value).draw();
            });
        });

        function toggleAddForm() {
            const form = document.getElementById('addForm');
            if (form.style.display === 'none' || form.style.display === '') {
                form.style.display = 'block';
            } else {
                form.style.display = 'none';
            }
        }

        function confirmCancel() {
            if (@Json.Serialize(Model.IsEditing)) {
                return confirm("Are you sure you want to cancel editing? Any unsaved changes will be lost.");
            }
            return true;
        }

        function deleteKlasa(id) {
            if (confirm('Дали сте сигурни дека сакате да ја избришете оваа класа?')) {
                fetch('?handler=DeleteKlasa', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    }
                })
                .catch(error => console.error('Error:', error));
            }
        }

        @if (Model.IsEditing)
        {
            <text>
            document.getElementById('saveChanges').addEventListener('click', function() {
                const rows = document.querySelectorAll('#klasiTable tbody tr');
                const updates = [];

                rows.forEach(row => {
                    const id = row.getAttribute('data-id');
                    const klasaBroj = row.querySelector('.klasaBroj-edit').value;
                    const klasaIme = row.querySelector('.klasaIme-edit').value;
                    const klasaOpis = row.querySelector('.klasaOpis-edit').value;
                    const disabled = !row.querySelector('.disabled-edit').checked;

                    updates.push({
                        id: parseInt(id),
                        klasaBroj: parseInt(klasaBroj),
                        klasaIme: klasaIme,
                        klasaOpis: klasaOpis,
                        disabled: disabled
                    });
                });

                fetch('?handler=SaveChanges', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify(updates)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = window.location.pathname;
                    }
                })
                .catch(error => console.error('Error:', error));
            });
            </text>
        }
    </script>
} 