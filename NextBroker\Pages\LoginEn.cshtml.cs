using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Data.SqlClient;
using System.Security.Cryptography;
using System.Text;

namespace NextBroker.Pages
{
    public class LoginEnModel : PageModel
    {
        private readonly string _connectionString;

        public string Message { get; set; }

        [BindProperty]
        [Display(Name = "Username")]
        [Required(ErrorMessage = "Username is required!")]
        public string Username { get; set; }

        [BindProperty]
        [Display(Name = "Password")]
        [Required(ErrorMessage = "Password is required!")]
        public string Password { get; set; }

        public LoginEnModel(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection");
        }

        public void OnGet()
        {
            // Initialization if needed
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (string.IsNullOrWhiteSpace(Username) || string.IsNullOrWhiteSpace(Password))
            {
                Message = "Username and password are required.";
                return Page();
            }

            try
            {
                using (var conn = new SqlConnection(_connectionString))
                {
                    await conn.OpenAsync();

                    // Check if the user is locked
                    bool isLocked;
                    using (var cmd = new SqlCommand("SELECT locked FROM users WHERE username = @username", conn))
                    {
                        cmd.Parameters.AddWithValue("@username", Username);

                        var result = await cmd.ExecuteScalarAsync();
                        if (result != null)
                        {
                            isLocked = (bool)result;
                        }
                        else
                        {
                            Message = "Invalid username or password.";
                            return Page();
                        }
                    }

                    if (isLocked)
                    {
                        Message = "This account is locked.";
                        return Page();
                    }

                    // Retrieve the PasswordSalt, Key, and IV for the given username
                    string passwordSalt, passwordKey, passwordIv;
                    using (var cmd = new SqlCommand("SELECT passwordsalt, passwordkey, passwordiv FROM users WHERE username = @username", conn))
                    {
                        cmd.Parameters.AddWithValue("@username", Username);

                        using var reader = await cmd.ExecuteReaderAsync();
                        if (await reader.ReadAsync())
                        {
                            passwordSalt = reader.GetString(0);
                            passwordKey = reader.GetString(1);
                            passwordIv = reader.GetString(2);
                        }
                        else
                        {
                            Message = "Invalid username or password.";
                            return Page();
                        }
                    }

                    // Hash the provided password with the retrieved key and IV
                    var hashedPassword = HashPasswordAES(Password, passwordSalt, Convert.FromBase64String(passwordKey), Convert.FromBase64String(passwordIv));

                    // Check if the hashed password matches the stored hash
                    using (var cmd = new SqlCommand("SELECT COUNT(1) FROM users WHERE username = @username AND passwordhash = @passwordhash", conn))
                    {
                        cmd.Parameters.AddWithValue("@username", Username);
                        cmd.Parameters.AddWithValue("@passwordhash", hashedPassword);

                        var count = (int)await cmd.ExecuteScalarAsync();
                        if (count == 1)
                        {
                            // Set session variable
                            HttpContext.Session.SetString("Username", Username);

                            // Redirect to English user portal
                            return RedirectToPage("UserPortalHomeEn");
                        }
                        else
                        {
                            Message = "Invalid username or password.";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Message = "An error occurred: " + ex.Message;
            }

            return Page();
        }

        private string HashPasswordAES(string password, string salt, byte[] key, byte[] iv)
        {
            using (var aes = Aes.Create())
            {
                aes.KeySize = 256;
                aes.BlockSize = 128;
                aes.Key = key;
                aes.IV = iv;
                aes.Mode = CipherMode.CBC;

                using (var encryptor = aes.CreateEncryptor(aes.Key, aes.IV))
                {
                    var passwordBytes = Encoding.UTF8.GetBytes(password);
                    var encryptedPassword = encryptor.TransformFinalBlock(passwordBytes, 0, passwordBytes.Length);
                    return Convert.ToBase64String(encryptedPassword);
                }
            }
        }
    }
} 