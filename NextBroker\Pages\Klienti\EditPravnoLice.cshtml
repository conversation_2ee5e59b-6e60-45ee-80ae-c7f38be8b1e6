@page "{id:long}"
@model NextBroker.Pages.Klienti.EditPravnoLiceModel
@{
    ViewData["Title"] = "Измени правно лице";
}

<div class="container mt-4">
    <h1 class="text-center">@ViewData["Title"]</h1>

    @if (!string.IsNullOrEmpty(TempData["SuccessMessage"]?.ToString()))
    {
        <div class="alert alert-success" id="successMessage">
            @TempData["SuccessMessage"]
        </div>
    }

    @if (!ModelState.IsValid)
    {
        <div class="alert alert-danger">
            <ul>
                @foreach (var error in ModelState.Values.SelectMany(v => v.Errors))
                {
                    <li>@error.ErrorMessage</li>
                }
            </ul>
        </div>
    }

    <form method="post">
        <input type="hidden" asp-for="Id" />

        <!-- Basic Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Основни информации</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Назив</label>
                        <input asp-for="Input.Naziv" class="form-control" />
                        <span class="text-danger" id="naziv-error"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">ЕДБ</label>
                        <input asp-for="Input.EDB" class="form-control" maxlength="15" readonlyonly/>
                        <span class="text-danger" id="edb-error"></span>
                        <span asp-validation-for="Input.EDB" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">МБ</label>
                        <input asp-for="Input.MB" class="form-control" maxlength="15" readonly/>
                        <span class="text-danger" id="mb-error"></span>
                        <span asp-validation-for="Input.MB" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Датум на тековна состојба</label>
                        <input asp-for="Input.DatumNaTekovnaSostojba" type="date" class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Дејност</label>
                        <select asp-for="Input.ListaDejnostiIdDejnost" 
                                asp-items="Model.ListaDejnosti" 
                                class="form-select">
                            <option value="">-- Избери дејност --</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-check">
                            <input type="radio" id="klientSorabotnik" name="clientType" class="form-check-input" 
                                   onchange="handleClientTypeChange(this)" 
                                   @(Model.Input.KlientSorabotnik ? "checked" : "") />
                            <label class="form-check-label" for="klientSorabotnik">Соработник</label>
                            <input type="hidden" asp-for="Input.KlientSorabotnik" id="Hidden_KlientSorabotnik" />
                        </div>
                        <div class="form-check">
                            <input type="radio" id="osiguritel" name="clientType" class="form-check-input" 
                                   onchange="handleClientTypeChange(this)" 
                                   @(Model.Input.Osiguritel ? "checked" : "") />
                            <label class="form-check-label" for="osiguritel">Осигурител</label>
                            <input type="hidden" asp-for="Input.Osiguritel" id="Hidden_Osiguritel" />
                        </div>
                        <div class="form-check">
                            <input type="radio" id="brokerskoDrustvo" name="clientType" class="form-check-input" 
                                   onchange="handleClientTypeChange(this)" 
                                   @(Model.Input.BrokerskoDrustvo ? "checked" : "") />
                            <label class="form-check-label" for="brokerskoDrustvo">Брокерско друштво</label>
                            <input type="hidden" asp-for="Input.BrokerskoDrustvo" id="Hidden_BrokerskoDrustvo" />
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-secondary mt-2" onclick="clearClientTypeSelection()">
                            Исчисти избор
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Address Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Адресни податоци</h5>
            </div>
            <div class="card-body">
                <h6 class="mb-3">Адреса од документ за идентификација</h6>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Општина</label>
                        <select asp-for="Input.ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija" 
                                asp-items="Model.Opstini" 
                                class="form-select">
                            <option value="">-- Избери општина --</option>
                        </select>
                        <span class="text-danger" id="opstina-id-error"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Улица</label>
                        <input asp-for="Input.UlicaOdDokumentZaIdentifikacija" class="form-control" />
                        <span class="text-danger" id="ulica-id-error"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Број</label>
                        <input asp-for="Input.BrojOdDokumentZaIdentifikacija" class="form-control" />
                        <span class="text-danger" id="broj-id-error"></span>
                    </div>
                </div>

                <h6 class="mb-3 mt-4">Адреса за комуникација</h6>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Општина</label>
                        <select asp-for="Input.ListaOpstiniIdOpstinaZaKomunikacija" 
                                asp-items="Model.Opstini" 
                                class="form-select">
                            <option value="">-- Избери општина --</option>
                        </select>
                        <span class="text-danger" id="opstina-kom-error"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Улица</label>
                        <input asp-for="Input.UlicaZaKomunikacija" class="form-control" />
                        <span class="text-danger" id="ulica-kom-error"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Број</label>
                        <input asp-for="Input.BrojZaKomunikacija" class="form-control" />
                        <span class="text-danger" id="broj-kom-error"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Забелешка</label>
                        <input asp-for="Input.Zabeleska" class="form-control" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Контакт информации</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Email</label>
                        <input asp-for="Input.Email" type="email" class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Телефон</label>
                        <input asp-for="Input.Tel" class="form-control" />
                        <span class="text-danger" id="telefon-error"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Веб страна</label>
                        <input asp-for="Input.Webstrana" class="form-control" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Real Owner Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Вистински сопственик</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Вистински сопственик</label>
                        <input asp-for="Input.VistinskiSopstvenik" class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Име</label>
                        <input asp-for="Input.VistinskiSopstvenikIme" class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Презиме</label>
                        <input asp-for="Input.VistinskiSopstvenikPrezime" class="form-control" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Public Function Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Носител на јавна функција</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="form-check">
                            <input asp-for="Input.NositelNaJF" class="form-check-input" type="checkbox" />
                            <label class="form-check-label">Носител на јавна функција</label>
                        </div>
                    </div>
                    <div class="col-md-8 mb-3">
                        <label class="form-label">Основ за носител на јавна функција</label>
                        <input asp-for="Input.OsnovZaNositelNaJF" class="form-control" />
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="form-check">
                            <input asp-for="Input.ZasilenaAnaliza" class="form-check-input" type="checkbox" />
                            <label class="form-check-label">Засилена анализа</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Risk Level Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Ниво на ризик</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Ниво на ризик</label>
                        <select asp-for="Input.NivoaNaRizikIdNivoNaRizik" 
                                asp-items="Model.NivoaNaRizik" 
                                class="form-select">
                            <option value="">-- Избери ниво на ризик --</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contract Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Договор</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Датум на овластување</label>
                        <input asp-for="Input.DatumNaOvlastuvanje" type="date" class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Датум на договор</label>
                        <input asp-for="Input.DaumNaDogovor" type="date" class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Договор важи до</label>
                        <input asp-for="Input.DogovorVaziDo" type="date" class="form-control" />
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Број на договор</label>
                        <input asp-for="Input.BrojNaDogovor" class="form-control" />
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Договор определено/неопределено</label>
                        <select asp-for="Input.DogovorOpredelenoNeopredeleno" class="form-select">
                            <option value="">-- Избери тип --</option>
                            <option value="Определено">Определено</option>
                            <option value="Неопределено">Неопределено</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bank Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Банкарски информации</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Платежна сметка</label>
                        <input asp-for="Input.PlateznaSmetka" class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Банка</label>
                        <select asp-for="Input.SifrarnikBankiIdBanka" 
                                asp-items="Model.Banki" 
                                class="form-select">
                            <option value="">-- Избери банка --</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Рок на плаќање за фактури кон клиентот (денови)</label>
                        <input asp-for="Input.RokNaPlakanjeDenovi" type="number" class="form-control" step="1" min="0" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Life/Non-Life Insurance Card -->
        @if (Model.Input.Osiguritel)
        {
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Живот/Неживот осигурување</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Тип на осигурување</label>
                            <select asp-for="Input.ZivotNezivot" class="form-select">
                                <option value="">-- Избери тип --</option>
                                <option value="Живот">Живот</option>
                                <option value="Неживот">Неживот</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        }

        <!-- License Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Лиценца</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="form-check">
                            <input asp-for="Input.ZadolzitelnoLicenca" class="form-check-input" type="checkbox" />
                            <label class="form-check-label">Задолжително лиценца</label>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Број на решение од АСО за лиценца</label>
                        <input asp-for="Input.BrojNaResenieOdASOZaLicenca" class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Датум на решение од АСО за лиценца</label>
                        <input asp-for="Input.DatumNaResenieOdASOZaLicenca" type="date" class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Датум на одземена лиценца</label>
                        <input asp-for="Input.DatumNaOdzemenaLicenca" type="date" class="form-control" />
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Број на дозвола за вршење осигурително брокерски работи</label>
                        <input asp-for="Input.BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti" class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Датум на дозвола за вршење осигурително брокерски работи</label>
                        <input asp-for="Input.DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti" type="date" class="form-control" />
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mb-4">
            <button type="submit" class="btn btn-primary">Зачувај измени</button>
            <a href="/Klienti/ListaKlienti" class="btn btn-secondary">Назад</a>
        </div>
    </form>
</div>

<!-- File Upload Section -->
<div class="container mt-4">
    @* Debug Information Section - Commented out since upload is working
    @if (!string.IsNullOrEmpty(TempData["DebugInfo"]?.ToString()))
    {
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Debug Information</h5>
            </div>
            <div class="card-body">
                <pre class="debug-info" style="white-space: pre-wrap;">@TempData["DebugInfo"]</pre>
            </div>
        </div>
    }
    *@

    @if (!string.IsNullOrEmpty(TempData["ErrorMessage"]?.ToString()))
    {
        <div class="alert alert-danger">
            @TempData["ErrorMessage"]
        </div>
    }

    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Прикачени документи</h5>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data" asp-page-handler="UploadFile">
                @Html.AntiForgeryToken()
                <input type="hidden" name="klientId" value="@Model.Id" />
                <div class="row mb-3">
                    <div class="col-md-8">
                        <input type="file" class="form-control" name="files" multiple required />
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary">Прикачи документи</button>
                    </div>
                </div>
            </form>

            @if (Model.Files != null && Model.Files.Any())
            {
                <div class="table-responsive mt-3">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Име на документ</th>
                                <th>Датум на прикачување</th>
                                <th>Прикачил</th>
                                <th>Акции</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var file in Model.Files)
                            {
                                <tr>
                                    <td>@file.FileName</td>
                                    <td>@file.DateCreated.ToString("dd.MM.yyyy HH:mm")</td>
                                    <td>@file.UsernameCreated</td>
                                    <td>
                                        <div class="btn-group">
                                            <a asp-page-handler="DownloadFile" asp-route-fileId="@file.Id" 
                                               class="btn btn-sm btn-primary">
                                                <i class="fas fa-download"></i> Преземи
                                            </a>
                                            <form method="post" asp-page-handler="DeleteFile" class="d-inline" 
                                                  onsubmit="return confirm('Дали сте сигурни дека сакате да го избришете овој документ?');">
                                                <input type="hidden" name="fileId" value="@file.Id" />
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i> Избриши
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Remove success message after 10 seconds
        document.addEventListener('DOMContentLoaded', function () {
            var successMessage = document.getElementById('successMessage');
            if (successMessage) {
                setTimeout(function () {
                    successMessage.style.transition = 'opacity 1s';
                    successMessage.style.opacity = '0';
                    setTimeout(function () {
                        successMessage.remove();
                    }, 1000);
                }, 10000);
            }
        });

        function handleClientTypeChange(radio) {
            // Reset all hidden inputs to false
            document.getElementById('Hidden_KlientSorabotnik').value = 'false';
            document.getElementById('Hidden_Osiguritel').value = 'false';
            document.getElementById('Hidden_BrokerskoDrustvo').value = 'false';

            // Set the selected one to true
            if (radio.id === 'klientSorabotnik') {
                document.getElementById('Hidden_KlientSorabotnik').value = 'true';
            } else if (radio.id === 'osiguritel') {
                document.getElementById('Hidden_Osiguritel').value = 'true';
            } else if (radio.id === 'brokerskoDrustvo') {
                document.getElementById('Hidden_BrokerskoDrustvo').value = 'true';
            }
        }

        function clearClientTypeSelection() {
            // Uncheck all radio buttons
            document.querySelectorAll('input[name="clientType"]').forEach(radio => {
                radio.checked = false;
            });

            // Reset all hidden inputs to false
            document.getElementById('Hidden_KlientSorabotnik').value = 'false';
            document.getElementById('Hidden_Osiguritel').value = 'false';
            document.getElementById('Hidden_BrokerskoDrustvo').value = 'false';
        }

        // Phone number validation
        function validatePhoneNumber(phoneNumber) {
            var phonePattern = /^\+?([0-9][\s-.]?){6,}[0-9]$/;
            return phonePattern.test(phoneNumber.replace(/[\s.-]/g, ''));
        }

        // Validate all required fields
        function validatePravnoLiceFields() {
            var isValid = true;
            
            // Validate Naziv
            var nazivField = $('#Input_Naziv');
            var nazivError = $('#naziv-error');
            if (!nazivField.val().trim()) {
                nazivError.text('Назив е задолжително поле');
                isValid = false;
            } else {
                nazivError.text('');
            }

            // Validate EDB
            var edbField = $('#Input_EDB');
            var edbError = $('#edb-error');
            var edbValue = edbField.val().trim();
            
            if (!edbValue) {
                edbError.text('ЕДБ е задолжително поле');
                isValid = false;
            } else if (!/^\d+$/.test(edbValue)) {
                edbError.text('ЕДБ може да содржи само броеви');
                isValid = false;
            } else {
                edbError.text('');
            }

            // Validate MB
            var mbField = $('#Input_MB');
            var mbError = $('#mb-error');
            var mbValue = mbField.val().trim();
            
            if (!mbValue) {
                mbError.text('МБ е задолжително поле');
                isValid = false;
            } else if (!/^\d+$/.test(mbValue)) {
                mbError.text('МБ може да содржи само броеви');
                isValid = false;
            } else {
                mbError.text('');
            }

            // Validate Address from ID
            var opstinaIdField = $('#Input_ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija');
            var opstinaIdError = $('#opstina-id-error');
            if (!opstinaIdField.val()) {
                opstinaIdError.text('Општина е задолжително поле');
                isValid = false;
            } else {
                opstinaIdError.text('');
            }

            var ulicaIdField = $('#Input_UlicaOdDokumentZaIdentifikacija');
            var ulicaIdError = $('#ulica-id-error');
            if (!ulicaIdField.val().trim()) {
                ulicaIdError.text('Улица е задолжително поле');
                isValid = false;
            } else {
                ulicaIdError.text('');
            }

            var brojIdField = $('#Input_BrojOdDokumentZaIdentifikacija');
            var brojIdError = $('#broj-id-error');
            if (!brojIdField.val().trim()) {
                brojIdError.text('Број е задолжително поле');
                isValid = false;
            } else {
                brojIdError.text('');
            }

            // Validate Communication Address
            var opstinaKomField = $('#Input_ListaOpstiniIdOpstinaZaKomunikacija');
            var opstinaKomError = $('#opstina-kom-error');
            if (!opstinaKomField.val()) {
                opstinaKomError.text('Општина е задолжително поле');
                isValid = false;
            } else {
                opstinaKomError.text('');
            }

            var ulicaKomField = $('#Input_UlicaZaKomunikacija');
            var ulicaKomError = $('#ulica-kom-error');
            if (!ulicaKomField.val().trim()) {
                ulicaKomError.text('Улица е задолжително поле');
                isValid = false;
            } else {
                ulicaKomError.text('');
            }

            var brojKomField = $('#Input_BrojZaKomunikacija');
            var brojKomError = $('#broj-kom-error');
            if (!brojKomField.val().trim()) {
                brojKomError.text('Број е задолжително поле');
                isValid = false;
            } else {
                brojKomError.text('');
            }

            // Validate Phone
            var telField = $('#Input_Tel');
            var telError = $('#telefon-error');
            var phoneValue = telField.val().trim();
            
            if (!phoneValue) {
                telError.text('Телефон е задолжително поле');
                isValid = false;
            } else if (!validatePhoneNumber(phoneValue)) {
                telError.text('Внесете валиден телефонски број');
                isValid = false;
            } else {
                telError.text('');
            }
            
            return isValid;
        }

        // Allow only numbers in EDB and MB fields
        $('#Input_EDB, #Input_MB').on('keypress', function(e) {
            if (e.which < 48 || e.which > 57) {
                e.preventDefault();
            }
        }).on('paste', function(e) {
            var pastedData = e.originalEvent.clipboardData.getData('text');
            if (!/^\d*$/.test(pastedData)) {
                e.preventDefault();
            }
        });

        // Add validation on input change
        $('#Input_Naziv, #Input_EDB, #Input_MB, ' +
        '#Input_ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija, #Input_UlicaOdDokumentZaIdentifikacija, ' +
        '#Input_BrojOdDokumentZaIdentifikacija, #Input_ListaOpstiniIdOpstinaZaKomunikacija, ' +
        '#Input_UlicaZaKomunikacija, #Input_BrojZaKomunikacija, #Input_Tel').on('input change', function() {
            validatePravnoLiceFields();
        });

        // Form submission validation
        $('form').on('submit', function(e) {
            if (!validatePravnoLiceFields()) {
                e.preventDefault();
            }
        });
    </script>
} 