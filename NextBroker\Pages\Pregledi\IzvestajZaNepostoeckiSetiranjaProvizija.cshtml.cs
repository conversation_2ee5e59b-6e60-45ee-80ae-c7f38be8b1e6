using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Data.SqlClient;
using RazorPortal.Services;

namespace NextBroker.Pages.Pregledi
{
    public class IzvestajZaNepostoeckiSetiranjaProvizijaModel : SecurePageModel
    {
        public List<PolisaBezProvizija> <PERSON>isi { get; set; } = new List<PolisaBezProvizija>();
        public bool IsDataLoaded { get; set; } = false;

        public IzvestajZaNepostoeckiSetiranjaProvizijaModel(IConfiguration configuration)
            : base(configuration)
        {
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("IzvestajZaNepostoeckiSetiranjaProvizija"))
            {
                return RedirectToAccessDenied();
            }

            await LoadData();
            return Page();
        }

        private async Task LoadData()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    select
                    p.Broj<PERSON>aPolisa,
                    osig.Naziv as [Osiguritel],
                    klas.KlasaIme as [Klasa],
                    prod.Ime as [Produkt],
                    case when p.Storno = 1 then 'Сторнирана'
                    else 'Активна'
                    end as [StatusPolisa]
                    from polisi p
                    left join klienti osig on p.KlientiIdOsiguritel = osig.id
                    left join KlasiOsiguruvanje klas on p.KlasiOsiguruvanjeIdKlasa = klas.Id
                    left join Produkti prod on p.ProduktiIdProizvod = prod.Id
                    where dbo.VratiDaliPolisaImaSetiranjeZaBrokerskaProvizija(p.id) = 0
                    and p.KoregiranaStapkaNaProvizija is null
                    ORDER BY p.BrojNaPolisa", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        Polisi.Add(new PolisaBezProvizija
                        {
                            BrojNaPolisa = reader["BrojNaPolisa"]?.ToString() ?? "",
                            Osiguritel = reader["Osiguritel"]?.ToString() ?? "",
                            Klasa = reader["Klasa"]?.ToString() ?? "",
                            Produkt = reader["Produkt"]?.ToString() ?? "",
                            StatusPolisa = reader["StatusPolisa"]?.ToString() ?? ""
                        });
                    }
                }
            }
            IsDataLoaded = true;
        }
    }

    public class PolisaBezProvizija
    {
        public string BrojNaPolisa { get; set; } = "";
        public string Osiguritel { get; set; } = "";
        public string Klasa { get; set; } = "";
        public string Produkt { get; set; } = "";
        public string StatusPolisa { get; set; } = "";
    }
}