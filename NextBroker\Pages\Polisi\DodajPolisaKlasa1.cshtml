@page
@model NextBroker.Pages.Polisi.DodajPolisaKlasa1Model
@{
    ViewData["Title"] = "Додај полиса Класа 1 - Незгода";
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container-fluid mt-4 px-4">
    <!-- Top Success Message -->
    <div id="successMessage" class="alert alert-success alert-dismissible fade" role="alert" style="display: none;">
        <strong>Успешно!</strong> Полисата е успешно зачувана.
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <!-- Add after the success message divs -->
    <div id="errorMessage" class="alert alert-danger alert-dismissible fade" role="alert" style="display: none;">
        <strong>Грешка!</strong>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <!-- Add before the bottom success message -->
    <div id="bottomErrorMessage" class="alert alert-danger alert-dismissible fade" role="alert" style="display: none;">
        <strong>Грешка!</strong>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <form method="post" id="polisaForm" class="dodaj-polisa-form">
        @Html.AntiForgeryToken()
        
        <!-- Add validation summary -->
        <div asp-validation-summary="All" class="text-danger"></div>

        <!-- Basic Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Основни информации</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Осигурител</label>
                        <select asp-for="Input.KlientiIdOsiguritel" 
                                asp-items="Model.Osiguriteli" 
                                class="form-select">
                            <option value="">-- Избери осигурител --</option>
                        </select>
                        <span asp-validation-for="Input.KlientiIdOsiguritel" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Класа на осигурување</label>
                        <select asp-for="Input.KlasiOsiguruvanjeIdKlasa" 
                                asp-items="Model.KlasiOsiguruvanje" 
                                class="form-select">
                            <option value="">-- Избери класа --</option>
                        </select>
                        <span asp-validation-for="Input.KlasiOsiguruvanjeIdKlasa" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Продукт</label>
                        <select asp-for="Input.ProduktiIdProizvod" 
                                asp-items="Model.Produkti" 
                                class="form-select">
                            <option value="">-- Избери продукт --</option>
                        </select>
                        <span asp-validation-for="Input.ProduktiIdProizvod" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.BrojNaPolisa" class="form-label">Број на полиса</label>
                        <input asp-for="Input.BrojNaPolisa" class="form-control" type="text" required />
                        <span asp-validation-for="Input.BrojNaPolisa" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" asp-for="Input.Kolektivna" id="kolektivna" />
                            <label class="form-check-label" asp-for="Input.Kolektivna">Колективна</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" asp-for="Input.KolektivnaNeodredenBrOsigurenici" id="kolektivnaNeodredenBr" />
                            <label class="form-check-label" asp-for="Input.KolektivnaNeodredenBrOsigurenici">Неодреден број осигуреници</label>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Договорувач (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" 
                                    data-target="dogovoruvac" 
                                    style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="dogovoruvacMBSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                            <input type="hidden" asp-for="Input.KlientiIdDogovoruvac" id="KlientiIdDogovoruvac" />
                        </div>
                        <div id="dogovoruvacSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Осигуреник (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" 
                                    data-target="osigurenik" 
                                    style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="osigurenikMBSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                            <input type="hidden" asp-for="Input.KlientiIdOsigurenik" id="KlientiIdOsigurenik" />
                        </div>
                        <div id="osigurenikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Соработник/вработен (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" 
                                    data-target="sorabotnik" 
                                    style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="sorabotnikMBSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                            <input type="hidden" asp-for="Input.KlientiIdSorabotnik" id="KlientiIdSorabotnik" />
                        </div>
                        <div id="sorabotnikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-check mb-3">
                            <input type="checkbox" 
                                   class="form-check-input faktoring-checkbox" 
                                   asp-for="Input.Faktoring" 
                                   data-val="true"
                                   data-val-required="false" />
                            <label class="form-check-label" asp-for="Input.Faktoring">Факторинг</label>
                        </div>
                        <div class="form-check mb-3" style="display: none;">
                            <input type="checkbox" class="form-check-input" asp-for="Input.GeneriranaFakturaIzlezna" id="generiranaFakturaIzlezna">
                            <label class="form-check-label" asp-for="Input.GeneriranaFakturaIzlezna">Генерирана излезна фактура</label>
                        </div>
                        <!-- Commented out Сторно field
                        <div class="form-check mb-3">
                            <input type="checkbox" class="form-check-input" asp-for="Input.Storno" id="storno">
                            <label class="form-check-label" asp-for="Input.Storno">Сторно</label>
                        </div>
                        <div id="pricinaZaStornoContainer" class="mb-3" style="display: none;">
                            <label asp-for="Input.SifrarnikPricinaZaStornoId" class="form-label">Причина за сторно</label>
                            <select asp-for="Input.SifrarnikPricinaZaStornoId" 
                                    asp-items="Model.PriciniZaStorno" 
                                    class="form-select">
                                <option value="">-- Избери причина за сторно --</option>
                            </select>
                            <span asp-validation-for="Input.SifrarnikPricinaZaStornoId" class="text-danger"></span>
                        </div>
                        -->
                        <!-- Add hidden fields to ensure values are still submitted -->
                        <div style="display: none;">
                            <input asp-for="Input.Storno" type="hidden" value="false" />
                            <input asp-for="Input.SifrarnikPricinaZaStornoId" type="hidden" />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.DatumVaziOd" class="form-label"></label>
                        <input asp-for="Input.DatumVaziOd" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.DatumVaziOd" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.DatumVaziDo" class="form-label"></label>
                        <input asp-for="Input.DatumVaziDo" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.DatumVaziDo" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.DatumNaIzdavanje" class="form-label"></label>
                        <input asp-for="Input.DatumNaIzdavanje" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.DatumNaIzdavanje" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.SifrarnikValutiIdValuta" class="form-label">Валута</label>
                        <select asp-for="Input.SifrarnikValutiIdValuta" 
                                asp-items="Model.Valuti" 
                                class="form-select">
                            <option value="">-- Избери валута --</option>
                        </select>
                        <span asp-validation-for="Input.SifrarnikValutiIdValuta" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.SifrarnikNacinNaPlakjanjeId" class="form-label">Начин на плаќање</label>
                        <select asp-for="Input.SifrarnikNacinNaPlakjanjeId" 
                                asp-items="Model.NaciniNaPlakanje" 
                                class="form-select">
                            <option value="">-- Избери начин на плаќање --</option>
                        </select>
                        <span asp-validation-for="Input.SifrarnikNacinNaPlakjanjeId" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <!-- Commented out Тип на плаќање field
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.SifrarnikTipNaPlakanjeId" class="form-label">Тип на плаќање</label>
                        <select asp-for="Input.SifrarnikTipNaPlakanjeId" 
                                asp-items="Model.TipoviNaPlakanje" 
                                class="form-select">
                            <option value="">-- Избери тип на плаќање --</option>
                        </select>
                        <span asp-validation-for="Input.SifrarnikTipNaPlakanjeId" class="text-danger"></span>
                    </div>
                    -->
                    <!-- Commented out Банка field
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.SifrarnikBankiIdBanka" class="form-label">Банка</label>
                        <select asp-for="Input.SifrarnikBankiIdBanka" 
                                asp-items="Model.Banki" 
                                class="form-select">
                            <option value="">-- Избери банка --</option>
                        </select>
                        <span asp-validation-for="Input.SifrarnikBankiIdBanka" class="text-danger"></span>
                    </div>
                    -->
                    <!-- Add hidden fields to ensure values are still submitted -->
                    <div style="display: none;">
                        <input asp-for="Input.SifrarnikTipNaPlakanjeId" type="hidden" />
                        <input asp-for="Input.SifrarnikBankiIdBanka" type="hidden" />
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.TipNaFaktura" class="form-label">Тип на фактура</label>
                        <select asp-for="Input.TipNaFaktura" 
                                asp-items="Model.TipoviNaFaktura" 
                                class="form-select"
                                required>
                            <option value="">-- Избери тип на фактура --</option>
                        </select>
                        <span asp-validation-for="Input.TipNaFaktura" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3" style="display: none;">
                        <label asp-for="Input.BrojNaVleznaFaktura" class="form-label"></label>
                        <input asp-for="Input.BrojNaVleznaFaktura" class="form-control" />
                        <span asp-validation-for="Input.BrojNaVleznaFaktura" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3" style="display: none;">
                        <label asp-for="Input.BrojNaIzleznaFaktura" class="form-label"></label>
                        <input asp-for="Input.BrojNaIzleznaFaktura" class="form-control" />
                        <span asp-validation-for="Input.BrojNaIzleznaFaktura" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3" style="display: none;">
                        <label asp-for="Input.DatumNaVleznaFaktura" class="form-label"></label>
                        <input asp-for="Input.DatumNaVleznaFaktura" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.DatumNaVleznaFaktura" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3" style="display: none;">
                        <label asp-for="Input.DatumNaIzleznaFaktura" class="form-label"></label>
                        <input asp-for="Input.DatumNaIzleznaFaktura" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.DatumNaIzleznaFaktura" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3" style="display: none;">
                        <label asp-for="Input.RokNaPlakanjeIzleznaFaktura" class="form-label"></label>
                        <input asp-for="Input.RokNaPlakanjeIzleznaFaktura" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.RokNaPlakanjeIzleznaFaktura" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3" style="display: none;">
                        <label asp-for="Input.RokNaPlakanjeVleznaFaktura" class="form-label"></label>
                        <input asp-for="Input.RokNaPlakanjeVleznaFaktura" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.RokNaPlakanjeVleznaFaktura" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.SifrarnikValutiIdFranshizaValuta" class="form-label">Валута за франшиза</label>
                        <select asp-for="Input.SifrarnikValutiIdFranshizaValuta" 
                                asp-items="Model.Valuti" 
                                class="form-select">
                            <option value="">-- Изберете валута --</option>
                        </select>
                        <span asp-validation-for="Input.SifrarnikValutiIdFranshizaValuta" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentFranshiza" class="form-label"></label>
                        <input asp-for="Input.ProcentFranshiza" class="form-control" type="number" step="0.01" />
                        <span asp-validation-for="Input.ProcentFranshiza" class="text-danger"></span>
                    </div>
                </div>
                	   <div class="col-md-3 mb-3">
                <label asp-for="Input.FranshizaIznos" class="form-label"></label>
                <div class="input-group">
                    <input asp-for="Input.FranshizaIznos" 
                           class="form-control decimal-input" 
                           type="number" 
                           step="0.0001" />
                </div>
                <span asp-validation-for="Input.FranshizaIznos" class="text-danger"></span>
            </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.KoregiranaStapkaNaProvizija" class="form-label"></label>
                        <input asp-for="Input.KoregiranaStapkaNaProvizija" class="form-control" type="number" step="0.01" />
                        <span asp-validation-for="Input.KoregiranaStapkaNaProvizija" class="text-danger"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add this after the existing fields -->
        <div id="osigureniciKolektivnoContainer" class="card mb-4" style="display: none;">
            <div class="card-header">
                <h5 class="mb-0">Осигуреници колективно</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="osigureniciTable">
                        <thead>
                            <tr>
                                <th>Име *</th>
                                <th>Презиме *</th>
                                <th>ЕМБГ *</th>
                                <th>Број на лична карта</th>
                                <th>Број на пасош</th>
                                <th>Општина *</th>
                                <th>Адреса *</th>
                                <th>Број</th>
                                <th>Телефон *</th>
                                <th>Email</th>
                                <th>Акции</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                    <button type="button" class="btn btn-primary" id="addOsigurenikBtn">
                        <i class="fas fa-plus"></i> Додај осигуреник
                    </button>
                </div>
            </div>
        </div>

        <!-- Add this after your last card section -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Информации незгода</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.ListaDejnostiId" class="form-label">Дејност</label>
                        <select asp-for="Input.ListaDejnostiId" 
                                asp-items="Model.ListaDejnosti" 
                                class="form-select">
                            <option value="">-- Избери дејност --</option>
                        </select>
                        <span asp-validation-for="Input.ListaDejnostiId" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.BrojNaOsigurenici" class="form-label"></label>
                        <input asp-for="Input.BrojNaOsigurenici" class="form-control" id="Input_BrojNaOsigurenici" value="1" />
                        <span asp-validation-for="Input.BrojNaOsigurenici" class="text-danger"></span>
                    </div>
                </div>

                <!-- New decimal fields -->
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.PremijaZaEdnoLice" class="form-label"></label>
                        <input asp-for="Input.PremijaZaEdnoLice" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.PremijaZaEdnoLice" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.VkupnaPremija" class="form-label"></label>
                        <input asp-for="Input.VkupnaPremija" class="form-control decimal-input" type="number" step="0.0001" style="background-color: #e9ecef;" />
                        <span asp-validation-for="Input.VkupnaPremija" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.SmrtOdNesrekjenSlucaj" class="form-label"></label>
                        <input asp-for="Input.SmrtOdNesrekjenSlucaj" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.SmrtOdNesrekjenSlucaj" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.TraenInvaliditet" class="form-label"></label>
                        <input asp-for="Input.TraenInvaliditet" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.TraenInvaliditet" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.TraenInvaliditet100Procenti" class="form-label"></label>
                        <input asp-for="Input.TraenInvaliditet100Procenti" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.TraenInvaliditet100Procenti" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.DnevenNadomest" class="form-label"></label>
                        <input asp-for="Input.DnevenNadomest" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.DnevenNadomest" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.Lekuvanje" class="form-label"></label>
                        <input asp-for="Input.Lekuvanje" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.Lekuvanje" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.TeskiBolesti" class="form-label"></label>
                        <input asp-for="Input.TeskiBolesti" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.TeskiBolesti" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.TrosociZaPogrebZaSmrtOdNezgoda" class="form-label"></label>
                        <input asp-for="Input.TrosociZaPogrebZaSmrtOdNezgoda" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.TrosociZaPogrebZaSmrtOdNezgoda" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.TrosociZaOstavinskaPostapka" class="form-label"></label>
                        <input asp-for="Input.TrosociZaOstavinskaPostapka" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.TrosociZaOstavinskaPostapka" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.TrosociZaOperacijaNezgoda" class="form-label"></label>
                        <input asp-for="Input.TrosociZaOperacijaNezgoda" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.TrosociZaOperacijaNezgoda" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.TrosociZaObrazovanie" class="form-label"></label>
                        <input asp-for="Input.TrosociZaObrazovanie" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.TrosociZaObrazovanie" class="text-danger"></span>
                    </div>
                </div>
            </div>
        </div>


         <!-- Popusti Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Попусти</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentNaPopustZaFakturaVoRok" class="form-label">Процент на попуст за фактура во рок</label>
                        <input asp-for="Input.ProcentNaPopustZaFakturaVoRok" class="form-control decimal-input" type="number" step="0.01" />
                        <span asp-validation-for="Input.ProcentNaPopustZaFakturaVoRok" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.IznosZaPlakjanjeVoRok" class="form-label">Износ за плаќање во рок</label>
                        <input asp-for="Input.IznosZaPlakjanjeVoRok" class="form-control decimal-input" type="number" step="0.0001" readonly style="background-color: #e9ecef;" />
                        <span asp-validation-for="Input.IznosZaPlakjanjeVoRok" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentKomercijalenPopust" class="form-label">Процент комерцијален попуст</label>
                        <input asp-for="Input.ProcentKomercijalenPopust" class="form-control decimal-input" type="number" step="0.01" />
                        <span asp-validation-for="Input.ProcentKomercijalenPopust" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentFinansiski" class="form-label">Процент финансиски</label>
                        <input asp-for="Input.ProcentFinansiski" class="form-control decimal-input" type="number" step="0.01" />
                        <span asp-validation-for="Input.ProcentFinansiski" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.PremijaZaNaplata" class="form-label">Премија за наплата</label>
                        <input asp-for="Input.PremijaZaNaplata" class="form-control decimal-input" type="number" step="0.0001" style="background-color: #e9ecef;" />
                        <span asp-validation-for="Input.PremijaZaNaplata" class="text-danger"></span>
                    </div>
                </div>
            </div>
        </div>


        <!-- Add this after the "Информации незгода" card section -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Сообраќајна</h5>
            </div>
            <div class="card-body">

<h5 class="mb-3">Група 1 - Основни податоци</h5>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.SifrarnikTipNaVozilo" class="form-label">Тип на возило</label>
                        <select asp-for="Input.SifrarnikTipNaVozilo" 
                                asp-items="Model.TipoviVozila" 
                                class="form-select">
                            <option value="">-- Избери тип на возило --</option>
                        </select>
                        <span asp-validation-for="Input.SifrarnikTipNaVozilo" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.RegisterskaOznaka" class="form-label"></label>
                        <input asp-for="Input.RegisterskaOznaka" class="form-control" />
                        <span asp-validation-for="Input.RegisterskaOznaka" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.Marka" class="form-label"></label>
                        <input asp-for="Input.Marka" class="form-control" />
                        <span asp-validation-for="Input.Marka" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.KomercijalnaOznaka" class="form-label"></label>
                        <input asp-for="Input.KomercijalnaOznaka" class="form-control" />
                        <span asp-validation-for="Input.KomercijalnaOznaka" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.Shasija" class="form-label"></label>
                        <input asp-for="Input.Shasija" class="form-control" />
                        <span asp-validation-for="Input.Shasija" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.GodinaNaProizvodstvo" class="form-label"></label>
                        <input asp-for="Input.GodinaNaProizvodstvo" class="form-control" type="number" />
                        <span asp-validation-for="Input.GodinaNaProizvodstvo" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ZafatninaNaMotorotcm3" class="form-label"></label>
                        <input asp-for="Input.ZafatninaNaMotorotcm3" class="form-control" type="number" />
                        <span asp-validation-for="Input.ZafatninaNaMotorotcm3" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.SilinaNaMotorotKW" class="form-label"></label>
                        <input asp-for="Input.SilinaNaMotorotKW" class="form-control" type="number" />
                        <span asp-validation-for="Input.SilinaNaMotorotKW" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.BrojNaSedista" class="form-label"></label>
                        <input asp-for="Input.BrojNaSedista" class="form-control" type="number" />
                        <span asp-validation-for="Input.BrojNaSedista" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.BojaNaVoziloto" class="form-label"></label>
                        <input asp-for="Input.BojaNaVoziloto" class="form-control" />
                        <span asp-validation-for="Input.BojaNaVoziloto" class="text-danger"></span>
                    </div>
                </div>

<h5 class="mb-3">Група 2 - Дополнителни податоци</h5>

                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.NosivostKG" class="form-label"></label>
                        <input asp-for="Input.NosivostKG" class="form-control" type="number" />
                        <span asp-validation-for="Input.NosivostKG" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.DatumNaRegistracija" class="form-label"></label>
                        <input asp-for="Input.DatumNaRegistracija" class="form-control" type="date" />
                        <span asp-validation-for="Input.DatumNaRegistracija" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.BrojNaVpisot" class="form-label"></label>
                        <input asp-for="Input.BrojNaVpisot" class="form-control" />
                        <span asp-validation-for="Input.BrojNaVpisot" class="text-danger"></span>
                    </div>
                </div>

                <!-- Add after the existing fields in the Сообраќајна section -->
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.DatumNaPrvataRegistracija" class="form-label"></label>
                        <input asp-for="Input.DatumNaPrvataRegistracija" class="form-control" type="date" />
                        <span asp-validation-for="Input.DatumNaPrvataRegistracija" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.DatumNaPrvaRegistracijaVoRSM" class="form-label"></label>
                        <input asp-for="Input.DatumNaPrvaRegistracijaVoRSM" class="form-control" />
                        <span asp-validation-for="Input.DatumNaPrvaRegistracijaVoRSM" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.DozvolataJaIzdal" class="form-label"></label>
                        <input asp-for="Input.DozvolataJaIzdal" class="form-control" />
                        <span asp-validation-for="Input.DozvolataJaIzdal" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.OznakaNaOdobrenie" class="form-label"></label>
                        <input asp-for="Input.OznakaNaOdobrenie" class="form-control" />
                        <span asp-validation-for="Input.OznakaNaOdobrenie" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label asp-for="Input.BrojNAEUPotvrdaZaSoobraznost" class="form-label"></label>
                        <input asp-for="Input.BrojNAEUPotvrdaZaSoobraznost" class="form-control" />
                        <span asp-validation-for="Input.BrojNAEUPotvrdaZaSoobraznost" class="text-danger"></span>
                    </div>
                </div>

                <!-- User Information -->
                <h6 class="mb-3 mt-4">Информации за корисникот</h6>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.PrezimeNazivNaKorisnikot" class="form-label"></label>
                        <input asp-for="Input.PrezimeNazivNaKorisnikot" class="form-control" />
                        <span asp-validation-for="Input.PrezimeNazivNaKorisnikot" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.Ime" class="form-label"></label>
                        <input asp-for="Input.Ime" class="form-control" />
                        <span asp-validation-for="Input.Ime" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.EMBNaKorisnikot" class="form-label"></label>
                        <input asp-for="Input.EMBNaKorisnikot" class="form-control" />
                        <span asp-validation-for="Input.EMBNaKorisnikot" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label asp-for="Input.AdresaNaPostojanoZivealiste" class="form-label"></label>
                        <input asp-for="Input.AdresaNaPostojanoZivealiste" class="form-control" />
                        <span asp-validation-for="Input.AdresaNaPostojanoZivealiste" class="text-danger"></span>
                    </div>
                </div>

                <!-- Owner Information -->
                <h6 class="mb-3 mt-4">Информации за сопственикот</h6>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.PrezimeNazivNaSopstvenikot" class="form-label"></label>
                        <input asp-for="Input.PrezimeNazivNaSopstvenikot" class="form-control" />
                        <span asp-validation-for="Input.PrezimeNazivNaSopstvenikot" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.ImeSopstvenik" class="form-label"></label>
                        <input asp-for="Input.ImeSopstvenik" class="form-control" />
                        <span asp-validation-for="Input.ImeSopstvenik" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.EMBNaFizickoLiceEMBNaPravnoLice" class="form-label"></label>
                        <input asp-for="Input.EMBNaFizickoLiceEMBNaPravnoLice" class="form-control" />
                        <span asp-validation-for="Input.EMBNaFizickoLiceEMBNaPravnoLice" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label asp-for="Input.AdresaNaPostojanoZivealisteSediste" class="form-label"></label>
                        <input asp-for="Input.AdresaNaPostojanoZivealisteSediste" class="form-control" />
                        <span asp-validation-for="Input.AdresaNaPostojanoZivealisteSediste" class="text-danger"></span>
                    </div>
                </div>

                <!-- Add after the Owner Information section -->
                <h6 class="mb-3 mt-4">Технички детали за возилото</h6>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.KategorijaIVidNaVoziloto" class="form-label"></label>
                        <input asp-for="Input.KategorijaIVidNaVoziloto" class="form-control" />
                        <span asp-validation-for="Input.KategorijaIVidNaVoziloto" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.OblikINamenaNaKaroserijata" class="form-label"></label>
                        <input asp-for="Input.OblikINamenaNaKaroserijata" class="form-control" />
                        <span asp-validation-for="Input.OblikINamenaNaKaroserijata" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.TipNaMotorot" class="form-label"></label>
                        <input asp-for="Input.TipNaMotorot" class="form-control" />
                        <span asp-validation-for="Input.TipNaMotorot" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.VidNaGorivo" class="form-label"></label>
                        <input asp-for="Input.VidNaGorivo" class="form-control" />
                        <span asp-validation-for="Input.VidNaGorivo" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.BrojNaVrtezi" class="form-label"></label>
                        <input asp-for="Input.BrojNaVrtezi" class="form-control" />
                        <span asp-validation-for="Input.BrojNaVrtezi" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.MaksimalnaBrzinaKM" class="form-label"></label>
                        <input asp-for="Input.MaksimalnaBrzinaKM" class="form-control" />
                        <span asp-validation-for="Input.MaksimalnaBrzinaKM" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.OdnosSilinaMasa" class="form-label"></label>
                        <input asp-for="Input.OdnosSilinaMasa" class="form-control" />
                        <span asp-validation-for="Input.OdnosSilinaMasa" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label asp-for="Input.IdentifikacionenBrojNaMotorot" class="form-label"></label>
                        <input asp-for="Input.IdentifikacionenBrojNaMotorot" class="form-control" />
                        <span asp-validation-for="Input.IdentifikacionenBrojNaMotorot" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.MasaNaVoziloto" class="form-label"></label>
                        <input asp-for="Input.MasaNaVoziloto" class="form-control" />
                        <span asp-validation-for="Input.MasaNaVoziloto" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG" class="form-label"></label>
                        <input asp-for="Input.NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG" class="form-control" />
                        <span asp-validation-for="Input.NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG" class="form-label"></label>
                        <input asp-for="Input.NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG" class="form-control" />
                        <span asp-validation-for="Input.NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG" class="form-label"></label>
                        <input asp-for="Input.NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG" class="form-control" />
                        <span asp-validation-for="Input.NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG" class="text-danger"></span>
                    </div>
                </div>

                <!-- Move the additional technical specifications here -->
                <h6 class="mb-3 mt-4">Дополнителни технички спецификации</h6>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.BrojNaOski" class="form-label"></label>
                        <input asp-for="Input.BrojNaOski" class="form-control" type="number" />
                        <span asp-validation-for="Input.BrojNaOski" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.Dolzhina" class="form-label"></label>
                        <input asp-for="Input.Dolzhina" class="form-control" type="number" />
                        <span asp-validation-for="Input.Dolzhina" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.Visina" class="form-label"></label>
                        <input asp-for="Input.Visina" class="form-control" type="number" />
                        <span asp-validation-for="Input.Visina" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.CO2" class="form-label"></label>
                        <input asp-for="Input.CO2" class="form-control" />
                        <span asp-validation-for="Input.CO2" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label asp-for="Input.RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka" class="form-label"></label>
                        <input asp-for="Input.RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka" class="form-control" />
                        <span asp-validation-for="Input.RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka" class="text-danger"></span>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label asp-for="Input.NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka" class="form-label"></label>
                        <input asp-for="Input.NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka" class="form-control" />
                        <span asp-validation-for="Input.NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label asp-for="Input.NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG" class="form-label"></label>
                        <input asp-for="Input.NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG" class="form-control" />
                        <span asp-validation-for="Input.NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG" class="text-danger"></span>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label asp-for="Input.NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG" class="form-label"></label>
                        <input asp-for="Input.NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG" class="form-control" />
                        <span asp-validation-for="Input.NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.BrojNaMestaZaStoenje" class="form-label"></label>
                        <input asp-for="Input.BrojNaMestaZaStoenje" class="form-control" type="number" />
                        <span asp-validation-for="Input.BrojNaMestaZaStoenje" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.BrojNaMestazaLezenje" class="form-label"></label>
                        <input asp-for="Input.BrojNaMestazaLezenje" class="form-control" type="number" />
                        <span asp-validation-for="Input.BrojNaMestazaLezenje" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.StacionarnaBucavost" class="form-label"></label>
                        <input asp-for="Input.StacionarnaBucavost" class="form-control" />
                        <span asp-validation-for="Input.StacionarnaBucavost" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label asp-for="Input.DozvoleniPnevmaticiINaplatki" class="form-label"></label>
                        <input asp-for="Input.DozvoleniPnevmaticiINaplatki" class="form-control" />
                        <span asp-validation-for="Input.DozvoleniPnevmaticiINaplatki" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label asp-for="Input.NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka" class="form-label"></label>
                        <input asp-for="Input.NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka" class="form-control" />
                        <span asp-validation-for="Input.NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka" class="text-danger"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Save Button and Bottom Success Message -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-save me-2"></i>Зачувај
                </button>
                <!-- Bottom Success Message -->
                <div id="bottomSuccessMessage" class="alert alert-success alert-dismissible fade mt-3" role="alert" style="display: none;">
                    <strong>Успешно!</strong> Полисата е успешно зачувана.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            </div>
        </div>
    </form>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Add this function at the top of your Scripts section
        function openAddClientWindow(type) {
            const width = 1200;
            const height = 800;
            const left = (screen.width - width) / 2;
            const top = (screen.height - height) / 2;

            const clientWindow = window.open(
                '/Klienti/DodajKlient?returnUrl=' + encodeURIComponent(window.location.pathname) + '&type=' + type,
                'DodajKlient',
                `width=${width},height=${height},left=${left},top=${top},location=no,menubar=no,toolbar=no,status=no`
            );

            // Handle message from popup window
            window.addEventListener('message', function(event) {
                if (event.data && event.data.type === 'clientAdded') {
                    // Clear the search field based on type
                    switch(type) {
                        case 'dogovoruvac':
                            $('#dogovoruvacMBSearch').val('');
                            $('#KlientiIdDogovoruvac').val('');
                            $('#dogovoruvacSearchResults').hide();
                            break;
                        case 'osigurenik':
                            $('#osigurenikMBSearch').val('');
                            $('#KlientiIdOsigurenik').val('');
                            $('#osigurenikSearchResults').hide();
                            break;
                        case 'sorabotnik':
                            $('#sorabotnikMBSearch').val('');
                            $('#KlientiIdSorabotnik').val('');
                            $('#sorabotnikSearchResults').hide();
                            break;
                    }
                }
            }, false);
        }

        $(document).ready(function() {
            // Function to update checkboxes and visibility based on selected product's kategorija
            function updateCheckboxesAndVisibility(kategorija) {
                const kolektivnaCheckbox = $('#kolektivna');
                const kolektivnaNeodredenaCheckbox = $('#kolektivnaNeodredenBr');
                const osigurenikRow = $('#osigurenikMBSearch').closest('.row');
                const osigureniciTable = $('#osigureniciKolektivnoContainer');
                const osigurenikInput = $('#osigurenikMBSearch');
                const osigurenikHiddenInput = $('#KlientiIdOsigurenik');
                
                switch(kategorija) {
                    case 'Индивидуално':
                        kolektivnaCheckbox.prop('checked', false);
                        kolektivnaNeodredenaCheckbox.prop('checked', false);
                        osigurenikRow.show();
                        osigureniciTable.hide();
                        $('#osigureniciTable tbody').empty(); // Clear table
                        // Add required validation
                        osigurenikInput.prop('required', true);
                        osigurenikHiddenInput.prop('required', true);
                        osigurenikInput.attr('data-val', 'true');
                        osigurenikInput.attr('data-val-required', 'Осигуреник е задолжително поле');
                        break;
                    case 'Колективно':
                        kolektivnaCheckbox.prop('checked', true);
                        kolektivnaNeodredenaCheckbox.prop('checked', false);
                        osigurenikRow.hide();
                        osigureniciTable.show(); // Show table only for Колективно
                        $('#osigureniciTable tbody').empty(); // Clear table
                        $('#osigurenikMBSearch').val('');
                        $('#KlientiIdOsigurenik').val('');
                        // Remove required validation
                        osigurenikInput.prop('required', false);
                        osigurenikHiddenInput.prop('required', false);
                        osigurenikInput.removeAttr('data-val');
                        osigurenikInput.removeAttr('data-val-required');
                        break;
                    case 'Неодредено':
                        kolektivnaCheckbox.prop('checked', true);
                        kolektivnaNeodredenaCheckbox.prop('checked', true);
                        osigurenikRow.hide();
                        osigureniciTable.hide(); // Hide table
                        $('#osigureniciTable tbody').empty(); // Clear table
                        $('#osigurenikMBSearch').val('');
                        $('#KlientiIdOsigurenik').val('');
                        // Remove required validation
                        osigurenikInput.prop('required', false);
                        osigurenikHiddenInput.prop('required', false);
                        osigurenikInput.removeAttr('data-val');
                        osigurenikInput.removeAttr('data-val-required');
                        break;
                }
            }

            // Handle product dropdown change
            $('#Input_ProduktiIdProizvod').on('change', function() {
                const selectedOption = $(this).find('option:selected');
                const kategorija = selectedOption.closest('optgroup').attr('label');
                if (kategorija) {
                    updateCheckboxesAndVisibility(kategorija);
                } else {
                    // Reset to default state if no product is selected
                    updateCheckboxesAndVisibility('Индивидуално');
                }
            });

            // Add this JavaScript to handle form submission
            $('#polisaForm').submit(function(e) {
                // Check form validation first
                if (!$(this).valid()) {
                    return false;
                }
                
                // Show success message
                $('#successMessage, #bottomSuccessMessage')
                    .show()
                    .addClass('show');
                
                // Disable submit button
                $(this).find('button[type="submit"]').prop('disabled', true);
                
                // Let form submit proceed
                return true;
            });

            // Auto-hide success messages after 3 seconds
            $('.alert-success').on('shown.bs.alert', function () {
                var $alert = $(this);
                setTimeout(function() {
                    $alert.alert('close');
                }, 3000);
            });

            // Search functionality
            function createSearchFunctionality(searchInputId, resultsContainerId, hiddenInputId, searchEndpoint = 'SearchKlienti') {
                let searchTimeout;
                const searchInput = $(`#${searchInputId}`);
                const resultsDiv = $(`#${resultsContainerId}`);

                // Handle input
                searchInput.on('input', function() {
                    clearTimeout(searchTimeout);
                    const searchTerm = $(this).val();
                    
                    if (searchTerm.length < 2) {
                        resultsDiv.hide();
                        return;
                    }

                    searchTimeout = setTimeout(function() {
                        $.ajax({
                            url: `?handler=${searchEndpoint}&mb=${encodeURIComponent(searchTerm)}`,
                            success: function(data) {
                                if (data && data.length > 0) {
                                    let html = '<div class="list-group">';
                                    data.forEach(item => {
                                        let displayText = '';
                                        if (item.tip === 'P') {
                                            displayText = `${item.naziv}`;
                                            let identifiers = [];
                                            if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                            if (item.edb) identifiers.push(`ЕДБ: ${item.edb}`);
                                            if (identifiers.length > 0) {
                                                displayText += ` (${identifiers.join(', ')})`;
                                            }
                                        } else {
                                            displayText = `${item.ime} ${item.prezime}`;
                                            let identifiers = [];
                                            if (item.embg) identifiers.push(`ЕМБГ: ${item.embg}`);
                                            if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                            if (identifiers.length > 0) {
                                                displayText += ` (${identifiers.join(', ')})`;
                                            }
                                        }
                                        html += `<a href="#" class="list-group-item list-group-item-action" data-id="${item.id}">
                                                  ${displayText}
                                               </a>`;
                                    });
                                    html += '</div>';
                                    resultsDiv.html(html).show();
                                } else {
                                    resultsDiv.html(`
                                        <div class="list-group">
                                            <div class="list-group-item text-center">
                                                <p class="mb-2">Нема пронајдени резултати</p>
                                                <button type="button" class="btn btn-primary" onclick="openAddClientWindow('${
                                                    searchInputId === 'dogovoruvacMBSearch' ? 'dogovoruvac' : 
                                                    searchInputId === 'osigurenikMBSearch' ? 'osigurenik' : 'sorabotnik'
                                                }')">
                                                    <i class="fas fa-plus"></i> Додај клиент
                                                </button>
                                            </div>
                                        </div>
                                    `).show();
                                }
                            }
                        });
                    }, 300);
                });

                // Handle selection
                $(document).on('click', `#${resultsContainerId} .list-group-item`, function(e) {
                    e.preventDefault();
                    const id = $(this).data('id');
                    const displayText = $(this).text();
                    $(`#${searchInputId}`).val(displayText.trim());
                    $(`#${hiddenInputId}`).val(id);
                    $(`#${resultsContainerId}`).hide();
                });

                // Hide results when clicking outside
                $(document).on('click', function(e) {
                    if (!$(e.target).closest(`#${searchInputId}, #${resultsContainerId}`).length) {
                        $(`#${resultsContainerId}`).hide();
                    }
                });
            }

            // Initialize search for all fields
            createSearchFunctionality('dogovoruvacMBSearch', 'dogovoruvacSearchResults', 'KlientiIdDogovoruvac');
            createSearchFunctionality('osigurenikMBSearch', 'osigurenikSearchResults', 'KlientiIdOsigurenik');
            createSearchFunctionality('sorabotnikMBSearch', 'sorabotnikSearchResults', 'KlientiIdSorabotnik', 'SearchSorabotnici');

            // Load opstini for dropdown
            let opstiniList = [];
            
            async function loadOpstini() {
                try {
                    const response = await fetch('?handler=LoadOpstini');
                    opstiniList = await response.json();
                } catch (error) {
                    console.error('Error loading opstini:', error);
                }
            }

            // Create opstini dropdown HTML
            function createOpstiniDropdown() {
                let html = '<select class="form-select" required>';
                html += '<option value="">-- Избери општина --</option>';
                opstiniList.forEach(opstina => {
                    html += `<option value="${opstina.id}">${opstina.opstina}</option>`;
                });
                html += '</select>';
                return html;
            }

            // Add new row to table
            function addNewRow() {
                const row = `
                    <tr>
                        <td><input type="text" class="form-control" required></td>
                        <td><input type="text" class="form-control" required></td>
                        <td><input type="text" class="form-control" required pattern="[0-9]{13}" title="ЕМБГ мора да содржи 13 цифри"></td>
                        <td><input type="text" class="form-control"></td>
                        <td><input type="text" class="form-control"></td>
                        <td>${createOpstiniDropdown()}</td>
                        <td><input type="text" class="form-control" required></td>
                        <td><input type="text" class="form-control"></td>
                        <td><input type="tel" class="form-control" required></td>
                        <td><input type="email" class="form-control"></td>
                        <td>
                            <button type="button" class="btn btn-danger btn-sm deleteRow">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
                $('#osigureniciTable tbody').append(row);
            }

            // Delete row
            $(document).on('click', '.deleteRow', function() {
                $(this).closest('tr').remove();
            });

            // Add row button click
            $('#addOsigurenikBtn').click(addNewRow);

            // Load opstini when page loads
            loadOpstini();

            /* Commented out because the Storno checkbox has been hidden
            // Handle Storno checkbox
            $('#storno').change(function() {
                if ($(this).is(':checked')) {
                    $('#pricinaZaStornoContainer').show();
                } else {
                    $('#pricinaZaStornoContainer').hide();
                    $('#SifrarnikPricinaZaStornoId').val('');
                }
            });
            */

            // Prevent user from changing checkbox states
            $('#kolektivna, #kolektivnaNeodredenBr').on('click', function(e) {
                e.preventDefault();
                return false;
            });

            // Make the checkboxes look slightly different to indicate they're read-only
            $('#kolektivna, #kolektivnaNeodredenBr').css('cursor', 'not-allowed');
            $('#kolektivna, #kolektivnaNeodredenBr').closest('label').css('cursor', 'not-allowed');

            // Remove any required attributes and validation for these fields
            $('#Input_BrojNaVleznaFaktura, #Input_BrojNaIzleznaFaktura')
                .removeAttr('required')
                .removeAttr('data-val-required');

            // Function to calculate VkupnaPremija
            function calculateVkupnaPremija() {
                console.log("Calculating total premium...");
                
                // Get base values
                const brojNaOsigurenici = parseFloat($('#Input_BrojNaOsigurenici').val()) || 0;
                const premijaZaEdnoLice = parseFloat($('#Input_PremijaZaEdnoLice').val()) || 0;
                
                // Calculate base premium
                let vkupnaPremija = brojNaOsigurenici * premijaZaEdnoLice;
                
                console.log("Total premium calculated:", vkupnaPremija);

                // Update the VkupnaPremija field without making it readonly
                $('#Input_VkupnaPremija').val(vkupnaPremija.toFixed(4));

                // Trigger calculation of IznosZaPlakjanjeVoRok
                calculateIznosZaPlakjanjeVoRok();
            }

            // Function to calculate IznosZaPlakjanjeVoRok
            function calculateIznosZaPlakjanjeVoRok() {
                let vkupnaPremija = parseFloat($('#Input_VkupnaPremija').val()) || 0;
                let procentPopust = parseFloat($('#Input_ProcentNaPopustZaFakturaVoRok').val()) || 0;

                let popustAmount = vkupnaPremija * (procentPopust / 100);
                let iznos = vkupnaPremija - popustAmount;
                
                console.log("IznosZaPlakjanjeVoRok calculated:", iznos);
                $('#Input_IznosZaPlakjanjeVoRok').val(iznos.toFixed(4));

                // Trigger calculation of PremijaZaNaplata
                calculatePremijaZaNaplata();
            }

            // Function to calculate PremijaZaNaplata
            function calculatePremijaZaNaplata() {
                let iznosZaPlakjanjeVoRok = parseFloat($('#Input_IznosZaPlakjanjeVoRok').val()) || 0;
                let procentKomercijalen = parseFloat($('#Input_ProcentKomercijalenPopust').val()) || 0;
                let procentFinansiski = parseFloat($('#Input_ProcentFinansiski').val()) || 0;

                let komercijalenPopust = iznosZaPlakjanjeVoRok * (procentKomercijalen / 100);
                let finansiskiPopust = iznosZaPlakjanjeVoRok * (procentFinansiski / 100);
                
                let premija = iznosZaPlakjanjeVoRok - komercijalenPopust - finansiskiPopust;
                
                console.log("PremijaZaNaplata calculated:", premija);
                $('#Input_PremijaZaNaplata').val(premija.toFixed(4));
            }

            // Add event listeners for premium calculation fields
            const premiumFields = [
                'Input_BrojNaOsigurenici',
                'Input_PremijaZaEdnoLice'
            ];

            // Add event listeners for discount fields
            const discountFields = [
                'Input_ProcentNaPopustZaFakturaVoRok',
                'Input_ProcentKomercijalenPopust',
                'Input_ProcentFinansiski'
            ];

            // Bind event listeners for premium fields
            premiumFields.forEach(fieldId => {
                $(`#${fieldId}`).on('input', function() {
                    calculateVkupnaPremija();
                });
            });

            // Bind event listeners for discount fields
            discountFields.forEach(fieldId => {
                $(`#${fieldId}`).on('input', function() {
                    calculateIznosZaPlakjanjeVoRok();
                });
            });

            // Initial calculation
            calculateVkupnaPremija();

            // Add this to your existing updateBrojNaOsigurenici function
            function updateBrojNaOsigurenici() {
                var produktKategorija = $('#Input_ProduktiIdProizvod option:selected').closest('optgroup').attr('label');
                var $brojNaOsigurenici = $('#Input_BrojNaOsigurenici');
                var $brojNaOsigureniciRow = $brojNaOsigurenici.closest('.mb-3');
                var $premijaZaEdnoLice = $('#Input_PremijaZaEdnoLice');
                var $premijaZaEdnoLiceRow = $premijaZaEdnoLice.closest('.mb-3');

                // Set default value to 1 if empty or not set
                if (!$brojNaOsigurenici.val()) {
                    $brojNaOsigurenici.val(1);
                }

                if (produktKategorija === 'Индивидуално') {
                    $brojNaOsigurenici.val(1);
                    $brojNaOsigurenici.prop('readonly', true);
                    $brojNaOsigurenici.css('background-color', '#e9ecef');
                    $brojNaOsigureniciRow.show();
                    $premijaZaEdnoLiceRow.show();
                } else if (produktKategorija === 'Колективно') {
                    var rowCount = $('#osigureniciTable tbody tr').length;
                    $brojNaOsigurenici.val(rowCount > 0 ? rowCount : 1);
                    $brojNaOsigurenici.prop('readonly', false);
                    $brojNaOsigurenici.css('background-color', '');
                    $brojNaOsigureniciRow.show();
                    $premijaZaEdnoLiceRow.show();
                } else if (produktKategorija === 'Неодредено') {
                    $brojNaOsigureniciRow.hide();
                    $premijaZaEdnoLiceRow.hide();
                } else {
                    $brojNaOsigurenici.prop('readonly', false);
                    $brojNaOsigurenici.css('background-color', '');
                    $brojNaOsigureniciRow.show();
                    $premijaZaEdnoLiceRow.show();
                }

                // Calculate VkupnaPremija after updating BrojNaOsigurenici
                calculateVkupnaPremija();
            }

            // Ensure the field has a value on page load
            $('#Input_BrojNaOsigurenici').val(1);
            
            // Call updateBrojNaOsigurenici on page load
            updateBrojNaOsigurenici();

            // Update when produkt changes
            $('#Input_ProduktiIdProizvod').on('change', updateBrojNaOsigurenici);

            // Update when rows are added/removed in kolektivno table
            $('#addOsigurenikBtn').on('click', function() {
                setTimeout(updateBrojNaOsigurenici, 100);
            });

            $(document).on('click', '.deleteRow', function() {
                setTimeout(updateBrojNaOsigurenici, 100);
            });

            // Function to toggle Сообраќајна section visibility
            function toggleSoobrakjajnaSection() {
                var selectedProduktId = $('#Input_ProduktiIdProizvod').val();
                var $soobrakjajnaSection = $('.card:has(.card-header:contains("Сообраќајна"))');
                
                if (selectedProduktId === '11' || selectedProduktId === '12') {
                    $soobrakjajnaSection.show();
                } else {
                    $soobrakjajnaSection.hide();
                }
            }

            // Call the function when the page loads
            toggleSoobrakjajnaSection();

            // Call the function when the Produkt dropdown changes
            $('#Input_ProduktiIdProizvod').change(function() {
                toggleSoobrakjajnaSection();
            });

            // Remove all validation attributes from soobrakajna fields
            $('#Input_RegisterskaOznaka, #Input_Marka, #Input_KomercijalnaOznaka, #Input_Shasija, \
               #Input_GodinaNaProizvodstvo, #Input_ZafatninaNaMotorotcm3, #Input_SilinaNaMotorotKW, \
               #Input_BrojNaSedista, #Input_BojaNaVoziloto, #Input_NosivostKG, #Input_DatumNaRegistracija, \
               #Input_BrojNaVpisot, #Input_DatumNaPrvataRegistracija, #Input_PrezimeNazivNaKorisnikot, \
               #Input_Ime, #Input_AdresaNaPostojanoZivealiste, #Input_EMBNaKorisnikot, \
               #Input_DatumNaPrvaRegistracijaVoRSM, #Input_DozvolataJaIzdal, #Input_OznakaNaOdobrenie, \
               #Input_BrojNAEUPotvrdaZaSoobraznost, #Input_PrezimeNazivNaSopstvenikot, #Input_ImeSopstvenik, \
               #Input_AdresaNaPostojanoZivealisteSediste, #Input_EMBNaFizickoLiceEMBNaPravnoLice, \
               #Input_KategorijaIVidNaVoziloto, #Input_OblikINamenaNaKaroserijata, #Input_TipNaMotorot, \
               #Input_VidNaGorivo, #Input_BrojNaVrtezi, #Input_IdentifikacionenBrojNaMotorot, \
               #Input_MaksimalnaBrzinaKM, #Input_OdnosSilinaMasa, #Input_MasaNaVoziloto, \
               #Input_NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG, \
               #Input_NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG, \
               #Input_NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG, \
               #Input_BrojNaOski, #Input_RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka, \
               #Input_NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka, #Input_Dolzhina, \
               #Input_Visina, #Input_NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG, \
               #Input_NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG, #Input_BrojNaMestaZaStoenje, \
               #Input_DozvoleniPnevmaticiINaplatki, #Input_BrojNaMestazaLezenje, #Input_CO2, \
               #Input_NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka, #Input_StacionarnaBucavost')
                .removeAttr('required')
                .removeAttr('data-val')
                .removeAttr('data-val-required')
                .removeAttr('data-val-number')
                .removeAttr('data-val-date')
                .removeAttr('aria-required');

            // Also disable form validation for these fields
            var form = $("#polisaForm");
            var validator = form.validate();
            if (validator) {
                validator.settings.ignore = ".soobrakajna-section input, .soobrakajna-section select";
            }

            // Handle Faktoring checkbox
            $('#faktoring').change(function() {
                var isChecked = $(this).prop('checked');
                $('#faktoringHidden').val(isChecked);
                console.log('Faktoring changed:', isChecked); // Debug log
            });

            // Before form submission
            $('#polisaForm').submit(function() {
                var faktoringChecked = $('#faktoring').prop('checked');
                $('#faktoringHidden').val(faktoringChecked);
                console.log('Form submitting, Faktoring value:', faktoringChecked); // Debug log
            });

            // Add this to your document.ready function
            $('.faktoring-checkbox').on('change', function() {
                console.log('Faktoring checkbox changed:', this.checked);
            });

            // Add click handlers for clear buttons
            $('.clear-field').on('click', function() {
                const target = $(this).data('target');
                if (target === 'dogovoruvac') {
                    $('#dogovoruvacMBSearch').val('');
                    $('#KlientiIdDogovoruvac').val('');
                    $('#dogovoruvacSearchResults').hide();
                } else if (target === 'osigurenik') {
                    $('#osigurenikMBSearch').val('');
                    $('#KlientiIdOsigurenik').val('');
                    $('#osigurenikSearchResults').hide();
                } else if (target === 'sorabotnik') {
                    $('#sorabotnikMBSearch').val('');
                    $('#KlientiIdSorabotnik').val('');
                    $('#sorabotnikSearchResults').hide();
                }
            });

            // Add policy number check
            $('#Input_BrojNaPolisa').on('change', function() {
                const brojNaPolisa = $(this).val();
                const errorDiv = $('#brojNaPolisa-error');
                if (!errorDiv.length) {
                    $('<div id="brojNaPolisa-error" class="text-danger" style="margin-bottom: 0.5rem;"></div>').insertBefore($(this));
                }
                
                if (brojNaPolisa) {
                    $.get('?handler=CheckBrojNaPolisa', { brojNaPolisa: brojNaPolisa })
                        .done(function(data) {
                            if (data.exists) {
                                $('#brojNaPolisa-error').text('Веќе постои полиса со овој број која не е сторнирана!');
                                $('#Input_BrojNaPolisa').addClass('is-invalid');
                                // Add a custom validation attribute to prevent form submission
                                $('#Input_BrojNaPolisa').attr('data-val-duplicate', 'true');
                            } else {
                                $('#brojNaPolisa-error').text('');
                                $('#Input_BrojNaPolisa').removeClass('is-invalid');
                                $('#Input_BrojNaPolisa').removeAttr('data-val-duplicate');
                            }
                        });
                } else {
                    $('#brojNaPolisa-error').text('');
                    $('#Input_BrojNaPolisa').removeClass('is-invalid');
                    $('#Input_BrojNaPolisa').removeAttr('data-val-duplicate');
                }
            });

            // Add form submission check
            $('form').on('submit', function(e) {
                if ($('#Input_BrojNaPolisa').attr('data-val-duplicate') === 'true') {
                    e.preventDefault();
                    return false;
                }
            });

            // Date validation function
            function validateDates() {
                var vaziOd = $('#Input_DatumVaziOd').val();
                var vaziDo = $('#Input_DatumVaziDo').val();
                
                if (vaziDo && !vaziOd) {
                    $('#Input_DatumVaziOd').addClass('input-validation-error');
                    $('<span class="text-danger field-validation-error">Мора прво да внесете датум важи од</span>')
                        .insertAfter('#Input_DatumVaziOd');
                    return false;
                }
                
                if (vaziOd && vaziDo) {
                    var dateVaziOd = new Date(vaziOd);
                    var dateVaziDo = new Date(vaziDo);
                    
                    if (dateVaziDo < dateVaziOd) {
                        $('#Input_DatumVaziOd').val(vaziDo);
                        return true;
                    }
                }
                
                $('#Input_DatumVaziOd').removeClass('input-validation-error');
                $('#Input_DatumVaziOd').next('.field-validation-error').remove();
                return true;
            }

            // Function to calculate months between two dates
            function calculateMonthsBetween(startDate, endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);
                return (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth()) + 1;
            }

            // Function to filter payment options based on policy duration
            function filterPaymentOptions() {
                const vaziOd = $('#Input_DatumVaziOd').val();
                const vaziDo = $('#Input_DatumVaziDo').val();
                
                if (vaziOd && vaziDo) {
                    const months = calculateMonthsBetween(vaziOd, vaziDo);
                    const paymentSelect = $('#Input_SifrarnikNacinNaPlakjanjeId');
                    
                    // Store original options if not already stored
                    if (!paymentSelect.data('original-options')) {
                        paymentSelect.data('original-options', paymentSelect.find('option').clone());
                    }
                    
                    // Reset options
                    paymentSelect.empty().append('<option value="">-- Избери начин на плаќање --</option>');
                    
                    // Get original options and filter them
                    const originalOptions = paymentSelect.data('original-options');
                    originalOptions.each(function() {
                        const $option = $(this);
                        if ($option.val() === '') return true; // Skip the placeholder option
                        
                        const optionText = $option.text().trim();
                        const rateMatch = optionText.match(/(\d+)\s+рати/);
                        
                        // Always include "Еднократно" option
                        if (optionText === 'Еднократно') {
                            paymentSelect.append($option.clone());
                            return true;
                        }
                        
                        // For rate options, check against policy duration
                        if (rateMatch) {
                            const rates = parseInt(rateMatch[1]);
                            if (rates <= months) {
                                paymentSelect.append($option.clone());
                            }
                        }
                    });
                }
            }

            // Add event listeners for date fields
            $('#Input_DatumVaziOd, #Input_DatumVaziDo').on('change', function() {
                validateDates();
                filterPaymentOptions();  // Call filterPaymentOptions after validateDates
            });

            // Add form validation
            var form = $('#polisaForm');
            form.data('validator').settings.ignore = "";
            
            var originalValidFunction = form.data('validator').settings.submitHandler;
            form.data('validator').settings.submitHandler = function(form) {
                if (validateDates()) {
                    if (originalValidFunction) {
                        originalValidFunction(form);
                    } else {
                        form.submit();
                    }
                }
                return false;
            };

            // Call updateBrojNaOsigurenici on initial load
            updateBrojNaOsigurenici();
            
            // Make sure the produkt change event is properly bound
            $('#Input_ProduktiIdProizvod').on('change', function() {
                updateBrojNaOsigurenici();
            });
        });
    </script>
} 