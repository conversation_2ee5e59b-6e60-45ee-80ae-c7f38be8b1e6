@page
@model NextBroker.Pages.AnalizaNaKlient.AnalizaNaKlientModel
@{
    ViewData["Title"] = "Анализа на клиент";
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

@if (!string.IsNullOrEmpty(TempData["SuccessMessage"]?.ToString()))
{
    <div class="alert alert-success" style="margin: 20px;">
        @TempData["SuccessMessage"]
    </div>
}

<div class="container mt-4">
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Анализа на клиент</h5>
        </div>
        <div class="card-body">
            <form method="post">
            <div class="row mb-3">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header cursor-pointer" id="headingUpravuvanje" data-toggle="collapse" data-target="#collapseUpravuvanje" aria-expanded="false" aria-controls="collapseUpravuvanje">
                                <h5 class="mb-0 d-flex align-items-center justify-content-between">
                                    <span>Управување</span>
                                    <svg class="collapse-arrow" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                                        <path d="M7.646 4.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 5.707l-5.646 5.647a.5.5 0 0 1-.708-.708l6-6z"/>
                                    </svg>
                                </h5>
                            </div>
                            <div id="collapseUpravuvanje" class="collapse" aria-labelledby="headingUpravuvanje">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="card mb-3">
                                                <div class="card-header cursor-pointer" id="headingKombinacija" data-toggle="collapse" data-target="#collapseKombinacija" aria-expanded="false" aria-controls="collapseKombinacija">
                                                    <h5 class="mb-0 d-flex align-items-center justify-content-between">
                                                        <span>Додај Ризични Покриености</span>
                                                        <svg class="collapse-arrow" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                                                            <path d="M7.646 4.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 5.707l-5.646 5.647a.5.5 0 0 1-.708-.708l6-6z"/>
                                                        </svg>
                                                    </h5>
                                                </div>
                                                <div id="collapseKombinacija" class="collapse" aria-labelledby="headingKombinacija">
                                                    <div class="card-body">
                                                        <div class="row">
                                                            <div class="col-md-4">
                                                                <div class="form-group">
                                                                    <label>Ризик</label>
                                                                    <select class="form-control form-control-sm select2-rizik" id="rizikSelect">
                                                                        <option value="">-- Изберете ризик --</option>
                                                                        @foreach (var rizik in Model.Rizici)
                                                                        {
                                                                            <option value="@rizik.Value">@rizik.Text</option>
                                                                        }
                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <div class="form-group">
                                                                    <label>Осигурител</label>
                                                                    <select class="form-control form-control-sm" id="osiguritelSelect">
                                                                        <option value="">-- Изберете осигурител --</option>
                                                                        @foreach (var osiguritel in Model.Osiguriteli)
                                                                        {
                                                                            <option value="@osiguritel.Value">@osiguritel.Text</option>
                                                                        }
                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <div class="form-group">
                                                                    <label>Покриено</label>
                                                                    <div class="custom-control custom-checkbox">
                                                                        <input type="checkbox" class="custom-control-input" id="pokrienoCheck">
                                                                        <label class="custom-control-label" for="pokrienoCheck">Покриено</label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-12">
                                                                <div class="form-group">
                                                                    <label>Белешка</label>
                                                                    <textarea class="form-control form-control-sm" id="beleshkaText" rows="2" placeholder="Внесете белешка..."></textarea>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-12">
                                                                <div id="kombinacijaAlert" class="alert alert-success" style="display: none;" role="alert">
                                                                    Комбинацијата е успешно додадена
                                                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                                                        <span aria-hidden="true">&times;</span>
                                                                    </button>
                                                                </div>
                                                                <button type="button" class="btn btn-primary btn-sm" id="addKombinacija">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-plus-circle" viewBox="0 0 16 16">
                                                                        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                                                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                                                    </svg>
                                                                    Додади комбинација
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="card">
                                                <div class="card-header cursor-pointer" id="headingPravnoEkonomski" data-toggle="collapse" data-target="#collapsePravnoEkonomski" aria-expanded="false" aria-controls="collapsePravnoEkonomski">
                                                    <h5 class="mb-0 d-flex align-items-center justify-content-between">
                                                        <span>Додај Податок за правно –економски односи</span>
                                                        <svg class="collapse-arrow" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                                                            <path d="M7.646 4.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 5.707l-5.646 5.647a.5.5 0 0 1-.708-.708l6-6z"/>
                                                        </svg>
                                                    </h5>
                                                </div>
                                                <div id="collapsePravnoEkonomski" class="collapse" aria-labelledby="headingPravnoEkonomski">
                                                    <div class="card-body">
                                                        <div class="row">
                                                            <div class="col-md-12">
                                                                <div class="form-group">
                                                                    <label>Изберете осигурител</label>
                                                                    <select class="form-control form-control-sm select2-osiguritel" id="pravnoEkonomskiOsiguritelSelect">
                                                                        <option value="">-- Изберете осигурител --</option>
                                                                        @foreach (var osiguritel in Model.Osiguriteli)
                                                                        {
                                                                            <option value="@osiguritel.Value">@osiguritel.Text</option>
                                                                        }
                                                                    </select>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="row mt-3">
                                                            <div class="col-md-12">
                                                                <div class="form-group">
                                                                    <label>Податок за правно –економски односи</label>
                                                                    <textarea class="form-control" id="pravnoEkonomskiText" rows="3"></textarea>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="row mt-3">
                                                            <div class="col-md-12">
                                                                <button type="button" class="btn btn-primary" id="savePravnoEkonomski">Зачувај</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Input.KlientiIdAnalizaDogovoruvac">1. Договорувач (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <button class="btn btn-outline-secondary clear-button" type="button" id="clearDogovoruvac">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-eraser" viewBox="0 0 16 16">
                                            <path d="M8.086 2.207a2 2 0 0 1 2.828 0l3.879 3.879a2 2 0 0 1 0 2.828l-5.5 5.5A2 2 0 0 1 7.879 15H5.12a2 2 0 0 1-1.414-.586l-2.5-2.5a2 2 0 0 1 0-2.828l6.879-6.879zm2.121.707a1 1 0 0 0-1.414 0L4.16 7.547l5.293 5.293 4.633-4.633a1 1 0 0 0 0-1.414l-3.879-3.879zM8.746 13.547 3.453 8.254 1.914 9.793a1 1 0 0 0 0 1.414l2.5 2.5a1 1 0 0 0 .707.293H7.88a1 1 0 0 0 .707-.293l.16-.16z"/>
                                        </svg>
                                    </button>
                                </div>
                                <input type="hidden" asp-for="Input.KlientiIdAnalizaDogovoruvac" id="dogovoruvacId" />
                                <input type="text" class="form-control" id="dogovoruvacSearch" placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." autocomplete="off" />
                            </div>
                            <span asp-validation-for="Input.KlientiIdAnalizaDogovoruvac" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Input.Ime">Име</label>
                            <input asp-for="Input.Ime" class="form-control" />
                            <span asp-validation-for="Input.Ime" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Input.Prezime">Презиме</label>
                            <input asp-for="Input.Prezime" class="form-control" />
                            <span asp-validation-for="Input.Prezime" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Input.EMBG">ЕМБГ</label>
                            <input asp-for="Input.EMBG" class="form-control" />
                            <span asp-validation-for="Input.EMBG" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Input.Naziv">Назив</label>
                            <input asp-for="Input.Naziv" class="form-control" />
                            <span asp-validation-for="Input.Naziv" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Input.MB">МБ</label>
                            <input asp-for="Input.MB" class="form-control" />
                            <span asp-validation-for="Input.MB" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Input.EDB">ЕДБ</label>
                            <input asp-for="Input.EDB" class="form-control" />
                            <span asp-validation-for="Input.EDB" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label asp-for="Input.Email" class="form-label">Е-пошта</label>
                        <input asp-for="Input.Email" class="form-control" />
                        <span asp-validation-for="Input.Email" class="text-danger"></span>
                    </div>
                    <div class="col-md-6">
                        <label asp-for="Input.Adresa" class="form-label">Адреса</label>
                        <input asp-for="Input.Adresa" class="form-control" />
                        <span asp-validation-for="Input.Adresa" class="text-danger"></span>
                    </div>
                </div>
                

                <div id="kombinacii-container" style="display: none;">
                    <!-- Hidden container for form data -->
                </div>

                <div class="row">
                    <div class="col-12">
                        <h5 class="mb-3">2.Табела на анализа</h5>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Input.SelectedKlasaId">2.1 Одбери Класа</label>
                                    <select asp-for="Input.SelectedKlasaId" asp-items="Model.Klasi" class="form-control">
                                        <option value="">-- Избери класа --</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Input.SelectedProduktId">2.2 Избери Продукт</label>
                                    <select asp-for="Input.SelectedProduktId" asp-items="Model.Produkti" class="form-control">
                                        <option value="">-- Избери продукт --</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div id="osiguriteli-container">
                        <label asp-for="Input.SelectedProduktId">2.3 Избери Осигурители</label>
                            <div class="row mb-2">
                                @for (int i = 0; i < Model.Input.Osiguriteli.Count; i++)
                                {
                                    var colClass = i < 3 ? "col-md-4" : "col-md-4";
                                    <div class="@colClass osiguritel-col">
                                        <div class="form-group">
                                            <label>Изберете осигурител @(i + 1)</label>
                                            <div class="input-group">
                                                <select asp-for="Input.Osiguriteli[i].Id" asp-items="Model.Osiguriteli" class="form-control form-control-sm osiguritel-select">
                                                    <option value="">-- Изберете --</option>
                                                </select>
                                                @if (i >= 3)
                                                {
                                                    <div class="input-group-append">
                                                        <button type="button" class="btn btn-danger btn-sm remove-osiguritel">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-trash" viewBox="0 0 16 16">
                                                                <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                                                                <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                                                            </svg>
                                                        </button>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                        
                        <button type="button" id="add-osiguritel" class="btn btn-success" @(Model.Input.Osiguriteli.Count >= 5 ? "disabled" : "")>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-plus-circle" viewBox="0 0 16 16">
                                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                            </svg>
                            Додади осигурител
                        </button>
                    </div>
                </div>

                <!-- Insurer-specific fields container -->
                <div id="osiguritel-fields-container" class="row">
                    @for (int i = 0; i < Model.Input.Osiguriteli.Count; i++)
                    {
                        <div class="col-md-6 osiguritel-fields" data-osiguritel-index="@i" style="display: none;">
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h6 class="mb-0">Податоци за осигурител @(i + 1)</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-12">
                                            <label class="form-label">Правно-економски односи со осигурител</label>
                                            <textarea asp-for="Input.Osiguriteli[i].PravnoEkonomskiOdnosiOsiguritel" class="form-control" rows="3"></textarea>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Провизија за предлог понуда</label>
                                            <input asp-for="Input.Osiguriteli[i].PrivizijaZaPredlogPonuda" class="form-control" />
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Премија износ</label>
                                            <input asp-for="Input.Osiguriteli[i].PremijaIznos" class="form-control" type="number" step="0.0001" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <!-- Global RiziciOsiguritel field -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="form-group">
                                    <label class="form-label">2.4 Ризици за предмет на осигурување</label>
                                    <textarea asp-for="Input.RiziciOsiguritel" class="form-control" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Global ProvizijaZaSiteProduktiIOsiguriteli field -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="form-group">
                                    <label class="form-label">2.5 Провизија за сите производи и осигурители</label>
                                    <input type="text" class="form-control" id="provizijaZaSiteProdukti" value="https://inco.com.mk/ProvizijaOsiguriteli" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Move Образложение за предлог here -->
                <div class="row mb-3">
                    <div class="col-md-12">
                        <label asp-for="Input.ObrazlozenieZaPredlog" class="form-label">2.6 Образложение за предлог</label>
                        <textarea asp-for="Input.ObrazlozenieZaPredlog" class="form-control" rows="3"></textarea>
                        <span asp-validation-for="Input.ObrazlozenieZaPredlog" class="text-danger"></span>
                    </div>
                </div>

                <!-- Comparison Section -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">3. Споредба на осигурители</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>3.1 Изберете ризици за споредба</label>
                                    <div class="d-flex align-items-center mb-2">
                                        <button type="button" id="selectAllRisks" class="btn btn-sm btn-outline-primary mr-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-check-all" viewBox="0 0 16 16">
                                                <path d="M8.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L2.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093L8.95 4.992a.252.252 0 0 1 .02-.022zm-.92 5.14.92.92a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 1 0-1.091-1.028L9.477 9.417l-.485-.486-.943 1.179z"/>
                                            </svg>
                                            Избери ги сите
                                        </button>
                                        <button type="button" id="deselectAllRisks" class="btn btn-sm btn-outline-secondary">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-x-circle" viewBox="0 0 16 16">
                                                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                                <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/>
                                            </svg>
                                            Поништи ги сите
                                        </button>
                                    </div>
                                    <!-- Add risk type filter checkboxes -->
                                    <div class="risk-type-filters mb-2">
                                        <div class="custom-control custom-checkbox custom-control-inline">
                                            <input type="checkbox" class="custom-control-input" id="filterRizik" checked>
                                            <label class="custom-control-label" for="filterRizik">Ризик</label>
                                        </div>
                                        <div class="custom-control custom-checkbox custom-control-inline">
                                            <input type="checkbox" class="custom-control-input" id="filterDopolnitelen" checked>
                                            <label class="custom-control-label" for="filterDopolnitelen">Дополнителен ризик</label>
                                        </div>
                                        <div class="custom-control custom-checkbox custom-control-inline">
                                            <input type="checkbox" class="custom-control-input" id="filterIsklucok" checked>
                                            <label class="custom-control-label" for="filterIsklucok">Исклучок</label>
                                        </div>
                                    </div>
                                    <select id="riskComparisonSelect" class="form-control" multiple>
                                        @foreach (var rizik in Model.Rizici)
                                        {
                                            <option value="@rizik.Value" data-type="@rizik.Group.Name">@rizik.Text</option>
                                        }
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div id="comparisonResults" class="table-responsive">
                            <!-- Comparison table will be inserted here -->
                        </div>
                    </div>
                </div>

                <div id="comparisonResults" class="mb-3">
                    <!-- Comparison results will be displayed here -->
                </div>

                <div class="row mb-3">
                    <div class="col-12 text-right">
                        <button type="button" class="btn btn-primary" id="testEmailBtn" style="display: none;">Test Email</button>
                        <button type="button" id="generatePdf" class="btn btn-primary" style="display: none;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-file-pdf" viewBox="0 0 16 16">
                                <path d="M8 4a.5.5 0 0 1 .5.5v3.793l1.146-1.147a.5.5 0 0 1 .708.708l-2 2a.5.5 0 0 1-.708 0l-2-2a.5.5 0 1 1 .708-.708L7.5 8.293V4.5A.5.5 0 0 1 8 4z"/>
                                <path d="M4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H4zm0 1h8a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1z"/>
                            </svg>
                            Генерирај PDF
                        </button>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12 text-center">
                        <button type="submit" class="btn btn-primary" id="zacuvajButton" disabled>Зачувај</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Risk Modal -->
<div class="modal fade" id="addRizikModal" tabindex="-1" aria-labelledby="addRizikModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRizikModalLabel">Додај нов ризик</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="newRizikNaziv">Назив на ризик</label>
                    <input type="text" class="form-control" id="newRizikNaziv" placeholder="Внесете назив на ризик">
                </div>
                <div class="form-group">
                    <label for="newRizikTip">Тип на ризик</label>
                    <select class="form-control" id="newRizikTip" required>
                        <option value="">-- Изберете тип --</option>
                        <option value="Ризик">Ризик</option>
                        <option value="Дополнителен ризик">Дополнителен ризик</option>
                        <option value="Исклучок">Исклучок</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="newRizikProdukt">Продукт</label>
                    <select class="form-control" id="newRizikProdukt" required>
                        <option value="">-- Изберете продукт --</option>
                        @foreach (var produkt in Model.Produkti)
                        {
                            <option value="@produkt.Value">@produkt.Text</option>
                        }
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Откажи</button>
                <button type="button" class="btn btn-primary" id="saveNewRizik">Зачувај</button>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
    <style>
        .ui-autocomplete {
            max-height: 200px;
            overflow-y: auto;
            overflow-x: hidden;
            border: 1px solid #ccc;
            background: white;
            z-index: 1050;
            padding: 0;
            margin: 0;
            list-style: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.15);
        }
        .ui-menu-item {
            padding: 0;
            margin: 0;
            border: none;
        }
        .ui-menu-item div {
            padding: 8px 12px;
            cursor: pointer;
            border: none;
            font-size: 14px;
            line-height: 1.4;
        }
        .ui-menu-item div:hover,
        .ui-menu-item div.ui-state-active {
            background-color: #0d6efd;
            color: white;
        }
        .clear-button {
            border: 1px solid #ced4da;
            border-right: none;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            padding: 0.375rem 0.75rem;
            color: #6c757d;
            background-color: #fff;
            transition: all 0.15s ease-in-out;
            height: 38px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
        }
        .clear-button:hover {
            color: #fff;
            background-color: #6c757d;
            border-color: #6c757d;
        }
        .clear-button:focus {
            box-shadow: none;
            outline: none;
        }
        .input-group > .form-control {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            height: 38px;
            font-size: 14px;
        }
        .input-group-prepend {
            margin-right: -1px;
        }
        .form-group label {
            font-weight: normal;
            margin-bottom: 0.5rem;
            color: #212529;
        }
        .form-control::placeholder {
            color: #6c757d;
            opacity: 0.8;
        }
        .remove-osiguritel {
            padding: 0.25rem 0.5rem;
        }
        #add-osiguritel {
            margin-top: 10px;
        }
        .input-group-append {
            margin-left: -1px;
        }
        .input-group-append .btn {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }
        .osiguritel-col {
            padding: 0 15px;
            width: 100%;
        }
        .osiguritel-col .form-group {
            margin-bottom: 1rem;
        }
        .osiguritel-col label {
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
            display: block;
        }
        .osiguritel-col .input-group {
            width: 100%;
        }
        .osiguritel-col select {
            width: 100%;
        }
        #osiguriteli-container .row {
            margin: 0 -15px;
            display: flex;
            flex-wrap: wrap;
        }
        #osiguriteli-container .row:before,
        #osiguriteli-container .row:after {
            display: none;
        }
        .input-group-append .btn {
            height: 31px;
            padding: 0.25rem 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        /* Media query for desktop view */
        @@media screen and (min-width: 768px) {
            .col-md-4 {
                flex: 0 0 33.333333%;
                max-width: 33.333333%;
            }
            .osiguritel-col {
                flex: 0 0 33.333333%;
                max-width: 33.333333%;
            }
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0,0,0,.125);
        }
        .card-header .btn-link {
            color: #212529;
            text-decoration: none;
            font-weight: 500;
            padding: 0;
        }
        .card-header .btn-link:hover {
            color: #0d6efd;
            text-decoration: none;
        }
        .card-header .btn-link:focus {
            box-shadow: none;
        }
        .card {
            margin-bottom: 1rem;
        }
        .custom-control {
            padding-top: 0.5rem;
        }
        .cursor-pointer {
            cursor: pointer;
        }
        
        .collapse-arrow {
            transition: transform 0.3s ease;
            color: #6c757d;
        }
        
        .collapsed .collapse-arrow {
            transform: rotate(-180deg);
        }
        
        #headingKombinacija:hover .collapse-arrow {
            color: #495057;
        }
        
        .card-header {
            padding: 0.75rem 1.25rem;
            transition: background-color 0.2s ease;
        }
        
        .card-header h5 {
            color: #212529;
            margin: 0;
        }
        
        .select2-container--default .select2-selection--single {
            height: calc(1.5em + 0.5rem + 2px);
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            line-height: 1.5;
            border-radius: 0.2rem;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 1.5;
            padding-left: 0;
            text-align: center;
        }

        .select2-container--default .select2-selection--single .select2-selection__placeholder {
            text-align: center;
            width: 100%;
            display: inline-block;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: calc(1.5em + 0.5rem + 2px);
        }

        .select2-dropdown {
            border-color: #ced4da;
        }

        .select2-container--default .select2-search--dropdown .select2-search__field {
            border-color: #ced4da;
            padding: 0.25rem 0.5rem;
        }

        .select2-results__option {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        /* Center the text when an option is selected */
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            text-align: center;
        }

        /* Adjust arrow position to maintain layout */
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            right: 5px;
        }

        /* Center the clear button */
        .select2-container--default .select2-selection--single .select2-selection__clear {
            float: none;
            margin-right: 25px;
        }

        .select2-add-risk {
            padding: 8px;
            border-top: 1px solid #e9ecef;
        }

        .select2-add-risk button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            width: 100%;
            padding: 6px;
            color: #0d6efd;
            background: transparent;
            border: none;
            transition: background-color 0.2s;
            cursor: pointer;
        }

        .select2-add-risk button:hover {
            background-color: #f8f9fa;
            text-decoration: none;
        }

        #addRizikModal .modal-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        #addRizikModal .modal-footer {
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }

        #addRizikModal .form-control:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .alert .close {
            color: #6c757d;
            opacity: 0.7;
            text-shadow: none;
        }
        .alert .close:hover {
            color: #495057;
            opacity: 1;
        }
    </style>
}

@section Scripts {
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>

    <script>
        // Function to load pravno-ekonomski odnosi data for an insurer
        function loadPravnoEkonomskiData(insurerId, callback) {
            console.log('Loading data for insurer:', insurerId);
            $.get('?handler=PravnoEkonomskiOdnosi', { insurerId: insurerId })
                .done(function(data) {
                    console.log('Received data:', data);
                    if (callback && typeof callback === 'function') {
                        callback(data);
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('Failed to load data:', error);
                });
        }

        // Function to update all insurer fields in the main form
        function updateAllInsurerFields() {
            $('.osiguritel-select').each(function() {
                const select = $(this);
                const selectedInsurerId = select.val();
                if (selectedInsurerId) {
                    const index = select.closest('.osiguritel-col').index();
                    console.log('Updating fields for insurer at index:', index);
                    
                    // Show the fields container
                    const fieldsContainer = $(`.osiguritel-fields[data-osiguritel-index="${index}"]`);
                    fieldsContainer.show();
                    
                    const pravnoEkonomskiField = fieldsContainer.find('textarea[name$="PravnoEkonomskiOdnosiOsiguritel"]');
                    
                    loadPravnoEkonomskiData(selectedInsurerId, function(data) {
                        console.log('Setting field value with data:', data);
                        if (data && data.pravnoEkonomskiOdnosi) {
                            pravnoEkonomskiField.val(data.pravnoEkonomskiOdnosi);
                            pravnoEkonomskiField.prop('readonly', true);
                        } else {
                            pravnoEkonomskiField.val('');
                            pravnoEkonomskiField.prop('readonly', false);
                        }
                    });
                }
            });
        }

        $(document).ready(function() {
            // Initialize by loading data for all selected insurers
            console.log('Initializing insurer fields');
            updateAllInsurerFields();

            // Handle insurer selection changes in the main form
            $('.osiguritel-select').off('change').on('change', function() {
                const select = $(this);
                const selectedInsurerId = select.val();
                const index = select.closest('.osiguritel-col').index();
                console.log('Insurer select changed:', { selectedInsurerId, index });
                
                const fieldsContainer = $(`.osiguritel-fields[data-osiguritel-index="${index}"]`);
                const pravnoEkonomskiField = fieldsContainer.find('textarea[name$="PravnoEkonomskiOdnosiOsiguritel"]');

                if (selectedInsurerId) {
                    // Show the fields container when an insurer is selected
                    fieldsContainer.show();
                    
                    // Load data for this insurer
                    loadPravnoEkonomskiData(selectedInsurerId, function(data) {
                        console.log('Received data for selected insurer:', data);
                        if (data && data.pravnoEkonomskiOdnosi) {
                            pravnoEkonomskiField.val(data.pravnoEkonomskiOdnosi);
                            pravnoEkonomskiField.prop('readonly', true);
                        } else {
                            pravnoEkonomskiField.val('');
                            pravnoEkonomskiField.prop('readonly', false);
                        }
                    });
                } else {
                    // Hide the fields container when no insurer is selected
                    fieldsContainer.hide();
                    pravnoEkonomskiField.val('');
                }
            });

            // Initial setup - show/hide fields based on initial selection
            $('.osiguritel-select').each(function() {
                const select = $(this);
                const selectedInsurerId = select.val();
                const index = select.closest('.osiguritel-col').index();
                const fieldsContainer = $(`.osiguritel-fields[data-osiguritel-index="${index}"]`);
                
                if (selectedInsurerId) {
                    fieldsContainer.show();
                    // Load initial data
                    loadPravnoEkonomskiData(selectedInsurerId, function(data) {
                        if (data && data.pravnoEkonomskiOdnosi) {
                            const pravnoEkonomskiField = fieldsContainer.find('textarea[name$="PravnoEkonomskiOdnosiOsiguritel"]');
                            pravnoEkonomskiField.val(data.pravnoEkonomskiOdnosi);
                            pravnoEkonomskiField.prop('readonly', true);
                        }
                    });
                } else {
                    fieldsContainer.hide();
                }
            });
        });
    </script>

    <script>
        $(document).ready(function () {
            // Auto-hide success message after 3 seconds
            setTimeout(function() {
                $(".alert-success").fadeOut();
            }, 3000);

            var dogovoruvacSearch = $('#dogovoruvacSearch');
            var dogovoruvacId = $('#dogovoruvacId');
            var clearDogovoruvac = $('#clearDogovoruvac');

            // Function to toggle field visibility based on content
            function toggleFieldVisibility(fieldId, value) {
                var fieldContainer = $(fieldId).closest('.form-group').closest('.col-md-6');
                if (value && value.trim() !== '') {
                    fieldContainer.show();
                } else {
                    fieldContainer.hide();
                }
            }

            if (!$.fn.autocomplete) {
                console.error('jQuery UI autocomplete is not loaded!');
                return;
            }

            // Setup antiforgery token
            var antiforgeryToken = $('input[name="__RequestVerificationToken"]').val();

            dogovoruvacSearch.autocomplete({
                source: function (request, response) {
                    $.ajax({
                        url: '?handler=SearchKlienti',
                        type: 'GET',
                        data: { mb: request.term },
                        headers: {
                            "RequestVerificationToken": antiforgeryToken
                        },
                        success: function (data) {
                            response($.map(data, function (item) {
                                return {
                                    label: item.tip === 'P' ? 
                                        item.naziv : 
                                        item.ime + ' ' + item.prezime + ' (ЕМБГ: ' + item.embg + ')',
                                    value: item.id,
                                    data: item // Store all client data
                                };
                            }));
                        },
                        error: function (xhr, status, error) {
                            console.error('Search failed:', status, error);
                        }
                    });
                },
                minLength: 1,
                select: function (event, ui) {
                    event.preventDefault();
                    dogovoruvacId.val(ui.item.value);
                    dogovoruvacSearch.val(ui.item.label);

                    // Populate form fields with client data
                    $('#Input_Ime').val(ui.item.data.ime);
                    $('#Input_Prezime').val(ui.item.data.prezime);
                    $('#Input_EMBG').val(ui.item.data.embg);
                    $('#Input_MB').val(ui.item.data.mb);
                    $('#Input_EDB').val(ui.item.data.edb);
                    $('#Input_Naziv').val(ui.item.data.naziv);
                    $('#Input_Email').val(ui.item.data.email);

                    // Populate Адреса with UlicaZaKomunikacija (+ BrojZaKomunikacija if available)
                    let adresa = ui.item.data.ulicaZaKomunikacija || '';
                    if (ui.item.data.brojZaKomunikacija) {
                        adresa += ' ' + ui.item.data.brojZaKomunikacija;
                    }
                    $('#Input_Adresa').val(adresa.trim());

                    // Toggle visibility of fields based on content
                    toggleFieldVisibility('#Input_Ime', ui.item.data.ime);
                    toggleFieldVisibility('#Input_Prezime', ui.item.data.prezime);
                    toggleFieldVisibility('#Input_EMBG', ui.item.data.embg);
                    toggleFieldVisibility('#Input_MB', ui.item.data.mb);
                    toggleFieldVisibility('#Input_EDB', ui.item.data.edb);
                    toggleFieldVisibility('#Input_Naziv', ui.item.data.naziv);
                    toggleFieldVisibility('#Input_Email', ui.item.data.email);
                },
                focus: function (event, ui) {
                    event.preventDefault();
                }
            }).data("ui-autocomplete")._renderItem = function (ul, item) {
                return $("<li>")
                    .append("<div>" + item.label + "</div>")
                    .appendTo(ul);
            };

            // Add field visibility logic
            function updateFieldVisibility() {
                var ime = $('#Input_Ime').val();
                var prezime = $('#Input_Prezime').val();
                var naziv = $('#Input_Naziv').val();
                
                if ((ime || prezime) && !naziv) {
                    // Hide company fields if person fields are filled
                    $('#Input_Naziv').closest('.form-group').parent().hide();
                    $('#Input_EDB').closest('.form-group').parent().hide();
                    $('#Input_MB').closest('.form-group').parent().hide();
                } else if (naziv) {
                    // Hide person fields if company name is filled
                    $('#Input_Ime').closest('.form-group').parent().hide();
                    $('#Input_Prezime').closest('.form-group').parent().hide();
                    $('#Input_EMBG').closest('.form-group').parent().hide();
                } else {
                    // Show all fields if nothing is filled
                    $('#Input_Ime').closest('.form-group').parent().show();
                    $('#Input_Prezime').closest('.form-group').parent().show();
                    $('#Input_EMBG').closest('.form-group').parent().show();
                    $('#Input_Naziv').closest('.form-group').parent().show();
                    $('#Input_EDB').closest('.form-group').parent().show();
                    $('#Input_MB').closest('.form-group').parent().show();
                }
            }

            // Monitor changes on relevant fields
            $('#Input_Ime, #Input_Prezime, #Input_Naziv').on('input', function() {
                updateFieldVisibility();
            });

            // Clear button handler
            $('#clearDogovoruvac').on('click', function(e) {
                e.preventDefault();
                dogovoruvacId.val('');
                dogovoruvacSearch.val('');
                
                // Clear all form fields
                $('#Input_Ime').val('');
                $('#Input_Prezime').val('');
                $('#Input_EMBG').val('');
                $('#Input_MB').val('');
                $('#Input_EDB').val('');
                $('#Input_Naziv').val('');
                $('#Input_Email').val('');
                $('#Input_ObrazlozenieZaPredlog').val('');
                
                
                // Clear and make Адреса editable again
                $('#Input_Adresa').val('');
                
                // Show all fields when clearing
                updateFieldVisibility();
                
                dogovoruvacSearch.focus();
            });

            // Initial check
            updateFieldVisibility();

            // Handle adding new insurer field
            $('#add-osiguritel').on('click', function() {
                var container = $('#osiguriteli-container .row');
                var count = container.find('.osiguritel-col').length;
                
                if (count < 5) {
                    var newCol = `
                        <div class="col-md-4 osiguritel-col">
                            <div class="form-group">
                                <label>Изберете осигурител ${count + 1}</label>
                                <div class="input-group">
                                    <select name="Input.Osiguriteli[${count}].Id" class="form-control form-control-sm osiguritel-select">
                                        <option value="">-- Изберете --</option>
                                        @foreach (var item in Model.Osiguriteli)
                                        {
                                            <option value="@item.Value">@item.Text</option>
                                        }
                                    </select>
                                    <div class="input-group-append">
                                        <button type="button" class="btn btn-danger btn-sm remove-osiguritel">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-trash" viewBox="0 0 16 16">
                                                <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                                                <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    container.append(newCol);

                    // Add new insurer fields section
                    var newFields = `
                        <div class="osiguritel-fields" data-osiguritel-index="${count}" style="display: none;">
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h6 class="mb-0">Податоци за осигурител ${count + 1}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-12">
                                            <label class="form-label">Правно-економски односи со осигурител</label>
                                            <textarea name="Input.Osiguriteli[${count}].PravnoEkonomskiOdnosiOsiguritel" id="Input_Osiguriteli_${count}__PravnoEkonomskiOdnosiOsiguritel" class="form-control" rows="3"></textarea>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Провизија за предлог понуда</label>
                                            <input name="Input.Osiguriteli[${count}].PrivizijaZaPredlogPonuda" id="Input_Osiguriteli_${count}__PrivizijaZaPredlogPonuda" class="form-control" />
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Премија износ</label>
                                            <input name="Input.Osiguriteli[${count}].PremijaIznos" id="Input_Osiguriteli_${count}__PremijaIznos" class="form-control" type="number" step="0.0001" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    $('#osiguritel-fields-container').append(newFields);
                    
                    if (count + 1 >= 5) {
                        $('#add-osiguritel').prop('disabled', true);
                    }

                    // Bind change event to the new select
                    container.find('.osiguritel-select').last().on('change', function() {
                        updateComparison();
                        toggleInsurerFields($(this));
                    });

                    // Update comparison if risks are already selected
                    if ($('#riskComparisonSelect').val() && $('#riskComparisonSelect').val().length > 0) {
                        updateComparison();
                    }
                }
            });

            // Handle removing insurer field
            $(document).on('click', '.remove-osiguritel', function() {
                var container = $('#osiguriteli-container .row');
                var count = container.find('.osiguritel-col').length;
                var index = $(this).closest('.osiguritel-col').index();
                
                if (count > 3) {
                    $(this).closest('.osiguritel-col').remove();
                    // Remove corresponding insurer fields
                    $(`.osiguritel-fields[data-osiguritel-index="${index}"]`).remove();
                    $('#add-osiguritel').prop('disabled', false);
                    
                    // Renumber the remaining fields
                    $('.osiguritel-col').each(function(newIndex) {
                        $(this).find('label').text(`Изберете осигурител ${newIndex + 1}`);
                        $(this).find('select').attr('name', `Input.Osiguriteli[${newIndex}].Id`);
                        // Update the data-osiguritel-index attribute
                        $(`.osiguritel-fields[data-osiguritel-index="${newIndex + 1}"]`).attr('data-osiguritel-index', newIndex);
                        // Update field names
                        $(`.osiguritel-fields[data-osiguritel-index="${newIndex}"]`).find('[name^="Input.Osiguriteli"]').each(function() {
                            var name = $(this).attr('name');
                            name = name.replace(/\[\d+\]/, `[${newIndex}]`);
                            $(this).attr('name', name);
                        });
                    });

                    // Update comparison if risks are selected
                    if ($('#riskComparisonSelect').val() && $('#riskComparisonSelect').val().length > 0) {
                        updateComparison();
                    }
                }
            });

            // Function to toggle insurer fields visibility
            function toggleInsurerFields(selectElement) {
                var index = selectElement.closest('.osiguritel-col').index();
                var fieldsContainer = $(`.osiguritel-fields[data-osiguritel-index="${index}"]`);
                
                if (selectElement.val()) {
                    fieldsContainer.show();
                } else {
                    fieldsContainer.hide();
                }
            }

            // Initialize insurer fields visibility
            $('.osiguritel-select').each(function() {
                toggleInsurerFields($(this));
            });

            // Handle insurer selection changes
            $(document).on('change', '.osiguritel-select', function() {
                toggleInsurerFields($(this));
            });

            // Handle adding new combination
            $('#addKombinacija').on('click', function() {
                var rizikId = $('#rizikSelect').val();
                var klientId = $('#osiguritelSelect').val();
                var pokrieno = $('#pokrienoCheck').is(':checked');
                var beleshka = $('#beleshkaText').val();

                if (!rizikId || !klientId) {
                    alert('Ве молиме изберете ризик и клиент');
                    return;
                }

                // First check if combination exists
                $.get('?handler=CheckKombinacija', { rizikId: rizikId, klientId: klientId })
                    .done(function(response) {
                        if (response.exists) {
                            alert('Оваа комбинација веќе постои во системот.');
                            return;
                        }

                        // If combination doesn't exist, proceed with adding it
                        $.post('?handler=AddKombinacija', {
                            rizikId: rizikId,
                            klientId: klientId,
                            pokrieno: pokrieno,
                            beleshka: beleshka,
                            __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                        })
                        .done(function(response) {
                            if (response.success) {
                                // Show success message
                                $('#kombinacijaAlert').fadeIn();
                                setTimeout(function() {
                                    $('#kombinacijaAlert').fadeOut();
                                }, 3000);

                                // Clear the form
                                $('#rizikSelect').val(null).trigger('change');
                                $('#osiguritelSelect').val(null).trigger('change');
                                $('#pokrienoCheck').prop('checked', false);
                                $('#beleshkaText').val('');

                                // If we have multiple insurers selected for comparison, update the comparison
                                var selectedInsurers = $('.osiguritel-col').map(function() {
                                    return $(this).data('insurer-id');
                                }).get();
                                
                                if (selectedInsurers.length > 1) {
                                    updateComparison();
                                }
                            } else {
                                alert('Грешка при зачувување: ' + response.message);
                            }
                        })
                        .fail(function(xhr, status, error) {
                            alert('Грешка при зачувување: ' + error);
                        });
                    })
                    .fail(function(xhr, status, error) {
                        alert('Грешка при проверка на комбинација: ' + error);
                    });
            });

            // Initialize Select2 for the risk comparison dropdown
            $('#riskComparisonSelect').select2({
                placeholder: "Изберете ризици за споредба",
                allowClear: true
            });

            // Store original options for filtering
            var originalOptions = $('#riskComparisonSelect option').clone();

            // Function to filter options based on selected risk types and product
            function filterRiskOptions() {
                var selectedTypes = [];
                if ($('#filterRizik').is(':checked')) selectedTypes.push('Ризик');
                if ($('#filterDopolnitelen').is(':checked')) selectedTypes.push('Дополнителен ризик');
                if ($('#filterIsklucok').is(':checked')) selectedTypes.push('Исклучок');

                var produktId = $('#Input_SelectedProduktId').val();
                
                // First, clear all selections
                $('#riskComparisonSelect').val(null).trigger('change');
                
                // Then, disable and hide all options
                $('#riskComparisonSelect option').each(function() {
                    var option = $(this);
                    var riskType = option.data('type');
                    var riskProduktId = option.data('produkt-id');
                    
                    // Hide if risk type doesn't match selected filters or doesn't belong to selected product
                    if (!selectedTypes.includes(riskType) || (produktId && riskProduktId != produktId)) {
                        option.prop('disabled', true);
                        option.hide();
                    } else {
                        option.prop('disabled', false);
                        option.show();
                    }
                });

                // Refresh Select2
                $('#riskComparisonSelect').trigger('change');
            }

            // Function to update risk options when product changes
            function updateRiskOptionsForProduct(produktId) {
                // Clear current selections
                $('#riskComparisonSelect').val(null).trigger('change');
                
                // Disable all options first
                $('#riskComparisonSelect option').each(function() {
                    $(this).prop('disabled', true).hide();
                });

                if (produktId) {
                    // Fetch risks for the selected product
                    $.get('?handler=RiziciByProdukt', { produktId: produktId })
                        .done(function(rizici) {
                            if (rizici && rizici.length > 0) {
                                // Enable and show only the risks for this product
                                rizici.forEach(function(rizik) {
                                    var option = $('#riskComparisonSelect option[value="' + rizik.id + '"]');
                                    if (option.length) {
                                        var riskType = option.data('type');
                                        // Check if this risk type is selected in the filters
                                        if ($('#filterRizik').is(':checked') && riskType === 'Ризик' ||
                                            $('#filterDopolnitelen').is(':checked') && riskType === 'Дополнителен ризик' ||
                                            $('#filterIsklucok').is(':checked') && riskType === 'Исклучок') {
                                            option.prop('disabled', false);
                                            option.show();
                                        }
                                    }
                                });
                            }
                            // Refresh Select2
                            $('#riskComparisonSelect').trigger('change');
                        });
                } else {
                    // If no product selected, show all risks based on selected filters
                    filterRiskOptions();
                }
            }

            // Update the product change handler
            $('#Input_SelectedProduktId').change(function() {
                var produktId = $(this).val();
                updateRiskOptionsForProduct(produktId);
            });

            // Update the risk type filter change handlers
            $('#filterRizik, #filterDopolnitelen, #filterIsklucok').change(function() {
                var produktId = $('#Input_SelectedProduktId').val();
                updateRiskOptionsForProduct(produktId);
            });

            // Handle risk type filter changes
            $('.risk-type-filters input[type="checkbox"]').on('change', function() {
                filterRiskOptions();
            });

            // Handle Select All button
            $('#selectAllRisks').off('click').on('click', function() {
                // Select only visible and enabled options that match the selected risk types
                var visibleOptions = $('#riskComparisonSelect option').filter(function() {
                    return !$(this).prop('disabled') && $(this).css('display') !== 'none';
                });
                
                visibleOptions.prop('selected', true);
                $('#riskComparisonSelect').trigger('change');
                updateComparison();
            });

            // Handle Deselect All button
            $('#deselectAllRisks').off('click').on('click', function() {
                $('#riskComparisonSelect option:selected').prop('selected', false);
                $('#riskComparisonSelect').trigger('change');
                updateComparison();
            });

            // Handle product selection change
            $('#Input_SelectedProduktId').off('change').on('change', function() {
                filterRiskOptions();
            });

            // Initial filter
            filterRiskOptions();

            // Handle insurer selection changes
            $('.osiguritel-select').on('change', function() {
                updateComparison();
            });

            // Handle risk selection changes
            $('#riskComparisonSelect').on('change', function() {
                updateComparison();
            });

            function updateComparison() {
                var selectedInsurers = [];
                $('.osiguritel-select').each(function() {
                    var value = $(this).val();
                    if (value) {
                        selectedInsurers.push(parseInt(value)); // Convert to integer
                    }
                });

                var selectedRisks = $('#riskComparisonSelect').val();
                if (selectedRisks) {
                    selectedRisks = selectedRisks.map(function(id) {
                        return parseInt(id); // Convert to integer
                    });
                }

                if (selectedInsurers.length < 2 || !selectedRisks || selectedRisks.length === 0) {
                    $('#comparisonResults').html('<div class="alert alert-info">Изберете најмалку два осигурители за споредба</div>');
                    $('#generatePdf').hide();
                    return;
                }

                // Log the data being sent
                console.log('Selected Insurers:', selectedInsurers);
                console.log('Selected Risks:', selectedRisks);

                // Ensure we're sending arrays
                var params = {
                    insurerIds: selectedInsurers,
                    riskIds: selectedRisks
                };

                $.ajax({
                    url: '?handler=CompareInsurers',
                    type: 'GET',
                    traditional: true, // This is important for array parameters
                    data: params,
                    success: function(response) {
                        console.log('Server Response:', response);
                        if (response.success) {
                            displayComparisonResults(response.data);
                        } else {
                            $('#comparisonResults').html('<div class="alert alert-danger">Грешка при споредбата: ' + response.message + '</div>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', error);
                        $('#comparisonResults').html('<div class="alert alert-danger">Грешка при споредбата: ' + error + '</div>');
                    }
                });
            }

            function displayComparisonResults(data) {
                if (data.length === 0) {
                    $('#comparisonResults').html('<div class="alert alert-info">Нема податоци за споредба</div>');
                    $('#generatePdf').hide();
                    return;
                }

                // Group data by risk
                const groupedByRisk = {};
                const insurers = new Set();
                
                data.forEach(function(item) {
                    if (!groupedByRisk[item.riskName]) {
                        groupedByRisk[item.riskName] = {};
                    }
                    groupedByRisk[item.riskName][item.insurerName] = {
                        pokrieno: item.pokrieno,
                        beleshka: item.beleshka
                    };
                    insurers.add(item.insurerName);
                });

                const insurerArray = Array.from(insurers);

                var tableHtml = `
                    <style>
                        .note-text {
                            display: inline-block;
                            font-size: 0.9em;
                            color: #666;
                            margin-left: 5px;
                        }
                        .coverage-cell {
                            text-align: center;
                            vertical-align: middle;
                            min-width: 150px;
                        }
                        .coverage-container {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            gap: 8px;
                        }
                        .coverage-status {
                            white-space: nowrap;
                        }
                    </style>
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th style="background-color: #f8f9fa; text-align: left; vertical-align: middle; padding-left: 15px;">Ризик</th>
                `;

                // Add insurer columns
                insurerArray.forEach(function(insurer) {
                    tableHtml += `<th style="background-color: #f8f9fa; text-align: center; vertical-align: middle;">${insurer}</th>`;
                });

                tableHtml += `</tr></thead><tbody>`;

                // Add rows for each risk
                Object.keys(groupedByRisk).sort().forEach(function(risk) {
                    tableHtml += `<tr><td style="text-align: left; vertical-align: middle; padding-left: 15px;">${risk}</td>`;
                    
                    insurerArray.forEach(function(insurer) {
                        const data = groupedByRisk[risk][insurer] || { pokrieno: false, beleshka: null };
                        const icon = data.pokrieno ? '✓' : '✗';
                        const text = data.pokrieno ? 'Да' : 'Не';
                        const color = data.pokrieno ? 'success' : 'danger';
                        const tooltip = data.beleshka ? `data-toggle="tooltip" title="${data.beleshka}"` : '';
                        
                        tableHtml += `
                            <td class="coverage-cell" ${tooltip}>
                                <div class="coverage-container">
                                    <span class="coverage-status text-${color}">
                                        ${icon} ${text}
                                    </span>
                                    ${data.beleshka ? `<span class="note-text">(${data.beleshka})</span>` : ''}
                                </div>
                            </td>`;
                    });
                    
                    tableHtml += `</tr>`;
                });

                tableHtml += `</tbody></table>`;

                $('#comparisonResults').html(tableHtml);
                $('#generatePdf').show();
                
                // Initialize tooltips
                $('[data-toggle="tooltip"]').tooltip();
            }

            // Add collapsed class initially
            $('#headingKombinacija').addClass('collapsed');

            // Toggle collapsed class on collapse events
            $('#collapseKombinacija').on('show.bs.collapse', function () {
                $('#headingKombinacija').removeClass('collapsed');
            }).on('hide.bs.collapse', function () {
                $('#headingKombinacija').addClass('collapsed');
            });
            
            // Remove the default button styles from the header
            $('#headingKombinacija .btn-link').remove();

            // Initialize Select2 for rizikSelect with search and custom "Add" button
            function initializeRizikSelect() {
                $('#rizikSelect').select2({
                    placeholder: "-- Изберете ризик --",
                    allowClear: true,
                    width: '100%',
                    minimumInputLength: 0,
                    language: {
                        noResults: function() {
                            return "Нема пронајдено резултати";
                        },
                        searching: function() {
                            return "Пребарување...";
                        }
                    },
                    templateResult: function(data) {
                        if (data.loading) return data.text;
                        if (data.id === "") return data.text;
                        return $('<span>' + data.text + '</span>');
                    }
                }).on('select2:open', function() {
                    // Add the "Add Risk" button to dropdown
                    if (!$('.select2-dropdown .select2-add-risk').length) {
                        $('.select2-dropdown').append(
                            '<div class="select2-add-risk">' +
                            '<button type="button" class="btn btn-link btn-sm btn-block text-primary add-risk-btn">' +
                            '<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-plus-circle" viewBox="0 0 16 16">' +
                            '<path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>' +
                            '<path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>' +
                            '</svg> Додај нов ризик</button></div>'
                        );
                    }
                });
            }

            initializeRizikSelect();

            // Handle "Add Risk" button click
            $(document).on('click', '.add-risk-btn', function() {
                $('#rizikSelect').select2('close');
                $('#addRizikModal').modal('show');
            });

            // Handle save new risk
            $('#saveNewRizik').on('click', function() {
                var naziv = $('#newRizikNaziv').val().trim();
                var tip = $('#newRizikTip').val().trim();
                var produktId = $('#newRizikProdukt').val().trim();
                if (!naziv) {
                    alert('Ве молиме внесете назив на ризик');
                    return;
                }
                if (!tip) {
                    alert('Ве молиме изберете тип на ризик');
                    return;
                }
                if (!produktId) {
                    alert('Ве молиме изберете продукт');
                    return;
                }

                $.ajax({
                    url: '?handler=AddRizik',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ naziv: naziv, tip: tip, produktId: produktId }),
                    headers: {
                        "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            // Add new option to rizikSelect
                            var newOption = new Option(response.naziv, response.id, true, true);
                            $('#rizikSelect').append(newOption).trigger('change');
                            
                            // Add new option to riskComparisonSelect
                            var newComparisonOption = new Option(response.naziv, response.id);
                            $('#riskComparisonSelect').append(newComparisonOption);
                            
                            // Close modal and clear inputs
                            $('#addRizikModal').modal('hide');
                            $('#newRizikNaziv').val('');
                            $('#newRizikTip').val('');
                            $('#newRizikProdukt').val('');

                            // Show success message
                            alert('Ризикот е успешно додаден');
                        } else {
                            alert('Грешка при зачувување: ' + response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        alert('Грешка при зачувување на ризик: ' + error);
                    }
                });
            });

            // Fix Select2 inside Bootstrap modal (update to include new features)
            $('#collapseKombinacija').on('shown.bs.collapse', function () {
                $('#rizikSelect').select2('destroy');
                initializeRizikSelect();
            });

            // Clear modal on hide
            $('#addRizikModal').on('hidden.bs.modal', function () {
                $('#newRizikNaziv').val('');
                $('#newRizikTip').val('');
                $('#newRizikProdukt').val('');
            });

            // Add validation function
            function validateForm() {
                var ime = $('#Input_Ime').val().trim();
                var prezime = $('#Input_Prezime').val().trim();
                var naziv = $('#Input_Naziv').val().trim();
                var embg = $('#Input_EMBG').val().trim();
                var mb = $('#Input_MB').val().trim();
                var edb = $('#Input_EDB').val().trim();
                var email = $('#Input_Email').val().trim();
                var dogovoruvacId = $('#dogovoruvacId').val().trim();

                // Clear previous error messages
                $('.validation-error').remove();
                $('.form-control').removeClass('is-invalid');

                var isValid = true;
                var errorMessages = [];

                // Email is always required
                if (!email) {
                    $('#Input_Email').addClass('is-invalid');
                    $('#Input_Email').after('<span class="text-danger validation-error">Email е задолжително поле</span>');
                    isValid = false;
                }

                // Check if we have a valid combination without Договорувач
                var hasValidPersonInfo = ime && prezime;
                var hasValidCompanyInfo = naziv;
                var hasValidIdentification = embg || (mb && edb);

                // If we don't have Договорувач, we need either:
                // 1. (Ime + Prezime) or Naziv
                // 2. EMBG or (MB + EDB)
                if (!dogovoruvacId) {
                    if (!((hasValidPersonInfo || hasValidCompanyInfo) && hasValidIdentification)) {
                        errorMessages.push('Потребно е да внесете или Договорувач или валидна комбинација на податоци:');
                        errorMessages.push('- (Име и Презиме) или Назив');
                        errorMessages.push('- ЕМБГ или (МБ и ЕДБ)');
                        isValid = false;
                    }
                }

                // Check if at least one insurer is selected
                var hasSelectedInsurer = false;
                $('.osiguritel-select').each(function() {
                    if ($(this).val()) {
                        hasSelectedInsurer = true;
                        return false; // break the loop
                    }
                });

                if (!hasSelectedInsurer) {
                    errorMessages.push('Потребно е да изберете најмалку еден осигурител');
                    isValid = false;
                }

                if (!isValid) {
                    var errorHtml = '<div class="alert alert-danger validation-error mt-3"><ul class="mb-0">';
                    errorMessages.forEach(function(msg) {
                        errorHtml += '<li>' + msg + '</li>';
                    });
                    errorHtml += '</ul></div>';
                    $('form').prepend(errorHtml);
                }

                return isValid;
            }

            // Add form submit handler
            $('form').on('submit', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Clear previous error messages
                $('.validation-error').remove();
                $('.form-control').removeClass('is-invalid');

                var isValid = true;
                var errorMessages = [];

                // Basic validation
                var email = $('#Input_Email').val().trim();
                if (!email) {
                    $('#Input_Email').addClass('is-invalid');
                    $('#Input_Email').after('<span class="text-danger validation-error">Email е задолжително поле</span>');
                    isValid = false;
                }

                // Check if at least one insurer is selected
                var hasSelectedInsurer = false;
                $('.osiguritel-select').each(function() {
                    if ($(this).val()) {
                        hasSelectedInsurer = true;
                        return false; // break the loop
                    }
                });

                if (!hasSelectedInsurer) {
                    errorMessages.push('Потребно е да изберете најмалку еден осигурител');
                    isValid = false;
                }

                if (!isValid) {
                    var errorHtml = '<div class="alert alert-danger validation-error mt-3"><ul class="mb-0">';
                    errorMessages.forEach(function(msg) {
                        errorHtml += '<li>' + msg + '</li>';
                    });
                    errorHtml += '</ul></div>';
                    $('form').prepend(errorHtml);
                    return false;
                }

                var formData = new FormData(this);
                
                // Add insurers and their specific fields
                $('.osiguritel-select').each(function(index) {
                    var insurerId = $(this).val();
                    if (insurerId) {
                        formData.append(`Input.Osiguriteli[${index}].Id`, insurerId);
                        
                        // Get the corresponding insurer fields
                        var fieldsContainer = $(`.osiguritel-fields[data-osiguritel-index="${index}"]`);
                        // Remove pravno-ekonomski odnosi from form submission
                        formData.append(`Input.Osiguriteli[${index}].PrivizijaZaPredlogPonuda`, fieldsContainer.find('input[name$="PrivizijaZaPredlogPonuda"]').val());
                        formData.append(`Input.Osiguriteli[${index}].ProvizijaZaSiteProduktiIOsiguriteli`, "https://inco.com.mk/ProvizijaOsiguriteli");
                        formData.append(`Input.Osiguriteli[${index}].PremijaIznos`, fieldsContainer.find('input[name$="PremijaIznos"]').val());
                    }
                });

                // Add the global fields
                formData.append('Input.ProvizijaZaSiteProduktiIOsiguriteli', "https://inco.com.mk/ProvizijaOsiguriteli");
                formData.append('Input.RiziciOsiguritel', $('#Input_RiziciOsiguritel').val());

                // Add combinations from the comparison table
                var kombinacijaIndex = 0;
                var selectedRisks = $('#riskComparisonSelect').val();
                if (selectedRisks && selectedRisks.length > 0) {
                    selectedRisks.forEach(function(riskId) {
                        $('.osiguritel-select').each(function() {
                            var insurerId = $(this).val();
                            if (insurerId) {
                                // Get coverage status from the table
                                var riskName = $('#riskComparisonSelect option[value="' + riskId + '"]').text();
                                var insurerName = $('.osiguritel-select option[value="' + insurerId + '"]').text();
                                var coverageCell = $('.comparison-table td:contains("' + riskName + '")').siblings('td:contains("' + insurerName + '")');
                                var isCovered = coverageCell.find('.coverage-status').hasClass('text-success');
                                var beleshka = coverageCell.find('.coverage-note').text() || '';

                                formData.append(`Input.Kombinacii[${kombinacijaIndex}].RizikIdRizici`, riskId);
                                formData.append(`Input.Kombinacii[${kombinacijaIndex}].KlientiIdAnalizaDogovoruvac`, insurerId);
                                formData.append(`Input.Kombinacii[${kombinacijaIndex}].Pokrieno`, isCovered);
                                formData.append(`Input.Kombinacii[${kombinacijaIndex}].Beleshka`, beleshka);
                                kombinacijaIndex++;
                            }
                        });
                    });
                }

                // Submit the form with the updated data
                $.ajax({
                    url: window.location.pathname,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    headers: {
                        RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function() {
                        // Show success message at the bottom
                        $('form').append('<div class="alert alert-success">Анализата е успешно зачувана и испратена на е-пошта...</div>');
                        
                        // Disable all buttons and show loading state
                        $('button').prop('disabled', true);
                        
                        // Update countdown every second
                        var countdown = 6;
                        var countdownInterval = setInterval(function() {
                            countdown--;
                            $('#countdown').text(countdown);
                            if (countdown <= 0) {
                                clearInterval(countdownInterval);
                            }
                        }, 1000);
                        
                        // Trigger test email after a short delay
                        setTimeout(function() {
                            $('#testEmailBtn').click();
                        }, 1000);
                        
                        // Redirect after 10 seconds
                        setTimeout(function() {
                            window.location.href = '/AnalizaNaKlient/AnalizaNaKlient';
                        }, 10000);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error saving data:', error);
                        $('form').prepend('<div class="alert alert-danger">Грешка при зачувување на податоците. Ве молиме обидете се повторно.</div>');
                    }
                });

                return false;
            });

            // Add PDF generation handler
            $('#generatePdf').on('click', function() {
                var selectedInsurers = [];
                var insurersData = [];
                
                // Collect insurer data
                $('.osiguritel-select').each(function(index) {
                    var value = $(this).val();
                    if (value) {
                        selectedInsurers.push(parseInt(value));
                        
                        // Get the corresponding insurer fields
                        var fieldsContainer = $(`.osiguritel-fields[data-osiguritel-index="${index}"]`);
                        insurersData.push({
                            id: parseInt(value),
                            pravnoEkonomskiOdnosi: fieldsContainer.find('textarea[name$="PravnoEkonomskiOdnosiOsiguritel"]').val(),
                            privizijaZaPredlogPonuda: fieldsContainer.find('input[name$="PrivizijaZaPredlogPonuda"]').val(),
                            provizijaZaSiteProdukti: "https://inco.com.mk/ProvizijaOsiguriteli",
                            premijaIznos: fieldsContainer.find('input[name$="PremijaIznos"]').val() ? parseFloat(fieldsContainer.find('input[name$="PremijaIznos"]').val()) : null
                        });
                    }
                });

                var selectedRisks = $('#riskComparisonSelect').val();
                if (selectedRisks) {
                    selectedRisks = selectedRisks.map(function(id) {
                        return parseInt(id);
                    });
                }

                if (selectedInsurers.length < 2 || !selectedRisks || selectedRisks.length === 0) {
                    alert('Ве молиме изберете најмалку два осигурители и еден ризик за споредба');
                    return;
                }

                // Show loading indicator
                $('#generatePdf').prop('disabled', true).html(`
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    Генерирање PDF...
                `);

                // Create the request data for PDF generation
                var requestData = {
                    insurerIds: selectedInsurers,
                    riskIds: selectedRisks,
                    additionalData: {
                        ime: $('#Input_Ime').val() || '',
                        prezime: $('#Input_Prezime').val() || '',
                        embg: $('#Input_EMBG').val() || '',
                        adresa: $('#Input_Adresa').val() || '',
                        naziv: $('#Input_Naziv').val() || '',
                        email: $('#Input_Email').val() || '',
                        obrazlozenieZaPredlog: $('#Input_ObrazlozenieZaPredlog').val() || '',
                        provizijaZaSiteProdukti: "https://inco.com.mk/ProvizijaOsiguriteli",
                        riziciOsiguritel: $('#Input_RiziciOsiguritel').val() || '',
                        insurers: insurersData.map(function(insurer) {
                            return {
                                id: insurer.id,
                                pravnoEkonomskiOdnosi: insurer.pravnoEkonomskiOdnosi,
                                privizijaZaPredlogPonuda: insurer.privizijaZaPredlogPonuda,
                                premijaIznos: insurer.premijaIznos
                            };
                        })
                    }
                };

                // Send request to generate PDF
                $.ajax({
                    url: '?handler=GeneratePdf',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(requestData),
                    headers: {
                        "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            try {
                                // Create a link to download the PDF
                                var link = document.createElement('a');
                                link.href = 'data:application/pdf;base64,' + response.pdfContent;
                                link.download = 'Споредба_на_осигурители.pdf';
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                            } catch (err) {
                                console.error('Error downloading PDF:', err);
                                alert('Грешка при преземање на PDF. Ве молиме обидете се повторно.');
                            }
                        } else {
                            console.error('PDF Generation Error:', response.message);
                            alert('Грешка при генерирање на PDF: ' + response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', {
                            status: status,
                            error: error,
                            responseText: xhr.responseText
                        });
                        alert('Грешка при генерирање на PDF. Ве молиме обидете се повторно.');
                    },
                    complete: function() {
                        // Reset button state
                        $('#generatePdf').prop('disabled', false).html(`
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-file-pdf" viewBox="0 0 16 16">
                                <path d="M8 4a.5.5 0 0 1 .5.5v3.793l1.146-1.147a.5.5 0 0 1 .708.708l-2 2a.5.5 0 0 1-.708 0l-2-2a.5.5 0 1 1 .708-.708L7.5 8.293V4.5A.5.5 0 0 1 8 4z"/>
                                <path d="M4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H4zm0 1h8a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1z"/>
                            </svg>
                            Генерирај PDF
                        `);
                    }
                });
            });

            // Test email button click handler
            $('#testEmailBtn').off('click').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                var formData = new FormData();
                
                // Add basic form fields
                formData.append('Input.Email', $('#Input_Email').val());
                formData.append('Input.Ime', $('#Input_Ime').val());
                formData.append('Input.Prezime', $('#Input_Prezime').val());
                formData.append('Input.EMBG', $('#Input_EMBG').val());
                formData.append('Input.Adresa', $('#Input_Adresa').val());
                formData.append('Input.Naziv', $('#Input_Naziv').val());
                formData.append('Input.ObrazlozenieZaPredlog', $('#Input_ObrazlozenieZaPredlog').val());
                formData.append('Input.RiziciOsiguritel', $('#Input_RiziciOsiguritel').val());

                // Add insurers and their specific fields
                $('.osiguritel-select').each(function(index) {
                    var insurerId = $(this).val();
                    if (insurerId) {
                        formData.append(`Input.Osiguriteli[${index}].Id`, insurerId);
                        
                        // Get the corresponding insurer fields
                        var fieldsContainer = $(`.osiguritel-fields[data-osiguritel-index="${index}"]`);
                        formData.append(`Input.Osiguriteli[${index}].PravnoEkonomskiOdnosiOsiguritel`, fieldsContainer.find('textarea[name$="PravnoEkonomskiOdnosiOsiguritel"]').val());
                        formData.append(`Input.Osiguriteli[${index}].PrivizijaZaPredlogPonuda`, fieldsContainer.find('input[name$="PrivizijaZaPredlogPonuda"]').val());
                        formData.append(`Input.Osiguriteli[${index}].ProvizijaZaSiteProduktiIOsiguriteli`, "https://inco.com.mk/ProvizijaOsiguriteli");
                        formData.append(`Input.Osiguriteli[${index}].PremijaIznos`, fieldsContainer.find('input[name$="PremijaIznos"]').val());
                    }
                });

                // Add risks from the comparison table
                var riskIndex = 0;
                var selectedRisks = $('#riskComparisonSelect').val();
                if (selectedRisks && selectedRisks.length > 0) {
                    selectedRisks.forEach(function(riskId) {
                        formData.append(`Input.Kombinacii[${riskIndex}].RizikIdRizici`, riskId);
                        // For each risk, add a combination with each insurer
                        $('.osiguritel-select').each(function(insurerIndex) {
                            var insurerId = $(this).val();
                            if (insurerId) {
                                formData.append(`Input.Kombinacii[${riskIndex}].KlientiIdAnalizaDogovoruvac`, insurerId);
                                // Get coverage status from the table
                                var riskName = $('#riskComparisonSelect option[value="' + riskId + '"]').text();
                                var insurerName = $('.osiguritel-select option[value="' + insurerId + '"]').text();
                                var coverageCell = $('td:contains("' + riskName + '")').siblings('td:contains("' + insurerName + '")');
                                var isCovered = coverageCell.find('.coverage-status').hasClass('text-success');
                                formData.append(`Input.Kombinacii[${riskIndex}].Pokrieno`, isCovered);
                                formData.append(`Input.Kombinacii[${riskIndex}].Beleshka`, '');
                                riskIndex++;
                            }
                        });
                    });
                }

                // Disable the button to prevent multiple clicks
                $(this).prop('disabled', true);

                $.ajax({
                    url: '?handler=TestEmail',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    headers: {
                        RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        // Removed the success alert
                    },
                    error: function(xhr, status, error) {
                        alert('Error sending test email: ' + error);
                    },
                    complete: function() {
                        // Re-enable the button after the request is complete
                        $('#testEmailBtn').prop('disabled', false);
                    }
                });
            });

            // Initialize simple select for the insurer dropdown
            $('#osiguritelSelect').on('change', function() {
                const selectedInsurer = $(this).val();
                if (selectedInsurer) {
                    // Check if insurer is already used
                    if (usedInsurers.has(selectedInsurer)) {
                        alert('Веќе имате внесено податок за овој осигурител');
                        $(this).val('');
                        $('#pravnoEkonomskiText').val('');
                        return;
                    }

                    // Load existing data if any
                    $.get('?handler=PravnoEkonomskiOdnosi', { insurerId: selectedInsurer })
                        .done(function(data) {
                            if (data && data.pravnoEkonomskiOdnosi) {
                                $('#pravnoEkonomskiText').val(data.pravnoEkonomskiOdnosi);
                                $('#pravnoEkonomskiText').prop('readonly', true);
                                $('#savePravnoEkonomski').prop('disabled', true);
                            } else {
                                $('#pravnoEkonomskiText').val('');
                                $('#pravnoEkonomskiText').prop('readonly', false);
                                $('#savePravnoEkonomski').prop('disabled', false);
                            }
                        });
                } else {
                    $('#pravnoEkonomskiText').val('');
                    $('#pravnoEkonomskiText').prop('readonly', false);
                    $('#savePravnoEkonomski').prop('disabled', false);
                }
            });

            // Handle save button click
            $('#savePravnoEkonomski').on('click', function() {
                const insurerId = $('#pravnoEkonomskiOsiguritelSelect').val();
                const pravnoEkonomskiText = $('#pravnoEkonomskiText').val().trim();

                if (!insurerId || insurerId === '') {
                    alert('Ве молиме изберете осигурител');
                    return;
                }

                if (!pravnoEkonomskiText) {
                    alert('Ве молиме внесете податок за правно –економски односи');
                    return;
                }

                // Disable the button to prevent double submission
                const saveButton = $(this);
                saveButton.prop('disabled', true);

                $.ajax({
                    url: '?handler=SavePravnoEkonomskiOdnosi',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        insurerId: parseInt(insurerId),
                        pravnoEkonomskiOdnosi: pravnoEkonomskiText
                    }),
                    headers: {
                        "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('Податокот е успешно зачуван');
                            
                            // Clear the form fields
                            $('#pravnoEkonomskiOsiguritelSelect').val(null).trigger('change');
                            $('#pravnoEkonomskiText').val('');
                            
                            // Close the section
                            $('#collapsePravnoEkonomski').collapse('hide');
                            
                            // Update the corresponding insurer field in the main form
                            updateInsurerFields();
                        } else {
                            alert(response.message || 'Грешка при зачувување на податокот');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error:', error);
                        alert('Грешка при зачувување: ' + error);
                    },
                    complete: function() {
                        // Re-enable the save button
                        saveButton.prop('disabled', false);
                    }
                });
            });

            // Handle insurer selection change
            $('#pravnoEkonomskiOsiguritelSelect').on('change', function() {
                const selectedInsurerId = $(this).val();
                const pravnoEkonomskiField = $('#pravnoEkonomskiText');
                
                if (!selectedInsurerId || selectedInsurerId === '') {
                    pravnoEkonomskiField.val('');
                    pravnoEkonomskiField.prop('readonly', false);
                    $('#savePravnoEkonomski').prop('disabled', false);
                    return;
                }

                // Show loading state
                pravnoEkonomskiField.prop('disabled', true);
                pravnoEkonomskiField.val('Вчитување...');

                $.get('?handler=PravnoEkonomskiOdnosi', { insurerId: selectedInsurerId })
                    .done(function(data) {
                        if (data && data.pravnoEkonomskiOdnosi) {
                            pravnoEkonomskiField.val(data.pravnoEkonomskiOdnosi);
                            pravnoEkonomskiField.prop('readonly', true);
                            $('#savePravnoEkonomski').prop('disabled', true);
                        } else {
                            pravnoEkonomskiField.val('');
                            pravnoEkonomskiField.prop('readonly', false);
                            $('#savePravnoEkonomski').prop('disabled', false);
                        }
                    })
                    .fail(function(xhr, status, error) {
                        console.error('Error loading data:', error);
                        pravnoEkonomskiField.val('');
                        pravnoEkonomskiField.prop('readonly', false);
                        $('#savePravnoEkonomski').prop('disabled', false);
                        alert('Грешка при вчитување на податоците');
                    })
                    .always(function() {
                        pravnoEkonomskiField.prop('disabled', false);
                    });
            });

            // Function to update insurer fields based on selected insurers
            function updateInsurerFields() {
                $('.osiguritel-select').each(function() {
                    const select = $(this);
                    const selectedInsurerId = select.val();
                    if (selectedInsurerId) {
                        const fieldsContainer = select.closest('.osiguritel-col').parent().find(`.osiguritel-fields[data-osiguritel-index="${select.closest('.osiguritel-col').index()}"]`);
                        const pravnoEkonomskiField = fieldsContainer.find('textarea[name$="PravnoEkonomskiOdnosiOsiguritel"]');
                        
                        // Load data for this insurer
                        $.get('?handler=PravnoEkonomskiOdnosi', { insurerId: selectedInsurerId })
                            .done(function(data) {
                                if (data && data.pravnoEkonomskiOdnosi) {
                                    pravnoEkonomskiField.val(data.pravnoEkonomskiOdnosi);
                                    pravnoEkonomskiField.prop('readonly', true);
                                } else {
                                    pravnoEkonomskiField.val('');
                                    pravnoEkonomskiField.prop('readonly', false);
                                }
                            });
                    }
                });
            }

            // Handle insurer selection changes in the main form
            $('.osiguritel-select').on('change', function() {
                const select = $(this);
                const selectedInsurerId = select.val();
                const fieldsContainer = select.closest('.osiguritel-col').parent().find(`.osiguritel-fields[data-osiguritel-index="${select.closest('.osiguritel-col').index()}"]`);
                const pravnoEkonomskiField = fieldsContainer.find('textarea[name$="PravnoEkonomskiOdnosiOsiguritel"]');

                if (selectedInsurerId) {
                    // Load data for this insurer
                    $.get('?handler=PravnoEkonomskiOdnosi', { insurerId: selectedInsurerId })
                        .done(function(data) {
                            if (data && data.pravnoEkonomskiOdnosi) {
                                pravnoEkonomskiField.val(data.pravnoEkonomskiOdnosi);
                                pravnoEkonomskiField.prop('readonly', true);
                            } else {
                                pravnoEkonomskiField.val('');
                                pravnoEkonomskiField.prop('readonly', false);
                            }
                        });
                } else {
                    pravnoEkonomskiField.val('');
                    pravnoEkonomskiField.prop('readonly', false);
                }
            });

            // Initial load of insurer data
            $(document).ready(function() {
                updateInsurerFields();
            });

            // Add collapsed class initially
            $('#headingPravnoEkonomski').addClass('collapsed');

            // Toggle collapsed class on collapse events
            $('#collapsePravnoEkonomski').on('show.bs.collapse', function () {
                $('#headingPravnoEkonomski').removeClass('collapsed');
            }).on('hide.bs.collapse', function () {
                $('#headingPravnoEkonomski').addClass('collapsed');
            });

            $('#insurerSelect').change(function() {
                var selectedInsurer = $(this).val();
                if (!selectedInsurer) {
                    $('#pravnoEkonomskiOdnosi').val('');
                    return;
                }

                console.log('Loading pravno ekonomski odnosi for insurer:', selectedInsurer);
                
                $.get('?handler=PravnoEkonomskiOdnosi', { insurerId: parseInt(selectedInsurer) })
                    .done(function(response) {
                        console.log('Received response:', response);
                        if (response && response.pravnoEkonomskiOdnosi !== undefined) {
                            $('#pravnoEkonomskiOdnosi').val(response.pravnoEkonomskiOdnosi || '');
                        } else {
                            console.error('Invalid response format:', response);
                            alert('Грешка при вчитување на податоците. Невалиден формат на одговор.');
                        }
                    })
                    .fail(function(jqXHR, textStatus, errorThrown) {
                        console.error('Error loading data:', textStatus, errorThrown);
                        alert('Грешка при вчитување на податоците. Ве молиме обидете се повторно.');
                    });
            });

            $('#savePravnoEkonomskiBtn').click(function() {
                var selectedInsurer = $('#insurerSelect').val();
                var pravnoEkonomskiValue = $('#pravnoEkonomskiOdnosi').val();

                if (!selectedInsurer) {
                    alert('Ве молиме изберете осигурител.');
                    return;
                }

                var data = {
                    insurerId: parseInt(selectedInsurer),
                    pravnoEkonomskiOdnosi: pravnoEkonomskiValue
                };

                console.log('Saving data:', data);

                $.ajax({
                    url: '?handler=SavePravnoEkonomskiOdnosi',
                    type: 'POST',
                    data: JSON.stringify(data),
                    contentType: 'application/json',
                    headers: {
                        'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        console.log('Save successful:', response);
                        alert('Податоците се успешно зачувани.');
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error('Save error:', textStatus, errorThrown);
                        alert('Грешка при зачувување. Ве молиме обидете се повторно.');
                    }
                });
            });

            // Handle Klasa selection change
            $('#Input_SelectedKlasaId').change(function() {
                var klasaId = $(this).val();
                var produktDropdown = $('#Input_SelectedProduktId');
                
                // Clear and disable Produkt dropdown
                produktDropdown.empty().append($('<option></option>').val('').text('Изберете продукт'));
                produktDropdown.prop('disabled', true);

                if (klasaId) {
                    // Fetch products for selected class
                    $.get('?handler=ProduktiByKlasa&klasaId=' + klasaId, function(produkti) {
                        produktDropdown.empty().append($('<option></option>').val('').text('Изберете продукт'));
                        
                        if (produkti && produkti.length > 0) {
                            produkti.forEach(function(produkt) {
                                produktDropdown.append($('<option></option>')
                                    .val(produkt.id)
                                    .text(produkt.ime));
                            });
                            produktDropdown.prop('disabled', false);
                        } else {
                            produktDropdown.append($('<option></option>')
                                .val('')
                                .text('Нема достапни производи за избраната класа'));
                        }
                    }).fail(function(jqXHR, textStatus, errorThrown) {
                        console.error('Error fetching products:', textStatus, errorThrown);
                        produktDropdown.append($('<option></option>')
                            .val('')
                            .text('Грешка при вчитување на производите'));
                    });
                }
            });

            // Initialize dropdowns
            $('#Input_SelectedKlasaId').trigger('change');
            $('#Input_SelectedProduktId').trigger('change');
        });
    </script>

    <script>
        $(document).ready(function() {
            // Single event handler for class selection change
            $('#Input_SelectedKlasaId').off('change').on('change', function() {
                var klasaId = $(this).val();
                var produktDropdown = $('#Input_SelectedProduktId');
                
                // Clear and disable product dropdown initially
                produktDropdown.empty();
                produktDropdown.append($('<option></option>').val('').text('Изберете продукт'));
                produktDropdown.prop('disabled', true);
                
                if (klasaId) {
                    $.get('?handler=ProduktiByKlasa&klasaId=' + klasaId, function(produkti) {
                        if (produkti && produkti.length > 0) {
                            produktDropdown.prop('disabled', false);
                            produkti.forEach(function(produkt) {
                                produktDropdown.append($('<option></option>').val(produkt.id).text(produkt.ime));
                            });
                        } else {
                            produktDropdown.append($('<option></option>').val('').text('Нема достапни производи за избраната класа'));
                        }
                    }).fail(function() {
                        produktDropdown.append($('<option></option>').val('').text('Грешка при вчитување на производите'));
                    });
                }
            });

            // Handle product selection change
            $('#Input_SelectedProduktId').off('change').on('change', function() {
                var produktId = $(this).val();
                console.log('Debug - Product selection changed:', produktId);

                // Clear the current selection
                $('#riskComparisonSelect').val(null).trigger('change');

                if (produktId) {
                    console.log('Debug - Fetching risks for product:', produktId);
                    $.get('?handler=RiziciByProdukt', { produktId: produktId })
                        .done(function(rizici) {
                            console.log('Debug - Received risks:', rizici);
                            
                            // Get all options and hide them first
                            $('#riskComparisonSelect option').each(function() {
                                $(this).prop('disabled', true);
                                $(this).hide();
                            });

                            // Enable and show only matching risks
                            if (rizici && rizici.length > 0) {
                                rizici.forEach(function(rizik) {
                                    console.log('Debug - Processing risk:', rizik.id, rizik.naziv);
                                    var option = $('#riskComparisonSelect option[value="' + rizik.id + '"]');
                                    if (option.length) {
                                        option.prop('disabled', false);
                                        option.show();
                                        console.log('Debug - Enabled risk:', rizik.naziv);
                                    }
                                });
                            }

                            // Destroy and reinitialize Select2
                            $('#riskComparisonSelect').select2('destroy');
                            $('#riskComparisonSelect').select2({
                                placeholder: "Изберете ризици за споредба",
                                allowClear: true,
                                width: '100%'
                            });

                            // Force Select2 to update its display
                            $('#riskComparisonSelect').trigger('change.select2');
                        })
                        .fail(function(jqXHR, textStatus, errorThrown) {
                            console.error('Debug - Error loading risks:', textStatus, errorThrown);
                            console.error('Debug - Response:', jqXHR.responseText);
                        });
                } else {
                    // If no product selected, show all risks
                    $('#riskComparisonSelect option').prop('disabled', false).show();
                    
                    // Reinitialize Select2
                    $('#riskComparisonSelect').select2('destroy');
                    $('#riskComparisonSelect').select2({
                        placeholder: "Изберете ризици за споредба",
                        allowClear: true,
                        width: '100%'
                    });
                }
            });

            // Initial trigger
            $('#Input_SelectedKlasaId').trigger('change');
        });
    </script>

    <script>
    $(document).ready(function() {
        // Single initialization of the modal
        var addRizikModal = new bootstrap.Modal(document.getElementById('addRizikModal'));
        
        // Single event handler for modal close
        $('#addRizikModal .btn-close, #addRizikModal button[data-bs-dismiss="modal"]').on('click', function() {
            addRizikModal.hide();
        });

        // Single event handler for modal hidden
        $('#addRizikModal').on('hidden.bs.modal', function() {
            $('#newRizikNaziv').val('');
            $('#newRizikTip').val('');
            $('#newRizikProdukt').val('');
        });

        // Single event handler for save button
        $('#saveNewRizik').off('click').on('click', function() {
            var naziv = $('#newRizikNaziv').val().trim();
            var tip = $('#newRizikTip').val();
            var produktId = $('#newRizikProdukt').val();

            if (!naziv) {
                alert('Внесете назив на ризик');
                return;
            }

            if (!tip) {
                alert('Изберете тип на ризик');
                return;
            }

            if (!produktId) {
                alert('Изберете продукт');
                return;
            }

            // Disable the save button to prevent double submission
            $(this).prop('disabled', true);

            $.ajax({
                url: '?handler=AddRizik',
                type: 'POST',
                data: JSON.stringify({
                    naziv: naziv,
                    tip: tip,
                    produktId: produktId
                }),
                contentType: 'application/json',
                headers: {
                    RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        // Add new risk to both dropdowns
                        var newOption = new Option(response.naziv, response.id, true, true);
                        $('#rizikSelect').append(newOption).trigger('change');
                        
                        // Add to comparison select if it exists
                        if ($('#riskComparisonSelect').length) {
                            var newComparisonOption = new Option(response.naziv, response.id);
                            $('#riskComparisonSelect').append(newComparisonOption);
                        }
                        
                        // Clear inputs and close modal
                        $('#newRizikNaziv').val('');
                        $('#newRizikTip').val('');
                        $('#newRizikProdukt').val('');
                        addRizikModal.hide();
                    } else {
                        alert(response.message || 'Грешка при зачувување на ризик');
                    }
                },
                error: function() {
                    alert('Грешка при зачувување на ризик');
                },
                complete: function() {
                    // Re-enable the save button
                    $('#saveNewRizik').prop('disabled', false);
                }
            });
        });

        // Single event handler for showing modal
        $('#addRizikBtn').off('click').on('click', function() {
            addRizikModal.show();
        });
    });
    </script>

    <script>
        // ... existing code ...

        function updateSaveButtonState() {
            // Get class and product values using correct IDs
            const klasaValue = $('#Input_SelectedKlasaId').val();
            const produktValue = $('#Input_SelectedProduktId').val();
            const klasaSelected = klasaValue !== null && klasaValue !== '' && klasaValue !== undefined;
            const produktSelected = produktValue !== null && produktValue !== '' && produktValue !== undefined;

            // Email and Ime/Naziv validation
            const emailValue = $('#Input_Email').val();
            const imeValue = $('#Input_Ime').val();
            const nazivValue = $('#Input_Naziv').val();
            const emailValid = emailValue && emailValue.trim() !== '';
            const imeOrNazivValid = (imeValue && imeValue.trim() !== '') || (nazivValue && nazivValue.trim() !== '');
            
            // Count selected insurers
            const selectedOsiguriteli = [];
            $('select[name^="Input.Osiguriteli"]').each(function() {
                const value = $(this).val();
                if (value && value !== '' && value !== '0') {
                    selectedOsiguriteli.push(value);
                }
            });
            const osiguriteliSelected = selectedOsiguriteli.length >= 2;

            // Count selected risks from the comparison select
            const selectedRizici = $('#riskComparisonSelect').val() || [];
            const riziciSelected = selectedRizici.length > 0;

            console.log('=== Validation State ===');
            console.log('Klasa selected:', klasaSelected, 'Value:', klasaValue);
            console.log('Produkt selected:', produktSelected, 'Value:', produktValue);
            console.log('Email valid:', emailValid, 'Value:', emailValue);
            console.log('Ime or Naziv valid:', imeOrNazivValid, 'Ime:', imeValue, 'Naziv:', nazivValue);
            console.log('Osiguriteli selected:', osiguriteliSelected, 'Count:', selectedOsiguriteli.length, 'Values:', selectedOsiguriteli);
            console.log('Rizici selected:', riziciSelected, 'Count:', selectedRizici.length, 'Values:', selectedRizici);
            console.log('All selects found:', $('select').length);
            console.log('Osiguriteli selects found:', $('select[name^="Input.Osiguriteli"]').length);
            // Removed Kombinacii selects log since it's not used anymore

            const isEnabled = klasaSelected && produktSelected && osiguriteliSelected && riziciSelected && emailValid && imeOrNazivValid;
            console.log('Button enabled:', isEnabled);
            $('#zacuvajButton').prop('disabled', !isEnabled);
        }

        $(document).ready(function() {
            // ... existing code ...

            // Add event listeners for validation using correct IDs
            $('#Input_SelectedKlasaId').on('change', updateSaveButtonState);
            $('#Input_SelectedProduktId').on('change', updateSaveButtonState);
            $(document).on('change', 'select[name^="Input.Osiguriteli"]', updateSaveButtonState);
            $(document).on('change', 'select[name^="Input.Kombinacii"]', updateSaveButtonState);

            // Also check when risks are loaded
            $(document).on('risksLoaded', updateSaveButtonState);

            // Check periodically in case of dynamic content
            setInterval(updateSaveButtonState, 1000);

            // Initial validation
            updateSaveButtonState();
        });
        // ... existing code ...
    </script>
}
