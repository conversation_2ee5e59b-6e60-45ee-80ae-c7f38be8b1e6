@page
@model NextBroker.Pages.Polisi.DodajPolisaKlasa19Model
@{
    ViewData["Title"] = "Додај полиса Класа 19 - Животно осигурување";
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container-fluid mt-4 px-4">
    <!-- Add this at the top of the form, after the opening <form> tag -->
    <div class="alert alert-success alert-dismissible fade" role="alert" id="successMessage" style="display:none;">
	        <strong>Успешно!</strong> Полисата е успешно зачувана.
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <div class="alert alert-danger alert-dismissible fade" role="alert" id="errorMessage" style="display:none;">
        <strong>Грешка!</strong> <span id="errorText"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <form method="post" id="polisaForm" class="dodaj-polisa-form">
        @Html.AntiForgeryToken()
        
        <!-- Add validation summary -->
        <div asp-validation-summary="All" class="text-danger"></div>

        <div class="card mb-3">
            <div class="card-header">
                <h5 class="mb-0">Основни информации</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.KlientiIdOsiguritel" class="control-label"></label>
                            <select asp-for="Input.KlientiIdOsiguritel" class="form-control" asp-items="Model.Osiguriteli">
                                <option value="">-- Изберете осигурител --</option>
                            </select>
                            <span asp-validation-for="Input.KlientiIdOsiguritel" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.KlasiOsiguruvanjeIdKlasa" class="control-label"></label>
                            <select asp-for="Input.KlasiOsiguruvanjeIdKlasa" class="form-control" asp-items="Model.KlasiOsiguruvanje">
                                <option value="">-- Изберете класа --</option>
                            </select>
                            <span asp-validation-for="Input.KlasiOsiguruvanjeIdKlasa" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.ProduktiIdProizvod" class="control-label"></label>
                            <select asp-for="Input.ProduktiIdProizvod" class="form-control" asp-items="Model.Produkti">
                                <option value="">-- Изберете продукт --</option>
                            </select>
                            <span asp-validation-for="Input.ProduktiIdProizvod" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.BrojNaPolisa" class="control-label"></label>
                            <input asp-for="Input.BrojNaPolisa" class="form-control" id="brojNaPolisaInput" />
                            <span asp-validation-for="Input.BrojNaPolisa" class="text-danger"></span>
                            <span id="brojNaPolisaExistsError" class="text-danger" style="display:none;">Полиса со овој број веќе постои.</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.BrojNaPonuda" class="control-label"></label>
                            <input asp-for="Input.BrojNaPonuda" class="form-control" />
                            <span asp-validation-for="Input.BrojNaPonuda" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="form-check">
                            <input asp-for="Input.Kolektivna" class="form-check-input" type="checkbox" />
                            <label asp-for="Input.Kolektivna" class="form-check-label">Колективна</label>
                            <span asp-validation-for="Input.Kolektivna" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Договорувач (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" 
                                    data-target="dogovoruvac" 
                                    style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="dogovoruvacMBSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                            <input type="hidden" asp-for="Input.KlientiIdDogovoruvac" id="KlientiIdDogovoruvac" />
                        </div>
                        <div id="dogovoruvacSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div id="osigurenikContainer" class="row">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Осигуреник (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" 
                                    data-target="osigurenik" 
                                    style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="osigurenikMBSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                            <input type="hidden" asp-for="Input.KlientiIdOsigurenik" id="KlientiIdOsigurenik" />
                        </div>
                        <div id="osigurenikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Соработник/вработен (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" 
                                    data-target="sorabotnik" 
                                    style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="sorabotnikMBSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                            <input type="hidden" asp-for="Input.KlientiIdSorabotnik" id="KlientiIdSorabotnik" />
                        </div>
                        <div id="sorabotnikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-check mb-3">
                            <input type="checkbox" class="form-check-input" asp-for="Input.Faktoring" id="faktoring">
                            <label class="form-check-label" asp-for="Input.Faktoring">Факторинг</label>
                        </div>
                        <div class="form-check mb-3" style="display: none;">
                            <input type="checkbox" class="form-check-input" asp-for="Input.GeneriranaFakturaIzlezna" id="generiranaFakturaIzlezna">
                            <label class="form-check-label" asp-for="Input.GeneriranaFakturaIzlezna">Генерирана излезна фактура</label>
                        </div>
                        <!-- Commented out Сторно field
                        <div class="form-check mb-3">
                            <input type="checkbox" class="form-check-input" asp-for="Input.Storno" id="storno">
                            <label class="form-check-label" asp-for="Input.Storno">Сторно</label>
                        </div>
                        <div id="pricinaZaStornoContainer" class="mb-3" style="display: none;">
                            <label asp-for="Input.SifrarnikPricinaZaStornoId" class="form-label">Причина за сторно</label>
                            <select asp-for="Input.SifrarnikPricinaZaStornoId" 
                                    asp-items="Model.PriciniZaStorno" 
                                    class="form-select">
                                <option value="">-- Избери причина за сторно --</option>
                            </select>
                            <span asp-validation-for="Input.SifrarnikPricinaZaStornoId" class="text-danger"></span>
                        </div>
                        -->
                        <!-- Add hidden fields to ensure values are still submitted -->
                        <div style="display: none;">
                            <input asp-for="Input.Storno" type="hidden" value="false" />
                            <input asp-for="Input.SifrarnikPricinaZaStornoId" type="hidden" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-4">
                <div class="form-group">
                    <label asp-for="Input.VaziOd" class="control-label"></label>
                    <input asp-for="Input.VaziOd" class="form-control" type="date" />
                    <span asp-validation-for="Input.VaziOd" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label asp-for="Input.VaziDo" class="control-label"></label>
                    <input asp-for="Input.VaziDo" class="form-control" type="date" />
                    <span asp-validation-for="Input.VaziDo" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label asp-for="Input.DatumNaIzdavanje" class="control-label"></label>
                    <input asp-for="Input.DatumNaIzdavanje" class="form-control" type="date" />
                    <span asp-validation-for="Input.DatumNaIzdavanje" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="Input.ValutiId" class="control-label"></label>
                    <select asp-for="Input.ValutiId" class="form-select" asp-items="Model.Valuti">
                        <option value="">-- Избери валута --</option>
                    </select>
                    <span asp-validation-for="Input.ValutiId" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="Input.NaciniNaPlakanjeId" class="control-label"></label>
                    <select asp-for="Input.NaciniNaPlakanjeId" class="form-select" asp-items="Model.NaciniNaPlakanje">
                        <option value="">-- Избери начин на плаќање --</option>
                    </select>
                    <span asp-validation-for="Input.NaciniNaPlakanjeId" class="text-danger"></span>
                </div>
            </div>
            <!-- Commented out Тип на плаќање field
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="Input.TipoviNaPlakanjeId" class="control-label"></label>
                    <select asp-for="Input.TipoviNaPlakanjeId" class="form-select" asp-items="Model.TipoviNaPlakanje">
                        <option value="">-- Избери тип на плаќање --</option>
                    </select>
                    <span asp-validation-for="Input.TipoviNaPlakanjeId" class="text-danger"></span>
                </div>
            </div>
            -->
            <!-- Commented out Банка field
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="Input.BankiId" class="control-label"></label>
                    <select asp-for="Input.BankiId" class="form-select" asp-items="Model.Banki">
                        <option value="">-- Избери банка --</option>
                    </select>
                    <span asp-validation-for="Input.BankiId" class="text-danger"></span>
                </div>
            </div>
            -->
            <!-- Add hidden fields to ensure values are still submitted -->
            <div style="display: none;">
                <input asp-for="Input.TipoviNaPlakanjeId" type="hidden" value="1" />
                <input asp-for="Input.BankiId" type="hidden" />
            </div>
        </div>

        <!-- Add these fields after the existing fields -->
        <div class="row mt-3">
            <div class="col-md-4">
                <div class="form-group">
                    <label asp-for="Input.TipNaFaktura" class="control-label">Тип на фактура</label>
                    <select asp-for="Input.TipNaFaktura" 
                            asp-items="Model.TipoviNaFaktura" 
                            class="form-select">
                        <option value="">-- Избери тип на фактура --</option>
                    </select>
                    <span asp-validation-for="Input.TipNaFaktura" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-6" style="display: none;">
                <div class="form-group">
                    <label asp-for="Input.BrojNaVleznaFaktura" class="control-label">Број на влезна фактура</label>
                    <input asp-for="Input.BrojNaVleznaFaktura" class="form-control" />
                    <span asp-validation-for="Input.BrojNaVleznaFaktura" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-6" style="display: none;">
                <div class="form-group">
                    <label asp-for="Input.BrojNaIzleznaFaktura" class="control-label">Број на излезна фактура</label>
                    <input asp-for="Input.BrojNaIzleznaFaktura" class="form-control" />
                    <span asp-validation-for="Input.BrojNaIzleznaFaktura" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-6" style="display: none;">
                <div class="form-group">
                    <label asp-for="Input.DatumNaVleznaFaktura" class="control-label">Датум на влезна фактура</label>
                    <input asp-for="Input.DatumNaVleznaFaktura" class="form-control" type="date" />
                    <span asp-validation-for="Input.DatumNaVleznaFaktura" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-6" style="display: none;">
                <div class="form-group">
                    <label asp-for="Input.DatumNaIzleznaFaktura" class="control-label">Датум на излезна фактура</label>
                    <input asp-for="Input.DatumNaIzleznaFaktura" class="form-control" type="date" />
                    <span asp-validation-for="Input.DatumNaIzleznaFaktura" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-6" style="display: none;">
                <div class="form-group">
                    <label asp-for="Input.RokNaPlakanjeVleznaFaktura" class="control-label">Рок на плаќање влезна фактура</label>
                    <input asp-for="Input.RokNaPlakanjeVleznaFaktura" class="form-control" type="date" />
                    <span asp-validation-for="Input.RokNaPlakanjeVleznaFaktura" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-6" style="display: none;">
                <div class="form-group">
                    <label asp-for="Input.RokNaPlakanjeIzleznaFaktura" class="control-label">Рок на плаќање излезна фактура</label>
                    <input asp-for="Input.RokNaPlakanjeIzleznaFaktura" class="form-control" type="date" />
                    <span asp-validation-for="Input.RokNaPlakanjeIzleznaFaktura" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="Input.SifrarnikValutiIdFranshizaValuta" class="control-label"></label>
                    <select asp-for="Input.SifrarnikValutiIdFranshizaValuta" 
                            class="form-select" 
                            asp-items="Model.Valuti">
                        <option value="">-- Изберете валута --</option>
                    </select>
                    <span asp-validation-for="Input.SifrarnikValutiIdFranshizaValuta" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="Input.ProcentFransiza" class="control-label">Процент франшиза</label>
                    <input asp-for="Input.ProcentFransiza" class="form-control" type="number" step="0.01" />
                    <span asp-validation-for="Input.ProcentFransiza" class="text-danger"></span>
                </div>
            </div>
                        <div class="col-md-3 mb-3">
                <label asp-for="Input.FranshizaIznos" class="form-label"></label>
                <div class="input-group">
                    <input asp-for="Input.FranshizaIznos" 
                           class="form-control decimal-input" 
                           type="number" 
                           step="0.0001" />
                </div>
                <span asp-validation-for="Input.FranshizaIznos" class="text-danger"></span>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-6">
                <div class="form-group">
                    <label asp-for="Input.KoregiranaStapkaNaProvizija" class="control-label">Корегирана стапка на провизија</label>
                    <input asp-for="Input.KoregiranaStapkaNaProvizija" class="form-control" type="number" step="0.01" />
                    <span asp-validation-for="Input.KoregiranaStapkaNaProvizija" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="card mb-3 mt-4">
            <div class="card-header">
                <h5 class="mb-0">Осигурување Живот</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.ListaDejnostiId" class="control-label"></label>
                            <select asp-for="Input.ListaDejnostiId" class="form-select" asp-items="Model.ListaDejnosti">
                                <option value="">-- Избери дејност --</option>
                            </select>
                            <span asp-validation-for="Input.ListaDejnostiId" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.BrojNaOsigurenici" class="control-label"></label>
                            <input asp-for="Input.BrojNaOsigurenici" class="form-control" type="number" />
                            <span asp-validation-for="Input.BrojNaOsigurenici" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.PremijaZaEdnoLice" class="control-label"></label>
                            <input asp-for="Input.PremijaZaEdnoLice" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.PremijaZaEdnoLice" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <!-- Основно Осигурување Section -->
                <div class="accordion mt-3" id="osnovnoOsiguruvanjeAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="osnovnoOsiguruvanjeHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#osnovnoOsiguruvanjeCollapse" aria-expanded="true" aria-controls="osnovnoOsiguruvanjeCollapse">
                                Основно Осигурување
                            </button>
                        </h2>
                        <div id="osnovnoOsiguruvanjeCollapse" class="accordion-collapse collapse show" aria-labelledby="osnovnoOsiguruvanjeHeading" data-bs-parent="#osnovnoOsiguruvanjeAccordion">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label asp-for="Input.OsigurenaSumaZaDozhivuvanje" class="control-label"></label>
                                            <input asp-for="Input.OsigurenaSumaZaDozhivuvanje" class="form-control" type="number" step="0.0001" />
                                            <span asp-validation-for="Input.OsigurenaSumaZaDozhivuvanje" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label asp-for="Input.OsigurenaSumaZaSmrtOdBolest" class="control-label"></label>
                                            <input asp-for="Input.OsigurenaSumaZaSmrtOdBolest" class="form-control" type="number" step="0.0001" />
                                            <span asp-validation-for="Input.OsigurenaSumaZaSmrtOdBolest" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label asp-for="Input.OsigurenaSumaZaSmrtOdNezgoda" class="control-label"></label>
                                            <input asp-for="Input.OsigurenaSumaZaSmrtOdNezgoda" class="form-control" type="number" step="0.0001" />
                                            <span asp-validation-for="Input.OsigurenaSumaZaSmrtOdNezgoda" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label asp-for="Input.PrivremenaOsiguritelnaZastita" class="control-label"></label>
                                            <input asp-for="Input.PrivremenaOsiguritelnaZastita" class="form-control" type="number" step="0.0001" />
                                            <span asp-validation-for="Input.PrivremenaOsiguritelnaZastita" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label asp-for="Input.PremijaGodishnaEdnokratna" class="control-label"></label>
                                            <input asp-for="Input.PremijaGodishnaEdnokratna" class="form-control" type="number" step="0.0001" />
                                            <span asp-validation-for="Input.PremijaGodishnaEdnokratna" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Дополнително Осигурување Section -->
                <div class="accordion mt-3" id="dopolnitelnoOsiguruvanjeAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="dopolnitelnoOsiguruvanjeHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#dopolnitelnoOsiguruvanjeCollapse" aria-expanded="true" aria-controls="dopolnitelnoOsiguruvanjeCollapse">
                                Дополнително Осигурување
                            </button>
                        </h2>
                        <div id="dopolnitelnoOsiguruvanjeCollapse" class="accordion-collapse collapse show" aria-labelledby="dopolnitelnoOsiguruvanjeHeading" data-bs-parent="#dopolnitelnoOsiguruvanjeAccordion">
                            <div class="accordion-body">
                                <!-- Класа 1 Section -->
                                <div class="accordion" id="klasa1Accordion">
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="klasa1Heading">
                                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#klasa1Collapse" aria-expanded="true" aria-controls="klasa1Collapse">
                                                Класа 1
                                            </button>
                                        </h2>
                                        <div id="klasa1Collapse" class="accordion-collapse collapse show" aria-labelledby="klasa1Heading" data-bs-parent="#klasa1Accordion">
                                            <div class="accordion-body">
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label asp-for="Input.OsigurenaSumaZa100PercentTraenInvaliditet" class="control-label"></label>
                                                            <input asp-for="Input.OsigurenaSumaZa100PercentTraenInvaliditet" class="form-control" type="number" step="0.0001" />
                                                            <span asp-validation-for="Input.OsigurenaSumaZa100PercentTraenInvaliditet" class="text-danger"></span>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label asp-for="Input.OsigurenaSumaZaTraenInvaliditet" class="control-label"></label>
                                                            <input asp-for="Input.OsigurenaSumaZaTraenInvaliditet" class="form-control" type="number" step="0.0001" />
                                                            <span asp-validation-for="Input.OsigurenaSumaZaTraenInvaliditet" class="text-danger"></span>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label asp-for="Input.DnevenNadomest" class="control-label"></label>
                                                            <input asp-for="Input.DnevenNadomest" class="form-control" type="number" step="0.0001" />
                                                            <span asp-validation-for="Input.DnevenNadomest" class="text-danger"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row mt-3">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label asp-for="Input.PremijaNezgodaGodishna" class="control-label"></label>
                                                            <input asp-for="Input.PremijaNezgodaGodishna" class="form-control" type="number" step="0.0001" />
                                                            <span asp-validation-for="Input.PremijaNezgodaGodishna" class="text-danger"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Класа 2 Section -->
                                <div class="accordion mt-3" id="klasa2Accordion">
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="klasa2Heading">
                                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#klasa2Collapse" aria-expanded="true" aria-controls="klasa2Collapse">
                                                Класа 2
                                            </button>
                                        </h2>
                                        <div id="klasa2Collapse" class="accordion-collapse collapse show" aria-labelledby="klasa2Heading" data-bs-parent="#klasa2Accordion">
                                            <div class="accordion-body">
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label asp-for="Input.OsigurenaSumaZaTeskoBolniSostojbi" class="control-label"></label>
                                                            <input asp-for="Input.OsigurenaSumaZaTeskoBolniSostojbi" class="form-control" type="number" step="0.0001" />
                                                            <span asp-validation-for="Input.OsigurenaSumaZaTeskoBolniSostojbi" class="text-danger"></span>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label asp-for="Input.PremijaTeshkoBolniSostojbiGodishna" class="control-label"></label>
                                                            <input asp-for="Input.PremijaTeshkoBolniSostojbiGodishna" class="form-control" type="number" step="0.0001" />
                                                            <span asp-validation-for="Input.PremijaTeshkoBolniSostojbiGodishna" class="text-danger"></span>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label asp-for="Input.OsigurenaSumaZaOperacii" class="control-label"></label>
                                                            <input asp-for="Input.OsigurenaSumaZaOperacii" class="form-control" type="number" step="0.0001" />
                                                            <span asp-validation-for="Input.OsigurenaSumaZaOperacii" class="text-danger"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row mt-3">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label asp-for="Input.PremijaZaOperaciiGodishna" class="control-label"></label>
                                                            <input asp-for="Input.PremijaZaOperaciiGodishna" class="form-control" type="number" step="0.0001" />
                                                            <span asp-validation-for="Input.PremijaZaOperaciiGodishna" class="text-danger"></span>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label asp-for="Input.OsigurenaSumazaTrajnaNesposobnost" class="control-label"></label>
                                                            <input asp-for="Input.OsigurenaSumazaTrajnaNesposobnost" class="form-control" type="number" step="0.0001" />
                                                            <span asp-validation-for="Input.OsigurenaSumazaTrajnaNesposobnost" class="text-danger"></span>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label asp-for="Input.PremijaZaTrajnaNesposobnostGodishna" class="control-label"></label>
                                                            <input asp-for="Input.PremijaZaTrajnaNesposobnostGodishna" class="form-control" type="number" step="0.0001" />
                                                            <span asp-validation-for="Input.PremijaZaTrajnaNesposobnostGodishna" class="text-danger"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row mt-3">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label asp-for="Input.OsigurenaSumaZaHirushkiIntervencii" class="control-label"></label>
                                                            <input asp-for="Input.OsigurenaSumaZaHirushkiIntervencii" class="form-control" type="number" step="0.0001" />
                                                            <span asp-validation-for="Input.OsigurenaSumaZaHirushkiIntervencii" class="text-danger"></span>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label asp-for="Input.PremijaZaHirushkiIntervenciiGodishna" class="control-label"></label>
                                                            <input asp-for="Input.PremijaZaHirushkiIntervenciiGodishna" class="form-control" type="number" step="0.0001" />
                                                            <span asp-validation-for="Input.PremijaZaHirushkiIntervenciiGodishna" class="text-danger"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row mt-3">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label asp-for="Input.VkupnaPremijaGodishna" class="control-label"></label>
                                                            <input asp-for="Input.VkupnaPremijaGodishna" class="form-control" type="number" step="0.0001" />
                                                            <span asp-validation-for="Input.VkupnaPremijaGodishna" class="text-danger"></span>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label asp-for="Input.VkupnaPremijaEdnokratna" class="control-label"></label>
                                                            <input asp-for="Input.VkupnaPremijaEdnokratna" class="form-control" type="number" step="0.0001" />
                                                            <span asp-validation-for="Input.VkupnaPremijaEdnokratna" class="text-danger"></span>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label asp-for="Input.DoplatociZaPodgodishnoPlakjanje" class="control-label"></label>
                                                            <input asp-for="Input.DoplatociZaPodgodishnoPlakjanje" class="form-control" type="number" step="0.0001" />
                                                            <span asp-validation-for="Input.DoplatociZaPodgodishnoPlakjanje" class="text-danger"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.VkupnaPremija" class="control-label">Вкупна премија</label>
                            <input asp-for="Input.VkupnaPremija" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="Input.VkupnaPremija" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        
        
                    <!-- Popusti Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Попусти</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentNaPopustZaFakturaVoRok" class="form-label">Процент на попуст за фактура во рок</label>
                        <input asp-for="Input.ProcentNaPopustZaFakturaVoRok" class="form-control decimal-input" type="number" step="0.01" />
                        <span asp-validation-for="Input.ProcentNaPopustZaFakturaVoRok" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.IznosZaPlakjanjeVoRok" class="form-label">Износ за плаќање во рок</label>
                        <input asp-for="Input.IznosZaPlakjanjeVoRok" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.IznosZaPlakjanjeVoRok" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentKomercijalenPopust" class="form-label">Процент комерцијален попуст</label>
                        <input asp-for="Input.ProcentKomercijalenPopust" class="form-control decimal-input" type="number" step="0.01" />
                        <span asp-validation-for="Input.ProcentKomercijalenPopust" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentFinansiski" class="form-label">Процент финансиски</label>
                        <input asp-for="Input.ProcentFinansiski" class="form-control decimal-input" type="number" step="0.01" />
                        <span asp-validation-for="Input.ProcentFinansiski" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.PremijaZaNaplata" class="form-label">Премија за наплата</label>
                        <input asp-for="Input.PremijaZaNaplata" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.PremijaZaNaplata" class="text-danger"></span>
                    </div>
                </div>
            </div>
        </div>


        <!-- Add this before the closing </form> tag -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Зачувај полиса
                    </button>
                </div>
            </div>
        </div>
    </form>

    <!-- Add this after the form, before the Scripts section -->
    <div class="alert alert-success alert-dismissible fade" role="alert" id="successMessage" style="display:none;">
	        <strong>Успешно!</strong> Полисата е успешно зачувана.
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
</div>

@section Styles {
    <style>
        #dogovoruvacSearchResults .list-group-item.text-center,
        #osigurenikSearchResults .list-group-item.text-center,
        #sorabotnikSearchResults .list-group-item.text-center {
            padding: 1rem;
        }
        #dogovoruvacSearchResults .list-group-item.text-center p,
        #osigurenikSearchResults .list-group-item.text-center p,
        #sorabotnikSearchResults .list-group-item.text-center p {
            color: #6c757d;
            margin-bottom: 0.75rem;
        }
        #dogovoruvacSearchResults .btn-primary,
        #osigurenikSearchResults .btn-primary,
        #sorabotnikSearchResults .btn-primary {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }

        /* Custom accordion styles */
        .accordion-button:not(.collapsed) {
            background-color: #f8f9fa;
            color: #212529;
        }
        .accordion-button:focus {
            box-shadow: none;
            border-color: rgba(0,0,0,.125);
        }
        .accordion-button:not(.collapsed)::after {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
        }
    </style>
}

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        // Define the function globally
        function openAddClientWindow(sourceField) {
            const width = 800;
            const height = 600;
            const left = (window.screen.width - width) / 2;
            const top = (window.screen.height - height) / 2;
            
            const popup = window.open(`/Klienti/DodajKlient?fromPolisa=true&source=${sourceField}`, 'DodajKlient', 
                `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`);
                
            // Listen for messages from the popup
            window.addEventListener('message', function(event) {
                if (event.data.type === 'clientAdded') {
                    // Clear the search field based on the source
                    if (event.data.source === 'dogovoruvac') {
                        $('#dogovoruvacMBSearch').val('');
                        $('#KlientiIdDogovoruvac').val('');
                        $('#dogovoruvacSearchResults').hide();
                    } else if (event.data.source === 'osigurenik') {
                        $('#osigurenikMBSearch').val('');
                        $('#KlientiIdOsigurenik').val('');
                        $('#osigurenikSearchResults').hide();
                    } else if (event.data.source === 'sorabotnik') {
                        $('#sorabotnikMBSearch').val('');
                        $('#KlientiIdSorabotnik').val('');
                        $('#sorabotnikSearchResults').hide();
                    }
                }
            });
        }

        $(document).ready(function() {
            // Function to create search functionality
            function createSearchFunctionality(searchInputId, resultsContainerId, hiddenInputId, searchHandler = 'SearchKlienti') {
                let searchTimeout;
                const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();

                $(`#${searchInputId}`).on('input', function() {
                    clearTimeout(searchTimeout);
                    const searchTerm = $(this).val();
                    const resultsDiv = $(`#${resultsContainerId}`);

                    if (searchTerm.length < 1) {
                        resultsDiv.hide();
                        return;
                    }

                    searchTimeout = setTimeout(function() {
                        $.ajax({
                            url: `?handler=${searchHandler}`,
                            type: 'GET',
                            data: { mb: searchTerm },
                            headers: {
                                "RequestVerificationToken": antiForgeryToken
                            },
                            success: function(data) {
                                if (data && data.length > 0) {
                                    let html = '<div class="list-group">';
                                    data.forEach(item => {
                                        let displayText = '';
                                        if (item.tip === 'P') {
                                            displayText = `${item.naziv}`;
                                            let identifiers = [];
                                            if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                            if (item.edb) identifiers.push(`ЕДБ: ${item.edb}`);
                                            if (identifiers.length > 0) {
                                                displayText += ` (${identifiers.join(', ')})`;
                                            }
                                        } else {
                                            displayText = `${item.ime} ${item.prezime}`;
                                            let identifiers = [];
                                            if (item.embg) identifiers.push(`ЕМБГ: ${item.embg}`);
                                            if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                            if (identifiers.length > 0) {
                                                displayText += ` (${identifiers.join(', ')})`;
                                            }
                                        }
                                        html += `<a href="#" class="list-group-item list-group-item-action" data-id="${item.id}">
                                                  ${displayText}
                                               </a>`;
                                    });
                                    html += '</div>';
                                    resultsDiv.html(html).show();
                                } else {
                                    resultsDiv.html(`
                                        <div class="list-group">
                                            <div class="list-group-item text-center">
                                                <p class="mb-2">Нема пронајдени резултати</p>
                                                <button type="button" class="btn btn-primary" onclick="openAddClientWindow('${
                                                    searchInputId === 'dogovoruvacMBSearch' ? 'dogovoruvac' : 
                                                    searchInputId === 'osigurenikMBSearch' ? 'osigurenik' : 'sorabotnik'
                                                }')">
                                                    <i class="fas fa-plus"></i> Додај клиент
                                                </button>
                                            </div>
                                        </div>
                                    `).show();
                                }
                            },
                            error: function() {
                                resultsDiv.html(`
                                    <div class="list-group">
                                        <div class="list-group-item text-center">
                                            <p class="text-danger mb-2">Грешка при пребарување</p>
                                            <button type="button" class="btn btn-primary" onclick="openAddClientWindow('${
                                                searchInputId === 'dogovoruvacMBSearch' ? 'dogovoruvac' : 
                                                searchInputId === 'osigurenikMBSearch' ? 'osigurenik' : 'sorabotnik'
                                            }')">
                                                <i class="fas fa-plus"></i> Додај клиент
                                            </button>
                                        </div>
                                    </div>
                                `).show();
                            }
                        });
                    }, 300);
                });

                // Handle selection
                $(document).on('click', `#${resultsContainerId} .list-group-item`, function(e) {
                    e.preventDefault();
                    const id = $(this).data('id');
                    const displayText = $(this).text();
                    $(`#${searchInputId}`).val(displayText.trim());
                    $(`#${hiddenInputId}`).val(id);
                    $(`#${resultsContainerId}`).hide();
                });

                // Hide results when clicking outside
                $(document).on('click', function(e) {
                    if (!$(e.target).closest(`#${searchInputId}, #${resultsContainerId}`).length) {
                        $(`#${resultsContainerId}`).hide();
                    }
                });
            }

            // Initialize search for all fields
            createSearchFunctionality('dogovoruvacMBSearch', 'dogovoruvacSearchResults', 'KlientiIdDogovoruvac');
            createSearchFunctionality('osigurenikMBSearch', 'osigurenikSearchResults', 'KlientiIdOsigurenik');
            createSearchFunctionality('sorabotnikMBSearch', 'sorabotnikSearchResults', 'KlientiIdSorabotnik', 'SearchSorabotnici');

            // Update click handlers for clear buttons
            $('.clear-field').click(function() {
                const target = $(this).data('target');
                switch(target) {
                    case 'dogovoruvac':
                        $('#dogovoruvacMBSearch').val('');
                        $('#KlientiIdDogovoruvac').val('');
                        $('#dogovoruvacSearchResults').hide();
                        break;
                    case 'osigurenik':
                        $('#osigurenikMBSearch').val('');
                        $('#KlientiIdOsigurenik').val('');
                        $('#osigurenikSearchResults').hide();
                        break;
                    case 'sorabotnik':
                        $('#sorabotnikMBSearch').val('');
                        $('#KlientiIdSorabotnik').val('');
                        $('#sorabotnikSearchResults').hide();
                        break;
                }
            });

            // Add this at the beginning of the document.ready function
            function toggleOsigurenikField() {
                if ($('#Input_Kolektivna').is(':checked')) {
                    $('#osigurenikContainer').hide();
                    // Clear the osigurenik field when hiding
                    $('#osigurenikMBSearch').val('');
                    $('#KlientiIdOsigurenik').val('');
                    $('#osigurenikSearchResults').hide();
                } else {
                    $('#osigurenikContainer').show();
                }
            }

            // Initial state check
            toggleOsigurenikField();

            // Add change event handler for the checkbox
            $('#Input_Kolektivna').change(function() {
                toggleOsigurenikField();
            });

            /* Commented out because the Storno checkbox has been hidden
            // Add this for Storno checkbox handling
            function togglePricinaZaStorno() {
                if ($('#storno').is(':checked')) {
                    $('#pricinaZaStornoContainer').show();
                } else {
                    $('#pricinaZaStornoContainer').hide();
                    $('#Input_SifrarnikPricinaZaStornoId').val('');
                }
            }

            // Initial state check for Storno
            togglePricinaZaStorno();

            // Add change event handler for Storno checkbox
            $('#storno').change(function() {
                togglePricinaZaStorno();
            });
            */

            // Initialize select2 for PricinaZaStorno dropdown
            $('#Input_SifrarnikPricinaZaStornoId').select2({
                width: '100%'
            });

            // Add custom validation rules
            $.validator.setDefaults({
                ignore: []
            });

            // Mark BrojNaVleznaFaktura as optional
            $('#Input_BrojNaVleznaFaktura').rules('remove', 'required');

            // Add form submit handler
            $('#polisaForm').submit(function(e) {
                // Don't prevent default - let the form submit normally
                
                // Show success message
                $('#successMessage, #bottomSuccessMessage')
                    .show()
                    .addClass('show');
                
                // Disable submit button
                $(this).find('button[type="submit"]').prop('disabled', true);
                
                // Let form submit proceed
                return true;
            });

            // Function to calculate total premium
            function calculateTotalPremium() {
                const isKolektivna = $('#Input_Kolektivna').is(':checked');
                const brojNaOsigurenici = parseFloat($('#Input_BrojNaOsigurenici').val()) || 0;
                
                // Get all the premium components
                let vkupnaPremijaGodishna = parseFloat($('#Input_VkupnaPremijaGodishna').val()) || 0;
                let vkupnaPremijaEdnokratna = parseFloat($('#Input_VkupnaPremijaEdnokratna').val()) || 0;
                let doplatociZaPodgodishnoPlakjanje = parseFloat($('#Input_DoplatociZaPodgodishnoPlakjanje').val()) || 0;
                
                // Calculate total premium
                let vkupnaPremija = vkupnaPremijaGodishna + vkupnaPremijaEdnokratna + doplatociZaPodgodishnoPlakjanje;

                // If collective insurance is selected, multiply total premium by number of insured persons
                if (isKolektivna) {
                    vkupnaPremija = vkupnaPremija * brojNaOsigurenici;
                }
                
                // Update VkupnaPremija field
                $('#Input_VkupnaPremija').val(vkupnaPremija.toFixed(4));

                // Calculate PremijaZaNaplata based on discounts
                let procentKomercijalenPopust = parseFloat($('#Input_ProcentKomercijalenPopust').val()) || 0;
                let procentFinansiski = parseFloat($('#Input_ProcentFinansiski').val()) || 0;
                let procentNaPopustZaFakturaVoRok = parseFloat($('#Input_ProcentNaPopustZaFakturaVoRok').val()) || 0;

                // Apply discounts
                let premijaZaNaplata = vkupnaPremija;
                premijaZaNaplata *= (1 - (procentKomercijalenPopust / 100));
                premijaZaNaplata *= (1 - (procentFinansiski / 100));
                premijaZaNaplata *= (1 - (procentNaPopustZaFakturaVoRok / 100));

                // Update PremijaZaNaplata field
                $('#Input_PremijaZaNaplata').val(premijaZaNaplata.toFixed(4));

                // Calculate IznosZaPlakjanjeVoRok
                let iznosZaPlakjanjeVoRok = premijaZaNaplata;
                $('#Input_IznosZaPlakjanjeVoRok').val(iznosZaPlakjanjeVoRok.toFixed(4));
            }

            // Add event listeners to all premium-related fields
            $('#Input_VkupnaPremijaGodishna, #Input_VkupnaPremijaEdnokratna, #Input_DoplatociZaPodgodishnoPlakjanje, ' +
              '#Input_ProcentKomercijalenPopust, #Input_ProcentFinansiski, #Input_ProcentNaPopustZaFakturaVoRok, ' +
              '#Input_Kolektivna, #Input_BrojNaOsigurenici')
                .on('input change', calculateTotalPremium);

            // Initial calculation
            calculateTotalPremium();
        });
    </script>
    <script>
 // Add this at the very end
        function checkPolicyNumber() {
            var brojNaPolisa = $('#brojNaPolisaInput').val();
            if (brojNaPolisa) {
                $.ajax({
                    url: '?handler=CheckBrojNaPolisa',
                    type: 'GET',
                    data: { brojNaPolisa: brojNaPolisa },
                    headers: {
                        "RequestVerificationToken": 
                            $('input:hidden[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result > 0) {
                            $('#brojNaPolisaExistsError').show();
                        } else {
                            $('#brojNaPolisaExistsError').hide();
                        }
                    },
                    error: function() {
                        $('#brojNaPolisaExistsError').hide();
                    }
                });
            } else {
                $('#brojNaPolisaExistsError').hide();
            }
        }

        // Attach the event handler after page load
        $(function() {
            $('#brojNaPolisaInput').on('blur', checkPolicyNumber);
        });
    </script>

       <script type="text/javascript">
        // Define the validation function
        function validateDates() {
            var vaziOd = $('#Input_VaziOd').val();
            var vaziDo = $('#Input_VaziDo').val();
            
            if (vaziOd && vaziDo) {
                var dateVaziOd = new Date(vaziOd);
                var dateVaziDo = new Date(vaziDo);
                
                if (dateVaziDo < dateVaziOd) {
                    // Add error message if it doesn't exist
                    if (!$('#vaziDoError').length) {
                        $('#Input_VaziDo').after('<span id="vaziDoError" class="text-danger">Датумот "Важи до" не може да биде пред "Важи од"</span>');
                    }
                    // Set VaziDo to VaziOd
                    $('#Input_VaziDo').val(vaziOd);
                    return false;
                } else {
                    // Remove error message if exists
                    $('#vaziDoError').remove();
                    return true;
                }
            }
            return true;
        }

        // Attach the event handler after page load
        $(function() {
            $('#Input_VaziOd, #Input_VaziDo').on('blur change', validateDates);
            
            // Run initial validation
            validateDates();
        });
    </script>

    <script type="text/javascript">
        // Premium calculation script
        $(document).ready(function() {
            console.log("Premium calculation script loading...");
            
            function calculateTotalPremium() {
                const isKolektivna = $('#Input_Kolektivna').is(':checked');
                const brojNaOsigurenici = parseFloat($('#Input_BrojNaOsigurenici').val()) || 0;
                
                // Get all the premium components
                let vkupnaPremijaGodishna = parseFloat($('#Input_VkupnaPremijaGodishna').val()) || 0;
                let vkupnaPremijaEdnokratna = parseFloat($('#Input_VkupnaPremijaEdnokratna').val()) || 0;
                let doplatociZaPodgodishnoPlakjanje = parseFloat($('#Input_DoplatociZaPodgodishnoPlakjanje').val()) || 0;
                
                // Calculate total premium
                let vkupnaPremija = vkupnaPremijaGodishna + vkupnaPremijaEdnokratna + doplatociZaPodgodishnoPlakjanje;

                // If collective insurance is selected, multiply total premium by number of insured persons
                if (isKolektivna) {
                    vkupnaPremija = vkupnaPremija * brojNaOsigurenici;
                }
                
                // Update VkupnaPremija field
                $('#Input_VkupnaPremija').val(vkupnaPremija.toFixed(4));

                // Calculate PremijaZaNaplata based on discounts
                let procentKomercijalenPopust = parseFloat($('#Input_ProcentKomercijalenPopust').val()) || 0;
                let procentFinansiski = parseFloat($('#Input_ProcentFinansiski').val()) || 0;
                let procentNaPopustZaFakturaVoRok = parseFloat($('#Input_ProcentNaPopustZaFakturaVoRok').val()) || 0;

                // Apply discounts
                let premijaZaNaplata = vkupnaPremija;
                premijaZaNaplata *= (1 - (procentKomercijalenPopust / 100));
                premijaZaNaplata *= (1 - (procentFinansiski / 100));
                premijaZaNaplata *= (1 - (procentNaPopustZaFakturaVoRok / 100));

                // Update PremijaZaNaplata field
                $('#Input_PremijaZaNaplata').val(premijaZaNaplata.toFixed(4));

                // Calculate IznosZaPlakjanjeVoRok
                let iznosZaPlakjanjeVoRok = premijaZaNaplata;
                $('#Input_IznosZaPlakjanjeVoRok').val(iznosZaPlakjanjeVoRok.toFixed(4));
            }

            // Add event listeners to all premium-related fields
            $('#Input_VkupnaPremijaGodishna, #Input_VkupnaPremijaEdnokratna, #Input_DoplatociZaPodgodishnoPlakjanje, ' +
              '#Input_ProcentKomercijalenPopust, #Input_ProcentFinansiski, #Input_ProcentNaPopustZaFakturaVoRok, ' +
              '#Input_Kolektivna, #Input_BrojNaOsigurenici')
                .on('input change', calculateTotalPremium);

            // Initial calculation
            calculateTotalPremium();
        });
    </script>

    <!-- Add new script block for Produkt and Kolektivna handling -->
    <script type="text/javascript">
        // Function to handle Kolektivna checkbox based on Produkt selection
        function handleKolektivnaBasedOnProdukt() {
            const produktSelect = document.getElementById('Input_ProduktiIdProizvod');
            const kolektivnaCheckbox = document.getElementById('Input_Kolektivna');
            const osigurenikSearch = document.getElementById('osigurenikMBSearch');
            const osigurenikLabel = document.querySelector('label[for="osigurenikMBSearch"]');
            const brojOsigureniciInput = document.getElementById('Input_BrojNaOsigurenici');
            
            if (!produktSelect || !kolektivnaCheckbox) return;

            const selectedProduktId = parseInt(produktSelect.value);

            // Set default value and min for BrojNaOsigurenici
            if (brojOsigureniciInput) {
                brojOsigureniciInput.value = brojOsigureniciInput.value || '1';
                brojOsigureniciInput.min = '1';
            }

            if (selectedProduktId === 35 || selectedProduktId === 37) {
                kolektivnaCheckbox.checked = false;
                kolektivnaCheckbox.readOnly = true;
                kolektivnaCheckbox.disabled = true;
                // Ensure the hidden value is set to 0
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = 'Input.Kolektivna';
                hiddenInput.value = 'false';
                kolektivnaCheckbox.parentNode.appendChild(hiddenInput);
                // Trigger change event
                $(kolektivnaCheckbox).trigger('change');

                // Make Осигуреник field required
                if (osigurenikSearch) {
                    osigurenikSearch.required = true;
                    if (osigurenikLabel && !osigurenikLabel.innerHTML.includes('*')) {
                        osigurenikLabel.innerHTML += ' <span class="text-danger">*</span>';
                    }
                }

                // Handle BrojNaOsigurenici field
                if (brojOsigureniciInput) {
                    brojOsigureniciInput.value = '1';
                    brojOsigureniciInput.readOnly = true;
                    brojOsigureniciInput.style.backgroundColor = '#e9ecef';
                    // Add hidden input to ensure value is passed
                    const hiddenBrojInput = document.createElement('input');
                    hiddenBrojInput.type = 'hidden';
                    hiddenBrojInput.name = 'Input.BrojNaOsigurenici';
                    hiddenBrojInput.value = '1';
                    brojOsigureniciInput.parentNode.appendChild(hiddenBrojInput);
                }
            } else if (selectedProduktId === 36) {
                kolektivnaCheckbox.checked = true;
                kolektivnaCheckbox.readOnly = true;
                kolektivnaCheckbox.disabled = true;
                // Ensure the hidden value is set to 1
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = 'Input.Kolektivna';
                hiddenInput.value = 'true';
                kolektivnaCheckbox.parentNode.appendChild(hiddenInput);
                // Trigger change event
                $(kolektivnaCheckbox).trigger('change');

                // Remove required from Осигуреник field
                if (osigurenikSearch) {
                    osigurenikSearch.required = false;
                    if (osigurenikLabel) {
                        osigurenikLabel.innerHTML = osigurenikLabel.innerHTML.replace(' <span class="text-danger">*</span>', '');
                    }
                }

                // Make BrojNaOsigurenici editable
                if (brojOsigureniciInput) {
                    brojOsigureniciInput.readOnly = false;
                    brojOsigureniciInput.style.backgroundColor = '';
                    // Remove any hidden inputs if they exist
                    const hiddenInputs = brojOsigureniciInput.parentNode.querySelectorAll('input[type="hidden"][name="Input.BrojNaOsigurenici"]');
                    hiddenInputs.forEach(input => input.remove());
                }
            } else {
                kolektivnaCheckbox.readOnly = false;
                kolektivnaCheckbox.disabled = false;
                // Remove any hidden inputs if they exist
                const hiddenInputs = kolektivnaCheckbox.parentNode.querySelectorAll('input[type="hidden"][name="Input.Kolektivna"]');
                hiddenInputs.forEach(input => input.remove());
                // Trigger change event
                $(kolektivnaCheckbox).trigger('change');

                // Remove required from Осигуреник field
                if (osigurenikSearch) {
                    osigurenikSearch.required = false;
                    if (osigurenikLabel) {
                        osigurenikLabel.innerHTML = osigurenikLabel.innerHTML.replace(' <span class="text-danger">*</span>', '');
                    }
                }

                // Make BrojNaOsigurenici editable
                if (brojOsigureniciInput) {
                    brojOsigureniciInput.readOnly = false;
                    brojOsigureniciInput.style.backgroundColor = '';
                    // Remove any hidden inputs if they exist
                    const hiddenInputs = brojOsigureniciInput.parentNode.querySelectorAll('input[type="hidden"][name="Input.BrojNaOsigurenici"]');
                    hiddenInputs.forEach(input => input.remove());
                }
            }
        }

        // Add event listener when DOM is fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            const produktSelect = document.getElementById('Input_ProduktiIdProizvod');
            const brojOsigureniciInput = document.getElementById('Input_BrojNaOsigurenici');

            // Set initial value and min for BrojNaOsigurenici
            if (brojOsigureniciInput) {
                brojOsigureniciInput.value = brojOsigureniciInput.value || '1';
                brojOsigureniciInput.min = '1';
                
                // Add input event listener to prevent negative values
                brojOsigureniciInput.addEventListener('input', function() {
                    const value = parseInt(this.value) || 1;
                    if (value < 1) {
                        this.value = '1';
                    }
                });
            }

            if (produktSelect) {
                // Run initial check
                handleKolektivnaBasedOnProdukt();
                
                // Add change event listener
                produktSelect.addEventListener('change', handleKolektivnaBasedOnProdukt);

                // Add form submit validation
                const form = produktSelect.closest('form');
                if (form) {
                    form.addEventListener('submit', function(e) {
                        const selectedProduktId = parseInt(produktSelect.value);
                        const osigurenikSearch = document.getElementById('osigurenikMBSearch');
                        const osigurenikId = document.getElementById('KlientiIdOsigurenik');

                        if ((selectedProduktId === 35 || selectedProduktId === 37) && 
                            osigurenikSearch && 
                            (!osigurenikSearch.value || !osigurenikId.value)) {
                            e.preventDefault();
                            alert('Полето Осигуреник е задолжително за избраниот продукт.');
                            osigurenikSearch.focus();
                        }
                    });
                }
            }
        });
    </script>

    <!-- Add new script block for premium calculations -->
    <script type="text/javascript">
        function calculatePremiums() {
            // Get all required input fields
            const brojOsigurenici = parseFloat(document.getElementById('Input_BrojNaOsigurenici').value) || 0;
            const premijaZaEdnoLice = parseFloat(document.getElementById('Input_PremijaZaEdnoLice').value) || 0;
            const isKolektivna = document.getElementById('Input_Kolektivna').checked;
            
            // Get all premium fields
            const premijaNezgodaGodishna = parseFloat(document.getElementById('Input_PremijaNezgodaGodishna').value) || 0;
            const premijaTeshkoBolniSostojbiGodishna = parseFloat(document.getElementById('Input_PremijaTeshkoBolniSostojbiGodishna').value) || 0;
            const premijaZaOperaciiGodishna = parseFloat(document.getElementById('Input_PremijaZaOperaciiGodishna').value) || 0;
            const premijaZaTrajnaNesposobnostGodishna = parseFloat(document.getElementById('Input_PremijaZaTrajnaNesposobnostGodishna').value) || 0;
            const premijaZaHirushkiIntervenciiGodishna = parseFloat(document.getElementById('Input_PremijaZaHirushkiIntervenciiGodishna').value) || 0;
            const premijaGodishnaEdnokratna = parseFloat(document.getElementById('Input_PremijaGodishnaEdnokratna').value) || 0;

            // Calculate base premium
            let vkupnaPremijaGodishna = premijaNezgodaGodishna +
                premijaTeshkoBolniSostojbiGodishna +
                premijaZaOperaciiGodishna +
                premijaZaTrajnaNesposobnostGodishna +
                premijaZaHirushkiIntervenciiGodishna +
                premijaGodishnaEdnokratna +
                premijaZaEdnoLice;

            // Set VkupnaPremijaGodishna
            document.getElementById('Input_VkupnaPremijaGodishna').value = vkupnaPremijaGodishna.toFixed(4);

            // Get additional values
            const vkupnaPremijaEdnokratna = parseFloat(document.getElementById('Input_VkupnaPremijaEdnokratna').value) || 0;
            const doplatociZaPodgodishnoPlakjanje = parseFloat(document.getElementById('Input_DoplatociZaPodgodishnoPlakjanje').value) || 0;

            // Calculate VkupnaPremija
            let vkupnaPremija = vkupnaPremijaGodishna + vkupnaPremijaEdnokratna + doplatociZaPodgodishnoPlakjanje;

            // If collective insurance is selected, multiply total premium by number of insured persons
            if (isKolektivna) {
                vkupnaPremija = vkupnaPremija * brojOsigurenici;
            }

            document.getElementById('Input_VkupnaPremija').value = vkupnaPremija.toFixed(4);

            // Get discount percentages
            const procentKomercijalenPopust = parseFloat(document.getElementById('Input_ProcentKomercijalenPopust').value) || 0;
            const procentFinansiski = parseFloat(document.getElementById('Input_ProcentFinansiski').value) || 0;
            const procentNaPopustZaFakturaVoRok = parseFloat(document.getElementById('Input_ProcentNaPopustZaFakturaVoRok').value) || 0;

            // Calculate discounted amounts
            let premijaZaNaplata = vkupnaPremija;
            premijaZaNaplata *= (1 - (procentKomercijalenPopust / 100));
            premijaZaNaplata *= (1 - (procentFinansiski / 100));
            premijaZaNaplata *= (1 - (procentNaPopustZaFakturaVoRok / 100));

            // Set PremijaZaNaplata and IznosZaPlakjanjeVoRok
            document.getElementById('Input_PremijaZaNaplata').value = premijaZaNaplata.toFixed(4);
            document.getElementById('Input_IznosZaPlakjanjeVoRok').value = premijaZaNaplata.toFixed(4);
        }

        // Add event listeners when DOM is fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            // List of all input fields that should trigger calculations
            const calculationFields = [
                'Input_BrojNaOsigurenici',
                'Input_PremijaZaEdnoLice',
                'Input_PremijaNezgodaGodishna',
                'Input_PremijaTeshkoBolniSostojbiGodishna',
                'Input_PremijaZaOperaciiGodishna',
                'Input_PremijaZaTrajnaNesposobnostGodishna',
                'Input_PremijaZaHirushkiIntervenciiGodishna',
                'Input_PremijaGodishnaEdnokratna',
                'Input_VkupnaPremijaEdnokratna',
                'Input_DoplatociZaPodgodishnoPlakjanje',
                'Input_ProcentKomercijalenPopust',
                'Input_ProcentFinansiski',
                'Input_ProcentNaPopustZaFakturaVoRok',
                'Input_Kolektivna'
            ];

            // Add input event listener to each field
            calculationFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    element.addEventListener('input', calculatePremiums);
                }
            });

            // Run initial calculation
            calculatePremiums();
        });
    </script>
} 