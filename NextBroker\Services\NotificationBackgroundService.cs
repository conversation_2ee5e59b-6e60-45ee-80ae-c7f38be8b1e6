using Microsoft.AspNetCore.SignalR;
using NextBroker.Hubs;

namespace NextBroker.Services
{
    public class NotificationBackgroundService : BackgroundService
    {
        private readonly IServiceProvider _services;
        private readonly IHubContext<NotificationHub> _hubContext;
        private Dictionary<string, int> _lastCountByUser = new();

        public NotificationBackgroundService(
            IServiceProvider services,
            IHubContext<NotificationHub> hubContext)
        {
            _services = services;
            _hubContext = hubContext;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                using (var scope = _services.CreateScope())
                {
                    var notificationService = scope.ServiceProvider.GetRequiredService<NotificationService>();
                    var users = await notificationService.GetAllUsers();
                    
                    foreach (var username in users)
                    {
                        var notifications = await notificationService.GetLatestNotifications(username);
                        var currentCount = notifications.Count();

                        if (!_lastCountByUser.ContainsKey(username))
                        {
                            _lastCountByUser[username] = currentCount;
                        }
                        else if (currentCount != _lastCountByUser[username])
                        {
                            _lastCountByUser[username] = currentCount;
                            await _hubContext.Clients.All.SendAsync("ReceiveNotifications", cancellationToken: stoppingToken);
                        }
                    }
                }

                await Task.Delay(3000, stoppingToken); // Check every 3 seconds
            }
        }
    }
} 