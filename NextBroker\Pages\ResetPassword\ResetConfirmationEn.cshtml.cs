using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;
using Microsoft.Data.SqlClient;
using System.Security.Cryptography;
using System.Text;

namespace RazorPortal.Pages
{
    public class ResetConfirmationEnModel : PageModel
    {
        private readonly IConfiguration _configuration;

        public ResetConfirmationEnModel(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        [BindProperty]
        [Required(ErrorMessage = "Required field!")]
        public string EMB { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "Required field!")]
        [RegularExpression(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\da-zA-Z]).{8,}$", 
            ErrorMessage = "Password must contain at least 8 characters, one uppercase letter, one number, and one special character.")]
        public string NewPassword { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "Required field!")]
        [Compare("NewPassword", ErrorMessage = "Passwords do not match.")]
        public string ConfirmPassword { get; set; }

        [BindProperty]
        [Required(ErrorMessage = "Required field!")]
        public string ResetRequest { get; set; }

        public string ErrorMessage { get; set; }
        public string SuccessMessage { get; set; }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            var connectionString = _configuration.GetConnectionString("DefaultConnection");

            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Check if the reset request code matches
                    string storedResetRequest = null;
                    using (var selectCommand = new SqlCommand("SELECT resetrequest FROM users WHERE emb = @emb", connection))
                    {
                        selectCommand.Parameters.AddWithValue("@emb", EMB);

                        var result = await selectCommand.ExecuteScalarAsync();
                        if (result == null)
                        {
                            ErrorMessage = "User with the provided ID number does not exist.";
                            return Page();
                        }

                        storedResetRequest = result.ToString();
                    }

                    if (storedResetRequest != ResetRequest)
                    {
                        ErrorMessage = "Invalid reset code.";
                        return Page();
                    }

                    // Generate random salt
                    string salt = GenerateSalt(10);

                    // Hash the new password
                    byte[] key;
                    byte[] iv;
                    string passwordHash = HashPasswordAES(NewPassword, salt, out key, out iv);

                    // Convert key and IV to Base64 for storage
                    string keyBase64 = Convert.ToBase64String(key);
                    string ivBase64 = Convert.ToBase64String(iv);

                    // Update the user's password
                    using (var updateCommand = new SqlCommand(@"
                        UPDATE users 
                        SET passwordhash = @passwordHash, passwordsalt = @passwordSalt, 
                            passwordkey = @passwordKey, passwordiv = @passwordIV, resetrequest = NULL 
                        WHERE emb = @emb", connection))
                    {
                        updateCommand.Parameters.AddWithValue("@passwordHash", passwordHash);
                        updateCommand.Parameters.AddWithValue("@passwordSalt", salt);
                        updateCommand.Parameters.AddWithValue("@passwordKey", keyBase64);
                        updateCommand.Parameters.AddWithValue("@passwordIV", ivBase64);
                        updateCommand.Parameters.AddWithValue("@emb", EMB);

                        await updateCommand.ExecuteNonQueryAsync();
                    }
                }

                SuccessMessage = "Your password has been successfully changed.";
                return Page();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"An error occurred: {ex.Message}";
                return Page();
            }
        }

        private string GenerateSalt(int length)
        {
            const string validChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(validChars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        private string HashPasswordAES(string password, string salt, out byte[] key, out byte[] iv)
        {
            using (var aes = Aes.Create())
            {
                aes.KeySize = 256;
                aes.BlockSize = 128;
                var keyGenerator = new Rfc2898DeriveBytes(password + salt, 16, 10000);
                key = keyGenerator.GetBytes(32);
                iv = keyGenerator.GetBytes(16);
                aes.Mode = CipherMode.CBC;

                using (var encryptor = aes.CreateEncryptor(key, iv))
                using (var ms = new MemoryStream())
                using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                using (var sw = new StreamWriter(cs))
                {
                    sw.Write(password);
                    sw.Flush();
                    cs.FlushFinalBlock();
                    return Convert.ToBase64String(ms.ToArray());
                }
            }
        }
    }
} 