using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Text;

namespace NextBroker.Pages.Pregledi
{
    public class IzvestajGeneriraniIzlezniFakturiKonKlientModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public IzvestajGeneriraniIzlezniFakturiKonKlientModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        [BindProperty]
        [Display(Name = "Датум од")]
        [DataType(DataType.Date)]
        public DateTime? DatumOd { get; set; }

        [BindProperty]
        [Display(Name = "Датум до")]
        [DataType(DataType.Date)]
        public DateTime? DatumDo { get; set; }

        public List<string> ColumnNames { get; set; } = new();
        public List<List<object>> Data { get; set; } = new();

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("IzvestajGeneriraniIzlezniFakturiKonKlient"))
            {
                return RedirectToAccessDenied();
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!await HasPageAccess("IzvestajGeneriraniIzlezniFakturiKonKlient"))
            {
                return RedirectToAccessDenied();
            }

            if (!DatumOd.HasValue || !DatumDo.HasValue)
            {
                ModelState.AddModelError("", "Датумите се задолжителни");
                return Page();
            }

            await LoadData();
            return Page();
        }

        public async Task<IActionResult> OnPostExportToCsvAsync()
        {
            if (!await HasPageAccess("IzvestajGeneriraniIzlezniFakturiKonKlient"))
            {
                return RedirectToAccessDenied();
            }

            if (!DatumOd.HasValue || !DatumDo.HasValue)
            {
                ModelState.AddModelError("", "Датумите се задолжителни");
                return Page();
            }

            await LoadData();

            var csv = new StringBuilder();

            // Add BOM for proper UTF-8 encoding with Cyrillic characters
            var bom = Encoding.UTF8.GetPreamble();
            var bomString = Encoding.UTF8.GetString(bom);
            csv.Append(bomString);

            // Add headers
            csv.AppendLine(string.Join(",", ColumnNames.Select(EscapeCsvField)));

            // Add data rows
            foreach (var row in Data)
            {
                var formattedFields = row.Select(field =>
                {
                    if (field == null)
                        return "";
                    else if (field is DateTime date)
                        return date.ToString("dd/MM/yyyy");
                    else if (field is decimal decimalValue)
                        return decimalValue.ToString("N2");
                    else if (field is double doubleValue)
                        return doubleValue.ToString("N2");
                    else if (field is float floatValue)
                        return floatValue.ToString("N2");
                    else
                        return field.ToString();
                }).Select(EscapeCsvField);

                csv.AppendLine(string.Join(",", formattedFields));
            }

            string fileName = $"IzvestajGeneriraniIzlezniFakturiKonKlient_{DateTime.Now:yyyyMMdd}.csv";
            byte[] bytes = Encoding.UTF8.GetBytes(csv.ToString());
            return File(bytes, "text/csv", fileName);
        }

        private string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field))
                return "";

            if (field.Contains(",") || field.Contains("\"") || field.Contains("\n") || field.Contains("\r"))
            {
                // Escape quotes by doubling them and wrap in quotes
                field = field.Replace("\"", "\"\"");
                return $"\"{field}\"";
            }

            return field;
        }

        private async Task LoadData()
        {
            // Clear existing data
            ColumnNames.Clear();
            Data.Clear();

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand("IzvestajGeneriraniIzlezniFakturiKonKlient", connection))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@DatumOd", DatumOd.Value);
                    cmd.Parameters.AddWithValue("@DatumDo", DatumDo.Value);

                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        // Get column names
                        for (int i = 0; i < reader.FieldCount; i++)
                        {
                            ColumnNames.Add(reader.GetName(i));
                        }

                        // Get data
                        while (await reader.ReadAsync())
                        {
                            var row = new List<object>();
                            for (int i = 0; i < reader.FieldCount; i++)
                            {
                                row.Add(reader.IsDBNull(i) ? null : reader.GetValue(i));
                            }
                            Data.Add(row);
                        }
                    }
                }
            }
        }
    }
}
