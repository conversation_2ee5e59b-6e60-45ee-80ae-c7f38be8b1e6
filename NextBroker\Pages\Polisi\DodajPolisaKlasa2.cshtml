@page
@model NextBroker.Pages.Polisi.DodajPolisaKlasa2Model
@{
    ViewData["Title"] = "Додај полиса Класа 2 - Здравствено осигурување";
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container-fluid mt-4 px-4">
    <!-- Add this at the top of the form, after the opening <form> tag -->
    <div class="alert alert-success alert-dismissible fade" role="alert" id="successMessage" style="display:none;">
        <strong>Успешно!</strong> Полисата е успешно зачувана.
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <div class="alert alert-danger alert-dismissible fade" role="alert" id="errorMessage" style="display:none;">
        <strong>Грешка!</strong> <span id="errorText"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <form method="post" id="polisaForm" class="dodaj-polisa-form">
        @Html.AntiForgeryToken()
        
        <!-- Make sure this is prominently displayed at the top of your form -->
        <div asp-validation-summary="All" class="text-danger"></div>

        <div class="card mb-3">
            <div class="card-header">
                <h5 class="mb-0">Основни информации</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.KlientiIdOsiguritel" class="control-label"></label>
                            <select asp-for="Input.KlientiIdOsiguritel" class="form-control" asp-items="Model.Osiguriteli">
                                <option value="">-- Изберете осигурител --</option>
                            </select>
                            <span asp-validation-for="Input.KlientiIdOsiguritel" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.KlasiOsiguruvanjeIdKlasa" class="control-label"></label>
                            <select asp-for="Input.KlasiOsiguruvanjeIdKlasa" class="form-control" asp-items="Model.KlasiOsiguruvanje">
                                <option value="">-- Изберете класа --</option>
                            </select>
                            <span asp-validation-for="Input.KlasiOsiguruvanjeIdKlasa" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.ProduktiIdProizvod" class="control-label"></label>
                            <select asp-for="Input.ProduktiIdProizvod" class="form-control" asp-items="Model.Produkti">
                                <option value="">-- Изберете продукт --</option>
                            </select>
                            <span asp-validation-for="Input.ProduktiIdProizvod" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.BrojNaPolisa" class="control-label"></label>
                            <input asp-for="Input.BrojNaPolisa" class="form-control" id="brojNaPolisaInput" required />
                            <span asp-validation-for="Input.BrojNaPolisa" class="text-danger"></span>
                            <span id="brojNaPolisaExistsError" class="text-danger" style="display:none;">Полиса со овој број веќе постои.</span>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="form-check">
                            <input asp-for="Input.Kolektivna" class="form-check-input" type="checkbox" />
                            <label asp-for="Input.Kolektivna" class="form-check-label">Колективна</label>
                            <span asp-validation-for="Input.Kolektivna" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Договорувач (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" 
                                    data-target="dogovoruvac" 
                                    style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="dogovoruvacMBSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                            <input type="hidden" asp-for="Input.KlientiIdDogovoruvac" id="KlientiIdDogovoruvac" />
                        </div>
                        <div id="dogovoruvacSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div id="osigurenikContainer" class="row">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Осигуреник (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" 
                                    data-target="osigurenik" 
                                    style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="osigurenikMBSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                            <input type="hidden" asp-for="Input.KlientiIdOsigurenik" id="KlientiIdOsigurenik" />
                        </div>
                        <div id="osigurenikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Соработник/вработен (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" 
                                    data-target="sorabotnik" 
                                    style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="sorabotnikMBSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                            <input type="hidden" asp-for="Input.KlientiIdSorabotnik" id="KlientiIdSorabotnik" />
                        </div>
                        <div id="sorabotnikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-check mb-3">
                            <input type="checkbox" class="form-check-input" asp-for="Input.Faktoring" id="faktoring">
                            <label class="form-check-label" asp-for="Input.Faktoring">Факторинг</label>
                        </div>
                        <div class="form-check mb-3" style="display: none;">
                            <input type="checkbox" class="form-check-input" asp-for="Input.GeneriranaFakturaIzlezna" id="generiranaFakturaIzlezna">
                            <label class="form-check-label" asp-for="Input.GeneriranaFakturaIzlezna">Генерирана излезна фактура</label>
                        </div>
                        <!-- Commented out Сторно field
                        <div class="form-check mb-3">
                            <input type="checkbox" class="form-check-input" asp-for="Input.Storno" id="storno">
                            <label class="form-check-label" asp-for="Input.Storno">Сторно</label>
                        </div>
                        <div id="pricinaZaStornoContainer" class="mb-3" style="display: none;">
                            <label asp-for="Input.SifrarnikPricinaZaStornoId" class="form-label">Причина за сторно</label>
                            <select asp-for="Input.SifrarnikPricinaZaStornoId" 
                                    asp-items="Model.PriciniZaStorno" 
                                    class="form-select">
                                <option value="">-- Избери причина за сторно --</option>
                            </select>
                            <span asp-validation-for="Input.SifrarnikPricinaZaStornoId" class="text-danger"></span>
                        </div>
                        -->
                        <!-- Add hidden fields to ensure values are still submitted -->
                        <div style="display: none;">
                            <input asp-for="Input.Storno" type="hidden" value="false" />
                            <input asp-for="Input.SifrarnikPricinaZaStornoId" type="hidden" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

       

        <!-- Move these rows to be last in the form, just before closing </div></div></form> -->

        
        <div class="row mt-3">
            <div class="col-md-4">
                <div class="form-group">
                    <label asp-for="Input.VaziOd" class="control-label"></label>
                    <input asp-for="Input.VaziOd" class="form-control datepicker" type="date" id="Input_VaziOd" />
                    <span asp-validation-for="Input.VaziOd" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label asp-for="Input.VaziDo" class="control-label"></label>
                    <input asp-for="Input.VaziDo" class="form-control datepicker" type="date" id="Input_VaziDo" />
                    <span asp-validation-for="Input.VaziDo" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label asp-for="Input.DatumNaIzdavanje" class="control-label"></label>
                    <input asp-for="Input.DatumNaIzdavanje" class="form-control datepicker" type="date" />
                    <span asp-validation-for="Input.DatumNaIzdavanje" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="Input.ValutiId" class="control-label"></label>
                    <select asp-for="Input.ValutiId" class="form-select" asp-items="Model.Valuti">
                        <option value="">-- Избери валута --</option>
                    </select>
                    <span asp-validation-for="Input.ValutiId" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="Input.NaciniNaPlakanjeId" class="control-label"></label>
                    <select asp-for="Input.NaciniNaPlakanjeId" class="form-select" asp-items="Model.NaciniNaPlakanje">
                        <option value="">-- Избери начин на плаќање --</option>
                    </select>
                    <span asp-validation-for="Input.NaciniNaPlakanjeId" class="text-danger"></span>
                </div>
            </div>
            <!-- Commented out Тип на плаќање field
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="Input.TipoviNaPlakanjeId" class="control-label"></label>
                    <select asp-for="Input.TipoviNaPlakanjeId" class="form-select" asp-items="Model.TipoviNaPlakanje">
                        <option value="">-- Избери тип на плаќање --</option>
                    </select>
                    <span asp-validation-for="Input.TipoviNaPlakanjeId" class="text-danger"></span>
                </div>
            </div>
            -->
            <!-- Commented out Банка field
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="Input.BankiId" class="control-label"></label>
                    <select asp-for="Input.BankiId" class="form-select" asp-items="Model.Banki">
                        <option value="">-- Избери банка --</option>
                    </select>
                    <span asp-validation-for="Input.BankiId" class="text-danger"></span>
                </div>
            </div>
            -->
            <!-- Add hidden fields to ensure values are still submitted -->
            <div style="display: none;">
                <input asp-for="Input.TipoviNaPlakanjeId" type="hidden" value="1" />
                <input asp-for="Input.BankiId" type="hidden" />
            </div>
        </div>

        <!-- Add these fields after the existing fields -->
        <div class="row mt-3">
            <div class="col-md-4">
                <div class="form-group">
                    <label asp-for="Input.TipNaFaktura" class="control-label">Тип на фактура</label>
                    <select asp-for="Input.TipNaFaktura" 
                            asp-items="Model.TipoviNaFaktura" 
                            class="form-select"
                            required>
                        <option value="">-- Избери тип на фактура --</option>
                    </select>
                    <span asp-validation-for="Input.TipNaFaktura" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-6" style="display: none;">
                <div class="form-group">
                    <label asp-for="Input.BrojNaVleznaFaktura" class="control-label">Број на влезна фактура</label>
                    <input asp-for="Input.BrojNaVleznaFaktura" class="form-control" />
                    <span asp-validation-for="Input.BrojNaVleznaFaktura" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-6" style="display: none;">
                <div class="form-group">
                    <label asp-for="Input.BrojNaIzleznaFaktura" class="control-label">Број на излезна фактура</label>
                    <input asp-for="Input.BrojNaIzleznaFaktura" class="form-control" />
                    <span asp-validation-for="Input.BrojNaIzleznaFaktura" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-6" style="display: none;">
                <div class="form-group">
                    <label asp-for="Input.DatumNaVleznaFaktura" class="control-label">Датум на влезна фактура</label>
                    <input asp-for="Input.DatumNaVleznaFaktura" class="form-control" type="date" />
                    <span asp-validation-for="Input.DatumNaVleznaFaktura" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-6" style="display: none;">
                <div class="form-group">
                    <label asp-for="Input.DatumNaIzleznaFaktura" class="control-label">Датум на излезна фактура</label>
                    <input asp-for="Input.DatumNaIzleznaFaktura" class="form-control" type="date" />
                    <span asp-validation-for="Input.DatumNaIzleznaFaktura" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-6"style="display: none;">
                <div class="form-group" >
                    <label asp-for="Input.RokNaPlakanjeVleznaFaktura" class="control-label">Рок на плаќање влезна фактура</label>
                    <input asp-for="Input.RokNaPlakanjeVleznaFaktura" class="form-control" type="date" />
                    <span asp-validation-for="Input.RokNaPlakanjeVleznaFaktura" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-6" style="display: none;">
                <div class="form-group">
                    <label asp-for="Input.RokNaPlakanjeIzleznaFaktura" class="control-label">Рок на плаќање излезна фактура</label>
                    <input asp-for="Input.RokNaPlakanjeIzleznaFaktura" class="form-control" type="date" />
                    <span asp-validation-for="Input.RokNaPlakanjeIzleznaFaktura" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="Input.SifrarnikValutiIdFranshizaValuta" class="control-label"></label>
                    <select asp-for="Input.SifrarnikValutiIdFranshizaValuta" 
                            class="form-select" 
                            asp-items="Model.Valuti">
                        <option value="">-- Изберете валута --</option>
                    </select>
                    <span asp-validation-for="Input.SifrarnikValutiIdFranshizaValuta" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="Input.ProcentFransiza" class="control-label">Процент франшиза</label>
                    <input asp-for="Input.ProcentFransiza" class="form-control" type="number" step="0.01" />
                    <span asp-validation-for="Input.ProcentFransiza" class="text-danger"></span>
                </div>
            </div>
                        <div class="col-md-3 mb-3">
                <label asp-for="Input.FranshizaIznos" class="form-label"></label>
                <div class="input-group">
                    <input asp-for="Input.FranshizaIznos" 
                           class="form-control decimal-input" 
                           type="number" 
                           step="0.0001" />
                </div>
                <span asp-validation-for="Input.FranshizaIznos" class="text-danger"></span>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label asp-for="Input.KoregiranaStapkaNaProvizija" class="control-label">Корегирана стапка на провизија</label>
                    <input asp-for="Input.KoregiranaStapkaNaProvizija" class="form-control" type="number" step="0.01" />
                    <span asp-validation-for="Input.KoregiranaStapkaNaProvizija" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="card mb-3 mt-4">
            <div class="card-header">
                <h5 class="mb-0">Здравствено осигурување</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Input.ListaDejnostiId" class="control-label">Дејност</label>
                            <select asp-for="Input.ListaDejnostiId" class="form-select" asp-items="Model.ListaDejnosti">
                                <option value="">-- Избери дејност --</option>
                            </select>
                            <span asp-validation-for="Input.ListaDejnostiId" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.BrojNaOsigurenici" class="control-label"></label>
                            <input asp-for="Input.BrojNaOsigurenici" class="form-control" type="number" />
                            <span asp-validation-for="Input.BrojNaOsigurenici" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.PremijaZaEdnoLice" class="control-label"></label>
                            <input asp-for="Input.PremijaZaEdnoLice" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.PremijaZaEdnoLice" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.VkupnaPremija" class="control-label"></label>
                            <input asp-for="Input.VkupnaPremija" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.VkupnaPremija" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.DopolnitelenRizik_Bremenost" class="control-label"></label>
                            <input asp-for="Input.DopolnitelenRizik_Bremenost" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.DopolnitelenRizik_Bremenost" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.Operacii" class="control-label"></label>
                            <input asp-for="Input.Operacii" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.Operacii" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.OperaciiVonRM" class="control-label"></label>
                            <input asp-for="Input.OperaciiVonRM" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.OperaciiVonRM" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Input.TroshociZaPrevozVonRM" class="control-label"></label>
                            <input asp-for="Input.TroshociZaPrevozVonRM" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.TroshociZaPrevozVonRM" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Input.TrosociZaNokjevanjeVonRM" class="control-label"></label>
                            <input asp-for="Input.TrosociZaNokjevanjeVonRM" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.TrosociZaNokjevanjeVonRM" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.Stomatologija" class="control-label"></label>
                            <input asp-for="Input.Stomatologija" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.Stomatologija" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.Oftamologija" class="control-label"></label>
                            <input asp-for="Input.Oftamologija" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.Oftamologija" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="Input.PsihijatriskiUslugi" class="control-label"></label>
                            <input asp-for="Input.PsihijatriskiUslugi" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.PsihijatriskiUslugi" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Input.ItnaMedicinskaIntervencija" class="control-label"></label>
                            <input asp-for="Input.ItnaMedicinskaIntervencija" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.ItnaMedicinskaIntervencija" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Input.DopolnitelniPregledi" class="control-label"></label>
                            <input asp-for="Input.DopolnitelniPregledi" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.DopolnitelniPregledi" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Input.PrevozSoSanitetskoVozilo" class="control-label"></label>
                            <input asp-for="Input.PrevozSoSanitetskoVozilo" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.PrevozSoSanitetskoVozilo" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Input.VtoroMislenje" class="control-label"></label>
                            <input asp-for="Input.VtoroMislenje" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.VtoroMislenje" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Input.AmbulatnaRehabilitacija" class="control-label"></label>
                            <input asp-for="Input.AmbulatnaRehabilitacija" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.AmbulatnaRehabilitacija" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Input.TroshociZaSmestuvanjeRoditel" class="control-label"></label>
                            <input asp-for="Input.TroshociZaSmestuvanjeRoditel" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.TroshociZaSmestuvanjeRoditel" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

                 <!-- Popusti Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Попусти</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentNaPopustZaFakturaVoRok" class="form-label">Процент на попуст за фактура во рок</label>
                        <input asp-for="Input.ProcentNaPopustZaFakturaVoRok" class="form-control decimal-input" type="number" step="0.01" />
                        <span asp-validation-for="Input.ProcentNaPopustZaFakturaVoRok" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.IznosZaPlakjanjeVoRok" class="form-label">Износ за плаќање во рок</label>
                        <input asp-for="Input.IznosZaPlakjanjeVoRok" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.IznosZaPlakjanjeVoRok" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentKomercijalenPopust" class="form-label">Процент комерцијален попуст</label>
                        <input asp-for="Input.ProcentKomercijalenPopust" class="form-control decimal-input" type="number" step="0.01" />
                        <span asp-validation-for="Input.ProcentKomercijalenPopust" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentFinansiski" class="form-label">Процент финансиски</label>
                        <input asp-for="Input.ProcentFinansiski" class="form-control decimal-input" type="number" step="0.01" />
                        <span asp-validation-for="Input.ProcentFinansiski" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.PremijaZaNaplata" class="form-label">Премија за наплата</label>
                        <input asp-for="Input.PremijaZaNaplata" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.PremijaZaNaplata" class="text-danger"></span>
                    </div>
                </div>
            </div>
        </div>

       

        <!-- Add this before the closing </form> tag -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Зачувај полиса
                    </button>
                </div>
            </div>
        </div>
    </form>

    <!-- Add this after the form, before the Scripts section -->
    <div class="alert alert-success alert-dismissible fade" role="alert" id="successMessage" style="display:none;">
        <strong>Успешно!</strong> Полисата е успешно зачувана.
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
</div>

@section Styles {
    <style>
        #dogovoruvacSearchResults .list-group-item.text-center,
        #osigurenikSearchResults .list-group-item.text-center,
        #sorabotnikSearchResults .list-group-item.text-center {
            padding: 1rem;
        }
        #dogovoruvacSearchResults .list-group-item.text-center p,
        #osigurenikSearchResults .list-group-item.text-center p,
        #sorabotnikSearchResults .list-group-item.text-center p {
            color: #6c757d;
            margin-bottom: 0.75rem;
        }
        #dogovoruvacSearchResults .btn-primary,
        #osigurenikSearchResults .btn-primary,
        #sorabotnikSearchResults .btn-primary {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }
    </style>
}

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        // Define the function globally
        function openAddClientWindow(sourceField) {
            const width = 800;
            const height = 600;
            const left = (window.screen.width - width) / 2;
            const top = (window.screen.height - height) / 2;
            
            const popup = window.open(`/Klienti/DodajKlient?fromPolisa=true&source=${sourceField}`, 'DodajKlient', 
                `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`);
                
            // Listen for messages from the popup
            window.addEventListener('message', function(event) {
                if (event.data.type === 'clientAdded') {
                    // Clear the search field based on the source
                    if (event.data.source === 'dogovoruvac') {
                        $('#dogovoruvacMBSearch').val('');
                        $('#KlientiIdDogovoruvac').val('');
                        $('#dogovoruvacSearchResults').hide();
                    } else if (event.data.source === 'osigurenik') {
                        $('#osigurenikMBSearch').val('');
                        $('#KlientiIdOsigurenik').val('');
                        $('#osigurenikSearchResults').hide();
                    } else if (event.data.source === 'sorabotnik') {
                        $('#sorabotnikMBSearch').val('');
                        $('#KlientiIdSorabotnik').val('');
                        $('#sorabotnikSearchResults').hide();
                    }
                }
            });
        }

        $(document).ready(function() {
            // Function to create search functionality
            function createSearchFunctionality(searchInputId, resultsContainerId, hiddenInputId, searchHandler = 'SearchKlienti') {
                let searchTimeout;
                const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();

                $(`#${searchInputId}`).on('input', function() {
                    clearTimeout(searchTimeout);
                    const searchTerm = $(this).val();
                    const resultsDiv = $(`#${resultsContainerId}`);

                    if (searchTerm.length < 1) {
                        resultsDiv.hide();
                        return;
                    }

                    searchTimeout = setTimeout(function() {
                        $.ajax({
                            url: `?handler=${searchHandler}`,
                            type: 'GET',
                            data: { mb: searchTerm },
                            headers: {
                                "RequestVerificationToken": antiForgeryToken
                            },
                            success: function(data) {
                                if (data && data.length > 0) {
                                    let html = '<div class="list-group">';
                                    data.forEach(item => {
                                        let displayText = '';
                                        if (item.tip === 'P') {
                                            displayText = `${item.naziv}`;
                                            let identifiers = [];
                                            if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                            if (item.edb) identifiers.push(`ЕДБ: ${item.edb}`);
                                            if (identifiers.length > 0) {
                                                displayText += ` (${identifiers.join(', ')})`;
                                            }
                                        } else {
                                            displayText = `${item.ime} ${item.prezime}`;
                                            let identifiers = [];
                                            if (item.embg) identifiers.push(`ЕМБГ: ${item.embg}`);
                                            if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                            if (identifiers.length > 0) {
                                                displayText += ` (${identifiers.join(', ')})`;
                                            }
                                        }
                                        html += `<a href="#" class="list-group-item list-group-item-action" data-id="${item.id}">
                                                  ${displayText}
                                               </a>`;
                                    });
                                    html += '</div>';
                                    resultsDiv.html(html).show();
                                } else {
                                    resultsDiv.html(`
                                        <div class="list-group">
                                            <div class="list-group-item text-center">
                                                <p class="mb-2">Нема пронајдени резултати</p>
                                                <button type="button" class="btn btn-primary" onclick="openAddClientWindow('${
                                                    searchInputId === 'dogovoruvacMBSearch' ? 'dogovoruvac' : 
                                                    searchInputId === 'osigurenikMBSearch' ? 'osigurenik' : 'sorabotnik'
                                                }')">
                                                    <i class="fas fa-plus"></i> Додај клиент
                                                </button>
                                            </div>
                                        </div>
                                    `).show();
                                }
                            },
                            error: function() {
                                resultsDiv.html(`
                                    <div class="list-group">
                                        <div class="list-group-item text-center">
                                            <p class="text-danger mb-2">Грешка при пребарување</p>
                                            <button type="button" class="btn btn-primary" onclick="openAddClientWindow('${
                                                searchInputId === 'dogovoruvacMBSearch' ? 'dogovoruvac' : 
                                                searchInputId === 'osigurenikMBSearch' ? 'osigurenik' : 'sorabotnik'
                                            }')">
                                                <i class="fas fa-plus"></i> Додај клиент
                                            </button>
                                        </div>
                                    </div>
                                `).show();
                            }
                        });
                    }, 300);
                });

                // Handle selection
                $(document).on('click', `#${resultsContainerId} .list-group-item`, function(e) {
                    e.preventDefault();
                    const id = $(this).data('id');
                    const displayText = $(this).text();
                    $(`#${searchInputId}`).val(displayText.trim());
                    $(`#${hiddenInputId}`).val(id);
                    $(`#${resultsContainerId}`).hide();
                });

                // Hide results when clicking outside
                $(document).on('click', function(e) {
                    if (!$(e.target).closest(`#${searchInputId}, #${resultsContainerId}`).length) {
                        $(`#${resultsContainerId}`).hide();
                    }
                });
            }

            // Initialize search for all fields
            createSearchFunctionality('dogovoruvacMBSearch', 'dogovoruvacSearchResults', 'KlientiIdDogovoruvac');
            createSearchFunctionality('osigurenikMBSearch', 'osigurenikSearchResults', 'KlientiIdOsigurenik');
            createSearchFunctionality('sorabotnikMBSearch', 'sorabotnikSearchResults', 'KlientiIdSorabotnik', 'SearchSorabotnici');

            // Update click handlers for clear buttons
            $('.clear-field').click(function() {
                const target = $(this).data('target');
                switch(target) {
                    case 'dogovoruvac':
                        $('#dogovoruvacMBSearch').val('');
                        $('#KlientiIdDogovoruvac').val('');
                        $('#dogovoruvacSearchResults').hide();
                        break;
                    case 'osigurenik':
                        $('#osigurenikMBSearch').val('');
                        $('#KlientiIdOsigurenik').val('');
                        $('#osigurenikSearchResults').hide();
                        break;
                    case 'sorabotnik':
                        $('#sorabotnikMBSearch').val('');
                        $('#KlientiIdSorabotnik').val('');
                        $('#sorabotnikSearchResults').hide();
                        break;
                }
            });

            // Add this at the beginning of the document.ready function
            function toggleOsigurenikField() {
                if ($('#Input_Kolektivna').is(':checked')) {
                    $('#osigurenikContainer').hide();
                    // Clear the osigurenik field when hiding
                    $('#osigurenikMBSearch').val('');
                    $('#KlientiIdOsigurenik').val('');
                    $('#osigurenikSearchResults').hide();
                } else {
                    $('#osigurenikContainer').show();
                }
            }

            // Initial state check
            toggleOsigurenikField();

            // Add change event handler for the checkbox
            $('#Input_Kolektivna').change(function() {
                toggleOsigurenikField();
            });

            /* Commented out because the Storno checkbox has been hidden
            // Add this for Storno checkbox handling
            function togglePricinaZaStorno() {
                if ($('#storno').is(':checked')) {
                    $('#pricinaZaStornoContainer').show();
                } else {
                    $('#pricinaZaStornoContainer').hide();
                    $('#Input_SifrarnikPricinaZaStornoId').val('');
                }
            }

            // Initial state check for Storno
            togglePricinaZaStorno();

            // Add change event handler for Storno checkbox
            $('#storno').change(function() {
                togglePricinaZaStorno();
            });
            */

            // Initialize select2 for PricinaZaStorno dropdown
            $('#Input_SifrarnikPricinaZaStornoId').select2({
                width: '100%'
            });

            // Add custom validation rules
            $.validator.setDefaults({
                ignore: []
            });

            // Mark BrojNaVleznaFaktura as optional
            $('#Input_BrojNaVleznaFaktura').rules('remove', 'required');

            // Add form submit handler
            $('#polisaForm').submit(function(e) {
                // Don't prevent default - let the form submit normally
                
                // Show success message
                $('#successMessage, #bottomSuccessMessage')
                    .show()
                    .addClass('show');
                
                // Disable submit button
                $(this).find('button[type="submit"]').prop('disabled', true);
                
                // Let form submit proceed
                return true;
            });

       
        });
    </script>


   <script type="text/javascript">
        // Define the policy number validation function
        function checkPolicyNumber() {
            var brojNaPolisa = $('#brojNaPolisaInput').val();
            if (brojNaPolisa) {
                $.ajax({
                    url: '?handler=CheckBrojNaPolisa',
                    type: 'GET',
                    data: { brojNaPolisa: brojNaPolisa },
                    headers: {
                        "RequestVerificationToken": 
                            $('input:hidden[name="__RequestVerificationToken"]').val()
                    },
                    success: function(result) {
                        if (result > 0) {
                            $('#brojNaPolisaExistsError').show();
                        } else {
                            $('#brojNaPolisaExistsError').hide();
                        }
                    },
                    error: function() {
                        $('#brojNaPolisaExistsError').hide();
                    }
                });
            } else {
                $('#brojNaPolisaExistsError').hide();
            }
        }

        // Attach the event handlers when document is ready
        $(document).ready(function() {
            // Policy number validation
            $('#brojNaPolisaInput').on('blur change', checkPolicyNumber);
            
            // Initial check
            checkPolicyNumber();
        });
    </script>

    <script type="text/javascript">
        // Existing date validation function
        function validateDates() {
            var vaziOd = $('#Input_VaziOd').val();
            var vaziDo = $('#Input_VaziDo').val();
            
            if (vaziOd && vaziDo) {
                var dateVaziOd = new Date(vaziOd);
                var dateVaziDo = new Date(vaziDo);
                
                if (dateVaziDo < dateVaziOd) {
                    if (!$('#vaziDoError').length) {
                        $('#Input_VaziDo').after('<span id="vaziDoError" class="text-danger">Датумот "Важи до" не може да биде пред "Важи од"</span>');
                    }
                    $('#Input_VaziDo').val(vaziOd);
                    return false;
                } else {
                    $('#vaziDoError').remove();
                    updatePaymentOptions(dateVaziOd, dateVaziDo);
                    return true;
                }
            }
            return true;
        }

        // Add new function to handle payment options
        function updatePaymentOptions(dateFrom, dateTo) {
            // Calculate months difference
            const months = (dateTo.getFullYear() - dateFrom.getFullYear()) * 12 + 
                         (dateTo.getMonth() - dateFrom.getMonth()) + 1;

            const $paymentSelect = $('#Input_NaciniNaPlakanjeId');
            const currentValue = $paymentSelect.val();

            // Store all options initially if not already stored
            if (!$paymentSelect.data('original-options')) {
                const originalOptions = [];
                $paymentSelect.find('option').each(function() {
                    originalOptions.push({
                        value: $(this).val(),
                        text: $(this).text()
                    });
                });
                $paymentSelect.data('original-options', originalOptions);
            }

            // Get original options
            const originalOptions = $paymentSelect.data('original-options');

            // Clear current options except the placeholder
            $paymentSelect.find('option:not(:first)').remove();

            // Add back only valid options based on months
            originalOptions.forEach(option => {
                if (option.value === '') return; // Skip placeholder

                let rateNumber = 1; // Default for "Еднократно"
                if (option.text.includes('рати')) {
                    rateNumber = parseInt(option.text.match(/\d+/)[0]);
                }

                // Add option only if number of rates is less than or equal to number of months
                if (rateNumber <= months) {
                    $paymentSelect.append(new Option(option.text, option.value));
                }
            });

            // If current value is still valid, keep it selected
            if ($paymentSelect.find(`option[value="${currentValue}"]`).length) {
                $paymentSelect.val(currentValue);
            } else {
                $paymentSelect.val(''); // Select placeholder if current value is no longer valid
            }
        }

        // Attach the date validation handlers when document is ready
        $(document).ready(function() {
            $('#Input_VaziOd, #Input_VaziDo').on('blur change', validateDates);
            
            // Run initial validation
            validateDates();
        });
    </script>

    <script>
        $(document).ready(function() {
            // Create validation message element
            const validationMessage = $('<div class="text-danger mt-1" style="display: none;">Задолжително поле</div>');
            $('#osigurenikMBSearch').after(validationMessage);

            // Function to handle Produkt selection changes
            function handleProduktChange() {
                const produktId = $('#Input_ProduktiIdProizvod').val();
                const brojNaOsigureniciInput = $('#Input_BrojNaOsigurenici');
                const kolektivnaCheckbox = $('#Input_Kolektivna');
                const osigurenikContainer = $('#osigurenikContainer');
                const osigurenikInput = $('#osigurenikMBSearch');
                const osigurenikHiddenInput = $('#KlientiIdOsigurenik');

                if (produktId === '24') {
                    // For Produkt ID 24 (Здравствено - Индивидуално)
                    brojNaOsigureniciInput.val(1);
                    brojNaOsigureniciInput.prop('readonly', true);
                    brojNaOsigureniciInput.css('background-color', '#e9ecef');
                    kolektivnaCheckbox.prop('checked', false);
                    kolektivnaCheckbox.prop('disabled', true);
                    osigurenikContainer.show();
                    
                    // Add required attribute and check validation
                    osigurenikInput.prop('required', true);
                    if (!osigurenikInput.val() || !osigurenikHiddenInput.val()) {
                        osigurenikInput.addClass('is-invalid');
                        validationMessage.show();
                    }

                    // Add form submit validation
                    $('form').on('submit.osigurenikValidation', function(e) {
                        if (!osigurenikInput.val() || !osigurenikHiddenInput.val()) {
                            e.preventDefault();
                            osigurenikInput.addClass('is-invalid');
                            validationMessage.show();
                            // Scroll to the field
                            $('html, body').animate({
                                scrollTop: osigurenikInput.offset().top - 100
                            }, 200);
                        }
                    });

                } else if (produktId === '25' || produktId === '26') {
                    // For Produkt IDs 25 and 26
                    brojNaOsigureniciInput.prop('readonly', false);
                    brojNaOsigureniciInput.css('background-color', '');
                    brojNaOsigureniciInput.attr('min', '0');
                    if (parseFloat(brojNaOsigureniciInput.val()) < 0) {
                        brojNaOsigureniciInput.val('0');
                    }
                    kolektivnaCheckbox.prop('checked', true);
                    kolektivnaCheckbox.prop('disabled', true);
                    osigurenikContainer.hide();
                    
                    // Remove required attribute and validation
                    osigurenikInput.prop('required', false);
                    osigurenikInput.removeClass('is-invalid');
                    validationMessage.hide();
                    $('form').off('submit.osigurenikValidation');
                } else {
                    // For all other products
                    brojNaOsigureniciInput.prop('readonly', false);
                    brojNaOsigureniciInput.css('background-color', '');
                    brojNaOsigureniciInput.attr('min', '0');
                    if (parseFloat(brojNaOsigureniciInput.val()) < 0) {
                        brojNaOsigureniciInput.val('0');
                    }
                    kolektivnaCheckbox.prop('disabled', false);
                    toggleOsigurenikField(); // Apply the standard visibility rules
                    
                    // Remove required attribute and validation
                    osigurenikInput.prop('required', false);
                    osigurenikInput.removeClass('is-invalid');
                    validationMessage.hide();
                    $('form').off('submit.osigurenikValidation');
                }
            }

            // Watch for changes in Produkt dropdown
            $('#Input_ProduktiIdProizvod').on('change', handleProduktChange);

            // Watch for changes in osigurenik field
            $('#osigurenikMBSearch').on('input', function() {
                const produktId = $('#Input_ProduktiIdProizvod').val();
                if (produktId === '24') {
                    if (!$(this).val() || !$('#KlientiIdOsigurenik').val()) {
                        $(this).addClass('is-invalid');
                        validationMessage.show();
                    } else {
                        $(this).removeClass('is-invalid');
                        validationMessage.hide();
                    }
                }
            });

            // Add validation check when a client is selected from the search results
            $(document).on('click', '#osigurenikSearchResults .list-group-item', function() {
                const produktId = $('#Input_ProduktiIdProizvod').val();
                if (produktId === '24') {
                    $('#osigurenikMBSearch').removeClass('is-invalid');
                    validationMessage.hide();
                }
            });

            // Initial check on page load
            handleProduktChange();

            // Preserve existing Kolektivna checkbox functionality
            function toggleOsigurenikField() {
                if ($('#Input_Kolektivna').is(':checked')) {
                    $('#osigurenikContainer').hide();
                    // Clear the osigurenik field when hiding
                    $('#osigurenikMBSearch').val('');
                    $('#KlientiIdOsigurenik').val('');
                    $('#osigurenikSearchResults').hide();
                    // Remove validation when hidden
                    $('#osigurenikMBSearch').removeClass('is-invalid');
                    validationMessage.hide();
                } else {
                    $('#osigurenikContainer').show();
                }
            }

            // Add change event handler for the Kolektivna checkbox
            $('#Input_Kolektivna').change(function() {
                const produktId = $('#Input_ProduktiIdProizvod').val();
                if (produktId !== '24' && produktId !== '25' && produktId !== '26') {
                    toggleOsigurenikField();
                }
            });

        });
    </script>

    <script>
        $(document).ready(function() {
            // Make Вкупна премија and Премија за наплата read-only
            $('#Input_VkupnaPremija, #Input_PremijaZaNaplata').prop('readonly', false)
                .css('background-color', '#e9ecef');

            // Function to calculate total premium
            function calculateTotalPremium() {
                const brojOsigurenici = parseFloat($('#Input_BrojNaOsigurenici').val()) || 0;
                const premijaZaEdnoLice = parseFloat($('#Input_PremijaZaEdnoLice').val()) || 0;
                
                // Base premium calculation
                let vkupnaPremija = brojOsigurenici === 0 ? premijaZaEdnoLice : (brojOsigurenici * premijaZaEdnoLice);

               

                // Set the total premium
                $('#Input_VkupnaPremija').val(vkupnaPremija.toFixed(4));

                // Calculate final premium after discounts
                calculateFinalPremium(vkupnaPremija);
            }

            // Function to calculate final premium with discounts
            function calculateFinalPremium(vkupnaPremija) {
                const popustFakturaVoRok = parseFloat($('#Input_ProcentNaPopustZaFakturaVoRok').val()) || 0;
                const komercijalenPopust = parseFloat($('#Input_ProcentKomercijalenPopust').val()) || 0;
                const finansiskiPopust = parseFloat($('#Input_ProcentFinansiski').val()) || 0;

                // Calculate discounts
                const popustFakturaVoRokAmount = (vkupnaPremija * popustFakturaVoRok) / 100;
                const komercijalenPopustAmount = (vkupnaPremija * komercijalenPopust) / 100;
                const finansiskiPopustAmount = (vkupnaPremija * finansiskiPopust) / 100;

                // Calculate amount for payment within deadline
                const iznosZaPlakjanjeVoRok = vkupnaPremija - popustFakturaVoRokAmount;
                $('#Input_IznosZaPlakjanjeVoRok').val(iznosZaPlakjanjeVoRok.toFixed(4));

                // Calculate final premium for payment
                const premijaZaNaplata = vkupnaPremija - popustFakturaVoRokAmount - 
                                       komercijalenPopustAmount - finansiskiPopustAmount;
                $('#Input_PremijaZaNaplata').val(premijaZaNaplata.toFixed(4));
            }

            // Add event listeners for all input fields that affect calculations
            const inputSelectors = [
                '#Input_BrojNaOsigurenici',
                '#Input_PremijaZaEdnoLice',
                '#Input_DopolnitelenRizik_Bremenost',
                '#Input_Operacii',
                '#Input_OperaciiVonRM',
                '#Input_TroshociZaPrevozVonRM',
                '#Input_TrosociZaNokjevanjeVonRM',
                '#Input_Stomatologija',
                '#Input_Oftamologija',
                '#Input_PsihijatriskiUslugi',
                '#Input_ItnaMedicinskaIntervencija',
                '#Input_DopolnitelniPregledi',
                '#Input_PrevozSoSanitetskoVozilo',
                '#Input_VtoroMislenje',
                '#Input_AmbulatnaRehabilitacija',
                '#Input_TroshociZaSmestuvanjeRoditel',
                '#Input_ProcentNaPopustZaFakturaVoRok',
                '#Input_ProcentKomercijalenPopust',
                '#Input_ProcentFinansiski'
            ].join(', ');

            $(inputSelectors).on('input', calculateTotalPremium);

            // Initial calculation
            calculateTotalPremium();

            // Add to existing handleProduktChange function
            const originalHandleProduktChange = handleProduktChange;
            handleProduktChange = function() {
                originalHandleProduktChange();
                calculateTotalPremium();
            };

        });
    </script>

} 