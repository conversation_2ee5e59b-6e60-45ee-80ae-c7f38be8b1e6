@page
@model NextBroker.Pages.Pregledi.IzvestajPrenosNaNaplataVlezniFakturiModel
@{
    ViewData["Title"] = "Извештај пренос на наплатa - сите фактури";
}

<div class="container-fluid">
    <h4 class="mb-3">@ViewData["Title"]</h4>

    <form method="post">
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="DatumOd" class="control-label"></label>
                    <input asp-for="DatumOd" class="form-control" />
                    <span asp-validation-for="DatumOd" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="DatumDo" class="control-label"></label>
                    <input asp-for="DatumDo" class="form-control" />
                    <span asp-validation-for="DatumDo" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-4 d-flex align-items-end gap-2">
                <button type="submit" class="btn btn-primary">Генерирај извештај</button>
                @if (Model.Data.Any())
                {
                    <button type="submit" asp-page-handler="ExportToCsv" class="btn btn-success">
                        <i class="fas fa-file-csv me-1"></i>Експорт во CSV
                    </button>
                    <button type="submit" asp-page-handler="ExportToExcel" class="btn btn-info">
                        <i class="fas fa-file-excel me-1"></i>Експорт во Excel
                    </button>
                }
            </div>
        </div>
    </form>

    @if (!ModelState.IsValid)
    {
        <div class="alert alert-danger">
            <ul>
                @foreach (var modelState in ViewData.ModelState.Values)
                {
                    foreach (var error in modelState.Errors)
                    {
                        <li>@error.ErrorMessage</li>
                    }
                }
            </ul>
        </div>
    }

    @if (Model.Data.Any())
    {
        <div class="table-responsive">
            <table id="resultsTable" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        @foreach (var columnName in Model.ColumnNames)
                        {
                            <th>@columnName</th>
                        }
                    </tr>
                </thead>
                <tbody>
                    @foreach (var row in Model.Data)
                    {
                        <tr>
                            @foreach (var item in row)
                            {
                                <td>
                                    @if (item is DateTime date)
                                    {
                                        @date.ToString("dd/MM/yyyy")
                                    }
                                    else if (item is decimal decimalValue)
                                    {
                                        @decimalValue.ToString("N2")
                                    }
                                    else if (item is double doubleValue)
                                    {
                                        @doubleValue.ToString("N2")
                                    }
                                    else if (item is float floatValue)
                                    {
                                        @floatValue.ToString("N2")
                                    }
                                    else
                                    {
                                        @item
                                    }
                                </td>
                            }
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    @if (Model.Data.Any())
    {
        <script>
            $(document).ready(function() {
                $('#resultsTable').DataTable({
                    "language": {
                        "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Macedonian.json",
                        "search": "Пребарај:",
                        "lengthMenu": "Прикажи _MENU_ записи",
                        "info": "Прикажани _START_ до _END_ од _TOTAL_ записи",
                        "infoEmpty": "Прикажани 0 до 0 од 0 записи",
                        "infoFiltered": "(филтрирани од _MAX_ вкупно записи)",
                        "paginate": {
                            "first": "Први",
                            "last": "Последен",
                            "next": "Следен",
                            "previous": "Претходен"
                        },
                        "emptyTable": "Нема податоци во табелата",
                        "zeroRecords": "Не се пронајдени записи"
                    },
                    "pageLength": 25,
                    "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Сите"]],
                    "order": [],
                    "columnDefs": [
                        { "orderable": true, "targets": "_all" }
                    ],
                    "dom": '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                           '<"row"<"col-sm-12"tr>>' +
                           '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                    "responsive": true,
                    "processing": true
                });
            });
        </script>
    }
}
