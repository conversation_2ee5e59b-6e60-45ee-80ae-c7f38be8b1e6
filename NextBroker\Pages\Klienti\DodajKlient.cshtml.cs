using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;

namespace NextBroker.Pages.Klienti
{
    public class DodajKlientModel : SecurePageModel
    {
        private readonly new IConfiguration _configuration;

        public DodajKlientModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        [BindProperty]
        public KlientInputModel Input { get; set; } = new();

        // Change SelectList to IEnumerable<SelectListItem> for better compatibility
        public IEnumerable<SelectListItem> Opstini { get; set; }
        public IEnumerable<SelectListItem> Dejnosti { get; set; }
        public IEnumerable<SelectListItem> NivoaNaRizik { get; set; }
        public IEnumerable<SelectListItem> Banki { get; set; }
        public IEnumerable<SelectListItem> OrganizacioniEdinici { get; set; }
        public IEnumerable<SelectListItem> RabotniPozicii { get; set; }

        // Add new property for ZhivotNezivot dropdown
        public IEnumerable<SelectListItem> ZivotNezivotOptions { get; set; }

        // Add new property for KlientFizickoPravnoLice dropdown
        public IEnumerable<SelectListItem> KlientFizickoPravnoLiceOptions { get; set; }

        // Add new property for KlientPol dropdown
        public IEnumerable<SelectListItem> KlientPolOptions { get; set; }

        // Add new property for EkspozituriIdEkspozitura
        public IEnumerable<SelectListItem> EkspozituriIdEkspozituraOptions { get; set; }


        public List<SelectListItem> DogovorOpredelenoNeopredelenoOptions { get; set; } = new List<SelectListItem>
        {
            new SelectListItem { Value = "Определено", Text = "Определено" },
            new SelectListItem { Value = "Неопределено", Text = "Неопределено" }
        };

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("DodajKlient"))
            {
                return RedirectToAccessDenied();
            }

            // Initialize ZhivotNezivot options
            ZivotNezivotOptions = new List<SelectListItem>
            {
                new SelectListItem("Живот", "Живот"),
                new SelectListItem("Неживот", "Неживот")
            };

            // Initialize KlientFizickoPravnoLice options
            KlientFizickoPravnoLiceOptions = new List<SelectListItem>
            {
                new SelectListItem("Физичко лице", "F"),
                new SelectListItem("Правно лице", "P")
            };

            // Initialize KlientPol options
            KlientPolOptions = new List<SelectListItem>
            {
                new SelectListItem("-- Избери пол --", "N"), 
                new SelectListItem("Машки", "M"),
                new SelectListItem("Женски", "F")
            };

            await LoadDropdownLists();
            return Page();
        }

        private async Task LoadDropdownLists()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // Load Opstini with code
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT 
                        Id,
                        Opstina,
                        Kod,
                        CONCAT(Opstina, ' (', Kod, ')') as DisplayText
                    FROM ListaOpstini 
                    ORDER BY Opstina", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayText"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Opstini = items;
                }

                // Load Dejnosti with code
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT 
                        Id,
                        SifraDejnost,
                        NazivDejnost,
                        CONCAT(NazivDejnost, ' (', SifraDejnost, ')') as DisplayText
                    FROM ListaDejnosti 
                    ORDER BY SifraDejnost", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayText"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Dejnosti = items;
                }

                // Load OrganizacioniEdinici
                using (SqlCommand cmd = new SqlCommand(
                    "SELECT Id, OrganizacionaEdinica FROM SifrarnikOrganizacioniEdinici ORDER BY OrganizacionaEdinica", 
                    connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["OrganizacionaEdinica"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    OrganizacioniEdinici = items;
                }

                // Load RabotniPozicii with OrganizacionaEdinica info
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT 
                        rp.Id,
                        rp.RabotnaPozicija,
                        oe.OrganizacionaEdinica,
                        CONCAT(rp.RabotnaPozicija, ' (', oe.OrganizacionaEdinica, ')') as DisplayText
                    FROM SifrarnikRabotniPozicii rp
                    INNER JOIN SifrarnikOrganizacioniEdinici oe ON rp.OrganizacionaEdinicaId = oe.Id
                    ORDER BY oe.OrganizacionaEdinica, rp.RabotnaPozicija", 
                    connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayText"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    RabotniPozicii = items;
                }

                // Load NivoaNaRizik
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT 
                        Id,
                        OpisNivoRizik
                    FROM NivoaNaRizik 
                    ORDER BY Id", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["OpisNivoRizik"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    NivoaNaRizik = items;
                }

                // Load Banki
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT 
                        Id,
                        Banka
                    FROM SifrarnikBanki 
                    ORDER BY Banka", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Banka"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Banki = items;
                }

                // Load EkspozituriIdEkspozitura
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT 
                        Id,
                        Ime
                    FROM Ekspozituri
                    ORDER BY Ime", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Ime"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    EkspozituriIdEkspozituraOptions = items;
                }

                // Similar modifications for other dropdowns...
                // The rest of LoadDropdownLists remains the same but using direct assignment
                // instead of creating new SelectList objects
            }
        }

        public class ValidateDocumentDatesAttribute : ValidationAttribute
        {
            protected override ValidationResult IsValid(object value, ValidationContext validationContext)
            {
                var model = (KlientInputModel)validationContext.ObjectInstance;

                // If both dates are set, check if VaziDo is before VaziOd
                if (model.DatumVaziOdPasosLicnaKarta.HasValue && model.DatumVaziDoPasosLicnaKarta.HasValue)
                {
                    if (model.DatumVaziDoPasosLicnaKarta < model.DatumVaziOdPasosLicnaKarta)
                    {
                        // Set VaziDo to match VaziOd
                        model.DatumVaziDoPasosLicnaKarta = model.DatumVaziOdPasosLicnaKarta;
                    }
                }

                return ValidationResult.Success;
            }
        }

        public class ValidateContractDatesAttribute : ValidationAttribute
        {
            protected override ValidationResult IsValid(object value, ValidationContext validationContext)
            {
                var model = (KlientInputModel)validationContext.ObjectInstance;

                // If both dates are set, check if DogovorVaziDo is before DaumNaDogovor
                if (model.DaumNaDogovor.HasValue && model.DogovorVaziDo.HasValue)
                {
                    if (model.DogovorVaziDo < model.DaumNaDogovor)
                    {
                        // Set DogovorVaziDo to match DaumNaDogovor
                        model.DogovorVaziDo = model.DaumNaDogovor;
                    }
                }

                return ValidationResult.Success;
            }
        }

        public class ValidateEMBGUniqueAttribute : ValidationAttribute
        {
            protected override ValidationResult IsValid(object value, ValidationContext validationContext)
            {
                var embg = value as string;
                
                // If EMBG is null or empty, it's valid (optional field)
                if (string.IsNullOrWhiteSpace(embg))
                {
                    return ValidationResult.Success;
                }

                // Check if EMBG is exactly 13 digits
                if (!System.Text.RegularExpressions.Regex.IsMatch(embg, @"^\d{13}$"))
                {
                    return new ValidationResult("ЕМБГ мора да содржи точно 13 цифри");
                }

                // Note: Duplicate check is handled in OnPostAsync to avoid database dependency in validation
                return ValidationResult.Success;
            }
        }

        public class KlientInputModel
        {
            // Optional fields (all string fields are nullable by default in .NET 6+)
            public string? Naziv { get; set; }
            public string? Ime { get; set; }
            public string? Prezime { get; set; }
            public string? EDB { get; set; }
            public string? MB { get; set; }
            [ValidateEMBGUnique]
            public string? EMBG { get; set; }
            
            public int? ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija { get; set; }
            public string? UlicaOdDokumentZaIdentifikacija { get; set; }
            public string? BrojOdDokumentZaIdentifikacija { get; set; }
            
            public int? ListaOpstiniIdOpstinaZaKomunikacija { get; set; }
            public string? UlicaZaKomunikacija { get; set; }
            public string? BrojZaKomunikacija { get; set; }
            
            public DateTime? DatumNaTekovnaSostojba { get; set; }
            public string? BrojPasosLicnaKarta { get; set; }
            public DateTime? DatumVaziOdPasosLicnaKarta { get; set; }
            public DateTime? DatumVaziDoPasosLicnaKarta { get; set; }
            
            public int? ListaDejnostiIdDejnost { get; set; }
            [EmailAddress(ErrorMessage = "Внесете валидна email адреса")]
            public string? Email { get; set; }
            public string? Tel { get; set; }
            public string? Webstrana { get; set; }
            
            public bool SoglasnostZaDirektenMarketing { get; set; }
            public bool SoglasnostZaEmailKomunikacija { get; set; }
            public bool SoglasnostZaTelKomunikacija { get; set; }
            public DateTime? DatumNaPovlecenaSoglasnostZaDirektenMarketing { get; set; }
            
            public string? VistinskiSopstvenik { get; set; }
            public string? VistinskiSopstvenikIme { get; set; }
            public string? VistinskiSopstvenikPrezime { get; set; }
            
            public bool NositelNaJF { get; set; }
            public string? OsnovZaNositelNaJF { get; set; }
            public bool ZasilenaAnaliza { get; set; }
            
            public int? NivoaNaRizikIdNivoNaRizik { get; set; }
            [ValidateContractDates]
            public DateTime? DaumNaDogovor { get; set; }
            [ValidateContractDates]
            public DateTime? DogovorVaziDo { get; set; }
            public string? BrojNaDogovor { get; set; }
            public string? DogovorOpredelenoNeopredeleno { get; set; }
            public string? PlateznaSmetka { get; set; }
            
            public int? SifrarnikBankiIdBanka { get; set; }
            public long? SifrarnikRabotniPoziciiIdRabotnaPozicija { get; set; }
            public long? SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica { get; set; }
            public long? SifrarnikRabotniPoziciiIdNadreden { get; set; }
            
            public DateTime? NadredenVaziOd { get; set; }
            public DateTime? NadredenDo { get; set; }
            
            public bool ZadolzitelnoLicenca { get; set; }
            public string? BrojNaResenieOdASOZaLicenca { get; set; }
            public DateTime? DatumNaResenieOdASOZaLicenca { get; set; }
            public DateTime? DatumNaOdzemenaLicenca { get; set; }
            
            public string? BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti { get; set; }
            public DateTime? DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti { get; set; }
            public string? ZivotNezivot { get; set; }

            // Add the new property
            public string? KlientFizickoPravnoLice { get; set; }

            // Add the new property
            public string? KlientPol { get; set; }

            // Change from bool? to bool
            public bool KlientVraboten { get; set; }

            // Add the new property
            public bool KlientSorabotnik { get; set; }

            // Add the new properties
            public bool Osiguritel { get; set; }
            public bool BrokerskoDrustvo { get; set; }

            // Add the new property
            public DateTime? DatumNaOvlastuvanje { get; set; }

            public int? RokNaPlakanjeDenovi { get; set; }

            public string? Zabeleska { get; set; }

            // Add the new property
            public int? EkspozituriIdEkspozitura { get; set; }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                await LoadDropdownLists();
                return Page();
            }

            // Check for duplicate EMBG if it's provided
            if (!string.IsNullOrWhiteSpace(Input.EMBG))
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        SELECT COUNT(*) 
                        FROM Klienti 
                        WHERE EMBG = @EMBG", connection))
                    {
                        cmd.Parameters.AddWithValue("@EMBG", Input.EMBG);
                        var count = await cmd.ExecuteScalarAsync();
                        if ((int)count > 0)
                        {
                            ModelState.AddModelError("Input.EMBG", "ЕМБГ веќе постои во системот");
                            await LoadDropdownLists();
                            return Page();
                        }
                    }
                }
            }

            // Check for duplicate EDB if it's provided
            if (!string.IsNullOrWhiteSpace(Input.EDB))
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        SELECT COUNT(*) 
                        FROM Klienti 
                        WHERE EDB = @EDB", connection))
                    {
                        cmd.Parameters.AddWithValue("@EDB", Input.EDB);
                        var count = await cmd.ExecuteScalarAsync();
                        if ((int)count > 0)
                        {
                            ModelState.AddModelError("Input.EDB", "ЕДБ веќе постои во системот");
                            await LoadDropdownLists();
                            return Page();
                        }
                    }
                }
            }

            // Check for duplicate MB if it's provided
            if (!string.IsNullOrWhiteSpace(Input.MB))
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        SELECT COUNT(*) 
                        FROM Klienti 
                        WHERE MB = @MB", connection))
                    {
                        cmd.Parameters.AddWithValue("@MB", Input.MB);
                        var count = await cmd.ExecuteScalarAsync();
                        if ((int)count > 0)
                        {
                            ModelState.AddModelError("Input.MB", "МБ веќе постои во системот");
                            await LoadDropdownLists();
                            return Page();
                        }
                    }
                }
            }

            string insertConnectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(insertConnectionString))
            { 
                await connection.OpenAsync();
                
                // Debug: Log the SQL and parameters
                System.Diagnostics.Debug.WriteLine("Executing insert...");
                System.Diagnostics.Debug.WriteLine($"Username from session: {HttpContext.Session.GetString("Username")}");

                string insertSql = @"
                    INSERT INTO [dbo].[Klienti] (
                        [DateCreated],
                        [UsernameCreated],
                        [KlientSorabotnik],
                        [KlientVraboten],
                        [KlientFizickoPravnoLice],
                        [KlientPol],
                        [Naziv],
                        [Ime],
                        [Prezime],
                        [EDB],
                        [MB],
                        [EMBG],
                        [ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija],
                        [UlicaOdDokumentZaIdentifikacija],
                        [BrojOdDokumentZaIdentifikacija],
                        [ListaOpstiniIdOpstinaZaKomunikacija],
                        [UlicaZaKomunikacija],
                        [BrojZaKomunikacija],
                        [DatumNaTekovnaSostojba],
                        [BrojPasosLicnaKarta],
                        [DatumVaziOdPasosLicnaKarta],
                        [DatumVaziDoPasosLicnaKarta],
                        [ListaDejnostiIdDejnost],
                        [Email],
                        [Tel],
                        [Webstrana],
                        [SoglasnostZaDirektenMarketing],
                        [SoglasnostZaEmailKomunikacija],
                        [SoglasnostZaTelKomunikacija],
                        [DatumNaPovlecenaSoglasnostZaDirektenMarketing],
                        [VistinskiSopstvenik],
                        [VistinskiSopstvenikIme],
                        [VistinskiSopstvenikPrezime],
                        [NositelNaJF],
                        [OsnovZaNositelNaJF],
                        [ZasilenaAnaliza],
                        [NivoaNaRizikIdNivoNaRizik],
                        [DaumNaDogovor],
                        [DogovorVaziDo],
                        [BrojNaDogovor],
                        [DogovorOpredelenoNeopredeleno],
                        [PlateznaSmetka],
                        [SifrarnikBankiIdBanka],
                        [SifrarnikRabotniPoziciiIdRabotnaPozicija],
                        [SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica],
                        [SifrarnikRabotniPoziciiIdNadreden],
                        [NadredenVaziOd],
                        [NadredenDo],
                        [ZadolzitelnoLicenca],
                        [BrojNaResenieOdASOZaLicenca],
                        [DatumNaResenieOdASOZaLicenca],
                        [DatumNaOdzemenaLicenca],
                        [BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti],
                        [DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti],
                        [ZivotNezivot],
                        [Osiguritel],
                        [BrokerskoDrustvo],
                        [DatumNaOvlastuvanje],
                        [RokNaPlakanjeDenovi],
                        [Zabeleska],
                        [EkspozituriIdEkspozitura]
                    ) VALUES (
                        GETDATE(),
                        @UsernameCreated,
                        @KlientSorabotnik,
                        @KlientVraboten,
                        @KlientFizickoPravnoLice,
                        @KlientPol,
                        @Naziv,
                        @Ime,
                        @Prezime,
                        @EDB,
                        @MB,
                        @EMBG,
                        @ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija,
                        @UlicaOdDokumentZaIdentifikacija,
                        @BrojOdDokumentZaIdentifikacija,
                        @ListaOpstiniIdOpstinaZaKomunikacija,
                        @UlicaZaKomunikacija,
                        @BrojZaKomunikacija,
                        @DatumNaTekovnaSostojba,
                        @BrojPasosLicnaKarta,
                        @DatumVaziOdPasosLicnaKarta,
                        @DatumVaziDoPasosLicnaKarta,
                        @ListaDejnostiIdDejnost,
                        @Email,
                        @Tel,
                        @Webstrana,
                        @SoglasnostZaDirektenMarketing,
                        @SoglasnostZaEmailKomunikacija,
                        @SoglasnostZaTelKomunikacija,
                        @DatumNaPovlecenaSoglasnostZaDirektenMarketing,
                        @VistinskiSopstvenik,
                        @VistinskiSopstvenikIme,
                        @VistinskiSopstvenikPrezime,
                        @NositelNaJF,
                        @OsnovZaNositelNaJF,
                        @ZasilenaAnaliza,
                        @NivoaNaRizikIdNivoNaRizik,
                        @DaumNaDogovor,
                        @DogovorVaziDo,
                        @BrojNaDogovor,
                        @DogovorOpredelenoNeopredeleno,
                        @PlateznaSmetka,
                        @SifrarnikBankiIdBanka,
                        @SifrarnikRabotniPoziciiIdRabotnaPozicija,
                        @SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica,
                        @SifrarnikRabotniPoziciiIdNadreden,
                        @NadredenVaziOd,
                        @NadredenDo,
                        @ZadolzitelnoLicenca,
                        @BrojNaResenieOdASOZaLicenca,
                        @DatumNaResenieOdASOZaLicenca,
                        @DatumNaOdzemenaLicenca,
                        @BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti,
                        @DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti,
                        @ZivotNezivot,
                        @Osiguritel,
                        @BrokerskoDrustvo,
                        @DatumNaOvlastuvanje,
                        @RokNaPlakanjeDenovi,
                        @Zabeleska,
                        @EkspozituriIdEkspozitura
                    );
                    SELECT CAST(SCOPE_IDENTITY() as bigint);";

                using (SqlCommand cmd = new SqlCommand(insertSql, connection))
                {
                    cmd.Parameters.AddWithValue("@UsernameCreated", HttpContext.Session.GetString("Username"));
                    cmd.Parameters.AddWithValue("@KlientSorabotnik", Input.KlientSorabotnik);
                    cmd.Parameters.AddWithValue("@KlientVraboten", Input.KlientVraboten);
                    cmd.Parameters.AddWithValue("@KlientFizickoPravnoLice", Input.KlientFizickoPravnoLice ?? "F");
                    cmd.Parameters.AddWithValue("@KlientPol", Input.KlientPol ?? "N");
                    cmd.Parameters.AddWithValue("@Naziv", Input.Naziv ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Ime", Input.Ime ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Prezime", Input.Prezime ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@EDB", Input.EDB ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@MB", Input.MB ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@EMBG", Input.EMBG ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija", 
                        Input.ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@UlicaOdDokumentZaIdentifikacija", 
                        Input.UlicaOdDokumentZaIdentifikacija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@BrojOdDokumentZaIdentifikacija", 
                        Input.BrojOdDokumentZaIdentifikacija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@ListaOpstiniIdOpstinaZaKomunikacija", 
                        Input.ListaOpstiniIdOpstinaZaKomunikacija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@UlicaZaKomunikacija", 
                        Input.UlicaZaKomunikacija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@BrojZaKomunikacija", 
                        Input.BrojZaKomunikacija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@DatumNaTekovnaSostojba", 
                        Input.DatumNaTekovnaSostojba ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@BrojPasosLicnaKarta", 
                        Input.BrojPasosLicnaKarta ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@DatumVaziOdPasosLicnaKarta", 
                        Input.DatumVaziOdPasosLicnaKarta ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@DatumVaziDoPasosLicnaKarta", 
                        Input.DatumVaziDoPasosLicnaKarta ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@ListaDejnostiIdDejnost", 
                        Input.ListaDejnostiIdDejnost ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Email", Input.Email ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Tel", Input.Tel ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Webstrana", Input.Webstrana ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@SoglasnostZaDirektenMarketing", Input.SoglasnostZaDirektenMarketing);
                    cmd.Parameters.AddWithValue("@SoglasnostZaEmailKomunikacija", 
                        Input.SoglasnostZaEmailKomunikacija);
                    cmd.Parameters.AddWithValue("@SoglasnostZaTelKomunikacija", Input.SoglasnostZaTelKomunikacija);
                    cmd.Parameters.AddWithValue("@DatumNaPovlecenaSoglasnostZaDirektenMarketing", 
                        Input.DatumNaPovlecenaSoglasnostZaDirektenMarketing ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@VistinskiSopstvenik", 
                        Input.VistinskiSopstvenik ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@VistinskiSopstvenikIme", 
                        Input.VistinskiSopstvenikIme ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@VistinskiSopstvenikPrezime", 
                        Input.VistinskiSopstvenikPrezime ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@NositelNaJF", Input.NositelNaJF);
                    cmd.Parameters.AddWithValue("@OsnovZaNositelNaJF", 
                        Input.OsnovZaNositelNaJF ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@ZasilenaAnaliza", Input.ZasilenaAnaliza);
                    cmd.Parameters.AddWithValue("@NivoaNaRizikIdNivoNaRizik", 
                        Input.NivoaNaRizikIdNivoNaRizik ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@DaumNaDogovor", 
                        Input.DaumNaDogovor ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@DogovorVaziDo", 
                        Input.DogovorVaziDo ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@BrojNaDogovor", 
                        Input.BrojNaDogovor ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@DogovorOpredelenoNeopredeleno", 
                        Input.DogovorOpredelenoNeopredeleno ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@PlateznaSmetka", 
                        Input.PlateznaSmetka ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@SifrarnikBankiIdBanka", 
                        Input.SifrarnikBankiIdBanka ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@SifrarnikRabotniPoziciiIdRabotnaPozicija", 
                        Input.SifrarnikRabotniPoziciiIdRabotnaPozicija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica", 
                        Input.SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@SifrarnikRabotniPoziciiIdNadreden", 
                        Input.SifrarnikRabotniPoziciiIdNadreden ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@NadredenVaziOd", 
                        Input.NadredenVaziOd ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@NadredenDo", 
                        Input.NadredenDo ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@ZadolzitelnoLicenca", Input.ZadolzitelnoLicenca);
                    cmd.Parameters.AddWithValue("@BrojNaResenieOdASOZaLicenca", 
                        Input.BrojNaResenieOdASOZaLicenca ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@DatumNaResenieOdASOZaLicenca", 
                        Input.DatumNaResenieOdASOZaLicenca ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@DatumNaOdzemenaLicenca", 
                        Input.DatumNaOdzemenaLicenca ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti", 
                        Input.BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti", 
                        Input.DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@ZivotNezivot", 
                        Input.ZivotNezivot ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Osiguritel", Input.Osiguritel);
                    cmd.Parameters.AddWithValue("@BrokerskoDrustvo", Input.BrokerskoDrustvo);
                    cmd.Parameters.AddWithValue("@DatumNaOvlastuvanje", 
                        Input.DatumNaOvlastuvanje ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@RokNaPlakanjeDenovi", 
                        Input.RokNaPlakanjeDenovi ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Zabeleska", 
                        Input.Zabeleska ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@EkspozituriIdEkspozitura", 
                        Input.EkspozituriIdEkspozitura ?? (object)DBNull.Value);

                    try
                    {
                        var newId = await cmd.ExecuteScalarAsync();

                        // Check if this was opened from polisa form
                        if (Request.Query["fromPolisa"].ToString() == "true")
                        {
                            // Return JavaScript that will send message to parent window
                            return Content($@"
                                <script>
                                    window.opener.postMessage({{
                                        type: 'clientAdded',
                                        source: '{Request.Query["source"]}',
                                        client: {{
                                            tip: '{Input.KlientFizickoPravnoLice}',
                                            naziv: '{Input.Naziv}',
                                            ime: '{Input.Ime}',
                                            prezime: '{Input.Prezime}',
                                            mb: '{Input.MB}',
                                            edb: '{Input.EDB}',
                                            embg: '{Input.EMBG}',
                                            id: '{newId}'
                                        }}
                                    }}, '*');
                                    window.close();
                                </script>
                            ", "text/html");
                        }

                        TempData["SuccessMessage"] = "Клиентот е успешно додаден.";
                        return RedirectToPage("./ListaKlienti");
                    }
                    catch (Exception ex)
                    {
                        // Debug: Log the full exception
                        System.Diagnostics.Debug.WriteLine($"SQL Error: {ex.ToString()}");
                        ModelState.AddModelError("", "Грешка при зачувување на податоците: " + ex.Message);
                        
                        // Reload dropdowns and options before returning
                        await LoadDropdownLists();
                        ZivotNezivotOptions = new List<SelectListItem>
                        {
                            new SelectListItem("Живот", "Живот"),
                            new SelectListItem("Неживот", "Неживот")
                        };
                        KlientFizickoPravnoLiceOptions = new List<SelectListItem>
                        {
                            new SelectListItem("Физичко лице", "F"),
                            new SelectListItem("Правно лице", "P")
                        };
                        return Page();
                    }
                }
            }
        }

        public async Task<IActionResult> OnGetSearchNadredenAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE KlientVraboten = 1
                       AND (MB LIKE @Search + '%'
                       OR EDB LIKE @Search + '%'
                       OR EMBG LIKE @Search + '%'
                       OR Naziv LIKE '%' + @Search + '%'
                       OR Ime LIKE '%' + @Search + '%'
                       OR Prezime LIKE '%' + @Search + '%')
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<IActionResult> OnGetCheckDuplicateEMBGAsync(string embg)
        {
            if (string.IsNullOrWhiteSpace(embg))
                return new JsonResult(new { exists = false });

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT COUNT(*) 
                    FROM Klienti 
                    WHERE EMBG = @EMBG", connection))
                {
                    cmd.Parameters.AddWithValue("@EMBG", embg);
                    var count = await cmd.ExecuteScalarAsync();
                    return new JsonResult(new { exists = (int)count > 0 });
                }
            }
        }

        public async Task<IActionResult> OnGetCheckDuplicateEDBAsync(string edb)
        {
            if (string.IsNullOrWhiteSpace(edb))
                return new JsonResult(new { exists = false });

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT COUNT(*) 
                    FROM Klienti 
                    WHERE EDB = @EDB", connection))
                {
                    cmd.Parameters.AddWithValue("@EDB", edb);
                    var count = await cmd.ExecuteScalarAsync();
                    return new JsonResult(new { exists = (int)count > 0 });
                }
            }
        }

        public async Task<IActionResult> OnGetCheckDuplicateMBAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new { exists = false });

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT COUNT(*) 
                    FROM Klienti 
                    WHERE MB = @MB", connection))
                {
                    cmd.Parameters.AddWithValue("@MB", mb);
                    var count = await cmd.ExecuteScalarAsync();
                    return new JsonResult(new { exists = (int)count > 0 });
                }
            }
        }

        public async Task<IActionResult> OnGetSearchDejnostAsync(string search)
        {
            if (string.IsNullOrWhiteSpace(search))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        SifraDejnost,
                        NazivDejnost
                    FROM ListaDejnosti 
                    WHERE SifraDejnost LIKE @Search + '%'
                       OR NazivDejnost LIKE '%' + @Search + '%'
                    ORDER BY 
                        CASE 
                            WHEN SifraDejnost LIKE @Search + '%' THEN 1
                            ELSE 2
                        END,
                        SifraDejnost, NazivDejnost", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", search);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            sifraDejnost = reader["SifraDejnost"],
                            nazivDejnost = reader["NazivDejnost"]
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<IActionResult> OnGetGetAllDejnostiAsync()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT 
                        Id, 
                        SifraDejnost,
                        NazivDejnost
                    FROM ListaDejnosti 
                    ORDER BY SifraDejnost", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            sifraDejnost = reader["SifraDejnost"],
                            nazivDejnost = reader["NazivDejnost"]
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }
    }
} 