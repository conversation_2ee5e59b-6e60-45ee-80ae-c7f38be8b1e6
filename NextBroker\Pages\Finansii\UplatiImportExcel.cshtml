@page
@model NextBroker.Pages.Finansii.UplatiImportExcelModel
@{
    ViewData["Title"] = "Импорт на уплати од Excel";
    var formClass = !Model.HasAdminAccess ? "form-disabled" : "";
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container-fluid mt-4 px-4">
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Импорт на уплати</h5>
        </div>
        <div class="card-body">
            @if (!Model.IsPreview)
            {
                <form method="post" enctype="multipart/form-data" class="@formClass">
                    @Html.AntiForgeryToken()
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Осигурител</label>
                                <select class="form-select" asp-for="OsiguritelId" required>
                                    <option value="">-- Изберете осигурител --</option>
                                    @foreach (var osiguritel in Model.Osiguriteli)
                                    {
                                        <option value="@osiguritel.Value">@osiguritel.Text</option>
                                    }
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Excel документ</label>
                                <input type="file" class="form-control" asp-for="ExcelFile" accept=".xls,.xlsx" required />
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <h6>Формат на Excel документот:</h6>
                        <p class="mb-0">
                            Excel документот треба да ги содржи следните колони:
                            <ul>
                                <li>UplataDatum (датум)</li>
                                <li>Iznos (децимален број)</li>
                                <li>BrojNaPolisa (број, опционално)</li>
                                <li>Neraspredelena (true/false)</li>
                                <li>PovratNaSredstva (true/false)</li>
                                <li>BrojNaPolisa (број, опционално)</li>
                            </ul>
                        </p>
                        <div class="mt-3">
                            <a href="/templates/UplatiImportExcelTemplate.xlsx" 
                               class="btn btn-outline-primary" download>
                                <i class="fas fa-download me-1"></i> Превземи шаблон
                            </a>
                        </div>
                    </div>

                    <div class="mt-3">
                        <button type="submit" class="btn btn-primary" @(Model.HasAdminAccess ? "" : "disabled")>
                            <i class="fas fa-upload me-1"></i> Импортирај
                        </button>
                        <a href="/Finansii/Uplati" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i> Откажи
                        </a>
                    </div>
                </form>
            }
            else
            {
                <div class="table-responsive">
                    <h5 class="mb-3">Преглед на податоци за импорт</h5>
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" class="form-check-input" id="selectAll" checked>
                                </th>
                                <th>Датум на уплата</th>
                                <th>Износ</th>
                                <th>Број на полиса</th>
                                <th>Нераспределена</th>
                                <th>Поврат на средства</th>
                                <th>Број на фактура</th>
                                <th>Статус</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var uplata in Model.PreviewUplati)
                            {
                                <tr class="@(!uplata.IsValid ? "table-danger" : "")">
                                    <td>
                                        <input type="checkbox" class="form-check-input row-checkbox" 
                                               @(uplata.IsSelected ? "checked" : "")
                                               @(!uplata.IsValid ? "disabled" : "")
                                               data-valid="@uplata.IsValid.ToString().ToLower()">
                                    </td>
                                    <td>@uplata.UplataDatum.ToShortDateString()</td>
                                    <td>@uplata.Iznos.ToString("N2")</td>
                                    <td>@(!string.IsNullOrEmpty(uplata.PolisaBroj) ? uplata.PolisaBroj : "")</td>
                                    <td>@(uplata.Neraspredelena ? "Да" : "Не")</td>
                                    <td>@(uplata.PovratNaSredstva ? "Да" : "Не")</td>
                                    <td>@(!string.IsNullOrEmpty(uplata.BrojNaFaktura) ? uplata.BrojNaFaktura : "")</td>
                                    <td>
                                        @if (!string.IsNullOrEmpty(uplata.ValidationMessage))
                                        {
                                            <span class="text-danger">@uplata.ValidationMessage</span>
                                        }
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>

                    <form method="post" asp-page-handler="Save" class="mt-3" id="saveForm">
                        @Html.AntiForgeryToken()
                        @Html.HiddenFor(m => m.OsiguritelId)
                        @Html.HiddenFor(m => m.HasAdminAccess)
                        @for (var i = 0; i < Model.PreviewUplati.Count; i++)
                        {
                            @Html.HiddenFor(m => m.PreviewUplati[i].UplataDatum)
                            @Html.HiddenFor(m => m.PreviewUplati[i].Iznos)
                            @Html.HiddenFor(m => m.PreviewUplati[i].PolisaBroj)
                            @Html.HiddenFor(m => m.PreviewUplati[i].Neraspredelena)
                            @Html.HiddenFor(m => m.PreviewUplati[i].PovratNaSredstva)
                            @Html.HiddenFor(m => m.PreviewUplati[i].IsSelected)
                            @Html.HiddenFor(m => m.PreviewUplati[i].BrojNaFaktura)
                        }
                        
                        <button type="submit" class="btn btn-success" @(Model.HasAdminAccess ? "" : "enabled")>
                            <i class="fas fa-save me-1"></i> Зачувај
                        </button>
                        <a href="/Finansii/UplatiImportExcel" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i> Откажи
                        </a>
                    </form>
                </div>
            }

            @if (!ModelState.IsValid)
            {
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach (var modelState in ViewData.ModelState.Values)
                        {
                            foreach (var error in modelState.Errors)
                            {
                                <li>@error.ErrorMessage</li>
                            }
                        }
                    </ul>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Handle "Select All" checkbox
            $("#selectAll").change(function () {
                $(".row-checkbox:not(:disabled)").prop('checked', $(this).prop('checked'));
                updateHiddenFields();
            });

            // Handle individual row checkboxes
            $(".row-checkbox").change(function () {
                updateHiddenFields();
                // Update "Select All" checkbox
                $("#selectAll").prop('checked', 
                    $(".row-checkbox:not(:disabled):checked").length === $(".row-checkbox:not(:disabled)").length);
            });

            // Update hidden fields based on checkboxes
            function updateHiddenFields() {
                $(".row-checkbox").each(function (index) {
                    $(`input[name="PreviewUplati[${index}].IsSelected"]`)
                        .val($(this).prop('checked'));
                });
            }

            // Initial "Select All" state
            $("#selectAll").prop('checked', 
                $(".row-checkbox:not(:disabled):checked").length === $(".row-checkbox:not(:disabled)").length);
        });
    </script>
} 