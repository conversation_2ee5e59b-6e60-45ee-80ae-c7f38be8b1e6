using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;

namespace NextBroker.Pages.AdministrationPages
{
    public class SifrarnikBankiSmetkiModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public SifrarnikBankiSmetkiModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        [BindProperty]
        public InputModel Input { get; set; } = new();

        public class InputModel
        {
            public int? Id { get; set; }

            [Required(ErrorMessage = "Банка е задолжително поле")]
            [Display(Name = "Банка")]
            public int SifrarnikBankiId { get; set; }

            [Required(ErrorMessage = "Број на сметка е задолжително поле")]
            [Display(Name = "Број на сметка")]
            public string BrojNaSmetka { get; set; }
        }

        public List<SelectListItem> Banki { get; set; }
        public List<BankAccountViewModel> BankAccounts { get; set; }

        public class BankAccountViewModel
        {
            public int Id { get; set; }
            public int SifrarnikBankiId { get; set; }
            public string BankName { get; set; }
            public string BrojNaSmetka { get; set; }
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("SifrarnikBankiSmetki"))
            {
                return RedirectToAccessDenied();
            }

            await LoadBanki();
            await LoadBankAccounts();
            return Page();
        }

        private async Task LoadBanki()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Banka 
                    FROM SifrarnikBanki 
                    ORDER BY Banka", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Banka"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Banki = items;
                }
            }
        }

        private async Task LoadBankAccounts()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT s.Id, s.SifrarnikBankiId, b.Banka, s.BrojNaSmetka
                    FROM SifrarnikBankiSmetki s
                    LEFT JOIN SifrarnikBanki b ON s.SifrarnikBankiId = b.Id
                    ORDER BY b.Banka, s.BrojNaSmetka", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    BankAccounts = new List<BankAccountViewModel>();
                    while (await reader.ReadAsync())
                    {
                        BankAccounts.Add(new BankAccountViewModel
                        {
                            Id = reader.GetInt32(0),
                            SifrarnikBankiId = reader.GetInt32(1),
                            BankName = reader.GetString(2),
                            BrojNaSmetka = reader.GetString(3)
                        });
                    }
                }
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                await LoadBanki();
                await LoadBankAccounts();
                return Page();
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                if (Input.Id.HasValue)
                {
                    // Update existing record
                    using (SqlCommand cmd = new SqlCommand(@"
                        UPDATE SifrarnikBankiSmetki 
                        SET SifrarnikBankiId = @SifrarnikBankiId,
                            BrojNaSmetka = @BrojNaSmetka
                        WHERE Id = @Id", connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", Input.Id.Value);
                        cmd.Parameters.AddWithValue("@SifrarnikBankiId", Input.SifrarnikBankiId);
                        cmd.Parameters.AddWithValue("@BrojNaSmetka", Input.BrojNaSmetka);
                        await cmd.ExecuteNonQueryAsync();
                    }
                }
                else
                {
                    // Insert new record
                    using (SqlCommand cmd = new SqlCommand(@"
                        INSERT INTO SifrarnikBankiSmetki (SifrarnikBankiId, BrojNaSmetka)
                        VALUES (@SifrarnikBankiId, @BrojNaSmetka)", connection))
                    {
                        cmd.Parameters.AddWithValue("@SifrarnikBankiId", Input.SifrarnikBankiId);
                        cmd.Parameters.AddWithValue("@BrojNaSmetka", Input.BrojNaSmetka);
                        await cmd.ExecuteNonQueryAsync();
                    }
                }
            }

            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostDeleteAsync(int id)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand("DELETE FROM SifrarnikBankiSmetki WHERE Id = @Id", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", id);
                    await cmd.ExecuteNonQueryAsync();
                }
            }

            return RedirectToPage();
        }

        public async Task<JsonResult> OnGetEditAsync(int id)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, SifrarnikBankiId, BrojNaSmetka
                    FROM SifrarnikBankiSmetki 
                    WHERE Id = @Id", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", id);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    if (await reader.ReadAsync())
                    {
                        return new JsonResult(new
                        {
                            id = reader.GetInt32(0),
                            sifrarnikBankiId = reader.GetInt32(1),
                            brojNaSmetka = reader.GetString(2)
                        });
                    }
                }
            }
            return new JsonResult(null);
        }
    }
}
