using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;

namespace NextBroker.Pages.Polisi
{
    public class DodajPolisaKlasa12Model : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public DodajPolisaKlasa12Model(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("DodajPolisaKlasa12"))
            {
                return RedirectToAccessDenied();
            }

            return Page();
        }
    }
}
