﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>d6f0f7ac-5609-49bf-8e8a-cbcf4cbe4fab</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ExcelDataReader" Version="3.7.0" />
    <PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0" />
    <PackageReference Include="itext7" Version="8.0.2" />
    <PackageReference Include="itext7.pdfhtml" Version="5.0.2" />
    <PackageReference Include="itext7.bouncy-castle-adapter" Version="8.0.2" />
    <PackageReference Include="iTextSharp" Version="5.5.13.4" />
    <PackageReference Include="Magick.NET-Q16-AnyCPU" Version="13.5.0" />
    <PackageReference Include="Magick.NET.Core" Version="13.5.0" />
    <PackageReference Include="MailKit" Version="4.7.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.DataProtection" Version="8.0.8" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="MimeKit" Version="4.7.1" />
    <PackageReference Include="Npgsql" Version="8.0.4" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.4" />
    <PackageReference Include="SSH.NET" Version="2024.1.0" />
    <PackageReference Include="EPPlus" Version="6.2.4" />
    <PackageReference Include="Tesseract" Version="5.2.0" />
    <PackageReference Include="Tesseract.Data.English" Version="4.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Data\" />
    <Folder Include="Models\" />
    <Folder Include="wwwroot\images\Icons\" />
    <Folder Include="wwwroot\images\contact\" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Services/Fonts/Arial.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
