@page
@model NextBroker.Pages.Finansii.PreraspredelbaNaUplatiModel
@{
    ViewData["Title"] = "Прераспределба на уплати";
}

<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">

<style>
    #polisaSearchResults {
        z-index: 1050;
        max-height: 250px;
        overflow-y: auto;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    
    #polisaSearchResults .list-group-item {
        padding: 8px 15px;
        cursor: pointer;
    }
    
    #polisaSearchResults .list-group-item:hover {
        background-color: #f8f9fa;
    }
    
    #polisaSearchResults small {
        font-size: 0.85em;
    }
</style>

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

@Html.AntiForgeryToken()

<div class="container-fluid mt-4">
    <div class="card">
        <div class="card-header">
            <h5>Листа на уплати за прераспределба</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-bordered table-hover" id="uplatiTable">
                    <thead class="thead-dark">
                        <tr>
                            <th>ИД</th>
                            <th>Датум на креирање</th>
                            <th>Креирано од</th>
                            <th>Уплата ИД</th>
                            <th>Датум на уплата</th>
                            <th>Тип на плаќање</th>
                            <th>Број на извод</th>
                            <th>Износ</th>
                            <th>Број на полиса</th>
                            <th>Нераспределена</th>
                            <th>Сторно</th>
                            <th>Банка</th>
                            <th>Уплаќач</th>
                            <th>Број на фактура</th>
                            <th>Пренесена премија кон осигурител</th>
                            <th>Прераспределена уплата</th>
                            <th>Средства од прераспределена уплата</th>
                            <th>Датум на прераспределување</th>
                            <th>Преостанат износ</th>
                            <th>Акции</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var uplata in Model.UplatiZaPreraspredelba)
                        {
                            bool isZeroAmount = uplata.PreostanatIznos == 0 || uplata.PreostanatIznos == null;
                            <tr class="@(isZeroAmount ? "table-secondary" : "")">
                                <td>@uplata.Id</td>
                                <td>@(uplata.DateCreated?.ToString("dd.MM.yyyy HH:mm"))</td>
                                <td>@uplata.UsernameCreated</td>
                                <td>@uplata.UplataId</td>
                                <td>@(uplata.UplataDatum?.ToString("dd.MM.yyyy"))</td>
                                <td>@uplata.TipNaPlakanje</td>
                                <td>@uplata.BrojIzvod</td>
                                <td>@(uplata.Iznos?.ToString("N2"))</td>
                                <td>@uplata.PolisaBroj</td>
                                <td>@(uplata.Neraspredelena ? "Да" : "Не")</td>
                                <td>@(uplata.Storno ? "Да" : "Не")</td>
                                <td>@uplata.Banka</td>
                                <td>@uplata.Uplakac</td>
                                <td>@uplata.BrojNaFaktura</td>
                                <td>@(uplata.PrenesenaPremijaKonOsiguritel ? "Да" : "Не")</td>
                                <td>@(uplata.PreraspredelenaUplata ? "Да" : "Не")</td>
                                <td>@(uplata.SredstvaOdPreraspredelenaUplata ? "Да" : "Не")</td>
                                <td>@(uplata.DatumPreraspredeluvanje?.ToString("dd.MM.yyyy"))</td>
                                <td class="@(isZeroAmount ? "text-danger fw-bold" : "")">@(uplata.PreostanatIznos?.ToString("N2"))</td>
                                <td>
                                    @{
                                        string buttonClass = isZeroAmount ? "btn-secondary" : "btn-primary";
                                    }
                                    <button type="button" class="btn @buttonClass btn-sm preraspredeli-btn" 
                                            data-id="@uplata.Id" 
                                            data-preostanatiznos="@uplata.PreostanatIznos"
                                            @(isZeroAmount ? "disabled" : "")
                                            title="@(isZeroAmount ? "Нема преостанат износ за прераспределба" : "Прераспредели")">
                                        Прераспредели
                                    </button>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Preraspredeli -->
<div class="modal fade" id="preraspredelbaNaUplatiModal" tabindex="-1" role="dialog" aria-labelledby="preraspredelbaNaUplatiModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="preraspredelbaNaUplatiModalLabel">Прераспределба на уплати</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="preraspredelbaNaUplatiForm">
                    <div class="form-group">
                        <label for="uplataId">ИД на уплата:</label>
                        <input type="text" class="form-control" id="uplataId" readonly>
                    </div>
                    <div class="form-group">
                        <label for="preostanIznos">Преостанат износ:</label>
                        <input type="text" class="form-control" id="preostanIznos" readonly>
                    </div>
                    <div class="form-group">
                        <label for="iznosZaPreraspredelba">Износ за прераспределба:</label>
                        <input type="number" step="0.0001" class="form-control" id="iznosZaPreraspredelba" required>
                        <div class="invalid-feedback">
                            Износот не може да биде поголем од преостанатиот износ.
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="brojNaPolisaZaRaspredeluvanje">Број на полиса за распределување:</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="brojNaPolisaZaRaspredeluvanje" required autocomplete="off">
                        </div>
                        <div id="polisaSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Затвори</button>
                <button type="button" class="btn btn-primary" id="preraspredeli-submit">Прераспредели</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <!-- DataTables JS -->
    <script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        $(document).ready(function () {
            // Initialize tooltips
            $('[data-toggle="tooltip"], [title]').tooltip();
            
            // Simple DataTable initialization with Macedonian language
            $('#uplatiTable').DataTable({
                pageLength: 10,
                lengthMenu: [[10, 20, 50, -1], [10, 20, 50, "Сите"]],
                language: {
                    "sProcessing":     "Обработка...",
                    "sLengthMenu":     "Прикажи _MENU_ записи",
                    "sZeroRecords":    "Не се пронајдени записи",
                    "sEmptyTable":     "Нема податоци во табелата",
                    "sInfo":           "Прикажани _START_ до _END_ од вкупно _TOTAL_ записи",
                    "sInfoEmpty":      "Прикажани 0 до 0 од вкупно 0 записи",
                    "sInfoFiltered":   "(филтрирано од вкупно _MAX_ записи)",
                    "sSearch":         "Пребарај:",
                    "oPaginate": {
                        "sFirst":    "Прва",
                        "sLast":     "Последна",
                        "sNext":     "Следна",
                        "sPrevious": "Претходна"
                    }
                },
                columnDefs: [
                    { targets: -1, orderable: false } // Disable sorting for the last column (actions)
                ]
            });
            
            // Open modal when button is clicked
            $('.preraspredeli-btn').on('click', function () {
                const id = $(this).data('id');
                const preostanatiznos = $(this).data('preostanatiznos');
                
                $('#uplataId').val(id);
                $('#preostanIznos').val(preostanatiznos);
                $('#iznosZaPreraspredelba').val('');
                $('#brojNaPolisaZaRaspredeluvanje').val('');
                
                $('#preraspredelbaNaUplatiModal').modal('show');
            });

            // Fix for close button
            $('.close, .btn-secondary').on('click', function() {
                $('#preraspredelbaNaUplatiModal').modal('hide');
            });

            // Validate the input amount
            $('#iznosZaPreraspredelba').on('input', function () {
                const preostanIznos = parseFloat($('#preostanIznos').val());
                const iznosZaPreraspredelba = parseFloat($(this).val());
                
                if (iznosZaPreraspredelba > preostanIznos) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            // Policy search functionality
            let searchTimeout;
            const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();

            $('#brojNaPolisaZaRaspredeluvanje').on('input', function() {
                clearTimeout(searchTimeout);
                const searchTerm = $(this).val();
                const resultsDiv = $('#polisaSearchResults');

                if (searchTerm.length < 1) {
                    resultsDiv.hide();
                    return;
                }

                searchTimeout = setTimeout(function() {
                    $.ajax({
                        url: '?handler=SearchPolisa',
                        type: 'GET',
                        data: { term: searchTerm },
                        headers: {
                            "RequestVerificationToken": antiForgeryToken
                        },
                        success: function(data) {
                            if (data && data.length > 0) {
                                let html = '<div class="list-group">';
                                data.forEach(item => {
                                    let displayText = `${item.brojNaPolisa} - ${item.dogovoruvac}`;
                                    let details = [];
                                    
                                    if (item.osiguritel) details.push(`Осигурител: ${item.osiguritel}`);
                                    if (item.datumVaziOd && item.datumVaziDo) {
                                        details.push(`Важи од: ${item.datumVaziOd} до ${item.datumVaziDo}`);
                                    }
                                    
                                    html += `<a href="#" class="list-group-item list-group-item-action" 
                                              data-broj-polisa="${item.brojNaPolisa}">
                                              <div><strong>${displayText}</strong></div>
                                              <small class="text-muted">${details.join(' | ')}</small>
                                           </a>`;
                                });
                                html += '</div>';
                                resultsDiv.html(html).show();
                            } else {
                                resultsDiv.html(`
                                    <div class="list-group">
                                        <div class="list-group-item text-center">
                                            <p class="mb-2">Нема пронајдени резултати</p>
                                        </div>
                                    </div>
                                `).show();
                            }
                        },
                        error: function() {
                            resultsDiv.html(`
                                <div class="list-group">
                                    <div class="list-group-item text-center">
                                        <p class="text-danger mb-2">Грешка при пребарување</p>
                                    </div>
                                </div>
                            `).show();
                        }
                    });
                }, 300);
            });

            // Handle selection of policy from search results
            $(document).on('click', '#polisaSearchResults .list-group-item', function(e) {
                e.preventDefault();
                const brojPolisa = $(this).data('broj-polisa');
                $('#brojNaPolisaZaRaspredeluvanje').val(brojPolisa);
                $('#polisaSearchResults').hide();
            });

            // Hide results when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('#brojNaPolisaZaRaspredeluvanje, #polisaSearchResults').length) {
                    $('#polisaSearchResults').hide();
                }
            });

            // Submit the form data
            $('#preraspredeli-submit').on('click', function () {
                const uplataId = $('#uplataId').val();
                const iznosZaPreraspredelba = $('#iznosZaPreraspredelba').val();
                const brojNaPolisaZaRaspredeluvanje = $('#brojNaPolisaZaRaspredeluvanje').val();
                const preostanIznos = parseFloat($('#preostanIznos').val());
                
                // Validate data
                if (!iznosZaPreraspredelba || !brojNaPolisaZaRaspredeluvanje) {
                    alert('Сите полиња се задолжителни!');
                    return;
                }
                
                if (parseFloat(iznosZaPreraspredelba) > preostanIznos) {
                    alert('Износот не може да биде поголем од преостанатиот износ!');
                    return;
                }
                
                // Make AJAX request to the handler method
                $.ajax({
                    url: '?handler=PreraspredeliUplata',
                    type: 'POST',
                    data: {
                        uplatiPreraspredelbaId: uplataId,
                        iznosPreraspredelba: iznosZaPreraspredelba,
                        brojNaPolisaZaRaspredeluvanje: brojNaPolisaZaRaspredeluvanje
                    },
                    headers: {
                        "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function (result) {
                        if (result.success) {
                            alert('Прераспределбата е успешно извршена!');
                            $('#preraspredelbaNaUplatiModal').modal('hide');
                            // Reload the page to reflect changes
                            location.reload();
                        } else {
                            alert('Грешка при прераспределба: ' + result.message);
                        }
                    },
                    error: function (xhr, status, error) {
                        alert('Грешка при прераспределба: ' + error);
                    }
                });
            });
        });
    </script>
}
