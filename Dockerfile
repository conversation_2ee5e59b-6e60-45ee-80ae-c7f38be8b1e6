# Use the base image for ASP.NET
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base

# Switch to root to install packages
USER root

# Install Tesseract OCR, PDF conversion tools and dependencies
RUN apt-get update && \
    apt-get install -y tesseract-ocr libtesseract5 liblept5 tesseract-ocr-eng wget \
    libtesseract-dev libleptonica-dev pkg-config \
    imagemagick ghostscript poppler-utils && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Configure ImageMagick security policy to allow PDF processing
RUN sed -i 's/rights="none" pattern="PDF"/rights="read|write" pattern="PDF"/' /etc/ImageMagick-6/policy.xml

# Set up tessdata and download language packs
RUN mkdir -p /app/wwwroot/tessdata && \
    wget -O /app/wwwroot/tessdata/mkd.traineddata https://github.com/tesseract-ocr/tessdata/raw/main/mkd.traineddata && \
    wget -O /app/wwwroot/tessdata/eng.traineddata https://github.com/tesseract-ocr/tessdata/raw/main/eng.traineddata && \
    wget -O /app/wwwroot/tessdata/osd.traineddata https://github.com/tesseract-ocr/tessdata/raw/main/osd.traineddata && \
    chmod -R 777 /app/wwwroot/tessdata

# Set environment variables for tessdata and library paths
ENV TESSDATA_PREFIX=/app/wwwroot/tessdata
ENV LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/lib:/lib/x86_64-linux-gnu
ENV PKG_CONFIG_PATH=/usr/lib/x86_64-linux-gnu/pkgconfig

# Create symbolic links and configure libraries for .NET Tesseract
RUN ln -sf /usr/lib/x86_64-linux-gnu/libtesseract.so.5 /usr/lib/x86_64-linux-gnu/libtesseract.so && \
    ln -sf /usr/lib/x86_64-linux-gnu/libleptonica.so.6 /usr/lib/x86_64-linux-gnu/libleptonica.so && \
    ln -sf /usr/lib/x86_64-linux-gnu/libtesseract.so.5 /usr/lib/libtesseract.so && \
    ln -sf /usr/lib/x86_64-linux-gnu/libleptonica.so.6 /usr/lib/libleptonica.so && \
    mkdir -p /app/runtimes/linux-x64/native && \
    ln -sf /usr/lib/x86_64-linux-gnu/libtesseract.so.5 /app/runtimes/linux-x64/native/libtesseract.so && \
    ln -sf /usr/lib/x86_64-linux-gnu/libleptonica.so.6 /app/runtimes/linux-x64/native/libleptonica.so && \
    ldconfig

# Switch back to app user
USER app
WORKDIR /app
EXPOSE 80    
EXPOSE 443  

# Build stage
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["NextBroker/NextBroker.csproj", "NextBroker/"]
RUN dotnet restore "./NextBroker/NextBroker.csproj"
COPY . .
WORKDIR "/src/NextBroker"
RUN dotnet build "./NextBroker.csproj" -c $BUILD_CONFIGURATION -o /app/build

# Publish stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./NextBroker.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# Final stage
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish . 
COPY ssl_certificate/certificate.pfx /https/certificate.pfx 
ENTRYPOINT ["dotnet", "NextBroker.dll"]
