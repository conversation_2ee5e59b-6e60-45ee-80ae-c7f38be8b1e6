@page
@model NextBroker.Pages.OCR.OCRAvtoOdgovornostTextModel
@{
    ViewData["Title"] = "Автоматско вчитување полиса";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-file-pdf mr-2"></i>
                        Автоматско вчитување полиса
                    </h3>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            @Model.ErrorMessage
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle mr-2"></i>
                            @Model.SuccessMessage
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    }

                    <form method="post" enctype="multipart/form-data" id="pdfUploadForm">
                        <div class="form-group">
                            <label for="PdfFile" class="form-label">
                                <i class="fas fa-upload mr-2"></i>
                                Изберете PDF датотека
                            </label>
                            <div class="custom-file">
                                <input type="file" 
                                       class="custom-file-input" 
                                       id="PdfFile" 
                                       name="PdfFile" 
                                       accept=".pdf"
                                       required>
                                <label class="custom-file-label" for="PdfFile">Изберете датотека...</label>
                            </div>
                            <small class="form-text text-muted">
                                Поддржани се само PDF датотеки со селектабилен текст.
                            </small>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary" id="processBtn">
                                <i class="fas fa-cog mr-2"></i>
                                Обработи PDF
                            </button>
                            <button type="button" class="btn btn-secondary ml-2" onclick="resetForm()">
                                <i class="fas fa-redo mr-2"></i>
                                Ресетирај
                            </button>
                        </div>
                        
                        <!-- Hidden fields to preserve extracted data when form is submitted -->
                        @if (Model.HasResults)
                        {
                            <input type="hidden" name="ShowDebugInfo" value="@Model.ShowDebugInfo" />
                        }
                    </form>
                </div>
            </div>

            @if (Model.HasResults)
            {
                <div class="card mt-4">
                    <div class="card-header">
                        <h4 class="card-title">
                            <i class="fas fa-clipboard-list mr-2"></i>
                            Извлечени податоци
                            <small class="text-muted ml-2">(можете да ги уредувате)</small>
                        </h4>
                    </div>
                    <div class="card-body">
                        <form method="post" id="editDataForm">
                            <input type="hidden" name="PdfFile" value="" />
                            <input type="hidden" name="HasResults" value="true" />
                            <input type="hidden" name="ShowDebugInfo" value="@Model.ShowDebugInfo" />
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Број на полиса:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="BrojNaPolisa"
                                               placeholder="Внесете број на полиса">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Регистрација:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="Registracija"
                                               placeholder="Внесете регистрација">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Продавач:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="Prodavac"
                                               placeholder="Внесете продавач">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Договорувач:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="Dogovoruvac"
                                               placeholder="Внесете договорувач">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Вид возило:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="VidVozilo"
                                               placeholder="Внесете вид возило">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Модел возило:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="ModelVozilo"
                                               placeholder="Внесете модел возило">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Марка возило:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="MarkaVozilo"
                                               placeholder="Внесете марка возило">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Шасија:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="Shasija"
                                               placeholder="Внесете шасија">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">ЕМБГ Договорувач:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="DogovoruvacEMBG"
                                               placeholder="Внесете ЕМБГ">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Адреса Договорувач:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="DogovoruvacAdresa"
                                               placeholder="Внесете адреса">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Место Договорувач:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="DogovoruvacMesto"
                                               placeholder="Внесете место">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Осигуреник:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="Osigurenik"
                                               placeholder="Внесете осигуреник">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">ЕМБГ Осигуреник:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="OsigurenikEMBG"
                                               placeholder="Внесете ЕМБГ">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Адреса Осигуреник:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="OsigurenikAdresa"
                                               placeholder="Внесете адреса">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Место Осигуреник:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="OsigurenikMesto"
                                               placeholder="Внесете место">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Година производство:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="GodinaProizvodstvo"
                                               placeholder="Внесете година">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Моќност во KW:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="MoknostVoKW"
                                               placeholder="Внесете моќност">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Зафатнина во cm3:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="ZafatninaVoCm3"
                                               placeholder="Внесете зафатнина">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Носивост во kg:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="NosivostVoKG"
                                               placeholder="Внесете носивост">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Second row for additional specifications -->
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Рег. места:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="RegMesta"
                                               placeholder="Внесете рег. места">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Основна премија АО:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="OsnovnaPremijaAO"
                                               placeholder="Внесете премија">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Бонус:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="Bonus"
                                               placeholder="Внесете бонус">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Кршење стакло:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="KrsenjeStaklo"
                                               placeholder="Внесете кршење стакло">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Осигурување патници:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="OsiguruvanjePatnici"
                                               placeholder="Внесете осигурување патници">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Дополнително осигурување:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="DopolnitelnoOsiguruvanje"
                                               placeholder="Внесете дополнително осигурување">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Асистенција:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="Asistencija"
                                               placeholder="Внесете асистенција">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Место издавање:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="MestoIzdavanje"
                                               placeholder="Внесете место издавање">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Датум на издавање:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="DatumNaIzdavanje"
                                               placeholder="Внесете датум издавање">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Полиса важи од:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="PolisaVaziOd"
                                               placeholder="Внесете полиса важи од">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label font-weight-bold">Полиса важи до:</label>
                                    <div class="input-group">
                                        <input type="text" 
                                               class="form-control" 
                                               asp-for="PolisaVaziDo"
                                               placeholder="Внесете полиса важи до">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" 
                                                    type="button" 
                                                    onclick="copyFieldValue(this)"
                                                    title="Копирај">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                           
                            <button type="button" class="btn btn-info ml-2" onclick="useExtractedData()">
                                <i class="fas fa-arrow-right mr-2"></i>
                                Користи податоци за нова полиса
                            </button>
                        </div>
                        </form>
                    </div>
                </div>
            }

            @if (Model.ShowDebugInfo)
            {
                <div class="card mt-4">
                    <div class="card-header">
                        <h4 class="card-title">
                            <i class="fas fa-bug mr-2"></i>
                            Детали за обработка
                            <button class="btn btn-sm btn-outline-secondary float-right" type="button" data-toggle="collapse" data-target="#debugDetails" aria-expanded="false">
                                <i class="fas fa-chevron-down"></i> Прикажи/скриј детали
                            </button>
                        </h4>
                    </div>
                    <div class="collapse show" id="debugDetails">
                        <div class="card-body">
                            @if (Model.DebugMessages.Any())
                            {
                                <div class="mb-4">
                                    <h5><i class="fas fa-list mr-2"></i>Лог од обработка:</h5>
                                    <div class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">
                                        @foreach (var message in Model.DebugMessages)
                                        {
                                            <div class="mb-1">
                                                @if (message.StartsWith("✓"))
                                                {
                                                    <span class="text-success">@message</span>
                                                }
                                                else if (message.StartsWith("✗"))
                                                {
                                                    <span class="text-danger">@message</span>
                                                }
                                                else
                                                {
                                                    <span class="text-info">@message</span>
                                                }
                                            </div>
                                        }
                                    </div>
                                </div>
                            }

                            @if (Model.FoundPatterns.Any())
                            {
                                <div class="mb-4">
                                    <h5><i class="fas fa-search mr-2"></i>Пронајдени шаблони:</h5>
                                    <div class="bg-light p-3 rounded">
                                        @foreach (var pattern in Model.FoundPatterns)
                                        {
                                            <div class="mb-1"><code>@pattern</code></div>
                                        }
                                    </div>
                                </div>
                            }

                            @if (!string.IsNullOrWhiteSpace(Model.CoordinateExtractedText))
                            {
                                <div class="mb-4">
                                    <h5><i class="fas fa-crosshairs mr-2"></i>Текст од координати:</h5>
                                    <div class="bg-light p-3 rounded">
                                        <pre>@Model.CoordinateExtractedText</pre>
                                    </div>
                                </div>
                            }

                            @if (!string.IsNullOrWhiteSpace(Model.FullExtractedText))
                            {
                                <div class="mb-4">
                                    <h5>
                                        <i class="fas fa-file-alt mr-2"></i>
                                        Целокупен извлечен текст (@Model.FullExtractedText.Length карактери):
                                        <button class="btn btn-sm btn-outline-secondary ml-2" type="button" onclick="copyToClipboard(`@Html.Raw(Html.Encode(Model.FullExtractedText).Replace("`", "\\`"))`)" title="Копирај целокупен текст">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </h5>
                                    <div class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;">
                                        <pre style="white-space: pre-wrap; word-wrap: break-word;">@Model.FullExtractedText</pre>
                                    </div>
                                </div>
                            }

                            @if (Model.PdfPageCount > 0)
                            {
                                <div class="mb-2">
                                    <strong>PDF информации:</strong>
                                    <ul class="mb-0">
                                        <li>Број на страници: @Model.PdfPageCount</li>
                                        <li>Обработена страна: 1</li>
                                        <li>Координати се прикажани во лог деталите погоре</li>
                                    </ul>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Update file input label when file is selected
        document.getElementById('PdfFile').addEventListener('change', function(e) {
            var fileName = e.target.files[0] ? e.target.files[0].name : 'Изберете датотека...';
            var label = document.querySelector('.custom-file-label');
            label.textContent = fileName;
        });

        // Show loading state when form is submitted
        document.getElementById('pdfUploadForm').addEventListener('submit', function() {
            var btn = document.getElementById('processBtn');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Обработува...';
            btn.disabled = true;
        });

        // Reset form function
        function resetForm() {
            document.getElementById('pdfUploadForm').reset();
            document.querySelector('.custom-file-label').textContent = 'Изберете датотека...';
            
            // Hide results if shown
            var resultsCard = document.querySelector('.card.mt-4');
            if (resultsCard) {
                resultsCard.style.display = 'none';
            }
        }

        // Copy field value to clipboard function
        function copyFieldValue(button) {
            var inputField = button.closest('.input-group').querySelector('input');
            var text = inputField.value;
            
            if (!text) {
                // Show warning if field is empty
                var originalHtml = button.innerHTML;
                button.innerHTML = '<i class="fas fa-exclamation-triangle text-warning"></i>';
                setTimeout(function() {
                    button.innerHTML = originalHtml;
                }, 1000);
                return;
            }
            
            navigator.clipboard.writeText(text).then(function() {
                // Show temporary success message
                var originalHtml = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check text-success"></i>';
                setTimeout(function() {
                    button.innerHTML = originalHtml;
                }, 1000);
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                // Fallback for older browsers
                var textArea = document.createElement("textarea");
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                try {
                    document.execCommand('copy');
                    var originalHtml = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-check text-success"></i>';
                    setTimeout(function() {
                        button.innerHTML = originalHtml;
                    }, 1000);
                } catch (err) {
                    console.error('Fallback: Could not copy text: ', err);
                }
                document.body.removeChild(textArea);
            });
        }

        // Copy to clipboard function (for backward compatibility)
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Show temporary success message
                var btn = event.target.closest('button');
                var originalHtml = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check text-success"></i>';
                setTimeout(function() {
                    btn.innerHTML = originalHtml;
                }, 1000);
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                // Fallback for older browsers
                var textArea = document.createElement("textarea");
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                try {
                    document.execCommand('copy');
                    var btn = event.target.closest('button');
                    var originalHtml = btn.innerHTML;
                    btn.innerHTML = '<i class="fas fa-check text-success"></i>';
                    setTimeout(function() {
                        btn.innerHTML = originalHtml;
                    }, 1000);
                } catch (err) {
                    console.error('Fallback: Could not copy text: ', err);
                }
                document.body.removeChild(textArea);
            });
        }

        // Use extracted data function - redirect to policy insert page
        function useExtractedData() {
            // Get all field values from the current form
            var params = new URLSearchParams();
            
            // Get values from the editable form inputs
            var brojNaPolisa = document.querySelector('input[name="BrojNaPolisa"]')?.value || '';
            var registracija = document.querySelector('input[name="Registracija"]')?.value || '';
            var prodavac = document.querySelector('input[name="Prodavac"]')?.value || '';
            var dogovoruvac = document.querySelector('input[name="Dogovoruvac"]')?.value || '';
            var dogovoruvacEMBG = document.querySelector('input[name="DogovoruvacEMBG"]')?.value || '';
            var dogovoruvacAdresa = document.querySelector('input[name="DogovoruvacAdresa"]')?.value || '';
            var dogovoruvacMesto = document.querySelector('input[name="DogovoruvacMesto"]')?.value || '';
            var osigurenik = document.querySelector('input[name="Osigurenik"]')?.value || '';
            var osigurenikEMBG = document.querySelector('input[name="OsigurenikEMBG"]')?.value || '';
            var osigurenikAdresa = document.querySelector('input[name="OsigurenikAdresa"]')?.value || '';
            var osigurenikMesto = document.querySelector('input[name="OsigurenikMesto"]')?.value || '';
            var vidVozilo = document.querySelector('input[name="VidVozilo"]')?.value || '';
            var modelVozilo = document.querySelector('input[name="ModelVozilo"]')?.value || '';
            var markaVozilo = document.querySelector('input[name="MarkaVozilo"]')?.value || '';
            var shasija = document.querySelector('input[name="Shasija"]')?.value || '';
            var godinaProizvodstvo = document.querySelector('input[name="GodinaProizvodstvo"]')?.value || '';
            var moknostVoKW = document.querySelector('input[name="MoknostVoKW"]')?.value || '';
            var zafatninaVoCm3 = document.querySelector('input[name="ZafatninaVoCm3"]')?.value || '';
            var nosivostVoKG = document.querySelector('input[name="NosivostVoKG"]')?.value || '';
            var regMesta = document.querySelector('input[name="RegMesta"]')?.value || '';
            var osnovnaPremijaAO = document.querySelector('input[name="OsnovnaPremijaAO"]')?.value || '';
            var bonus = document.querySelector('input[name="Bonus"]')?.value || '';
            var krsenjeStaklo = document.querySelector('input[name="KrsenjeStaklo"]')?.value || '';
            var osiguruvanjePatnici = document.querySelector('input[name="OsiguruvanjePatnici"]')?.value || '';
            var dopolnitelnoOsiguruvanje = document.querySelector('input[name="DopolnitelnoOsiguruvanje"]')?.value || '';
            var asistencija = document.querySelector('input[name="Asistencija"]')?.value || '';
            var mestoIzdavanje = document.querySelector('input[name="MestoIzdavanje"]')?.value || '';
            var datumNaIzdavanje = document.querySelector('input[name="DatumNaIzdavanje"]')?.value || '';
            var polisaVaziOd = document.querySelector('input[name="PolisaVaziOd"]')?.value || '';
            var polisaVaziDo = document.querySelector('input[name="PolisaVaziDo"]')?.value || '';
            
            // Add all parameters to URL
            if (brojNaPolisa) params.append('brojNaPolisa', brojNaPolisa);
            if (registracija) params.append('registracija', registracija);
            if (prodavac) params.append('prodavac', prodavac);
            if (dogovoruvac) params.append('dogovoruvac', dogovoruvac);
            if (dogovoruvacEMBG) params.append('dogovoruvacEMBG', dogovoruvacEMBG);
            if (dogovoruvacAdresa) params.append('dogovoruvacAdresa', dogovoruvacAdresa);
            if (dogovoruvacMesto) params.append('dogovoruvacMesto', dogovoruvacMesto);
            if (osigurenik) params.append('osigurenik', osigurenik);
            if (osigurenikEMBG) params.append('osigurenikEMBG', osigurenikEMBG);
            if (osigurenikAdresa) params.append('osigurenikAdresa', osigurenikAdresa);
            if (osigurenikMesto) params.append('osigurenikMesto', osigurenikMesto);
            if (vidVozilo) params.append('vidVozilo', vidVozilo);
            if (modelVozilo) params.append('modelVozilo', modelVozilo);
            if (markaVozilo) params.append('markaVozilo', markaVozilo);
            if (shasija) params.append('shasija', shasija);
            if (godinaProizvodstvo) params.append('godinaProizvodstvo', godinaProizvodstvo);
            if (moknostVoKW) params.append('moknostVoKW', moknostVoKW);
            if (zafatninaVoCm3) params.append('zafatninaVoCm3', zafatninaVoCm3);
            if (nosivostVoKG) params.append('nosivostVoKG', nosivostVoKG);
            if (regMesta) params.append('regMesta', regMesta);
            if (osnovnaPremijaAO) params.append('osnovnaPremijaAO', osnovnaPremijaAO);
            if (bonus) params.append('bonus', bonus);
            if (krsenjeStaklo) params.append('krsenjeStaklo', krsenjeStaklo);
            if (osiguruvanjePatnici) params.append('osiguruvanjePatnici', osiguruvanjePatnici);
            if (dopolnitelnoOsiguruvanje) params.append('dopolnitelnoOsiguruvanje', dopolnitelnoOsiguruvanje);
            if (asistencija) params.append('asistencija', asistencija);
            if (mestoIzdavanje) params.append('mestoIzdavanje', mestoIzdavanje);
            if (datumNaIzdavanje) params.append('datumNaIzdavanje', datumNaIzdavanje);
            if (polisaVaziOd) params.append('polisaVaziOd', polisaVaziOd);
            if (polisaVaziDo) params.append('polisaVaziDo', polisaVaziDo);
            
            // Redirect to the policy insert page with all data
            window.location.href = '/OCR/OCRAvtoOdgovornostPolicyInsert?' + params.toString();
        }

        // Toggle debug details
        document.addEventListener('DOMContentLoaded', function() {
            var toggleBtn = document.querySelector('[data-target="#debugDetails"]');
            var debugDetails = document.getElementById('debugDetails');
            
            if (toggleBtn && debugDetails) {
                toggleBtn.addEventListener('click', function() {
                    var icon = toggleBtn.querySelector('i');
                    if (debugDetails.classList.contains('show')) {
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    } else {
                        icon.classList.remove('fa-chevron-down');
                        icon.classList.add('fa-chevron-up');
                    }
                });
            }
        });
    </script>
}
