﻿.carousel,
.carousel-inner {
    width: 100vw; /* Full viewport width */
    max-height: 60vh; /* Adjust this to your desired height */
    margin-left: calc(-50vw + 50%); /* Center it horizontally */
    
}

.carousel-bckgrnd {
    background-color: rgba(0, 51, 102, 0.65);
    padding: 20px;
    border-radius: 10px;
}

.carousel-inner img {
    width: 500px; /* Ensure it takes full width */
    height: 500px; /* Take the full height of carousel */
    object-fit: cover; /* Adjust image without stretching */
    max-height: 60vh; /* Same as carousel max-height */
}

.carousel-indicators {
    position: static;
    display: flex;
    justify-content: center;
    padding-top: 10px; /* Space above indicators */
}

    .carousel-indicators button {
        width: 12px !important; /* Circle width */
        height: 12px !important; /* Circle height */
        background-color: rgba(76, 175, 80, 0.5) !important; /* Semi-transparent */
        border-radius: 6px !important; /* Rounded corners */
        transition: background-color 0.3s ease, transform 0.3s ease !important; /* Smooth transition */
        margin: 0 5px !important; /* Space between buttons */
        border: none !important; /* Remove border */
        opacity: 0.7 !important; /* Default opacity */
    }

    .carousel-indicators .active {
        background-color: #003366 !important; /* Active color */
        width: 50px !important; /* Wider active indicator */
        height: 12px !important; /* Same height for all indicators */
        border-radius: 6px !important; /* Rounded corners */
        transform: scale(1.2) !important; /* Slightly larger for active state */
    }

.carousel-caption {
    bottom: 20px; /* Moves the caption a bit higher */
    box-shadow: 0 4px 20px rgba(0, 51, 102, 0.7) !important;
}


/* Modal popup border and rounding */
.modal-content {
    border-radius: 15px;
    border: 4px solid #1c8bab; /* Company colors */
}

.modal-header h1 {
    color: #1c8bab; /* Company color */
}

.carousel-control-prev, .carousel-control-next {
    position: absolute; /* Absolute positioning for centering */
    top: 50%; /* Center vertically */
    transform: translateY(-50%); /* Adjust to truly center */
    width: 60px !important; /* Width of the button */
    height: 60px !important; /* Height of the button */
    border-radius: 50% !important; /* Makes the button circular */
    background-color: rgba(0, 51, 102, 0.2) !important; /* Semi-transparent background */
    border: none !important; /* Remove border */
    display: flex !important; /* Flexbox for centering content */
    justify-content: center !important; /* Centering content */
    align-items: center !important; /* Centering content vertically */
    opacity: 0.5 !important; /* Default opacity (faded) */
    transition: background-color 0.3s ease, opacity 0.3s ease, transform 0.6s ease; /* Smooth transitions */
}

    .carousel-control-prev:hover, .carousel-control-next:hover {
        background-color: rgba(76, 175, 80, 0.4) !important; /* Change background on hover */
        opacity: 1 !important; /* Fully visible on hover */
        transform: translateY(-50%) scale(1.15); /* Scale up the button without moving */
    }

.carousel-control-prev-icon, .carousel-control-next-icon {
    width: 20px !important; /* Size of the arrows */
    height: 20px !important; /* Size of the arrows */
    background-size: 100% !important; /* Ensure the icon fills the button */
    transition: transform 0.6s ease; /* Smooth scaling transition for arrows */
}

.carousel-control-prev:hover .carousel-control-prev-icon,
.carousel-control-next:hover .carousel-control-next-icon {
    transform: scale(1.2); /* Scale up the arrows on hover */
}

/* Positioning the previous and next buttons */
.carousel-control-prev {
    left: 30px; /* Position to the left */
}

.carousel-control-next {
    right: 30px; /* Position to the right */
}

/* Card styling */
.card {
    opacity: 0; /* Ensure cards are hidden initially */
    border-radius: 15px; /* Smooth rounded corners */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* Subtle shadow for a 3D effect */
    height: 700px; /* Increase height to make cards larger */
    width: auto; /* Increase width */
    margin: auto; /* Reduce margin for closer spacing and center cards */
    transform-origin: center; /* Center scaling */
    overflow: visible;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease, box-shadow 0.3s ease; /* Smooth transition for scale and shadow */
}

    /* Hover effect on card */
    .card:hover {
        transform: scale(1.1); /* Slightly enlarge the card on hover */
        box-shadow: 0 5px 15px rgba(0, 51, 102, 0.4); /* Stronger shadow on hover */

    }

/* Card image */
.card-img-top {
    border-top-left-radius: 15px;
    border-top-right-radius: 15px; /* Keep the corners rounded for the image */
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
    object-fit: scale-down; /* Make sure the image covers the entire area */
    height: 200px; /* Ensure consistent height for the image */
    padding: 20px;
    transition: transform 0.3s ease; /* Add transition to the image */
}
.card:hover .card-img-top {
    transform: scale(1.4); /* Scale image on card hover */
    overflow: visible;
}

/* Card body spacing */
.card-body {
    display: flex;
    flex-direction: column;
    justify-content: flex-start; /* Align items at the top */
    padding: 20px;
    flex-grow: 1;
}

/* Card title */
.card-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #003366; /* Dark blue title color */
    margin-bottom: 10px; /* Spacing below the title */
    text-align: center; /* Ensure text aligns to the center */
}

/* Card text */
.card-text {
    font-size: 1rem;
    color: #333; /* Slightly darker text for readability */
    margin: 10px; /* Space below the text */
    text-align: left; /* Left align text */
    padding: 50px;
}


/* Button styling */
.btn-primary {
    background-color: rgba(76, 175, 80, 0.7);
    color: #003366;
    border: none;
    border-radius: 25px; /* Make buttons rounded */
    padding: 10px 30px;
    font-size: 20px;
    transition: background-color 0.3s ease, transform 0.3s ease; /* Added transform transition */
    align-self: center;
    margin-top: auto; /* Push button to the bottom */
    margin-bottom:20px;
}

    .btn-primary:hover {
        background-color: rgba(0, 51, 102, 0.3);
        color: #003366;
        border: none;
        border-radius: 25px; /* Make buttons rounded */
        padding: 10px 30px;
        font-size: 20px;
        transition: background-color 0.3s ease, transform 0.3s ease; /* Added transform transition */
        align-self: center;
        margin-top: auto; /* Push button to the bottom */
        margin-bottom:20px;
        transform: scale(1.05); /* Scale up button on hover */
    }

/* Tick mark styling */
.card-ticks {
    list-style: none; /* Remove default bullets */
    padding: 40px;
    margin: 10px 0; /* Space between text and ticks */
}

    .card-ticks li {
        display: flex;
        align-items: center;
        font-size: 1rem;
        color: #003366; /* Dark blue text color */
        margin-bottom: 8px; /* Space between each point */
    }

        .card-ticks li span {
            color: rgba(76, 175, 80, 0.9); /* Green tick color */
            font-size: 1.2rem;
            margin-right: 10px; /* Space between tick and text */
        }


.index-cards-container {
    background-color: rgba(76, 175, 100, 0.2); /* Desaturated green */
    width: 100vw !important; /* Full viewport width */
    margin-left: calc(-50vw + 50%) !important;
    box-sizing: border-box !important; /* Ensures padding does not affect the width */
    padding: 100px 20px; /* Reduce padding to bring cards closer */
    display: flex;
    flex-wrap: wrap; /* Allow cards to wrap within the container */
    justify-content: center; /* Center cards in the container */
    
}

.scroll-top {
    position: fixed;
    bottom: 60px; /* Adjusted to match Contact page */
    right: 40px;
    width: 60px; /* Increased from 50px to 60px */
    height: 60px; /* Increased from 50px to 60px */
    background-color: rgba(0, 51, 102, 0.2);
    border-radius: 50%;
    border: none;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0.5;
    cursor: pointer;
    transition: background-color 0.3s ease, opacity 0.3s ease, transform 0.3s ease;
    z-index: 1000;
}

    .scroll-top:hover {
        background-color: rgba(0, 51, 102, 0.8);
        opacity: 1;
        transform: scale(1.15);
    }

    .scroll-top:focus {
        outline: 2px solid rgba(76, 175, 80, 0.8) !important;
        outline-offset: 2px !important;
    }

    .scroll-top i {
        font-size: 24px; /* Increased from 20px to 24px */
        color: #fff;
    }

/* Adjust for mobile screens */
@media (max-width: 767px) {
    .scroll-top {
        bottom: 30px;
        right: 30px;
        width: 50px; /* Increased from 40px to 50px */
        height: 50px; /* Increased from 40px to 50px */
    }

    .scroll-top i {
        font-size: 20px; /* Increased from 16px to 20px */
    }
}

.contact-wrapper {
    display: flex;
    justify-content: center; /* Center horizontally */
    align-items: center; /* Center vertically */

}

.contact-card {
    background-color: #f8f9fc;
    width: 600px;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.icon-container {
    font-size: 50px;
    color: rgba(0, 51, 102, 0.8);
    margin-bottom: 20px;
}

h2 {
    color: #1c1e56;
    font-size: 30px;
    margin-bottom: 10px;
}

@media (max-width: 768px) {
    .card-img-top {
        height: 150px; /* Adjust image height for smaller screens */
    }

    .card-body {
        padding: 15px; /* Slightly reduce padding on mobile */
    }

    .card-title {
        font-size: 1.1rem; /* Adjust font size for smaller screens */
    }

    .btn-primary {
        padding: 8px 16px; /* Smaller button padding on mobile */
    }
}



