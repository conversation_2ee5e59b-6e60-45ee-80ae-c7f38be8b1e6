@page
@model NextBroker.Pages.Finansii.GenerirajIzleznaFakturaKonKlientModel
@using System.Data
@using Microsoft.Data.SqlClient
@{
    ViewData["Title"] = "Генерирај излезна фактура кон клиент";
    var karticaColumns = new Dictionary<string, string>
    {
        {"Id", "ИД"},
        {"RataBroj", "Рата број"},
        {"DatumNaDospevanje", "Датум на доспевање"},
        {"IznosRata", "Износ на рата"}
    };
}

<style>
    /* Add styles for download button */
    .download-invoice {
        transition: all 0.3s ease;
    }
    
    .download-invoice:hover {
        background-color: #0056b3;
    }
    
    /* Loading overlay for SFTP download */
    .loading-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }
    
    .spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    .loading-text {
        color: white;
        margin-top: 20px;
        font-size: 18px;
        font-weight: bold;
    }
    
    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>

<div class="container-fluid">
    <!-- Add hidden antiforgery token -->
    @Html.AntiForgeryToken()
    
    <!-- Loading overlay for SFTP downloads -->
    <div class="loading-overlay" id="downloadOverlay">
        <div class="spinner"></div>
        <div class="loading-text">Преземање на фактура од СФТП сервер...</div>
    </div>
    
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h4>@ViewData["Title"]</h4>
        @if (Model.PolisaId.HasValue)
        {
            <div class="badge bg-primary fs-6">
                Полиса ID: @Model.PolisaId
            </div>
        }
    </div>

    @if (Model.FakturaData != null && Model.FakturaData.Rows.Count > 0)
    {
        var row = Model.FakturaData.Rows[0];
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Податоци за фактура</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Број на фактура</label>
                            <div>@row["BrojNaFaktura"]</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Датум на фактура</label>
                            <div>@(row["DatumNaFaktura"] != DBNull.Value ? Convert.ToDateTime(row["DatumNaFaktura"]).ToString("dd.MM.yyyy") : "")</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Број на полиса</label>
                            <div>@row["BrojNaPolisa"]</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Осигурител</label>
                            <div>@row["Osiguritel"]</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Продукт</label>
                            <div>@row["Produkt"]</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Договорувач</label>
                            <div>
                                @if (row["DogovoruvacNaziv"] != DBNull.Value)
                                {
                                    @row["DogovoruvacNaziv"]
                                }
                                else
                                {
                                    @($"{row["DogovoruvacIme"]} {row["DogovoruvacPrezime"]}")
                                }
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Осигуреник</label>
                            <div>
                                @if (row["OsigurenikNaziv"] != DBNull.Value)
                                {
                                    @row["OsigurenikNaziv"]
                                }
                                else
                                {
                                    @($"{row["OsigurenikIme"]} {row["OsigurenikPrezime"]}")
                                }
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Вкупна премија</label>
                            <div>@(row["VkupnaPremija"] != DBNull.Value ? (Convert.ToDecimal(row["VkupnaPremija"])) : (Model.IznosOZPoPolisa ?? 0).ToString("N2"))</div>
                            @if (row["VkupnaPremija"] != DBNull.Value)
                            {
                                <div class="text-muted small">Со зборови: @row["VkupnaPremijaSoZborovi"]</div>
                            }
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Премија за наплата</label>
                            <div>@(row["PremijaZaNaplata"] != DBNull.Value ? Convert.ToDecimal(row["PremijaZaNaplata"]).ToString("N2") : "")</div>
                            @if (row["PremijaZaNaplata"] != DBNull.Value)
                            {
                                <div class="text-muted small">Со зборови: @row["PremijaZaNaplataSoZborovi"]</div>
                            }
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Рок на плаќање (денови)</label>
                            <div>@(row["RokNaPlakanjeDenovi"] != DBNull.Value ? row["RokNaPlakanjeDenovi"] : "")</div>
                        </div>                        
                                                    <div class="mb-3">
                            <label class="form-label fw-bold">Процент на попуст за фактура во рок</label>
                            <div>@Model.ProcentNaPopustZaFakturaVoRok.Value.ToString("N2")%</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Износ Одобрување / задолжување по полиса</label>
                            <div>@(Model.IznosOZPoPolisa.HasValue ? Model.IznosOZPoPolisa.Value.ToString("N2") : "0.00")</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="alert alert-info mb-4">
            Нема податоци за фактура.
        </div>
    }

    @if (Model.KarticaData != null && Model.KarticaData.Rows.Count > 0)
    {
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Картица</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                @foreach (System.Data.DataColumn column in Model.KarticaData.Columns)
                                {
                                    <th>@(karticaColumns.ContainsKey(column.ColumnName) ? karticaColumns[column.ColumnName] : column.ColumnName)</th>
                                }
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (System.Data.DataRow row in Model.KarticaData.Rows)
                            {
                                <tr>
                                    @foreach (System.Data.DataColumn column in Model.KarticaData.Columns)
                                    {
                                        <td>
                                            @if (row[column] != DBNull.Value)
                                            {
                                                @if (column.DataType == typeof(DateTime))
                                                {
                                                    @(((DateTime)row[column]).ToString("dd.MM.yyyy"))
                                                }
                                                else if (column.DataType == typeof(decimal))
                                                {
                                                    @(((decimal)row[column]).ToString("N2"))
                                                }
                                                else
                                                {
                                                    @row[column]
                                                }
                                            }
                                        </td>
                                    }
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="alert alert-info">
            Нема податоци во картица.
        </div>
    }

    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0">Претходно генерирани фактури</h5>
        </div>
        <div class="card-body">
            @if (Model.PreviousInvoicesData != null && Model.PreviousInvoicesData.Rows.Count > 0)
            {
                <div class="table-responsive">
                    <table class="table table-striped table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>ИД</th>
                                <th>Датум на креирање</th>
                                <th>Креирал</th>
                                <th>Број на фактура</th>
                                <th>Датум на фактура</th>
                                <th>Рок на плаќање</th>
                                <th>Вкупна премија</th>
                                <th>Износ одобрување/задолжување по фактура</th>
                                <th>Премија за наплата</th>                                
                                <th>Рата износ</th>
                                <th>Акции</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (DataRow row in Model.PreviousInvoicesData.Rows)
                            {
                                <tr>
                                    <td>@row["Id"]</td>
                                    <td>@(row["DateCreated"] != DBNull.Value ? Convert.ToDateTime(row["DateCreated"]).ToString("dd.MM.yyyy HH:mm") : "")</td>
                                    <td>@row["UsernameCreated"]</td>
                                    <td>@row["BrojNaFaktura"]</td>
                                    <td>@(row["DatumNaFaktura"] != DBNull.Value ? Convert.ToDateTime(row["DatumNaFaktura"]).ToString("dd.MM.yyyy") : "")</td>
                                    <td>@(row["RokNaPlakanjeFakturaIzlezna"] != DBNull.Value ? Convert.ToDateTime(row["RokNaPlakanjeFakturaIzlezna"]).ToString("dd.MM.yyyy") : "")</td>
                                    <td class="text-end">@(row["VkupnaPremija"] != DBNull.Value ? Convert.ToDecimal(row["VkupnaPremija"]).ToString("N2") : "")</td>
                                    <td class="text-end">@(row["IznosOzdobrenjeZadolzuvane"] != DBNull.Value ? Convert.ToDecimal(row["IznosOzdobrenjeZadolzuvane"]).ToString("N2") : "")</td>
                                    <td class="text-end">@(row["PremijaZaNaplata"] != DBNull.Value ? Convert.ToDecimal(row["PremijaZaNaplata"]).ToString("N2") : "")</td>
                                    <td class="text-end">@(row["RataIznos"] != DBNull.Value ? Convert.ToDecimal(row["RataIznos"]).ToString("N2") : "")</td>
                                    <td class="text-center">
                                        @if (Model.PreviousInvoicesData.Columns.Contains("FilePath") && Model.PreviousInvoicesData.Columns.Contains("FileName") && 
                                             row["FilePath"] != DBNull.Value && row["FileName"] != DBNull.Value)
                                        {
                                            <button type="button" class="btn btn-sm btn-primary download-invoice" 
                                                    data-invoice-id="@row["Id"]" 
                                                    data-filepath="@row["FilePath"]" 
                                                    data-filename="@row["FileName"]"
                                                    data-brojnafaktura="@row["BrojNaFaktura"]">
                                                <i class="bi bi-download"></i> Преземи
                                            </button>
                                        }
                                        else
                                        {
                                            <span class="text-muted"><i class="bi bi-exclamation-circle"></i> Нема PDF</span>
                                        }
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="alert alert-info">
                    Нема претходно генерирани фактури за оваа полиса.
                </div>
            }
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0">Контроли</h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="fakturaKon" class="form-label">Фактура кон:</label>
                    <select class="form-select" id="fakturaKon">
                        <option value="dogovoruvac">Договорувач</option>
                        <option value="osigurenik">Осигуреник</option>
                    </select>
                    <input type="hidden" id="selectedFakturaKon" name="selectedFakturaKon" value="dogovoruvac" />
                </div>
            </div>
            <button type="button" class="btn btn-primary" id="btnGenerirajPregledNaFaktura">
                <i class="bi bi-file-earmark-text me-1"></i> Генерирај преглед на фактура
            </button>
            <button type="button" class="btn btn-success d-none ms-2" id="btnSocuvajFaktura">
                <i class="bi bi-save me-1"></i> Сочувај фактура
            </button>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0">Преглед на фактура</h5>
        </div>
        <div class="card-body">
            <div id="fakturaPreview" class="border p-4 d-none">
                <!-- This will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <!-- Add jsPDF and html2canvas libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Add selectedFakturaKon variable to store the selection
            let selectedFakturaKon = 'dogovoruvac';
            
            // Handle dropdown change
            $('#fakturaKon').on('change', function() {
                selectedFakturaKon = $(this).val();
                $('#selectedFakturaKon').val(selectedFakturaKon);
            });

            $('#btnGenerirajPregledNaFaktura').on('click', function() {
                generateInvoicePreview();
                // Show the Save button when preview is generated
                $('#btnSocuvajFaktura').removeClass('d-none');
            });
            
            // Add click handler for the Сочувај фактура button
            $('#btnSocuvajFaktura').on('click', function(e) {
                // Check if there are previous invoices
                const hasPreviousInvoices = @(Model.PreviousInvoicesData != null && Model.PreviousInvoicesData.Rows.Count > 0 ? "true" : "false");
                
                if (hasPreviousInvoices) {
                    // Show confirmation dialog
                    confirm('Веќе има генерирано фактура за оваа полиса, не е можно да генерирате нова?');
                    // Prevent further processing no matter what is clicked
                    e.preventDefault();
                    e.stopPropagation(); 
                    return false;
                }
                
                // If we get here, either there are no previous invoices or the user confirmed
                // Disable the button immediately to prevent double-clicking
                $(this).prop('disabled', true);
                
                // Change text to show processing
                $(this).html('<i class="bi bi-hourglass-split me-1"></i> Зачувување...');
                
                // Start the save process
                saveInvoiceAndGeneratePDF(this);
            });
            
            function saveInvoiceAndGeneratePDF(buttonElement) {
                // Get button reference
                const saveBtn = $(buttonElement);
                
                // Get the original button text for reference only
                const originalBtnText = saveBtn.html();
                
                // Note: We don't need to disable the button again since it's already disabled in the click handler
                
                // Get the anti-forgery token
                const token = $('input[name="__RequestVerificationToken"]').val();
                
                // Store polisaId for later use in SFTP upload
                const polisaId = @Model.PolisaId;
                let savedInvoiceNumber = '';
                
                // Send AJAX request to save the invoice
                $.ajax({
                    url: '?handler=SaveInvoice',
                    type: 'POST',
                    data: {
                        polisaId: polisaId,
                        selectedFakturaKon: $('#selectedFakturaKon').val()
                    },
                    headers: {
                        'RequestVerificationToken': token
                    },
                    success: function(response) {
                        if (response.success) {
                            // Store invoice number for SFTP upload
                            savedInvoiceNumber = response.brojNaFaktura;
                            console.log('Invoice saved successfully, number: ' + savedInvoiceNumber);
                            
                            // Update button text to indicate PDF generation
                            saveBtn.html('<i class="bi bi-hourglass-split me-1"></i> Генерирање PDF...');
                            
                            // Generate PDF after successful save
                            generatePDF(polisaId, savedInvoiceNumber, saveBtn);
                        } else {
                            // Show error message but keep the button disabled
                            alert('Грешка при зачувување на фактура: ' + response.message);
                            saveBtn.html('<i class="bi bi-x-circle me-1"></i> Неуспешно зачувување');
                            
                            // We intentionally keep the button disabled to prevent retries
                        }
                    },
                    error: function(xhr, status, error) {
                        // Show error message but keep the button disabled
                        alert('Грешка при зачувување на фактура: ' + error);
                        saveBtn.html('<i class="bi bi-x-circle me-1"></i> Неуспешно зачувување');
                        
                        // We intentionally keep the button disabled to prevent retries
                    }
                });
            }
            
            // Function to upload PDF to SFTP server
            function uploadPdfToSftp(pdfData, polisaId, brojNaFaktura, originalFileName) {
                console.log('Starting PDF upload to SFTP...');
                
                // Check if PDF data is valid
                if (!pdfData) {
                    console.error('PDF data is null or undefined');
                    return Promise.resolve({success: false, message: 'PDF data is empty'});
                }
                
                // Convert data URL to base64 string
                let base64Data = pdfData;
                if (pdfData.startsWith('data:')) {
                    // Get content type for logging
                    const commaIndex = pdfData.indexOf(',');
                    if (commaIndex > 0) {
                        const contentType = pdfData.substring(0, commaIndex);
                        console.log(`PDF data format: ${contentType}`);
                        base64Data = pdfData.substring(commaIndex + 1);
                    } else {
                        console.warn('Data URL format is unusual, no comma found');
                    }
                } else {
                    console.warn('Data does not start with "data:", assuming it\'s already base64');
                }
                
                // Calculate size for logging
                const sizeInKB = Math.round((base64Data.length * 3/4) / 1024);
                console.log(`PDF size: ${sizeInKB} KB`);
                console.log(`Base64 data length: ${base64Data.length} characters`);
                
                // Validate that we have actual data
                if (!base64Data || base64Data.length < 100) {
                    console.error('Base64 data is too short or empty');
                    return Promise.resolve({success: false, message: 'Base64 data is too short or empty'});
                }
                
                // Log a small sample of the data for debugging (first 100 chars)
                console.log(`Sample of base64 data: ${base64Data.substring(0, 100)}...`);
                
                // Warning for very large files
                if (sizeInKB > 50000) { // 50MB
                    console.warn('PDF file is very large (>50MB), upload may fail');
                }
                
                // Get the anti-forgery token
                const token = $('input[name="__RequestVerificationToken"]').val();
                
                // Return a promise to allow the calling code to continue regardless of upload result
                return new Promise((resolve, reject) => {
                    // Split data for larger PDFs to avoid request size limits
                    if (sizeInKB > 3000) { // For PDFs larger than 3MB
                        console.log('PDF is large, sending in compact format');
                    }
                    
                    // Create direct binary blob for upload instead of using base64
                    try {
                        // Create a FormData object for the upload
                        const formData = new FormData();
                        formData.append('polisaId', polisaId);
                        formData.append('brojNaFaktura', brojNaFaktura);
                        formData.append('fileName', originalFileName);
                        formData.append('pdfBase64', base64Data);
                        
                        // Send AJAX request
                        $.ajax({
                            url: '?handler=UploadPdfToSftp',
                            type: 'POST',
                            data: formData,
                            headers: {
                                'RequestVerificationToken': token
                            },
                            processData: false,  // Don't process the data
                            contentType: false,  // Let the browser set the content type
                            timeout: 180000, // 3 minute timeout for large files
                            success: function(response) {
                                if (response.success) {
                                    console.log('PDF uploaded to SFTP successfully: ' + response.filePath);
                                    resolve(response);
                                } else {
                                    console.warn('Failed to upload PDF to SFTP: ' + response.message);
                                    // Resolve anyway to allow download to continue
                                    resolve({success: false, message: response.message});
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error(`Error uploading PDF to SFTP: ${status}, code: ${xhr.status}`);
                                if (xhr.status === 413) {
                                    console.error('The PDF exceeds the maximum allowed size (Request Entity Too Large)');
                                }
                                console.error(`Error details: ${error}`);
                                // Try to get more details from the response
                                try {
                                    if (xhr.responseText) {
                                        console.error(`Server response: ${xhr.responseText.substring(0, 200)}...`);
                                    }
                                } catch (e) {
                                    console.error('Could not read response text');
                                }
                                // Resolve anyway to allow download to continue
                                resolve({success: false, message: error});
                            }
                        });
                    } catch (error) {
                        console.error('Error preparing PDF data for upload:', error);
                        resolve({success: false, message: 'Error preparing PDF data for upload: ' + error.message});
                    }
                });
            }

            function generatePDF(polisaId, brojNaFaktura, saveBtn) {
                // Display loading indicator
                const originalBtnText = saveBtn.html();
                saveBtn.html('<i class="bi bi-hourglass-split me-1"></i> Генерирање PDF...');
                saveBtn.prop('disabled', true);
                
                // Get the invoice preview element
                const element = document.getElementById('fakturaPreview');
                
                // Make temporary adjustments to improve PDF appearance
                const originalStyle = element.getAttribute('style') || '';
                element.setAttribute('style', originalStyle + '; width: 794px; padding: 20px;');
                
                // Use html2canvas to capture the element as an image
                html2canvas(element, {
                    scale: 1.5,  // Higher scale for better quality
                    useCORS: true,
                    logging: false,
                    allowTaint: true,
                    backgroundColor: '#ffffff'
                }).then(canvas => {
                    // Restore original styling
                    element.setAttribute('style', originalStyle);
                    
                    // Initialize jsPDF
                    const { jsPDF } = window.jspdf;
                    const doc = new jsPDF('p', 'mm', 'a4', true); // The 'true' enables compression
                    
                    // Calculate dimensions to fill the page
                    const imgData = canvas.toDataURL('image/jpeg', 0.7); // Use JPEG format with lower quality to reduce size
                    console.log(`Generated image size: ${Math.round(imgData.length / 1024)} KB`);
                    
                    const pageWidth = doc.internal.pageSize.getWidth();
                    const pageHeight = doc.internal.pageSize.getHeight();
                    
                    // Use larger margins to fill the page better
                    const margin = 5; // 5mm margin
                    const usableWidth = pageWidth - (margin * 2);
                    const usableHeight = pageHeight - (margin * 2);
                    
                    // Add the image to the PDF with custom dimensions to fit the page
                    doc.addImage(imgData, 'JPEG', margin, margin, usableWidth, usableHeight);
                    
                    // Generate a filename based on the policy number
                    let fileName = "faktura";
                    @if (Model.FakturaData != null && Model.FakturaData.Rows.Count > 0)
                    {
                        <text>
                        fileName = "faktura_@(Model.FakturaData.Rows[0]["BrojNaPolisa"])_" + new Date().toISOString().slice(0, 10);
                        </text>
                    }
                    
                    try {
                        // Get PDF as binary data instead of data URL to avoid format issues
                        const pdfBlob = doc.output('blob');
                        const reader = new FileReader();
                        
                        reader.onloadend = function() {
                            // This contains the base64 data
                            const base64data = reader.result;
                            console.log(`PDF converted to base64, size: ${Math.round(base64data.length / 1024)} KB`);
                            console.log(`PDF data format prefix: ${base64data.substring(0, 50)}...`);
                            
                            // Upload PDF to SFTP server before downloading
                            saveBtn.html('<i class="bi bi-cloud-upload me-1"></i> Поставување на PDF...');
                            
                            // Debug output before upload
                            console.log(`Starting upload for invoice number: ${brojNaFaktura}`);
                            
                            uploadPdfToSftp(base64data, polisaId, brojNaFaktura, fileName + '.pdf')
                                .then(function(response) {
                                    // Continue with download regardless of upload success
                                    // Save the PDF
                                    doc.save(fileName + '.pdf');
                                    
                                    // Show appropriate message based on upload result and keep button disabled
                                    if (response.success) {
                                        console.log('PDF uploaded and downloaded successfully');
                                        saveBtn.html('<i class="bi bi-check-circle me-1"></i> Процесот е завршен');
                                    } else {
                                        console.warn('PDF downloaded but upload failed: ' + response.message);
                                        saveBtn.html('<i class="bi bi-exclamation-triangle me-1"></i> Преземено без качување');
                                    }
                                    
                                    // Keep the button disabled
                                    saveBtn.prop('disabled', true);
                                    
                                    // Add a new button to restart the process if needed
                                    const resetButton = $('<button type="button" class="btn btn-outline-primary ms-2"><i class="bi bi-arrow-repeat me-1"></i> Нова фактура</button>');
                                    resetButton.on('click', function() {
                                        // Reload the page to start fresh
                                        location.reload();
                                    });
                                    saveBtn.after(resetButton);
                                    
                                    // Add automatic page refresh after 3 seconds
                                    setTimeout(function() {
                                        location.reload();
                                    }, 3000);
                                })
                                .catch(function(error) {
                                    // Still save the PDF even if upload fails
                                    doc.save(fileName + '.pdf');
                                    console.error('Error during PDF upload: ', error);
                                    
                                    // Update button text to indicate partial completion
                                    saveBtn.html('<i class="bi bi-exclamation-triangle me-1"></i> Преземено со грешка');
                                    
                                    // Keep the button disabled
                                    saveBtn.prop('disabled', true);
                                    
                                    // Add automatic page refresh after 3 seconds
                                    setTimeout(function() {
                                        location.reload();
                                    }, 3000);
                                });
                        };
                        
                        reader.readAsDataURL(pdfBlob);
                    } catch (error) {
                        console.error('Error preparing PDF for upload: ', error);
                        // Still save the PDF even if there's an error
                        doc.save(fileName + '.pdf');
                        
                        // Update button text to indicate error
                        saveBtn.html('<i class="bi bi-exclamation-triangle me-1"></i> Грешка при подготовка');
                        
                        // Keep the button disabled
                        saveBtn.prop('disabled', true);
                        
                        // Add automatic page refresh after 3 seconds
                        setTimeout(function() {
                            location.reload();
                        }, 3000);
                    }
                }).catch(function(error) {
                    console.error('Error generating PDF with html2canvas: ', error);
                    alert('Грешка при генерирање на PDF: ' + error);
                    
                    // Update button text to indicate error
                    saveBtn.html('<i class="bi bi-x-circle me-1"></i> Неуспешно генерирање');
                    
                    // Keep the button disabled
                    saveBtn.prop('disabled', true);
                    
                    // Add automatic page refresh after 3 seconds
                    setTimeout(function() {
                        location.reload();
                    }, 3000);
                });
            }

            function generateInvoicePreview() {
                // Only proceed if we have data
                @if (Model.FakturaData != null && Model.FakturaData.Rows.Count > 0 && Model.KarticaData != null)
                {
                    <text>
                    const fakturaPreview = $('#fakturaPreview');
                    fakturaPreview.removeClass('d-none');
                    
                    // Get Izdal and Mesto from the model
                    const izdal = '@Html.Raw(Json.Serialize(Model.Izdal))';
                    const mesto = '@Html.Raw(Json.Serialize(Model.Mesto))';
                    
                    // Get addresses for Dogovoruvac and Osigurenik
                    const adresaDogovoruvac = '@Html.Raw(Json.Serialize(Model.AdresaDogovoruvac))';
                    const adresaOsigurenik = '@Html.Raw(Json.Serialize(Model.AdresaOsigurenik))';
                    
                    // Get vehicle registration information
                    const registracijaVozilo = '@Html.Raw(Json.Serialize(Model.RegistracijaVozilo))';
                    
                    // Format current date
                    const today = new Date();
                    const formattedDate = today.getDate().toString().padStart(2, '0') + '.' + 
                                        (today.getMonth() + 1).toString().padStart(2, '0') + '.' + 
                                        today.getFullYear();
                    
                    // Calculate payment due date
                    let paymentDueDate = new Date();
                    @if (Model.FakturaData.Rows[0]["RokNaPlakanjeDenovi"] != DBNull.Value)
                    {
                        <text>
                        paymentDueDate.setDate(today.getDate() + @Model.FakturaData.Rows[0]["RokNaPlakanjeDenovi"]);
                        </text>
                    }
                    else
                    {
                        <text>
                        paymentDueDate.setDate(today.getDate() + 30); // Default 30 days
                        </text>
                    }
                    const formattedPaymentDueDate = paymentDueDate.getDate().toString().padStart(2, '0') + '.' + 
                                                 (paymentDueDate.getMonth() + 1).toString().padStart(2, '0') + '.' + 
                                                 paymentDueDate.getFullYear();

                    // Get contractor name
                    let dogovoruvacName = "";
                    @if (Model.FakturaData.Rows[0]["DogovoruvacNaziv"] != DBNull.Value)
                    {
                        <text>
                        dogovoruvacName = '@Model.FakturaData.Rows[0]["DogovoruvacNaziv"]';
                        </text>
                    }
                    else
                    {
                        <text>
                        dogovoruvacName = '@(Model.FakturaData.Rows[0]["DogovoruvacIme"]) @(Model.FakturaData.Rows[0]["DogovoruvacPrezime"])';
                        </text>
                    }

                    // Get insured name
                    let osigurenikName = "";
                    @if (Model.FakturaData.Rows[0]["OsigurenikNaziv"] != DBNull.Value)
                    {
                        <text>
                        osigurenikName = '@Model.FakturaData.Rows[0]["OsigurenikNaziv"]';
                        </text>
                    }
                    else
                    {
                        <text>
                        osigurenikName = '@(Model.FakturaData.Rows[0]["OsigurenikIme"]) @(Model.FakturaData.Rows[0]["OsigurenikPrezime"])';
                        </text>
                    }

                    // Get the selected recipient name
                    const fakturaZa = selectedFakturaKon === 'dogovoruvac' ? dogovoruvacName : osigurenikName;
                    
                    // Get IznosOZPoPolisa value
                    const iznosOZPoPolisa = "@(Model.IznosOZPoPolisa.HasValue ? Model.IznosOZPoPolisa.Value.ToString("N2", System.Globalization.CultureInfo.InvariantCulture) : "0.00")";
                    
                    // Create the invoice HTML
                    const invoiceHtml = `
                        <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; position: relative; padding: 20px 40px;">
                            <!-- Background Logo -->
                            <div style="position: absolute; top: 20px; right: 40px; width: 150px; opacity: 0.15; z-index: 0; pointer-events: none;">
                                <img src="/images/logo/INCO_LOGO_Regular.svg" style="width: 100%; height: auto;" />
                            </div>
                            
                            <!-- Decorative Corner Elements -->
                            <div style="position: absolute; top: 10px; left: 10px; width: 20px; height: 20px; border-left: 1px solid rgba(212, 175, 55, 0.25); border-top: 1px solid rgba(212, 175, 55, 0.25);"></div>
                            <div style="position: absolute; top: 10px; right: 10px; width: 20px; height: 20px; border-right: 1px solid rgba(212, 175, 55, 0.25); border-top: 1px solid rgba(212, 175, 55, 0.25);"></div>
                            <div style="position: absolute; bottom: 10px; left: 10px; width: 20px; height: 20px; border-left: 1px solid rgba(212, 175, 55, 0.25); border-bottom: 1px solid rgba(212, 175, 55, 0.25);"></div>
                            <div style="position: absolute; bottom: 10px; right: 10px; width: 20px; height: 20px; border-right: 1px solid rgba(212, 175, 55, 0.25); border-bottom: 1px solid rgba(212, 175, 55, 0.25);"></div>
                            
                            <div style="position: relative; z-index: 1;">
                                <div style="text-align: left; margin-bottom: 20px; padding-right: 160px;">
                                    <h3 style="color: #2F4F4F; margin-bottom: 5px; font-size: 22px; text-shadow: 1px 1px 1px rgba(0,0,0,0.1);">Осигурително Брокерско Друштво ИНКО АД Скопје</h3>
                                    <h4 style="color: #000; margin-top: 0; font-size: 18px;">ФАКТУРА БРОЈ: @(Model.FakturaData.Rows[0]["BrojNaFaktura"])</h4>
                                </div>
                                
                                <div style="margin-bottom: 20px; background-color: rgba(47, 79, 79, 0.03); padding: 15px; border-radius: 6px; border: 1px solid rgba(212, 175, 55, 0.2); box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
                                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F; min-width: 120px; display: inline-block;">Фактура за:</strong> ${fakturaZa}</p>
                                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F; min-width: 120px; display: inline-block;">Договорувач:</strong> ${dogovoruvacName}</p>
                                    ${adresaDogovoruvac ? `<p style="margin: 5px 0; font-size: 12px; color: #666; margin-left: 120px;">${adresaDogovoruvac}</p>` : ''}
                                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F; min-width: 120px; display: inline-block;">Осигуреник:</strong> ${osigurenikName != "" ? osigurenikName : "Не е применливо"}</p>
                                    ${adresaOsigurenik && osigurenikName != "" ? `<p style="margin: 5px 0; font-size: 12px; color: #666; margin-left: 120px;">${adresaOsigurenik}</p>` : ''}
                                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F; min-width: 120px; display: inline-block;">Датум на фактура:</strong> ${formattedDate}</p>
                                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F; min-width: 120px; display: inline-block;">Рок на плаќање:</strong> ${formattedPaymentDueDate}</p>
                                    ${registracijaVozilo && registracijaVozilo.trim().length >= 3 ? `<p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F; min-width: 120px; display: inline-block;">Регистрација возило:</strong> ${registracijaVozilo}</p>` : ''}
                                </div>
                                
                                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); background-color: white;">
                                    <thead>
                                        <tr style="background: linear-gradient(90deg, #2F4F4F, #1a2f2f); color: white;">
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 13px;">Број на полиса</th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 13px;">Датум на издавање</th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 13px;">Осигурител</th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 13px;">Производ</th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: right; font-size: 13px;">Вкупна премија</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr style="background-color: rgba(47, 79, 79, 0.02);">
                                            <td style="border: 1px solid #ddd; padding: 8px; font-size: 13px;">@(Model.FakturaData.Rows[0]["BrojNaPolisa"])</td>
                                            <td style="border: 1px solid #ddd; padding: 8px; font-size: 13px;">@(Model.FakturaData.Rows[0]["DatumNaIzdavanje"] != DBNull.Value ? Convert.ToDateTime(Model.FakturaData.Rows[0]["DatumNaIzdavanje"]).ToString("dd.MM.yyyy") : "")</td>
                                            <td style="border: 1px solid #ddd; padding: 8px; font-size: 13px;">@(Model.FakturaData.Rows[0]["Osiguritel"])</td>
                                            <td style="border: 1px solid #ddd; padding: 8px; font-size: 13px;">@(Model.FakturaData.Rows[0]["Produkt"])</td>
                                            <td style="border: 1px solid #ddd; padding: 8px; text-align: right; font-size: 13px;">${(parseFloat("@(Model.FakturaData.Rows[0]["VkupnaPremija"] != DBNull.Value ? Convert.ToDecimal(Model.FakturaData.Rows[0]["VkupnaPremija"]).ToString("N2", System.Globalization.CultureInfo.InvariantCulture) : "0.00")".replace(',', '')) + parseFloat(iznosOZPoPolisa.replace(',', ''))).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                                        </tr>
                                    </tbody>
                                </table>
                                
                                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); background-color: white;">
                                    <thead>
                                        <tr style="background: linear-gradient(90deg, #2F4F4F, #1a2f2f); color: white;">
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 13px;">Рата број</th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 13px;">Датум доспевање</th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: right; font-size: 13px;">Износ рата</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @{var rowCounter = 0;}
                                        @foreach (System.Data.DataRow rataRow in Model.KarticaData.Rows)
                                        {
                                            <tr style="background-color: @(rowCounter % 2 == 0 ? "white" : "rgba(47, 79, 79, 0.02)")">
                                                <td style="border: 1px solid #ddd; padding: 8px; font-size: 13px;">@rataRow["RataBroj"]</td>
                                                <td style="border: 1px solid #ddd; padding: 8px; font-size: 13px;">@(rataRow["DatumNaDospevanje"] != DBNull.Value ? Convert.ToDateTime(rataRow["DatumNaDospevanje"]).ToString("dd.MM.yyyy") : "")</td>
                                                <td style="border: 1px solid #ddd; padding: 8px; text-align: right; font-size: 13px;">@(rataRow["IznosRata"] != DBNull.Value ? Convert.ToDecimal(rataRow["IznosRata"]).ToString("N2") : "")</td>
                                            </tr>
                                            rowCounter++;
                                        }
                                    </tbody>
                                </table>

                                <div style="margin-bottom: 20px; background-color: rgba(47, 79, 79, 0.03); padding: 15px; border-radius: 6px; border: 1px solid rgba(212, 175, 55, 0.2); box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
                                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F;">Со букви, износ на рата:</strong> @(Model.RataSoZborovi)</p>
                                    <p style="margin: 5px 0; color: #000; font-size: 13px;">Ве молиме фактурираниот износ да го платите до назначениот рок на плаќање.</p>
                                    <p style="margin: 5px 0; color: #000; font-size: 13px;">Доколку износот за фактурата не биде платен до наведениот датум, се пресметува затезна камата од денот на доспевање до денот на плаќање.</p>
                                    <p style="margin: 5px 0; color: #000; font-size: 13px;">Со најмалку две доспеани, а неплатени рати, целиот износ на фактурата се смета за доспеан и ИНКО стекнува право на негова наплата со правни средства.</p>
                                </div>
                                
                                @if (Model.ProcentNaPopustZaFakturaVoRok > 0)
                                {
                                    <div style="margin-bottom: 20px; background-color: rgba(47, 79, 79, 0.03); padding: 15px; border-radius: 6px; border: 1px solid rgba(212, 175, 55, 0.2); box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
                                        <p style="margin: 5px 0; color: #000; font-size: 13px;">Ако фактурата биде платена во рок, тогаш одобруваме попуст при што износот на фактура ќе биде: @(((Model.IznosZaPlakjanjeVoRok ?? 0) + (Model.IznosOZPoPolisa ?? 0)).ToString("N2")) - @(Model.IznosZaPlakjanjeVoRokSoZborovi) денари.</p>
                                    </div>
                                }
                                
                                <div style="display: flex; justify-content: space-between; margin: 30px 0; position: relative;">
                                    <!-- Gold Accent Line -->
                                    <div style="position: absolute; top: -10px; left: 0; right: 0; height: 1px; background: linear-gradient(90deg, #D4AF37, transparent); opacity: 0.3;"></div>
                                    
                                    <div style="text-align: center; flex: 1; padding: 0 10px;">
                                        <p style="margin-bottom: 20px;"><strong style="color: #2F4F4F; font-size: 13px;">Фактурирал</strong></p>                                        
                                        <p style="margin-top: 10px; font-size: 12px;">${izdal}</p>
                                        <p style="margin-top: 5px; font-size: 12px;">${mesto}</p>
                                    </div>
                                    <div style="text-align: center; flex: 1; padding: 0 10px;">
                                        <p style="margin-bottom: 20px;"><strong style="color: #2F4F4F; font-size: 13px;">Примил</strong></p>
                                        <p style="border-top: 1px solid rgba(47, 79, 79, 0.3); width: 80%; margin: 0 auto;"></p>
                                    </div>
                                    <div style="text-align: center; flex: 1; padding: 0 10px;">
                                        <p style="margin-bottom: 20px;"><strong style="color: #2F4F4F; font-size: 13px;">Одговорно лице за потпис на фактура</strong></p>
                                        <p style="border-top: 1px solid rgba(47, 79, 79, 0.3); width: 80%; margin: 0 auto;"></p>
                                    </div>
                                </div>

                                <!-- Gold Accent Bottom -->
                                <div style="position: relative; margin-top: 20px;">
                                    <div style="position: absolute; top: -10px; left: 0; right: 0; height: 1px; background: linear-gradient(90deg, transparent, #D4AF37, transparent); opacity: 0.3;"></div>
                                    <div style="position: absolute; top: -8px; left: 0; right: 0; height: 1px; background: linear-gradient(90deg, transparent, #D4AF37, transparent); opacity: 0.2;"></div>
                                    
                                    <div style="display: flex; justify-content: space-between; font-size: 0.85em; background-color: rgba(47, 79, 79, 0.03); padding: 15px; border-radius: 6px; border: 1px solid rgba(212, 175, 55, 0.2); box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
                                        <div>
                                            <p style="margin: 3px 0;"><strong style="color: #2F4F4F;">ЕДБ 4080025630210</strong></p>
                                            <p style="margin: 3px 0; color: #000;">Сметка: 210 078354340265</p>
                                        </div>
                                        <div>
                                            <p style="margin: 3px 0;"><strong style="color: #2F4F4F;">МБ 7835434</strong></p>
                                            <p style="margin: 3px 0; color: #000;">Банка: НЛБ Банка АД Скопје</p>
                                        </div>
                                        <div>
                                            <p style="margin: 3px 0;"><strong style="color: #2F4F4F;">Контакт маил: <EMAIL></strong></p>
                                            <p style="margin: 3px 0; color: #000;">Адреса: Ул.11 Октомври бр.86/1-1 , Скопје</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    fakturaPreview.html(invoiceHtml);
                    </text>
                }
                else
                {
                    <text>
                    alert('Нема податоци за генерирање на преглед на фактура.');
                    </text>
                }
            }
        });
    </script>
    
    <!-- Add script for handling invoice downloads -->
    <script>
        $(document).ready(function() {
            // Handle download invoice button clicks
            $(document).on('click', '.download-invoice', function() {
                const button = $(this);
                const invoiceId = button.data('invoice-id');
                const filePath = button.data('filepath');
                const fileName = button.data('filename');
                const brojNaFaktura = button.data('brojnafaktura');
                
                // Original button text
                const originalButtonHtml = button.html();
                
                // Change button state to show loading
                button.prop('disabled', true);
                button.html('<i class="bi bi-hourglass-split"></i> Преземање...');
                
                // Show loading overlay
                const overlay = $('#downloadOverlay');
                overlay.css('display', 'flex');
                
                // Get anti-forgery token
                const token = $('input[name="__RequestVerificationToken"]').val();
                
                // AJAX request to download invoice
                $.ajax({
                    url: '?handler=DownloadInvoice',
                    type: 'POST',
                    data: {
                        invoiceId: invoiceId,
                        filePath: filePath,
                        fileName: fileName
                    },
                    headers: {
                        'RequestVerificationToken': token
                    },
                    xhrFields: {
                        responseType: 'blob'
                    },
                    success: function(data) {
                        // Hide loading overlay
                        overlay.css('display', 'none');
                        
                        // Create a download link and trigger it
                        const downloadUrl = window.URL.createObjectURL(new Blob([data]));
                        const link = document.createElement('a');
                        link.href = downloadUrl;
                        link.download = fileName;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        
                        // Reset button state
                        button.prop('disabled', false);
                        button.html(originalButtonHtml);
                    },
                    error: function(xhr, status, error) {
                        // Hide loading overlay
                        overlay.css('display', 'none');
                        
                        console.error('Error downloading invoice:', error);
                        
                        // Show error message
                        alert('Грешка при преземање на фактурата: ' + error);
                        
                        // Reset button state
                        button.prop('disabled', false);
                        button.html(originalButtonHtml);
                    }
                });
            });
        });
    </script>
}

