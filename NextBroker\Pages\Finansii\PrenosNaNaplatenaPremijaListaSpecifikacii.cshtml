@page
@model NextBroker.Pages.Finansii.PrenosNaNaplatenaPremijaListaSpecifikaciiModel
@{
    ViewData["Title"] = "Пренос на наплатена премија - Сите влезни фактури";
}

@section Styles {
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" />
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css" />
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container-fluid mt-4 px-4">
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Листа на спецификации</h5>
                <div>
                    <form method="post" asp-page-handler="ExportToExcel" class="d-inline">
                        <input type="hidden" name="BrojNaFakturaFilter" value="@Model.BrojNaFakturaFilter" />
                        <input type="hidden" name="DatumNaFakturaFilter" value="@Model.DatumNaFakturaFilter?.ToString("yyyy-MM-dd")" />
                        <input type="hidden" name="RokNaPlakanjeFilter" value="@Model.RokNaPlakanjeFilter?.ToString("yyyy-MM-dd")" />
                        <button type="submit" class="btn btn-success me-2">
                            <i class="fas fa-file-excel"></i> Експорт Excel
                        </button>
                    </form>
                    <a asp-page="./PrenosNaNaplatenaPremijaSpecifikacii" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Нова спецификација
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <form method="get" class="mb-4">
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="BrojNaFakturaFilter">Број на фактура</label>
                            <input type="text" class="form-control" id="BrojNaFakturaFilter" name="BrojNaFakturaFilter" value="@Model.BrojNaFakturaFilter">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="DatumNaFakturaFilter">Датум на фактура</label>
                            <input type="date" class="form-control" id="DatumNaFakturaFilter" name="DatumNaFakturaFilter" value="@Model.DatumNaFakturaFilter?.ToString("yyyy-MM-dd")">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="RokNaPlakanjeFilter">Рок на плаќање</label>
                            <input type="date" class="form-control" id="RokNaPlakanjeFilter" name="RokNaPlakanjeFilter" value="@Model.RokNaPlakanjeFilter?.ToString("yyyy-MM-dd")">
                        </div>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Филтрирај
                        </button>
                        <a asp-page="./PrenosNaNaplatenaPremijaListaSpecifikacii" class="btn btn-secondary">
                            <i class="fas fa-undo"></i> Ресетирај
                        </a>
                    </div>
                </div>
            </form>
            <div class="table-responsive">
                <table id="specifikaciiTable" class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Датум на креирање</th>
                            <th>Креирал</th>
                            <th>Датум на промена</th>
                            <th>Променил</th>
                            <th>Назив</th>
                            <th>Број на фактура</th>
                            <th>Датум на влезна фактура</th>
                            <th>Рок на плаќање</th>
                            <th>Износ на фактура</th>
                            <th>Износ на фактура во рок</th>
                            <th>Платено од договорувач</th>
                            <th>Должи договорувач</th>
                            <th>Банка</th>
                            <th>Број на извод</th>
                            <th>Ставка во извод</th>
                            <th>Тип на фактура</th>
                            <th>Платено по фактура</th>
                            <th>Долг по фактура</th>
                            <th>Датум на плаќање</th>
                            <th>Статус на плаќање</th>
                            <th>Акции</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model.Specifikacii)
                        {
                            <tr>
                                <td>@item.Id</td>
                                <td>@item.DateCreated.ToString("dd.MM.yyyy")</td>
                                <td>@item.UsernameCreated</td>
                                <td>@(item.DateModified?.ToString("dd.MM.yyyy"))</td>
                                <td>@item.UsernameModified</td>
                                <td>@item.Naziv</td>
                                <td>@item.BrojNaFaktura</td>
                                <td>@(item.DatumNaVleznaFaktura?.ToString("dd.MM.yyyy"))</td>
                                <td>@(item.RokNaPlakjanjeFakturaVlezna?.ToString("dd.MM.yyyy"))</td>
                                <td class="text-end">@(item.IznosNaFaktura?.ToString("N2"))</td>
                                <td class="text-end">@(item.IznosNaFakturaVoRok?.ToString("N2"))</td>
                                <td class="text-end">@(item.PlatenoOdDogovoruvac?.ToString("N2"))</td>
                                <td class="text-end">@(item.Dolzi?.ToString("N2"))</td>
                                <td>@item.Banka</td>
                                <td>@item.BrojNaIzvod</td>
                                <td>@item.StavkaVoIzvod</td>
                                <td>@item.TipNaFaktura</td>
                                <td class="text-end">@(item.PlatenIznosPoFaktura?.ToString("N2"))</td>
                                <td class="text-end">@(item.DolgPoFaktura?.ToString("N2"))</td>
                                <td>@(item.DatumNaPlakanjeFaktura?.ToString("dd.MM.yyyy"))</td>
                                <td>@item.StatusNaPlakanje</td>
                                <td>
                                    <a asp-page="./PrenosNaNaplatenaPremijaViewEditSpecifikacija" asp-route-id="@item.Id" class="btn btn-sm btn-primary" title="Преглед/Измена">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#specifikaciiTable').DataTable({
                language: {
                    "sEmptyTable": "Нема податоци во табелата",
                    "sInfo": "Прикажани _START_ до _END_ од вкупно _TOTAL_ записи",
                    "sInfoEmpty": "Прикажани 0 до 0 од вкупно 0 записи",
                    "sInfoFiltered": "(филтрирано од вкупно _MAX_ записи)",
                    "sInfoPostFix": "",
                    "sInfoThousands": ",",
                    "sLengthMenu": "Прикажи _MENU_ записи",
                    "sLoadingRecords": "Вчитување...",
                    "sProcessing": "Обработка...",
                    "sSearch": "Пребарај:",
                    "sZeroRecords": "Не се пронајдени записи",
                    "oPaginate": {
                        "sFirst": "Прва",
                        "sLast": "Последна",
                        "sNext": "Следна",
                        "sPrevious": "Претходна"
                    },
                    "oAria": {
                        "sSortAscending": ": активирај за растечко сортирање на колоната",
                        "sSortDescending": ": активирај за опаѓачко сортирање на колоната"
                    }
                },
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, "Сите"]],
                order: [[0, 'desc']],
                columnDefs: [
                    {
                        targets: [9, 10, 11],
                        render: function (data, type, row) {
                            if (type === 'sort') {
                                return data.replace(/[^\d.-]/g, '');
                            }
                            return data;
                        }
                    },
                    {
                        targets: -1,
                        orderable: false,
                        searchable: false
                    }
                ]
            });
        });
    </script>
}


