@page
@model NextBroker.Pages.Pregledi.IzvestajProvizijaBrokerMesecenVlezniFakturiKonKlientModel
@{
    ViewData["Title"] = "Извештај за провизија за  брокер - влезни фактури кон клиент";
}

<div class="container-fluid">
    <h2>@ViewData["Title"]</h2>

    <form method="post" class="mb-4">
        <div class="row">
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="DatumOd"></label>
                    <input asp-for="DatumOd" class="form-control" id="datumOd" />
                    <span asp-validation-for="DatumOd" class="text-danger"></span>
                    <small id="datumOdValidation" class="text-danger" style="display: none;"></small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="DatumDo"></label>
                    <input asp-for="DatumDo" class="form-control" id="datumDo" />
                    <span asp-validation-for="DatumDo" class="text-danger"></span>
                    <small id="datumDoValidation" class="text-danger" style="display: none;"></small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="SelectedOsiguritel"></label>
                    <select asp-for="SelectedOsiguritel" class="form-control" asp-items="@Model.Osiguriteli">
                        <option value="">-- Изберете осигурител --</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="BrojNaFakturaZaProvizijaFilter"></label>
                    <input asp-for="BrojNaFakturaZaProvizijaFilter" class="form-control" />
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="form-check">
                    <input asp-for="ExcludeInvoicedPolicies" class="form-check-input" type="checkbox" checked/>
                    <label asp-for="ExcludeInvoicedPolicies" class="form-check-label"></label>
                </div>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">Генерирај извештај</button>
                @if (Model.ReportData != null && Model.ReportData.Rows.Count > 0)
                {
                    <button type="submit" asp-page-handler="ExportExcel" class="btn btn-success me-2">
                        <i class="fas fa-file-excel me-1"></i> Генерирај Excel
                    </button>
                    @if (Model.HasExistingInvoices)
                    {
                        <button type="button" class="btn btn-info" disabled title="Еден или повеќе записи веќе се дел од фактура за овој период!">
                            <i class="fas fa-file-invoice me-1"></i> Генерирај фактура
                        </button>
                        <div class="text-danger mt-2">
                            <small><i class="fas fa-exclamation-triangle me-1"></i> Еден или повеќе записи веќе се дел од фактура за овој период!</small>
                        </div>
                    }
                    else
                    {
                        <button type="submit" asp-page-handler="GenerateInvoicePreview" class="btn btn-info">
                            <i class="fas fa-file-invoice me-1"></i> Генерирај фактура
                        </button>
                    }
                }
            </div>
        </div>
    </form>

    @if (Model.ReportData != null && Model.ReportData.Rows.Count > 0)
    {
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>Број на полиса</th>
                        <th>Износ на Премија за Исплата на провизија</th>
                        <th>Датум на уплата</th>
                        <th>Назив</th>
                        <th>Класа</th>
                        <th>Продукт</th>
                        <th>Клиент</th>
                        <th>Процент на провизија</th>
                        <th>Износ на провизија</th>
                        <th>Бр. Фактура за провизија</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (System.Data.DataRow row in Model.ReportData.Rows)
                    {
                        <tr class="polisi-row" data-polisi-id="@row["Id"]">
                            <td>@row["BrojNaPolisa"]</td>
                            <td>@(((decimal)row["IznosNaPremijaZaIsplataNaProvizija"]).ToString("N2"))</td>
                            <td>@(row.Table.Columns.Contains("DatumNaUplata") && row["DatumNaUplata"] != DBNull.Value ? ((DateTime)row["DatumNaUplata"]).ToString("dd.MM.yyyy") : "N/A")</td>
                            <td>@row["Naziv"]</td>
                            <td>@row["KlasaIme"]</td>
                            <td>@row["Ime"]</td>
                            <td>@row["KlientIme"]</td>
                            <td>@(((decimal)row["ProcentNaProvizija"]).ToString("N2"))</td>
                            <td>@(((decimal)row["IznosProvizija"]).ToString("N2"))</td>
                            <td>@row["BrojNaFakturaZaProvizija"]</td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>

        @if (Model.ShowInvoicePreview)
        {
            <div class="mt-5">
                <h3>Преглед на фактура</h3>
                <div class="text-end mb-3">
                    <button type="button" class="btn btn-success" id="btnSocuvajFaktura">
                        <i class="bi bi-save me-1"></i> Зачувај фактура
                    </button>
                </div>
                <div class="card">
                    <div class="card-body">
                        <div id="fakturaPreview" style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; position: relative; padding: 20px 40px;">
                            <!-- Background Logo -->
                            <div style="position: absolute; top: 20px; right: 40px; width: 150px; opacity: 0.15; z-index: 0; pointer-events: none;">
                                <img src="/images/logo/INCO_LOGO_Regular.svg" style="width: 100%; height: auto;" />
                            </div>
                            
                            <!-- Decorative Corner Elements -->
                            <div style="position: absolute; top: 10px; left: 10px; width: 20px; height: 20px; border-left: 1px solid rgba(212, 175, 55, 0.25); border-top: 1px solid rgba(212, 175, 55, 0.25);"></div>
                            <div style="position: absolute; top: 10px; right: 10px; width: 20px; height: 20px; border-right: 1px solid rgba(212, 175, 55, 0.25); border-top: 1px solid rgba(212, 175, 55, 0.25);"></div>
                            <div style="position: absolute; bottom: 10px; left: 10px; width: 20px; height: 20px; border-left: 1px solid rgba(212, 175, 55, 0.25); border-bottom: 1px solid rgba(212, 175, 55, 0.25);"></div>
                            <div style="position: absolute; bottom: 10px; right: 10px; width: 20px; height: 20px; border-right: 1px solid rgba(212, 175, 55, 0.25); border-bottom: 1px solid rgba(212, 175, 55, 0.25);"></div>
                            
                            <div style="position: relative; z-index: 1;">
                                <div style="text-align: left; margin-bottom: 20px; padding-right: 160px;">
                                    <h3 style="color: #2F4F4F; margin-bottom: 5px; font-size: 22px; text-shadow: 1px 1px 1px rgba(0,0,0,0.1);">Осигурително Брокерско Друштво ИНКО АД Скопје</h3>
                                    <h4 style="color: #000; margin-top: 0; font-size: 18px;">ФАКТУРА БРОЈ: @Model.InvoiceNumber</h4>
                                </div>
                                
                                <div style="margin-bottom: 20px; background-color: rgba(47, 79, 79, 0.03); padding: 15px; border-radius: 6px; border: 1px solid rgba(212, 175, 55, 0.2); box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
                                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F; min-width: 120px; display: inline-block;">До:</strong> @Model.SelectedOsiguritelNaziv</p>
                                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F; min-width: 120px; display: inline-block;">Датум на фактура:</strong> @Model.InvoiceDate.ToString("dd.MM.yyyy")</p>
                                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F; min-width: 120px; display: inline-block;">Рок на плаќање:</strong> @Model.DueDate.ToString("dd.MM.yyyy")</p>
                                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F; min-width: 120px; display: inline-block;">Фактура за период:</strong> @Model.DatumOd.ToString("dd.MM.yyyy") - @Model.DatumDo.ToString("dd.MM.yyyy")</p>
                                </div>

                                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); background-color: white;">
                                    <thead>
                                        <tr style="background: linear-gradient(90deg, #2F4F4F, #1a2f2f); color: white;">
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 13px;">Реден број</th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 13px;">Класа</th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: right; font-size: 13px;">Износ во МКД</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (System.Data.DataRow row in Model.GroupedInvoiceData.Rows)
                                        {
                                            <tr style="background-color: rgba(47, 79, 79, 0.02);">
                                                <td style="border: 1px solid #ddd; padding: 8px; font-size: 13px;">@row["RedenBroj"]</td>
                                                <td style="border: 1px solid #ddd; padding: 8px; font-size: 13px;">@row["Klasa"]</td>
                                                <td style="border: 1px solid #ddd; padding: 8px; text-align: right; font-size: 13px;">@(((decimal)row["IznosVoMKD"]).ToString("N2"))</td>
                                            </tr>
                                        }
                                        <tr>
                                            <td colspan="2" style="border: 1px solid #ddd; padding: 8px; text-align: right; font-weight: bold; font-size: 13px;">Вкупно:</td>
                                            <td style="border: 1px solid #ddd; padding: 8px; text-align: right; font-weight: bold; font-size: 13px;">@Model.TotalAmount.ToString("N2")</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <div style="margin-bottom: 20px; background-color: rgba(47, 79, 79, 0.03); padding: 15px; border-radius: 6px; border: 1px solid rgba(212, 175, 55, 0.2); box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
                                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F;">Со букви, износ на фактура:</strong> @Model.TotalAmountInWords</p>
                                </div>

                                <div style="display: flex; justify-content: space-between; margin: 30px 0; position: relative;">
                                    <!-- Gold Accent Line -->
                                    <div style="position: absolute; top: -10px; left: 0; right: 0; height: 1px; background: linear-gradient(90deg, #D4AF37, transparent); opacity: 0.3;"></div>
                                    
                                    <div style="text-align: center; flex: 1; padding: 0 10px;">
                                        <p style="margin-bottom: 20px;"><strong style="color: #2F4F4F; font-size: 13px;">Фактурирал</strong></p>
                                        <p style="margin-top: 10px; font-size: 12px;">@Model.FakturiralIme</p>
                                    </div>
                                    <div style="text-align: center; flex: 1; padding: 0 10px;">
                                        <p style="margin-bottom: 20px;"><strong style="color: #2F4F4F; font-size: 13px;">Примил</strong></p>
                                        <p style="border-top: 1px solid rgba(47, 79, 79, 0.3); width: 80%; margin: 0 auto;"></p>
                                    </div>
                                    <div style="text-align: center; flex: 1; padding: 0 10px;">
                                        <p style="margin-bottom: 20px;"><strong style="color: #2F4F4F; font-size: 13px;">Одговорно лице за потпис на фактура</strong></p>
                                        <p style="border-top: 1px solid rgba(47, 79, 79, 0.3); width: 80%; margin: 0 auto;"></p>
                                    </div>
                                </div>

                                <!-- Gold Accent Bottom -->
                                <div style="position: relative; margin-top: 20px;">
                                    <div style="position: absolute; top: -10px; left: 0; right: 0; height: 1px; background: linear-gradient(90deg, transparent, #D4AF37, transparent); opacity: 0.3;"></div>
                                    <div style="position: absolute; top: -8px; left: 0; right: 0; height: 1px; background: linear-gradient(90deg, transparent, #D4AF37, transparent); opacity: 0.2;"></div>
                                    
                                    <div style="display: flex; justify-content: space-between; font-size: 0.85em; background-color: rgba(47, 79, 79, 0.03); padding: 15px; border-radius: 6px; border: 1px solid rgba(212, 175, 55, 0.2); box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
                                        <div>
                                            <p style="margin: 3px 0;"><strong style="color: #2F4F4F;">ЕДБ 4080025630210</strong></p>
                                            <p style="margin: 3px 0; color: #000;">Сметка: 210 078354340168</p>
                                        </div>
                                        <div>
                                            <p style="margin: 3px 0;"><strong style="color: #2F4F4F;">МБ 7835434</strong></p>
                                            <p style="margin: 3px 0; color: #000;">Банка: НЛБ Банка АД Скопје</p>
                                        </div>
                                        <div>
                                            <p style="margin: 3px 0;"><strong style="color: #2F4F4F;">Контакт маил: <EMAIL></strong></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Add Debug Panel -->
                <div class="card mt-3" style="display: none;">
                    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Debug Information</h5>
                        <button type="button" class="btn btn-sm btn-outline-light" onclick="clearDebugLog()">Clear Log</button>
                    </div>
                    <div class="card-body">
                        <div id="debugLog" style="max-height: 300px; overflow-y: auto; font-family: monospace;">
                        </div>
                    </div>
                </div>
            </div>
        }
    }
    else if (Model.ReportData != null)
    {
        <div class="alert alert-info">
            Нема податоци за избраниот период.
        </div>
    }
</div>

@section Scripts {
    <!-- Add jsPDF and html2canvas libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <script>
        // Add debug logging function
        function addDebugLog(message, type = 'info') {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-danger' : 
                         type === 'success' ? 'text-success' : 
                         type === 'warning' ? 'text-warning' : 'text-info';
            
            debugLog.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            debugLog.scrollTop = debugLog.scrollHeight;
        }

        function clearDebugLog() {
            document.getElementById('debugLog').innerHTML = '';
        }

        $(document).ready(function() {
            // Date validation functionality
            function validateDates() {
                const datumOd = new Date($('#datumOd').val());
                const datumDo = new Date($('#datumDo').val());
                const submitButton = $('button[type="submit"]');
                
                let isValid = true;
                let errorMessages = [];
                
                // Clear previous validation messages
                $('#datumOdValidation, #datumDoValidation').hide().text('');
                
                if ($('#datumOd').val() && $('#datumDo').val()) {
                    // Check if datumDo is smaller than datumOd
                    if (datumDo < datumOd) {
                        $('#datumDoValidation').text('Датумот "до" не може да биде помал од датумот "од"').show();
                        isValid = false;
                    }
                    
                    // Check if dates are in the same month and year
                    if (datumOd.getMonth() !== datumDo.getMonth() || datumOd.getFullYear() !== datumDo.getFullYear()) {
                        $('#datumOdValidation').text('Двата датума мора да бидат од истиот месец').show();
                        isValid = false;
                    }
                }
                
                // Enable/disable submit buttons based on validation
                if (isValid) {
                    submitButton.prop('disabled', false);
                    $('#datumOd, #datumDo').removeClass('is-invalid');
                } else {
                    submitButton.prop('disabled', true);
                    if (!isValid) {
                        $('#datumOd, #datumDo').addClass('is-invalid');
                    }
                }
                
                return isValid;
            }
            
            // Bind date validation to date input changes
            $('#datumOd, #datumDo').on('change blur', function() {
                validateDates();
            });
            
            // Initial validation on page load
            validateDates();
            
            // Prevent form submission if dates are invalid
            $('form').on('submit', function(e) {
                if (!validateDates()) {
                    e.preventDefault();
                    return false;
                }
            });

            // Get anti-forgery token
            const token = $('input[name="__RequestVerificationToken"]').val();
            
            // Function to save policy-invoice relationships to PolisiInvoiceHistory table
            function updatePolisiWithInvoiceNumber(invoiceNumber) {
                // Collect all polisi IDs from the report data
                const polisiIds = [];
                
                // This assumes there's a hidden ID column or data attribute for each row
                // We'll collect the IDs by adding a data attribute to each row in the table
                $('.polisi-row').each(function() {
                    const polisiId = $(this).data('polisi-id');
                    if (polisiId) {
                        polisiIds.push(polisiId);
                    }
                });
                
                if (polisiIds.length === 0) {
                    console.log('No polisi IDs found to update');
                    return;
                }
                
                console.log(`Saving ${polisiIds.length} polisi records to invoice history with invoice number: ${invoiceNumber}`);
                
                // Primary system: Save to PolisiInvoiceHistory table with period tracking
                const datumOd = '@Model.DatumOd.ToString("yyyy-MM-dd")';
                const datumDo = '@Model.DatumDo.ToString("yyyy-MM-dd")';
                
                $.ajax({
                    url: '?handler=SavePolisiInvoiceHistory',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        brojNaFaktura: invoiceNumber,
                        polisiIds: polisiIds,
                        datumOd: datumOd,
                        datumDo: datumDo
                    }),
                    headers: {
                        'RequestVerificationToken': token
                    }
                }).then(function(response) {
                    if (response.success) {
                        console.log(`Successfully saved ${polisiIds.length} polisi records to invoice history`);
                        console.log(`Invoice: ${invoiceNumber}, Period: ${datumOd} to ${datumDo}`);
                    } else {
                        console.error(`Failed to save polisi records to history: ${response.message}`);
                    }
                }).catch(function(error) {
                    console.error('Error saving polisi records to history:', error);
                });
                
                // Optional: Also update old column for backward compatibility (can be removed in future)
                $.ajax({
                    url: '?handler=UpdatePolisi',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        brojNaFaktura: invoiceNumber,
                        polisiIds: polisiIds
                    }),
                    headers: {
                        'RequestVerificationToken': token
                    }
                }).then(function(response) {
                    if (response.success) {
                        console.log(`Successfully updated ${polisiIds.length} polisi records (backward compatibility)`);
                    } else {
                        console.error(`Failed to update polisi records: ${response.message}`);
                    }
                }).catch(function(error) {
                    console.error('Error updating polisi records:', error);
                });
            }
            
            // Make the function available globally for later use
            window.updatePolisiWithInvoiceNumber = updatePolisiWithInvoiceNumber;
            
            $('#btnSocuvajFaktura').on('click', function() {
                // Disable the button immediately to prevent double-clicking
                $(this).prop('disabled', true);
                
                // Change text to show processing
                $(this).html('<i class="bi bi-hourglass-split me-1"></i> Зачувување...');
                
                // Start the PDF generation
                generatePDF(this);
            });

            function generatePDF(saveBtn) {
                // Get the invoice preview element
                const element = document.getElementById('fakturaPreview');
                
                // Make temporary adjustments to improve PDF appearance
                const originalStyle = element.getAttribute('style') || '';
                element.setAttribute('style', originalStyle + '; width: 794px; padding: 20px;');
                
                // Use html2canvas to capture the element as an image
                html2canvas(element, {
                    scale: 1.5,  // Higher scale for better quality
                    useCORS: true,
                    logging: false,
                    allowTaint: true,
                    backgroundColor: '#ffffff'
                }).then(canvas => {
                    // Restore original styling
                    element.setAttribute('style', originalStyle);
                    
                    // Initialize jsPDF
                    const { jsPDF } = window.jspdf;
                    const doc = new jsPDF('p', 'mm', 'a4', true); // The 'true' enables compression
                    
                    // Calculate dimensions to fill the page
                    const imgData = canvas.toDataURL('image/jpeg', 0.7); // Use JPEG format with lower quality to reduce size
                    
                    const pageWidth = doc.internal.pageSize.getWidth();
                    const pageHeight = doc.internal.pageSize.getHeight();
                    
                    // Use larger margins to fill the page better
                    const margin = 5; // 5mm margin
                    const usableWidth = pageWidth - (margin * 2);
                    const usableHeight = pageHeight - (margin * 2);
                    
                    // Add the image to the PDF with custom dimensions to fit the page
                    doc.addImage(imgData, 'JPEG', margin, margin, usableWidth, usableHeight);
                    
                    // Generate filename based on invoice number
                    const invoiceNumber = '@Model.InvoiceNumber';
                    const fileName = `faktura_${invoiceNumber}_${new Date().toISOString().slice(0, 10)}.pdf`;
                    
                    // Save the PDF
                    doc.save(fileName);
                    
                    // Get PDF as binary data for SFTP upload
                    const pdfBlob = doc.output('blob');
                    const reader = new FileReader();
                    
                    addDebugLog('Starting PDF to base64 conversion...', 'info');
                    
                    reader.onloadend = function() {
                        // This contains the base64 data
                        const base64data = reader.result;
                        const sizeKB = Math.round(base64data.length / 1024);
                        addDebugLog(`PDF converted to base64, size: ${sizeKB} KB`, 'success');
                        
                        // Upload PDF to SFTP server
                        addDebugLog('Starting SFTP upload...', 'info');
                        
                        // Get the invoice number and encode it properly
                        addDebugLog(`Invoice number for upload: ${invoiceNumber}`, 'info');
                        
                        const uploadData = {
                            brojNaFaktura: invoiceNumber,
                            fileName: fileName,
                            pdfBase64: base64data
                        };
                        
                        addDebugLog('Preparing upload request with data:', 'info');
                        addDebugLog(`- brojNaFaktura: ${uploadData.brojNaFaktura}`, 'info');
                        addDebugLog(`- fileName: ${uploadData.fileName}`, 'info');
                        addDebugLog(`- pdfBase64 length: ${uploadData.pdfBase64.length} chars`, 'info');
                        
                        $.ajax({
                            url: '?handler=UploadPdfToSftp',
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify(uploadData),
                            headers: {
                                'RequestVerificationToken': token
                            }
                        }).then(function(response) {
                            if (response.success) {
                                addDebugLog(`PDF uploaded to SFTP successfully: ${response.filePath}`, 'success');
                                addDebugLog(`Saved with filename: ${response.fileName}`, 'success');
                            } else {
                                addDebugLog(`Failed to upload PDF to SFTP: ${response.message}`, 'error');
                            }

                            // Display server debug messages if available
                            if (response.debugLog && Array.isArray(response.debugLog)) {
                                addDebugLog('--- Server Debug Log ---', 'info');
                                response.debugLog.forEach(log => {
                                    addDebugLog(log.Message, log.Type);
                                });
                                addDebugLog('--- End Server Debug Log ---', 'info');
                            }
                        }).catch(function(error) {
                            addDebugLog(`Error uploading PDF to SFTP: ${error.statusText || error.message}`, 'error');
                            if (error.responseJSON) {
                                addDebugLog(`Server error details: ${error.responseJSON.message}`, 'error');
                                
                                // Display server debug messages if available in error response
                                if (error.responseJSON.debugLog && Array.isArray(error.responseJSON.debugLog)) {
                                    addDebugLog('--- Server Error Debug Log ---', 'info');
                                    error.responseJSON.debugLog.forEach(log => {
                                        addDebugLog(log.Message, log.Type);
                                    });
                                    addDebugLog('--- End Server Error Debug Log ---', 'info');
                                }
                            }
                        });
                    };
                    
                    reader.onerror = function(error) {
                        addDebugLog(`Error converting PDF to base64: ${error}`, 'error');
                    };
                    
                    reader.readAsDataURL(pdfBlob);
                    
                    // Save invoice data to database
                    const invoiceData = {
                        brojNaFaktura: '@Model.InvoiceNumber',
                        fakturaDo: $('<div/>').html('@Model.SelectedOsiguritelNaziv').text(), // Decode HTML entities
                        datumNaFaktura: '@Model.InvoiceDate.ToString("yyyy-MM-dd")',
                        rokNaPlakanje: '@Model.DueDate.ToString("yyyy-MM-dd")',
                        iznos: @Model.TotalAmount.ToString(System.Globalization.CultureInfo.InvariantCulture),
                        datumOd: '@Model.DatumOd.ToString("yyyy-MM-dd")',
                        datumDo: '@Model.DatumDo.ToString("yyyy-MM-dd")'
                    };

                    // Save invoice data to database
                    $.ajax({
                        url: '?handler=SaveInvoiceData',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify(invoiceData),
                        headers: {
                            'RequestVerificationToken': token
                        }
                    }).then(function(response) {
                        if (!response.success) {
                            console.error('Failed to save invoice data:', response.message);
                        } else {
                            // Now update the Polisi records with the invoice number
                            updatePolisiWithInvoiceNumber(invoiceNumber);
                        }
                    }).catch(function(error) {
                        console.error('Error saving invoice data:', error);
                    });
                    
                    // Update button state
                    $(saveBtn).html('<i class="bi bi-check-circle me-1"></i> Зачувано');
                    $(saveBtn).removeClass('btn-success').addClass('btn-outline-success');
                    
                    // Add a new button to generate another invoice if needed
                    const resetButton = $('<button type="button" style="display: none;" class="btn btn-outline-primary ms-2"><i class="bi bi-arrow-repeat me-1"></i> Нова фактура</button>');
                    resetButton.on('click', function() {
                        location.reload();
                    });
                    $(saveBtn).after(resetButton);
                    
                }).catch(function(error) {
                    console.error('Error generating PDF:', error);
                    $(saveBtn).html('<i class="bi bi-x-circle me-1"></i> Грешка');
                    $(saveBtn).removeClass('btn-success').addClass('btn-danger');
                    alert('Грешка при генерирање на PDF: ' + error);
                });
            }
        });
    </script>
}
