using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;

namespace NextBroker.Pages.Polisi
{
    public class DodajPolisaKlasa17Model : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public DodajPolisaKlasa17Model(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("DodajPolisaKlasa17"))
            {
                return RedirectToAccessDenied();
            }

            return Page();
        }
    }
}
