using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;

namespace RazorPortal.Services
{
    public abstract class SecurePageModel : PageModel
    {
        protected readonly IConfiguration _configuration;

        public SecurePageModel(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public virtual async Task<bool> HasPageAccess(string pageName)
        {
            var username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                return false;
            }

            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using (var command = new SqlCommand(
                    "SELECT COUNT(1) FROM UserPageAccess WHERE Username = @Username AND PageName = @PageName", 
                    connection))
                {
                    command.Parameters.AddWithValue("@Username", username);
                    command.Parameters.AddWithValue("@PageName", pageName);

                    var count = (int)await command.ExecuteScalarAsync();
                    return count > 0;
                }
            }
        }

        public IActionResult RedirectToAccessDenied()
        {
            return RedirectToPage("/AccessDenied");
        }

        public static async Task<List<string>> GetUserAccessiblePages(string username, IConfiguration configuration)
        {
            if (string.IsNullOrEmpty(username))
            {
                return new List<string>();
            }

            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using (var command = new SqlCommand(
                    "SELECT PageName FROM UserPageAccess WHERE Username = @Username", 
                    connection))
                {
                    command.Parameters.AddWithValue("@Username", username);
                    var pages = new List<string>();
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            pages.Add(reader.GetString(0));
                        }
                    }
                    
                    return pages;
                }
            }
        }
    }
} 