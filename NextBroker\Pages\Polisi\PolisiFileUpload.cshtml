@page "{id:long}"
@model NextBroker.Pages.Polisi.PolisiFileUploadModel
@{
    ViewData["Title"] = "Прикачи документи за полиса";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2>@ViewData["Title"]</h2>
            <h4>Полиса ID: @Model.PolisaId</h4>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(TempData["ErrorMessage"]?.ToString()))
    {
        <div class="alert alert-danger">
            @TempData["ErrorMessage"]
        </div>
    }

    @if (!string.IsNullOrEmpty(TempData["SuccessMessage"]?.ToString()))
    {
        <div class="alert alert-success" id="successMessage">
            @TempData["SuccessMessage"]
        </div>
    }

    @if (!string.IsNullOrEmpty(TempData["DebugInfo"]?.ToString()))
    {
        <div class="alert alert-info">
            <pre>@TempData["DebugInfo"]</pre>
        </div>
    }

    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0">Прикачени документи</h5>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data" asp-page-handler="UploadFile">
                @Html.AntiForgeryToken()
                <div class="row mb-3">
                    <div class="col-md-8">
                        <input type="file" class="form-control" name="files" multiple required />
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary">Прикачи документи</button>
                        <a href="/Polisi/ListaPolisi" class="btn btn-secondary">Назад кон листа полиси</a>
                    </div>
                </div>
            </form>

            @if (Model.Files != null && Model.Files.Any())
            {
                <div class="table-responsive mt-3">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Име на документ</th>
                                <th>Датум на прикачување</th>
                                <th>Прикачил</th>
                                <th>Акции</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var file in Model.Files)
                            {
                                <tr>
                                    <td>@file.FileName</td>
                                    <td>@file.DateCreated.ToString("dd.MM.yyyy HH:mm")</td>
                                    <td>@file.UsernameCreated</td>
                                    <td>
                                        <div class="btn-group">
                                            <a asp-page-handler="DownloadFile" asp-route-fileId="@file.Id" 
                                               class="btn btn-sm btn-primary">
                                                <i class="fas fa-download"></i> Преземи
                                            </a>
                                            <form method="post" asp-page-handler="DeleteFile" class="d-inline" 
                                                  onsubmit="return confirm('Дали сте сигурни дека сакате да го избришете овој документ?');">
                                                <input type="hidden" name="fileId" value="@file.Id" />
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i> Избриши
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Remove success message after 10 seconds
        document.addEventListener('DOMContentLoaded', function () {
            var successMessage = document.getElementById('successMessage');
            if (successMessage) {
                setTimeout(function () {
                    successMessage.style.transition = 'opacity 1s';
                    successMessage.style.opacity = '0';
                    setTimeout(function () {
                        successMessage.remove();
                    }, 1000);
                }, 10000);
            }
        });
    </script>
} 