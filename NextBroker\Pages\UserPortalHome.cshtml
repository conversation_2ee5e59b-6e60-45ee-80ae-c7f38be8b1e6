﻿@page
@model RazorPortal.Pages.UserPortalHomeModel
@{
    ViewData["Title"] = "Кориснички портал - Дома";
}

<div class="user-portal-container">
    <!-- User Welcome Section -->
    <div class="welcome-section">
        <div class="user-info-card">
            <div class="user-header d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-user-circle"></i> Добредојдовте, @Model.FirstName @Model.LastName!</h2>
                    <p class="text-muted mb-0">Последна најава: @DateTime.Now.ToString("dd.MM.yyyy HH:mm")</p>
                </div>
                <div class="datetime-display">
                    <div class="date-item">
                        <i class="fas fa-calendar-alt"></i>
                        <span>@DateTime.Now.ToString("dd.MM.yyyy")</span>
                    </div>
                    <div class="time-item">
                        <i class="fas fa-clock"></i>
                        <span>@DateTime.Now.ToString("HH:mm")</span>
                    </div>
                </div>
            </div>
            <div class="user-details mt-3">
                <div class="row">
                    <div class="col-md-6">
                        <p><i class="fas fa-envelope"></i> E-mail: @Model.Email</p>
                        <p><i class="fas fa-phone"></i> Телефон: @Model.Phone</p>
                        <p><i class="fas fa-building"></i> Експозитура: @Model.EkspozituraName</p>
                    </div>
                    <div class="col-md-6">
                        <p><i class="fas fa-id-card"></i> ЕМБГ: @Model.Emb</p>
                        <p><i class="fas fa-user"></i> Корисничко име: @Model.Username</p>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </div>

    <!-- Quick Links Section -->
    <div class="quick-links-section mt-4">
        <div class="row justify-content-center">
            @if (Model.AccessiblePages.Contains("DodajKlient"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Klienti/DodajKlient" class="quick-link-card">
                        <i class="fas fa-user-plus"></i>
                        <span>Нов Клиент</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("ListaKlienti"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Klienti/ListaKlienti" class="quick-link-card">
                        <i class="fas fa-users"></i>
                        <span>Листа на Клиенти</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("ListaPolisi"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/ListaPolisi" class="quick-link-card">
                        <i class="fas fa-clipboard-list"></i>
                        <span>Листа на Полиси</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaAO"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaAO" class="quick-link-card">
                        <i class="fas fa-car"></i>
                        <span>Нова Полиса Авто Одговорност</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaZK"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaZK" class="quick-link-card">
                        <i class="fas fa-passport"></i>
                        <span>Нова Полиса Зелена Карта</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaKasko"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaKasko" class="quick-link-card">
                        <i class="fas fa-car-crash"></i>
                        <span>Нова Полиса Каско</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaGR"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaGR" class="quick-link-card">
                        <i class="fas fa-globe-europe"></i>
                        <span>Нова Полиса Гранично</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaKlasa1"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaKlasa1" class="quick-link-card">
                        <i class="fas fa-ambulance"></i>
                        <span>Нова Полиса Незгода</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaKlasa2"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaKlasa2" class="quick-link-card">
                        <i class="fas fa-heartbeat"></i>
                        <span>Нова Полиса Здравствено</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaKlasa8"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaKlasa8" class="quick-link-card">
                        <i class="fas fa-fire"></i>
                        <span>Нова Полиса Имот пожар</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaKlasa9"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaKlasa9" class="quick-link-card">
                        <i class="fas fa-home"></i>
                        <span>Нова Полиса Имот друго</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaKlasa18"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaKlasa18" class="quick-link-card">
                        <i class="fas fa-plane"></i>
                        <span>Нова Полиса Патничко</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaKlasa19"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaKlasa19" class="quick-link-card">
                        <i class="fas fa-heart"></i>
                        <span>Нова Полиса Живот</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaKlasa4"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaKlasa4" class="quick-link-card">
                        <i class="fas fa-train"></i>
                        <span>Нова Полиса Шински возила</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaKlasa5"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaKlasa5" class="quick-link-card">
                        <i class="fas fa-plane"></i>
                        <span>Нова Полиса Воздухоплови</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaKlasa6"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaKlasa6" class="quick-link-card">
                        <i class="fas fa-ship"></i>
                        <span>Нова Полиса Пловни објекти</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaKlasa7"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaKlasa7" class="quick-link-card">
                        <i class="fas fa-truck"></i>
                        <span>Нова Полиса Стока во превоз</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaKlasa11"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaKlasa11" class="quick-link-card">
                        <i class="fas fa-plane-slash"></i>
                        <span>Нова Полиса Одговорност воздухоплови</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaKlasa12"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaKlasa12" class="quick-link-card">
                        <i class="fas fa-anchor"></i>
                        <span>Нова Полиса Одговорност пловни објекти</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaKlasa13"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaKlasa13" class="quick-link-card">
                        <i class="fas fa-shield-alt"></i>
                        <span>Нова Полиса Општо осигурување</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaKlasa14"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaKlasa14" class="quick-link-card">
                        <i class="fas fa-credit-card"></i>
                        <span>Нова Полиса Кредити</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaKlasa15"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaKlasa15" class="quick-link-card">
                        <i class="fas fa-certificate"></i>
                        <span>Нова Полиса Гаранции</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaKlasa16"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaKlasa16" class="quick-link-card">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>Нова Полиса Финансиски загуби</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaKlasa17"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaKlasa17" class="quick-link-card">
                        <i class="fas fa-gavel"></i>
                        <span>Нова Полиса Правна заштита</span>
                    </a>
                </div>
            }
            @if (Model.AccessiblePages.Contains("DodajPolisaKlasa21"))
            {
                <div class="col-md-3 mb-3">
                    <a href="/Polisi/DodajPolisaKlasa21" class="quick-link-card">
                        <i class="fas fa-chart-line"></i>
                        <span>Нова Полиса Живот со удели во инвестициони фондови</span>
                    </a>
                </div>
            }
        </div>
    </div>

    <!-- Navigation Guide Section -->
    <div class="navigation-guide mt-4">
        <h3><i class="fas fa-bolt"></i> Водич за Навигација</h3>
        <div class="guide-content">
            <div class="guide-section">
                <h4><i class="fas fa-users"></i> Клиенти</h4>
                <p>Управувајте со вашите клиенти преку менито "Клиенти". Додавајте нови клиенти или прегледајте ја постоечката база на клиенти.</p>
            </div>
            <div class="guide-section">
                <h4><i class="fas fa-file-alt"></i> Полиси</h4>
                <p>Креирајте и управувајте со полиси преку менито "Полиси". Достапни се различни видови на осигурување.</p>
            </div>
            <div class="guide-section">
                <h4><i class="fas fa-money-bill-wave"></i> Финансии</h4>
                <p>Пристапете до финансиските извештаи и трансакции преку менито "Финансии".</p>
            </div>
        </div>
    </div>
</div>

<style>
    .user-portal-container {
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }

    .user-info-card, .quick-link-card, .navigation-guide {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .user-header {
        border-bottom: 2px solid #f0f0f0;
        padding-bottom: 15px;
    }

    .datetime-display {
        background: #f8f9fa;
        padding: 10px 20px;
        border-radius: 8px;
        text-align: right;
    }

    .date-item, .time-item {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 1.1rem;
        color: #0056b3;
    }

    .time-item {
        margin-top: 5px;
    }

    .datetime-display i {
        font-size: 1.2rem;
    }

    .user-details {
        color: #555;
    }

    .quick-link-card {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
        text-decoration: none;
        color: #333;
        height: 100%;
        transition: all 0.3s ease;
        text-align: center;
    }

    .quick-link-card i {
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .quick-link-card:hover {
        transform: translateY(-2px);
        background-color: #f8f9fa;
        color: #333;
    }

    .guide-content {
        display: grid;
        gap: 20px;
        margin-top: 15px;
    }

    .guide-section {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
    }

    .guide-section h4 {
        color: #0056b3;
        margin-bottom: 10px;
    }

    i {
        color: #0056b3;
    }

    .navigation-guide {
        margin-top: 2rem;
    }

    .navigation-guide h3 {
        text-align: center;
        margin-bottom: 1.5rem;
    }
</style>

