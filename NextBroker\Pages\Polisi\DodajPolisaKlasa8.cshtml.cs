using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Data;
using System.Runtime.Serialization;
using System.Reflection;

namespace NextBroker.Pages.Polisi
{
    public enum TipNaFakturaEnumKlasa8
    {
        [Display(Name = "Влезна фактура кон клиент")]
        [EnumMember(Value = "Влезна фактура кон клиент")]
        VleznaFakturaKonKlient,
        
        [Display(Name = "Влезна фактура кон брокер")]
        [EnumMember(Value = "Влезна фактура кон брокер")]
        VleznaFakturaKonBroker
    }

    [IgnoreAntiforgeryToken(Order = 1001)]
    public class DodajPolisaKlasa8Model : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public bool HasAdminAccess { get; private set; }

        public IEnumerable<SelectListItem> Osiguriteli { get; set; }
        public IEnumerable<SelectListItem> KlasiOsiguruvanje { get; set; }
        public IEnumerable<SelectListItem> Produkti { get; set; }
        public IEnumerable<SelectListItem> PriciniZaStorno { get; set; }
        public IEnumerable<SelectListItem> Valuti { get; set; }
        public IEnumerable<SelectListItem> NaciniNaPlakanje { get; set; }
        public IEnumerable<SelectListItem> TipoviNaPlakanje { get; set; }
        public IEnumerable<SelectListItem> Banki { get; set; }
        public IEnumerable<SelectListItem> ListaOpstini { get; set; }
        public IEnumerable<SelectListItem> PredmetiNaOsiguruvanjeKlasa8i9 { get; set; }
        public IEnumerable<SelectListItem> TipoviNaFaktura { get; set; }

        [BindProperty]
        public PolisaInputModel Input { get; set; } = new();

        public DodajPolisaKlasa8Model(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public async Task<IActionResult> OnGetAsync()
        {
            if (!await HasPageAccess("DodajPolisaKlasa8"))
            {
                return RedirectToAccessDenied();
            }

            // Check admin access
            HasAdminAccess = await HasPageAccess("DodajPolisaKlasa8Admin");

            await LoadOsiguriteli();
            await LoadKlasiOsiguruvanje();
            await LoadProdukti();
            await LoadPriciniZaStorno();
            await LoadValuti();
            await LoadNaciniNaPlakanje();
            await LoadTipoviNaPlakanje();
            await LoadBanki();
            await LoadListaOpstini();
            await LoadPredmetiNaOsiguruvanjeKlasa8i9();
            LoadTipoviNaFaktura();

            return Page();
        }

        private async Task LoadOsiguriteli()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Naziv 
                    FROM Klienti 
                    WHERE Osiguritel = 1 
                    AND ZivotNezivot = N'Неживот'
                    AND DogovorVaziDo > Convert(date,GETDATE())
                    ORDER BY Naziv", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Naziv"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Osiguriteli = items;
                }
            }
        }

        private async Task LoadKlasiOsiguruvanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, CONCAT(KlasaBroj, ' - ', KlasaIme) as DisplayName 
                    FROM KlasiOsiguruvanje 
                    WHERE (Disabled = 0 OR Disabled IS NULL)
                    AND Id = '8'
                    ORDER BY KlasaBroj", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayName"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    KlasiOsiguruvanje = items;
                }
            }
        }

        private async Task LoadProdukti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Ime 
                    FROM Produkti 
                    WHERE KlasaOsiguruvanjeId = '8'
                    ORDER BY Ime", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Ime"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Produkti = items;
                }
            }
        }

        private async Task LoadPriciniZaStorno()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, PricinaZaStorno 
                    FROM SifrarnikPricinaZaStorno 
                    ORDER BY PricinaZaStorno", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["PricinaZaStorno"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    PriciniZaStorno = items;
                }
            }
        }

        private async Task LoadValuti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Valuta 
                    FROM SifrarnikValuti 
                    ORDER BY Valuta", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Valuta"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Valuti = items;
                }
            }
        }

        private async Task LoadNaciniNaPlakanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, NacinNaPlakanje 
                    FROM SifrarnikNacinNaPlakanje 
                    WHERE Id not in ('2','3','4')
                    ORDER BY NacinNaPlakanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["NacinNaPlakanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    NaciniNaPlakanje = items;
                }
            }
        }

        private async Task LoadTipoviNaPlakanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, TipNaPlakanje 
                    FROM SifrarnikTipNaPlakanje 
                    ORDER BY TipNaPlakanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["TipNaPlakanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    TipoviNaPlakanje = items;
                }
            }
        }

        private async Task LoadBanki()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Banka 
                    FROM SifrarnikBanki 
                    ORDER BY Banka", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Banka"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Banki = items;
                }
            }
        }

        private async Task LoadListaOpstini()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Opstina 
                    FROM ListaOpstini 
                    ORDER BY Opstina", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Opstina"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    ListaOpstini = items;
                }
            }
        }

        private async Task LoadPredmetiNaOsiguruvanjeKlasa8i9()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, PredmetNaOsiguruvanje 
                    FROM SifrarnikPredmetNaOsiguruvanjeKlasa8i9 
                    ORDER BY PredmetNaOsiguruvanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["PredmetNaOsiguruvanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    PredmetiNaOsiguruvanjeKlasa8i9 = items;
                }
            }
        }

        private void LoadTipoviNaFaktura()
        {
            TipoviNaFaktura = Enum.GetValues(typeof(TipNaFakturaEnumKlasa8))
                .Cast<TipNaFakturaEnumKlasa8>()
                .Select(e => new SelectListItem
                {
                    Text = e.GetType()
                        .GetMember(e.ToString())
                        .First()
                        .GetCustomAttribute<DisplayAttribute>()
                        ?.Name ?? e.ToString(),
                    Value = e.GetType()
                        .GetMember(e.ToString())
                        .First()
                        .GetCustomAttribute<EnumMemberAttribute>()
                        ?.Value ?? e.ToString()
                })
                .ToList();
        }

        public class PolisaInputModel
        {
	            [Required(ErrorMessage = "Осигурител е задолжително поле")]
            [Display(Name = "Осигурител")]
            public long KlientiIdOsiguritel { get; set; }

	            [Required(ErrorMessage = "Класа на осигурување е задолжително поле")]
            [Display(Name = "Класа на осигурување")]
            public int KlasiOsiguruvanjeIdKlasa { get; set; }

            [Display(Name = "Продукт")]
            public int? ProduktiIdProizvod { get; set; }

            [Display(Name = "Број на полиса")]
            public string? BrojNaPolisa { get; set; }

            [Display(Name = "Колективна")]
            public bool Kolektivna { get; set; }

	            [Required(ErrorMessage = "Договорувач е задолжително поле")]
            [Display(Name = "Договорувач")]
            public long KlientiIdDogovoruvac { get; set; }

            [Display(Name = "Осигуреник")]
            public long? KlientiIdOsigurenik { get; set; }

            [Display(Name = "Соработник")]
            public long? KlientiIdSorabotnik { get; set; }

            [Display(Name = "Факторинг")]
            public bool Faktoring { get; set; }

            [Display(Name = "Генерирана излезна фактура")]
            public bool GeneriranaFakturaIzlezna { get; set; }

            [Display(Name = "Сторно")]
            public bool Storno { get; set; }

            [Display(Name = "Причина за сторно")]
            public int? SifrarnikPricinaZaStornoId { get; set; }

	            [Required(ErrorMessage = "Важи од е задолжително поле")]
            [Display(Name = "Важи од")]
            public DateTime? VaziOd { get; set; }

	            [Required(ErrorMessage = "Важи до е задолжително поле")]
            [Display(Name = "Важи до")]
            public DateTime? VaziDo { get; set; }

	            [Required(ErrorMessage = "Датум на издавање е задолжително поле")]
            [Display(Name = "Датум на издавање")]
            public DateTime? DatumNaIzdavanje { get; set; }

	            [Required(ErrorMessage = "Валута е задолжително поле")]
            [Display(Name = "Валута")]
            public int ValutiId { get; set; }

	            [Required(ErrorMessage = "Начин на плаќање е задолжително поле")]
            [Display(Name = "Начин на плаќање")]
            public int NaciniNaPlakanjeId { get; set; }

	            [Required(ErrorMessage = "Тип на плаќање е задолжително поле")]
            [Display(Name = "Тип на плаќање")]
            public int TipoviNaPlakanjeId { get; set; }

            [Display(Name = "Банка")]
            public int? BankiId { get; set; }

            [Display(Name = "Тип на фактура")]
            public string TipNaFaktura { get; set; }

            [Display(Name = "Број на влезна фактура")]
            [DisplayFormat(NullDisplayText = "")]
            public string? BrojNaVleznaFaktura { get; set; }

            [Display(Name = "Број на излезна фактура")]
            public string? BrojNaIzleznaFaktura { get; set; }

            [Display(Name = "Датум на влезна фактура")]
            public DateTime? DatumNaVleznaFaktura { get; set; }

            [Display(Name = "Датум на излезна фактура")]
            public DateTime? DatumNaIzleznaFaktura { get; set; }

            [Display(Name = "Рок на плаќање излезна фактура")]
            public DateTime? RokNaPlakanjeIzleznaFaktura { get; set; }

            [Display(Name = "Рок на плаќање влезна фактура")]
            public DateTime? RokNaPlakanjeVleznaFaktura { get; set; }

            [Display(Name = "Процент франшиза")]
            public decimal? ProcentFransiza { get; set; }

            [Display(Name = "Франшиза износ")]            
            public decimal? FranshizaIznos { get; set; }

            [Display(Name = "Корегирана стапка на провизија")]
            public decimal? KoregiranaStapkaNaProvizija { get; set; }

            [Display(Name = "Список на општини на објект на општина")]
            public int? ListaOpstiniIdOsiguranObjektOpstina { get; set; }

            [Display(Name = "Адреса на осигуран објект")]
            public string? OsiguranObjektAdresa { get; set; }

            [Display(Name = "Број на осигуран објект")]
            public string? OsiguranObjektBroj { get; set; }

            [Display(Name = "Имотен лист број/Лист за предбележување")]
            public string? ImotenListBrojListZaPredbelezuvanje { get; set; }

            [Display(Name = "КО")]
            public string? KO { get; set; }

            [Display(Name = "КП Бр")]
            public string? KPBr { get; set; }

            [Display(Name = "Објект м²")]
            public decimal? Objektm2 { get; set; }

            [Display(Name = "Помошни простории м²")]
            public decimal? PomoshniProstoriim2 { get; set; }

            [Display(Name = "Предмет на осигурување")]
            public long? SifrarnikPredmetNaOsiguruvanjeKlasa8i9Id { get; set; }

            [Display(Name = "Опис на предмет на осигурување")]
            public string? PredmetNaOsiguruvanjeOpis { get; set; }

            [Display(Name = "Вредност")]
            public decimal? Vrednost { get; set; }

            [Display(Name = "Сума на осигурување")]
            public decimal? SumaNaOsiguruvanje { get; set; }

            [Display(Name = "Осигурување од пожар и природни непогоди")]
            public decimal? OsiguruvanjeOdPozarIPrirodniNepogodi { get; set; }

            [Display(Name = "Поплава високи води")]
            public decimal? PoplavaVisokiVodi { get; set; }

            [Display(Name = "Земјотрес")]
            public decimal? Zemjotres { get; set; }

            [Display(Name = "Дополнителни трошоци")]
            public decimal? DopolnitelniTrosoci { get; set; }

            [Display(Name = "Основна премија")]
            public decimal? OsnovnaPremija { get; set; }

            [Display(Name = "Дополнителна премија")]
            public decimal? DopolnitelnaPremija { get; set; }

            [Display(Name = "Вкупна премија")]
            public decimal? VkupnaPremija { get; set; }

            [Display(Name = "Валута за франшиза")]
            public long? SifrarnikValutiIdFranshizaValuta { get; set; }

            [Display(Name = "Процент на попуст за фактура во рок")]
            public decimal? ProcentNaPopustZaFakturaVoRok { get; set; }

            [Display(Name = "Износ за плаќање во рок")]
            public decimal? IznosZaPlakjanjeVoRok { get; set; }

            [Display(Name = "Процент комерцијален попуст")]
            public decimal? ProcentKomercijalenPopust { get; set; }

            [Display(Name = "Процент финансиски")]
            public decimal? ProcentFinansiski { get; set; }

            [Display(Name = "Премија за наплата")]
            public decimal? PremijaZaNaplata { get; set; }
        }

        public async Task<IActionResult> OnGetSearchKlientiAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE MB LIKE @Search + '%'
                       OR EDB LIKE @Search + '%'
                       OR EMBG LIKE @Search + '%'
                       OR Naziv LIKE '%' + @Search + '%'
                       OR Ime LIKE '%' + @Search + '%'
                       OR Prezime LIKE '%' + @Search + '%'
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<IActionResult> OnGetSearchSorabotniciAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE (KlientSorabotnik = 1 or KlientVraboten = 1)
                        AND (MB LIKE @Search + '%'
                        OR EDB LIKE @Search + '%'
                        OR EMBG LIKE @Search + '%'
                        OR Naziv LIKE '%' + @Search + '%'
                        OR Ime LIKE '%' + @Search + '%'
                        OR Prezime LIKE '%' + @Search + '%')
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Page();
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // Get username from session
                            var username = HttpContext.Session.GetString("Username");

                            // Insert into Polisi table
                            using (SqlCommand cmd = new SqlCommand(@"
                                INSERT INTO Polisi (
                                    UsernameCreated,
                                    KlientiIdOsiguritel,
                                    KlasiOsiguruvanjeIdKlasa,
                                    ProduktiIdProizvod,
                                    BrojNaPolisa,
                                    KlientiIdDogovoruvac,
                                    KlientiIdOsigurenik,
                                    Kolektivna,
                                    DatumVaziOd,
                                    DatumVaziDo,
                                    DatumNaIzdavanje,
                                    SifrarnikValutiIdValuta,
                                    KlientiIdSorabotnik,
                                    Faktoring,
                                    SifrarnikValutiIdFranshizaValuta,
                                    ProcentFranshiza,
                                    FranshizaIznos,
                                    KoregiranaStapkaNaProvizija,
                                    SifrarnikNacinNaPlakjanjeId,
                                    TipNaFaktura,
                                    BrojNaFakturaVlezna,
                                    DatumNaFakturaVlezna,
                                    RokNaPlakjanjeFakturaVlezna,
                                    SifrarnikTipNaPlakanjeId,
                                    SifrarnikBankiIdBanka,
                                    GeneriranaFakturaIzlezna,
                                    BrojNaFakturaIzlezna,
                                    DatumNaIzleznaFaktura,
                                    RokNaPlakjanjeFakturaIzlezna,
                                    Storno,
                                    PricinaZaStorno
                                ) VALUES (
                                    @UsernameCreated,
                                    @KlientiIdOsiguritel,
                                    @KlasiOsiguruvanjeIdKlasa,
                                    @ProduktiIdProizvod,
                                    @BrojNaPolisa,
                                    @KlientiIdDogovoruvac,
                                    @KlientiIdOsigurenik,
                                    @Kolektivna,
                                    @DatumVaziOd,
                                    @DatumVaziDo,
                                    @DatumNaIzdavanje,
                                    @SifrarnikValutiIdValuta,
                                    @KlientiIdSorabotnik,
                                    @Faktoring,
                                    @SifrarnikValutiIdFranshizaValuta,
                                    @ProcentFranshiza,
                                    @FranshizaIznos,
                                    @KoregiranaStapkaNaProvizija,
                                    @SifrarnikNacinNaPlakjanjeId,
                                    @TipNaFaktura,
                                    @BrojNaVleznaFaktura,
                                    @DatumNaVleznaFaktura,
                                    @RokNaPlakanjeVleznaFaktura,
                                    @SifrarnikTipNaPlakanjeId,
                                    @SifrarnikBankiIdBanka,
                                    @GeneriranaFakturaIzlezna,
                                    @BrojNaIzleznaFaktura,
                                    @DatumNaIzleznaFaktura,
                                    @RokNaPlakanjeIzleznaFaktura,
                                    @Storno,
                                    @PricinaZaStorno
                                );
                                SELECT SCOPE_IDENTITY();", connection, transaction))
                            {
                                cmd.Parameters.AddWithValue("@UsernameCreated", username);
                                cmd.Parameters.AddWithValue("@KlientiIdOsiguritel", Input.KlientiIdOsiguritel);
                                cmd.Parameters.AddWithValue("@KlasiOsiguruvanjeIdKlasa", Input.KlasiOsiguruvanjeIdKlasa);
                                cmd.Parameters.AddWithValue("@ProduktiIdProizvod", (object)Input.ProduktiIdProizvod ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@BrojNaPolisa", (object)Input.BrojNaPolisa ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@KlientiIdDogovoruvac", Input.KlientiIdDogovoruvac);
                                cmd.Parameters.AddWithValue("@KlientiIdOsigurenik", (object)Input.KlientiIdOsigurenik ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@Kolektivna", Input.Kolektivna);
                                cmd.Parameters.AddWithValue("@DatumVaziOd", Input.VaziOd);
                                cmd.Parameters.AddWithValue("@DatumVaziDo", Input.VaziDo);
                                cmd.Parameters.AddWithValue("@DatumNaIzdavanje", Input.DatumNaIzdavanje);
                                cmd.Parameters.AddWithValue("@SifrarnikValutiIdValuta", Input.ValutiId);
                                cmd.Parameters.AddWithValue("@KlientiIdSorabotnik", (object)Input.KlientiIdSorabotnik ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@Faktoring", Input.Faktoring);
                                cmd.Parameters.AddWithValue("@SifrarnikValutiIdFranshizaValuta", 
                                    Input.SifrarnikValutiIdFranshizaValuta ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@ProcentFranshiza", (object)Input.ProcentFransiza ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@FranshizaIznos", Input.FranshizaIznos.HasValue ? (object)Input.FranshizaIznos.Value : DBNull.Value);
                                cmd.Parameters.AddWithValue("@KoregiranaStapkaNaProvizija", (object)Input.KoregiranaStapkaNaProvizija ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@SifrarnikNacinNaPlakjanjeId", Input.NaciniNaPlakanjeId);
                                cmd.Parameters.AddWithValue("@TipNaFaktura", Input.TipNaFaktura);
                                cmd.Parameters.AddWithValue("@BrojNaVleznaFaktura", (object)Input.BrojNaVleznaFaktura ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@DatumNaVleznaFaktura", (object)Input.DatumNaVleznaFaktura ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@RokNaPlakanjeVleznaFaktura", (object)Input.RokNaPlakanjeVleznaFaktura ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@SifrarnikTipNaPlakanjeId", Input.TipoviNaPlakanjeId);
                                cmd.Parameters.AddWithValue("@SifrarnikBankiIdBanka", (object)Input.BankiId ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@GeneriranaFakturaIzlezna", Input.GeneriranaFakturaIzlezna);
                                cmd.Parameters.AddWithValue("@BrojNaIzleznaFaktura", (object)Input.BrojNaIzleznaFaktura ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@DatumNaIzleznaFaktura", (object)Input.DatumNaIzleznaFaktura ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@RokNaPlakanjeIzleznaFaktura", (object)Input.RokNaPlakanjeIzleznaFaktura ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@Storno", Input.Storno);
                                cmd.Parameters.AddWithValue("@PricinaZaStorno", (object)Input.SifrarnikPricinaZaStornoId ?? DBNull.Value);

                                // Get the PolisaId from the insert
                                var polisaId = Convert.ToInt64(await cmd.ExecuteScalarAsync());

                                // Insert into PolisiKlasa8 table
                                using (SqlCommand cmd2 = new SqlCommand(@"
                                    INSERT INTO PolisiKlasa8 (
                                        UsernameCreated,
                                        PolisaId,
                                        ListaOpstiniIdOsiguranObjektOpstina,
                                        OsiguranObjektAdresa,
                                        OsiguranObjektBroj,
                                        ImotenListBrojListZaPredbelezuvanje,
                                        KO,
                                        KPBr,
                                        Objektm2,
                                        PomoshniProstoriim2,
                                        SifrarnikPredmetNaOsiguruvanjeKlasa8i9Id,
                                        PredmetNaOsiguruvanjeOpis,
                                        Vrednost,
                                        SumaNaOsiguruvanje,
                                        OsiguruvanjeOdPozarIPrirodniNepogodi,
                                        PoplavaVisokiVodi,
                                        Zemjotres,
                                        DopolnitelniTrosoci,
                                        OsnovnaPremija,
                                        DopolnitelnaPremija,
                                        VkupnaPremija,
                                        ProcentNaPopustZaFakturaVoRok,
                                        IznosZaPlakjanjeVoRok,
                                        ProcentKomercijalenPopust,
                                        ProcentFinansiski,
                                        PremijaZaNaplata
                                    ) VALUES (
                                        @UsernameCreated,
                                        @PolisaId,
                                        @ListaOpstiniIdOsiguranObjektOpstina,
                                        @OsiguranObjektAdresa,
                                        @OsiguranObjektBroj,
                                        @ImotenListBrojListZaPredbelezuvanje,
                                        @KO,
                                        @KPBr,
                                        @Objektm2,
                                        @PomoshniProstoriim2,
                                        @SifrarnikPredmetNaOsiguruvanjeKlasa8i9Id,
                                        @PredmetNaOsiguruvanjeOpis,
                                        @Vrednost,
                                        @SumaNaOsiguruvanje,
                                        @OsiguruvanjeOdPozarIPrirodniNepogodi,
                                        @PoplavaVisokiVodi,
                                        @Zemjotres,
                                        @DopolnitelniTrosoci,
                                        @OsnovnaPremija,
                                        @DopolnitelnaPremija,
                                        @VkupnaPremija,
                                        @ProcentNaPopustZaFakturaVoRok,
                                        @IznosZaPlakjanjeVoRok,
                                        @ProcentKomercijalenPopust,
                                        @ProcentFinansiski,
                                        @PremijaZaNaplata
                                    )", connection, transaction))
                                {
                                    cmd2.Parameters.AddWithValue("@UsernameCreated", username);
                                    cmd2.Parameters.AddWithValue("@PolisaId", polisaId);
                                    cmd2.Parameters.AddWithValue("@ListaOpstiniIdOsiguranObjektOpstina", 
                                        (object)Input.ListaOpstiniIdOsiguranObjektOpstina ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@OsiguranObjektAdresa", 
                                        (object)Input.OsiguranObjektAdresa ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@OsiguranObjektBroj", 
                                        (object)Input.OsiguranObjektBroj ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@ImotenListBrojListZaPredbelezuvanje", 
                                        (object)Input.ImotenListBrojListZaPredbelezuvanje ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@KO", 
                                        (object)Input.KO ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@KPBr", 
                                        (object)Input.KPBr ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@Objektm2", 
                                        (object)Input.Objektm2 ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@PomoshniProstoriim2", 
                                        (object)Input.PomoshniProstoriim2 ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@SifrarnikPredmetNaOsiguruvanjeKlasa8i9Id", 
                                        (object)Input.SifrarnikPredmetNaOsiguruvanjeKlasa8i9Id ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@PredmetNaOsiguruvanjeOpis", 
                                        (object)Input.PredmetNaOsiguruvanjeOpis ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@Vrednost", 
                                        (object)Input.Vrednost ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@SumaNaOsiguruvanje", 
                                        (object)Input.SumaNaOsiguruvanje ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@OsiguruvanjeOdPozarIPrirodniNepogodi", 
                                        (object)Input.OsiguruvanjeOdPozarIPrirodniNepogodi ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@PoplavaVisokiVodi", 
                                        (object)Input.PoplavaVisokiVodi ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@Zemjotres", 
                                        (object)Input.Zemjotres ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@DopolnitelniTrosoci", 
                                        (object)Input.DopolnitelniTrosoci ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@OsnovnaPremija", 
                                        (object)Input.OsnovnaPremija ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@DopolnitelnaPremija", 
                                        (object)Input.DopolnitelnaPremija ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@VkupnaPremija", 
                                        (object)Input.VkupnaPremija ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@ProcentNaPopustZaFakturaVoRok", 
                                        (object)Input.ProcentNaPopustZaFakturaVoRok ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@IznosZaPlakjanjeVoRok", 
                                        (object)Input.IznosZaPlakjanjeVoRok ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@ProcentKomercijalenPopust", 
                                        (object)Input.ProcentKomercijalenPopust ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@ProcentFinansiski", 
                                        (object)Input.ProcentFinansiski ?? DBNull.Value);
                                    cmd2.Parameters.AddWithValue("@PremijaZaNaplata", 
                                        (object)Input.PremijaZaNaplata ?? DBNull.Value);


                                    await cmd2.ExecuteNonQueryAsync();
                                }

                                // Call the stored procedure to generate payment installments
                                using (SqlCommand cmdRati = new SqlCommand("GenerirajRatiPolisa", connection, transaction))
                                {
                                    cmdRati.CommandType = CommandType.StoredProcedure;

                                    cmdRati.Parameters.AddWithValue("@PolisaId", polisaId);
                                    cmdRati.Parameters.AddWithValue("@SifrarnikValutiId", Input.ValutiId);
                                    cmdRati.Parameters.AddWithValue("@DatumNaIzdavanje", Input.DatumNaIzdavanje ?? (object)DBNull.Value);
                                    cmdRati.Parameters.AddWithValue("@SifrarnikNacinNaPlakanjeId", Input.NaciniNaPlakanjeId);
                                    cmdRati.Parameters.AddWithValue("@Premija", Input.VkupnaPremija ?? (object)DBNull.Value);

                                    await cmdRati.ExecuteNonQueryAsync();
                                }

                                await transaction.CommitAsync();
                                TempData["SuccessMessage"] = "Полисата е успешно зачувана.";
                                return RedirectToPage("/Polisi/ListaPolisi");
                            }
                        }
                        catch (Exception ex)
                        {
                            await transaction.RollbackAsync();
                            ModelState.AddModelError("", $"Грешка при зачувување: {ex.Message}");
                            if (ex.InnerException != null)
                            {
                                ModelState.AddModelError("", $"Дополнителни информации: {ex.InnerException.Message}");
                            }
                            return Page();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", $"Неочекувана грешка: {ex.Message}");
                if (ex.InnerException != null)
                {
                    ModelState.AddModelError("", $"Дополнителни информации: {ex.InnerException.Message}");
                }
                return Page();
            }
        }

        public async Task<JsonResult> OnGetCheckBrojNaPolisaAsync(string brojNaPolisa)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand("SELECT dbo.ProverkaZaPostoenjeNaPolisaBezStorno(@brojNaPolisa)", connection))
                {
                    cmd.Parameters.AddWithValue("@brojNaPolisa", brojNaPolisa);
                    var result = await cmd.ExecuteScalarAsync();
                    return new JsonResult(Convert.ToInt32(result));
                }
            }
        }
    }
} 