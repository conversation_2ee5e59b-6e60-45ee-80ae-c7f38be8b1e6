﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Data.SqlClient;

namespace RazorPortal.Pages
{
    public class UserValidationModel : PageModel
    {
        private readonly IConfiguration _configuration;

        public string Message { get; set; }
        public bool IsConfirmed { get; set; } = false;

        public UserValidationModel(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public void OnGet()
        {
            // This can handle any logic needed when the page loads
        }

        public IActionResult OnPost(string validationCode)
        {
            if (string.IsNullOrEmpty(validationCode))
            {
                Message = "Погрешен код за потврда, обидете се повторно...";
                return Page();
            }

            // Retrieve the connection string from the configuration
            var connectionString = _configuration.GetConnectionString("DefaultConnection");

            // Use a using statement to ensure the connection is properly disposed
            using (var connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (var command = new SqlCommand("UPDATE users SET locked = 0, resetrequest = NULL WHERE resetrequest = @code", connection))
                {
                    command.Parameters.AddWithValue("@code", validationCode);
                    int rowsAffected = command.ExecuteNonQuery();

                    if (rowsAffected > 0)
                    {
                        Message = "Корисникот е потврден.";
                        IsConfirmed = true;
                    }
                    else
                    {
                        Message = "Погрешен код за потврда, обидете се повторно...";
                    }
                }
            }

            return Page();
        }
    }
}