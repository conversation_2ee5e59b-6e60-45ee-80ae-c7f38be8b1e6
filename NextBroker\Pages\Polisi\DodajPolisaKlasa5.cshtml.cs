using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;

namespace NextBroker.Pages.Polisi
{
    public class DodajPolisaKlasa5Model : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public DodajPolisaKlasa5Model(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("DodajPolisaKlasa5"))
            {
                return RedirectToAccessDenied();
            }

            return Page();
        }
    }
}
