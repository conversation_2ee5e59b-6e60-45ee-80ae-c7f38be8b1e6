﻿using MailKit.Net.Smtp;
using MimeKit;
using Microsoft.Extensions.Configuration;
using System.IO;
using System.Threading.Tasks;

namespace RazorPortal.Services
{
    public class MailerService
    {
        private readonly IConfiguration _configuration;

        public MailerService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public async Task SendEmailAsync(string recipients, string subject, string messageBody, string attachmentPath = null)
        {
            var email = _configuration["EmailSettings:Email"];
            var appCode = _configuration["EmailSettings:AppCode"];
            var smtpServer = _configuration["EmailSettings:SmtpServer"];
            var smtpPort = int.Parse(_configuration["EmailSettings:SmtpPort"]);

            var message = new MimeMessage();
            message.From.Add(new MailboxAddress("NextBroker Core", email));
            message.To.Add(new MailboxAddress("", recipients));
            message.Subject = subject;

            var bodyBuilder = new BodyBuilder
            {
                HtmlBody = messageBody
            };

            // Add attachment if the path is provided
            if (!string.IsNullOrEmpty(attachmentPath) && File.Exists(attachmentPath))
            {
                bodyBuilder.Attachments.Add(attachmentPath);
            }

            message.Body = bodyBuilder.ToMessageBody();

            using (var client = new SmtpClient())
            {
                // Accept all SSL certificates (not recommended for production)
                client.ServerCertificateValidationCallback = (s, c, h, e) => true;

                await client.ConnectAsync(smtpServer, smtpPort, MailKit.Security.SecureSocketOptions.StartTls);
                await client.AuthenticateAsync(email, appCode);
                await client.SendAsync(message);
                await client.DisconnectAsync(true);
            }
        }
    }
}
