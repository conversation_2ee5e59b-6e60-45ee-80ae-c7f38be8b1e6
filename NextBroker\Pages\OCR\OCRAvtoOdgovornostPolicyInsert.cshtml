@page
@model NextBroker.Pages.OCR.OCRAvtoOdgovornostPolicyInsertModel
@{
    ViewData["Title"] = "Преглед и уредување на податоци од полиса";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-file-invoice mr-2"></i>
                        Преглед и уредување на податоци од полиса
                    </h3>
                    <div class="card-tools">
                        <a href="/OCR/OCRAvtoOdgovornostText" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-1"></i>
                            Назад кон Автоматско вчитување полиса
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                    {
                        <div class="alert alert-danger alert-dismissible">
                            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                            <h5><i class="icon fas fa-ban"></i> Грешка!</h5>
                            @Model.ErrorMessage
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
                    {
                        <div class="alert alert-success alert-dismissible">
                            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                            <h5><i class="icon fas fa-check"></i> Успех!</h5>
                            @Model.SuccessMessage
                        </div>
                    }

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        Прегледајте ги и уредете ги податоците извлечени од полисата пред да продолжите понатаму.
                        <br><small><i class="fas fa-asterisk text-danger mr-1"></i>Полињата означени со <span class="text-danger">*</span> се задолжителни.</small>
                    </div>

                    <form method="post">
                        <div class="row">
                            <!-- Policy Basic Information -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">
                                            <i class="fas fa-file-contract mr-2"></i>
                                            Основни податоци за полисата
                                        </h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="BrojNaPolisa">Број на полиса <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" asp-for="BrojNaPolisa" placeholder="Внесете број на полиса" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="Registracija">Регистрација <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" asp-for="Registracija" placeholder="Внесете регистрација" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="Prodavac">Продавач <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" asp-for="Prodavac" placeholder="Внесете продавач" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="MestoIzdavanjeId">Место издавање <span class="text-danger">*</span></label>
                                            <select class="form-control" asp-for="MestoIzdavanjeId" required>
                                                <option value="">-- Изберете место --</option>
                                                @foreach (var opstina in Model.OpstiniList)
                                                {
                                                    <option value="@opstina.Id">@opstina.Opstina</option>
                                                }
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="DatumNaIzdavanje">Датум на издавање <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" asp-for="DatumNaIzdavanje" placeholder="Внесете датум издавање" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="PolisaVaziOd">Полиса важи од <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" asp-for="PolisaVaziOd" placeholder="Внесете полиса важи од" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="PolisaVaziDo">Полиса важи до <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" asp-for="PolisaVaziDo" placeholder="Внесете полиса важи до" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="TipNaFaktura">Тип на фактура <span class="text-danger">*</span></label>
                                            <select class="form-control" asp-for="TipNaFaktura" required>
                                                <option value="">-- Изберете тип на фактура --</option>
                                                <option value="Влезна фактура кон брокер">Влезна фактура кон брокер</option>
                                                <option value="Влезна фактура кон клиент">Влезна фактура кон клиент</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="KlientiIdOsiguritel">Осигурител <span class="text-danger">*</span></label>
                                            <select class="form-control" asp-for="KlientiIdOsiguritel" required>
                                                <option value="">-- Изберете осигурител --</option>
                                                @foreach (var klient in Model.KlientiList)
                                                {
                                                    <option value="@klient.Id">@klient.Naziv</option>
                                                }
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="SifrarnikNacinNaPlakanjeId">Начин на плаќање <span class="text-danger">*</span></label>
                                            <select class="form-control" asp-for="SifrarnikNacinNaPlakanjeId" required>
                                                <option value="">-- Изберете начин на плаќање --</option>
                                                @foreach (var nacinPlakanje in Model.NacinNaPlakanjeList)
                                                {
                                                    <option value="@nacinPlakanje.Id">@nacinPlakanje.NacinNaPlakanje</option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Contracting Party Information -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">
                                            <i class="fas fa-user-tie mr-2"></i>
                                            Податоци за договорувач
                                        </h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="Dogovoruvac">Договорувач <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" asp-for="Dogovoruvac" placeholder="Внесете договорувач" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="DogovoruvacEMBG">ЕМБГ Договорувач <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" asp-for="DogovoruvacEMBG" placeholder="Внесете ЕМБГ" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="DogovoruvacAdresa">Адреса Договорувач <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" asp-for="DogovoruvacAdresa" placeholder="Внесете адреса" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="DogovoruvacMestoId">Место Договорувач <span class="text-danger">*</span></label>
                                            <select class="form-control" asp-for="DogovoruvacMestoId" required>
                                                <option value="">-- Изберете место --</option>
                                                @foreach (var opstina in Model.OpstiniList)
                                                {
                                                    <option value="@opstina.Id">@opstina.Opstina</option>
                                                }
                                            </select>
                                        </div>                                       
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Insured Person Information -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">
                                            <i class="fas fa-user-shield mr-2"></i>
                                            Податоци за осигуреник
                                        </h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="Osigurenik">Осигуреник <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" asp-for="Osigurenik" placeholder="Внесете осигуреник" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="OsigurenikEMBG">ЕМБГ Осигуреник <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" asp-for="OsigurenikEMBG" placeholder="Внесете ЕМБГ" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="OsigurenikMestoId">Место Осигуреник <span class="text-danger">*</span></label>
                                            <select class="form-control" asp-for="OsigurenikMestoId" required>
                                                <option value="">-- Изберете место --</option>
                                                @foreach (var opstina in Model.OpstiniList)
                                                {
                                                    <option value="@opstina.Id">@opstina.Opstina</option>
                                                }
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="AdresaOsigurenik">Адреса Осигуреник <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" asp-for="AdresaOsigurenik" placeholder="Внесете адреса" required>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Vehicle Information -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">
                                            <i class="fas fa-car mr-2"></i>
                                            Податоци за возило
                                        </h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="VidVoziloId">Вид возило <span class="text-danger">*</span></label>
                                            <select class="form-control" asp-for="VidVoziloId" required>
                                                <option value="">-- Изберете вид возило --</option>
                                                @foreach (var vidVozilo in Model.VidVoziloList)
                                                {
                                                    <option value="@vidVozilo.Id">@vidVozilo.TipNaVozilo</option>
                                                }
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="MarkaVozilo">Марка возило <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" asp-for="MarkaVozilo" placeholder="Внесете марка возило" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="ModelVozilo">Модел возило <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" asp-for="ModelVozilo" placeholder="Внесете модел возило" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="Shasija">Шасија</label>
                                            <input type="text" class="form-control" asp-for="Shasija" placeholder="Внесете шасија">
                                        </div>
                                        <div class="form-group">
                                            <label for="GodinaProizvodstvo">Година производство</label>
                                            <input type="text" class="form-control" asp-for="GodinaProizvodstvo" placeholder="Внесете година">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Vehicle Technical Data -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">
                                            <i class="fas fa-cogs mr-2"></i>
                                            Технички податоци
                                        </h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="MoknostVoKW">Моќност во KW</label>
                                            <input type="text" class="form-control" asp-for="MoknostVoKW" placeholder="Внесете моќност">
                                        </div>
                                        <div class="form-group">
                                            <label for="ZafatninaVoCm3">Зафатнина во cm³</label>
                                            <input type="text" class="form-control" asp-for="ZafatninaVoCm3" placeholder="Внесете зафатнина">
                                        </div>
                                        <div class="form-group">
                                            <label for="NosivostVoKG">Носивост во kg</label>
                                            <input type="text" class="form-control" asp-for="NosivostVoKG" placeholder="Внесете носивост">
                                        </div>
                                        <div class="form-group">
                                            <label for="RegMesta">Рег. места</label>
                                            <input type="text" class="form-control" asp-for="RegMesta" placeholder="Внесете рег. места">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Insurance Information -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">
                                            <i class="fas fa-shield-alt mr-2"></i>
                                            Податоци за осигурување
                                        </h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="OsnovnaPremijaAO">Основна премија АО <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" asp-for="OsnovnaPremijaAO" placeholder="Внесете премија" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="Bonus">Бонус</label>
                                            <input type="text" class="form-control" asp-for="Bonus" placeholder="Внесете бонус">
                                        </div>
                                        <div class="form-group">
                                            <label for="Doplatok">Доплаток</label>
                                            <input type="number" step="1" class="form-control" asp-for="Doplatok" placeholder="Внесете доплаток">
                                        </div>
                                        <div class="form-group">
                                            <label for="Popust">Попуст</label>
                                            <input type="number" step="1" class="form-control" asp-for="Popust" placeholder="Внесете попуст">
                                        </div>
                                        <div class="form-group">
                                            <label for="VkupnoOsnovnaPremijaAO">Вкупно основна премија АО <span class="text-danger">*</span></label>
                                            <input type="number" step="1" class="form-control" asp-for="VkupnoOsnovnaPremijaAO" placeholder="Внесете вкупна основна премија" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="KrsenjeStaklo">Кршење стакло</label>
                                            <input type="text" class="form-control" asp-for="KrsenjeStaklo" placeholder="Внесете кршење стакло">
                                        </div>
                                        <div class="form-group">
                                            <label for="OsiguruvanjePatnici">Осигурување патници</label>
                                            <input type="text" class="form-control" asp-for="OsiguruvanjePatnici" placeholder="Внесете осигурување патници">
                                        </div>
                                        <div class="form-group">
                                            <label for="DopolnitelnoOsiguruvanje">Дополнително осигурување</label>
                                            <input type="text" class="form-control" asp-for="DopolnitelnoOsiguruvanje" placeholder="Внесете дополнително осигурување">
                                        </div>
                                        <div class="form-group">
                                            <label for="Asistencija">Асистенција</label>
                                            <input type="text" class="form-control" asp-for="Asistencija" placeholder="Внесете асистенција">
                                        </div>
                                        <div class="form-group">
                                            <label for="VkupnaPremija">Вкупна премија <span class="text-danger">*</span></label>
                                            <input type="number" step="1" class="form-control" asp-for="VkupnaPremija" placeholder="Внесете вкупна премија" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-footer">
                                        <button type="submit" class="btn btn-success btn-lg">
                                            <i class="fas fa-database mr-2"></i>
                                            Внеси во база на податоци
                                        </button>
                                        <a href="/OCR/OCRAvtoOdgovornostText" class="btn btn-secondary btn-lg ml-2">
                                            <i class="fas fa-arrow-left mr-2"></i>
                                            Назад кон Автоматско вчитување полиса
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Auto-dismiss error alerts after 5 seconds (but not success alerts as they trigger redirect)
        setTimeout(function() {
            $('.alert-danger, .alert-warning, .alert-info').fadeOut('slow');
        }, 5000);

        // Helper function to parse numeric value from input (whole numbers only)
        function parseNumericValue(value) {
            if (!value || value.trim() === '') return 0;
            var parsed = parseFloat(value.replace(',', '.'));
            return isNaN(parsed) ? 0 : Math.round(parsed);
        }

        // Helper function to remove decimals from input value (allow negative numbers)
        function removeDecimals(value) {
            if (!value || value.trim() === '') return '';
            
            // Check if the value starts with a minus sign
            var isNegative = value.charAt(0) === '-';
            
            // Remove all non-numeric characters except minus at the beginning
            var cleaned = value.replace(/[^0-9-]/g, '');
            
            // Ensure only one minus sign and only at the beginning
            if (isNegative) {
                cleaned = '-' + cleaned.replace(/-/g, '');
            } else {
                cleaned = cleaned.replace(/-/g, '');
            }
            
            return cleaned;
        }

        // Helper function to convert decimal values to integers for technical fields
        function convertToInteger(value) {
            if (!value || value.trim() === '') return '';
            
            // Replace comma with dot for consistent decimal handling
            var normalizedValue = value.replace(',', '.');
            
            // Extract the integer part (everything before the decimal point)
            var integerPart = normalizedValue.split('.')[0];
            
            // Remove any non-numeric characters except minus at the beginning
            var isNegative = integerPart.charAt(0) === '-';
            var cleaned = integerPart.replace(/[^0-9-]/g, '');
            
            // Ensure only one minus sign and only at the beginning
            if (isNegative) {
                cleaned = '-' + cleaned.replace(/-/g, '');
            } else {
                cleaned = cleaned.replace(/-/g, '');
            }
            
            return cleaned;
        }

        // Function to calculate "Вкупно основна премија АО"
        function calculateVkupnoOsnovnaPremijaAO() {
            var osnovnaPremijaAO = parseNumericValue($('#OsnovnaPremijaAO').val());
            var bonus = parseNumericValue($('#Bonus').val());
            var doplatok = parseNumericValue($('#Doplatok').val());
            
            var total = osnovnaPremijaAO + bonus + doplatok;
            $('#VkupnoOsnovnaPremijaAO').val(total.toString());
            
            // Also trigger calculation of total premium
            calculateVkupnaPremija();
        }

        // Function to calculate "Вкупна премија"
        function calculateVkupnaPremija() {
            var osnovnaPremijaAO = parseNumericValue($('#OsnovnaPremijaAO').val());
            var bonus = parseNumericValue($('#Bonus').val());
            var doplatok = parseNumericValue($('#Doplatok').val());
            var krsenjeStaklo = parseNumericValue($('#KrsenjeStaklo').val());
            var osiguruvanjePatnici = parseNumericValue($('#OsiguruvanjePatnici').val());
            var dopolnitelnoOsiguruvanje = parseNumericValue($('#DopolnitelnoOsiguruvanje').val());
            var asistencija = parseNumericValue($('#Asistencija').val());
            var popust = parseNumericValue($('#Popust').val());
            
            var total = osnovnaPremijaAO + bonus + doplatok + krsenjeStaklo + 
                       osiguruvanjePatnici + dopolnitelnoOsiguruvanje + asistencija - popust;
            
            $('#VkupnaPremija').val(total.toString());
        }

        // Document ready function
        $(document).ready(function() {
            // Check if there's a success message on page load and start redirect countdown
            if ($('.alert-success').length > 0) {
                // Hide submit button and show countdown
                var button = $('button[type="submit"]');
                button.hide();
                button.after('<div id="loading-message" class="text-center mt-3"><i class="fas fa-spinner fa-spin mr-2"></i>Внесување во процес... Ќе бидете пренасочени за <span id="countdown">3</span> секунди.</div>');
                
                // Start countdown for successful submission
                var countdown = 3;
                var countdownInterval = setInterval(function() {
                    countdown--;
                    $('#countdown').text(countdown);
                    
                    if (countdown <= 0) {
                        clearInterval(countdownInterval);
                        // Redirect to ListaPolisi page
                        window.location.href = '/Polisi/ListaPolisi';
                    }
                }, 1000);
            }
            // Insurance fields that should only accept whole numbers
            var insuranceFields = '#OsnovnaPremijaAO, #Bonus, #Doplatok, #Popust, #VkupnoOsnovnaPremijaAO, #KrsenjeStaklo, #OsiguruvanjePatnici, #DopolnitelnoOsiguruvanje, #Asistencija, #VkupnaPremija';
            
            // Remove decimals from insurance fields on input
            $(insuranceFields).on('input', function() {
                var currentValue = $(this).val();
                var cleanedValue = removeDecimals(currentValue);
                if (currentValue !== cleanedValue) {
                    $(this).val(cleanedValue);
                }
            });

            // Convert decimal values to integers for MoknostVoKW field
            $('#MoknostVoKW').on('input', function() {
                var currentValue = $(this).val();
                var integerValue = convertToInteger(currentValue);
                if (currentValue !== integerValue) {
                    $(this).val(integerValue);
                }
            });

            // Convert existing value on page load for MoknostVoKW field
            var existingMoknostValue = $('#MoknostVoKW').val();
            if (existingMoknostValue) {
                var convertedValue = convertToInteger(existingMoknostValue);
                $('#MoknostVoKW').val(convertedValue);
            }

            // Add event listeners to all relevant fields for "Вкупно основна премија АО" calculation
            $('#OsnovnaPremijaAO, #Bonus, #Doplatok').on('input change blur', function() {
                calculateVkupnoOsnovnaPremijaAO();
            });

            // Add event listeners to all relevant fields for "Вкупна премија" calculation
            $('#OsnovnaPremijaAO, #Bonus, #Doplatok, #KrsenjeStaklo, #OsiguruvanjePatnici, #DopolnitelnoOsiguruvanje, #Asistencija, #Popust').on('input change blur', function() {
                calculateVkupnaPremija();
            });

            // Form submission handling is now automatic - no need to intercept since redirect is handled on page load

            // Perform initial calculations on page load
            calculateVkupnoOsnovnaPremijaAO();
            calculateVkupnaPremija();
        });
    </script>
}
