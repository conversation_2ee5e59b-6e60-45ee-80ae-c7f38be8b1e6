using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.Data;
using Microsoft.Data.SqlClient;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.Rendering;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using Renci.SshNet;
using System.IO;
using System.Text;

namespace NextBroker.Pages.Pregledi
{
    public class IzvestajProvizijaBrokerMesecenVlezniFakturiKonKlientModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public IzvestajProvizijaBrokerMesecenVlezniFakturiKonKlientModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
            DatumOd = DateTime.Now.Date.AddDays(-DateTime.Now.Day + 1); // First day of current month
            DatumDo = DateTime.Now.Date;
        }

        [BindProperty]
        [Display(Name = "Датум од")]
        [DataType(DataType.Date)]
        public DateTime DatumOd { get; set; }

        [BindProperty]
        [Display(Name = "Датум до")]
        [DataType(DataType.Date)]
        public DateTime DatumDo { get; set; }

        [BindProperty]
        [Display(Name = "Осигурител")]
        public long SelectedOsiguritel { get; set; }

        [BindProperty]
        [Display(Name = "Број на фактура за провизија")]
        public string BrojNaFakturaZaProvizijaFilter { get; set; }

        [BindProperty]
        [Display(Name = "Исклучи полиси кои веќе се дел од фактура")]
        public bool ExcludeInvoicedPolicies { get; set; }

        public IEnumerable<SelectListItem> Osiguriteli { get; set; }

        public DataTable ReportData { get; set; }

        // New properties for invoice preview
        public string SelectedOsiguritelNaziv { get; set; }
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public DateTime DueDate { get; set; }
        public DataTable GroupedInvoiceData { get; set; }
        public decimal TotalAmount { get; set; }
        public DateTime DatumNaUplata { get; set; }
        public string TotalAmountInWords { get; set; }
        public bool ShowInvoicePreview { get; set; }
        public string FakturiralIme { get; set; }
        public string BrojNaFakturaZaProvizija { get; set; }
        public bool HasExistingInvoices { get; set; }

        // Date validation methods
        private bool ValidateDateRange()
        {
            bool isValid = true;
            
            // Check if DatumDo is smaller than DatumOd
            if (DatumDo < DatumOd)
            {
                ModelState.AddModelError(nameof(DatumDo), "Датумот 'до' не може да биде помал од датумот 'од'");
                isValid = false;
            }
            
            // Check if dates are in the same month and year
            if (DatumOd.Month != DatumDo.Month || DatumOd.Year != DatumDo.Year)
            {
                ModelState.AddModelError(nameof(DatumOd), "Двата датума мора да бидат од истиот месец");
                isValid = false;
            }
            
            return isValid;
        }

        private async Task LoadOsiguriteli()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Naziv 
                    FROM Klienti 
                    WHERE Osiguritel = 1 
                    ORDER BY Naziv", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Naziv"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Osiguriteli = items;
                }
            }
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("IzvestajProvizijaBrokerMesecen"))
            {
                return RedirectToAccessDenied();
            }

            await LoadOsiguriteli();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!await HasPageAccess("IzvestajProvizijaBrokerMesecen"))
            {
                return RedirectToAccessDenied();
            }

            await LoadOsiguriteli();

            // Validate date range
            if (!ValidateDateRange())
            {
                return Page();
            }

            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();

                using (var command = new SqlCommand("IzvestajProvizijaZaBrokerMesecenVlezniFakturiKonKlient", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@DatumOd", DatumOd);
                    command.Parameters.AddWithValue("@DatumDo", DatumDo);
                    command.Parameters.AddWithValue("@KlientiIdOsiguritel", 93);
                    command.Parameters.AddWithValue("@Osiguritel2", SelectedOsiguritel);
                    command.Parameters.AddWithValue("@BrojNaFakturaZaProvizija", string.IsNullOrWhiteSpace(BrojNaFakturaZaProvizijaFilter) ? (object)DBNull.Value : BrojNaFakturaZaProvizijaFilter);
                    command.Parameters.AddWithValue("@ExcludeInvoicedPolicies", ExcludeInvoicedPolicies);

                    using (var adapter = new SqlDataAdapter(command))
                    {
                        ReportData = new DataTable();
                        adapter.Fill(ReportData);
                    }
                }

                // Filter out policies that already have invoices for this specific period
                if (ReportData != null && ReportData.Rows.Count > 0 && ExcludeInvoicedPolicies)
                {
                    var polisiIds = ReportData.AsEnumerable().Select(row => row.Field<long>("Id")).ToList();
                    
                    // Get policies that already have invoices for this period from PolisiInvoiceHistory table
                    var excludeIds = await GetPoliciesWithInvoicesForPeriod(connection, polisiIds, DatumOd, DatumDo);
                    
                    if (excludeIds.Any())
                    {
                        // Filter out policies that already have invoices for this specific period
                        var filteredRows = ReportData.AsEnumerable()
                            .Where(row => !excludeIds.Contains(row.Field<long>("Id")))
                            .ToList();
                        
                        if (filteredRows.Any())
                        {
                            var newTable = ReportData.Clone();
                            foreach (var row in filteredRows)
                            {
                                newTable.ImportRow(row);
                            }
                            ReportData = newTable;
                        }
                        else
                        {
                            ReportData.Clear();
                        }
                    }
                }

                // Get the selected osiguritel name
                if (SelectedOsiguritel > 0)
                {
                    using (var command = new SqlCommand("SELECT Naziv FROM Klienti WHERE Id = @Id", connection))
                    {
                        command.Parameters.AddWithValue("@Id", SelectedOsiguritel);
                        SelectedOsiguritelNaziv = (string)await command.ExecuteScalarAsync();
                    }
                }
                
                // Check if any rows have existing invoice numbers for the current period (only check new table)
                HasExistingInvoices = false;
                if (ReportData != null && ReportData.Rows.Count > 0)
                {
                    // Only check the new table for period-specific invoices
                    var polisiIds = ReportData.AsEnumerable().Select(row => row.Field<long>("Id")).ToList();
                    HasExistingInvoices = await CheckExistingInvoicesForPeriod(connection, polisiIds, DatumOd, DatumDo);
                }
            }

            return Page();
        }

        public async Task<IActionResult> OnPostGenerateInvoicePreviewAsync()
        {
            if (!await HasPageAccess("IzvestajProvizijaBrokerMesecen"))
            {
                return RedirectToAccessDenied();
            }

            await LoadOsiguriteli();
            
            // Validate date range
            if (!ValidateDateRange())
            {
                return Page();
            }
            
            await OnPostAsync(); // Load the report data first
            
            // If there are existing invoices, prevent generating a new one
            if (HasExistingInvoices)
            {
                return Page();
            }

            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();

                // Get the username from session and get the full name
                var username = HttpContext.Session.GetString("Username");
                if (!string.IsNullOrEmpty(username))
                {
                    using (var command = new SqlCommand("SELECT dbo.VratiImePrezimePoUsername(@Username)", connection))
                    {
                        command.Parameters.AddWithValue("@Username", username);
                        FakturiralIme = (await command.ExecuteScalarAsync())?.ToString();
                    }
                }

                // Get the highest existing invoice number from both tables and increment it
                using (var command = new SqlCommand(@"
                    SELECT TOP 1 InvoiceNumber
                    FROM (
                        SELECT BrojNaFaktura as InvoiceNumber 
                        FROM PolisiInvoiceHistory 
                        WHERE BrojNaFaktura IS NOT NULL AND BrojNaFaktura != ''
                        UNION ALL
                        SELECT BrojNaFaktura as InvoiceNumber 
                        FROM FakturiZaProvizijaKonOsiguritel 
                        WHERE BrojNaFaktura IS NOT NULL AND BrojNaFaktura != ''
                    ) AS AllInvoices
                    WHERE InvoiceNumber LIKE 'O.K %'
                    ORDER BY InvoiceNumber DESC", connection))
                {
                    var lastInvoiceNumber = await command.ExecuteScalarAsync() as string;
                    var twoDigitYear = DateTime.Now.Year % 100; // Gets last 2 digits of year
                    
                    int nextNumber = 1; // Default to 1 if no previous invoices found
                    
                    if (!string.IsNullOrEmpty(lastInvoiceNumber))
                    {
                        // Parse the number from format "O.K XXXX/YY"
                        // Expected format: "O.K 0011/25"
                        try
                        {
                            var parts = lastInvoiceNumber.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                            if (parts.Length >= 2 && parts[0] == "O.K")
                            {
                                var numberPart = parts[1].Split('/')[0]; // Get "0011" part
                                if (int.TryParse(numberPart, out int currentNumber))
                                {
                                    nextNumber = currentNumber + 1;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            // Log the error but continue with default next number
                            System.Diagnostics.Debug.WriteLine($"Error parsing invoice number '{lastInvoiceNumber}': {ex.Message}");
                        }
                    }
                    
                    InvoiceNumber = $"O.K {nextNumber.ToString("0000")}/{twoDigitYear}";
                }

                // Set invoice dates
                InvoiceDate = DateTime.Now.Date;
                DueDate = InvoiceDate.AddDays(15);

                // Group the data by Klasa
                GroupedInvoiceData = new DataTable();
                GroupedInvoiceData.Columns.Add("RedenBroj", typeof(int));
                GroupedInvoiceData.Columns.Add("Klasa", typeof(string));
                GroupedInvoiceData.Columns.Add("IznosVoMKD", typeof(decimal));

                if (ReportData != null && ReportData.Rows.Count > 0)
                {
                    var groupedData = ReportData.AsEnumerable()
                        .GroupBy(row => row.Field<string>("KlasaIme"))
                        .Select((group, index) => new
                        {
                            RedenBroj = index + 1,
                            Klasa = group.Key,
                            IznosVoMKD = group.Sum(row => row.Field<decimal>("IznosProvizija"))
                        });

                    foreach (var item in groupedData)
                    {
                        GroupedInvoiceData.Rows.Add(item.RedenBroj, item.Klasa, item.IznosVoMKD);
                    }

                    TotalAmount = groupedData.Sum(x => x.IznosVoMKD);

                    // Get text representation of total amount
                    using (var cmdText = new SqlCommand("SELECT dbo.fn_BrojVoZborovi(@Number)", connection))
                    {
                        cmdText.Parameters.AddWithValue("@Number", Convert.ToInt32(TotalAmount));
                        TotalAmountInWords = (await cmdText.ExecuteScalarAsync())?.ToString();
                    }
                }

                ShowInvoicePreview = true;
            }

            return Page();
        }

        public async Task<IActionResult> OnPostExportExcelAsync()
        {
            if (!await HasPageAccess("IzvestajProvizijaBrokerMesecen"))
            {
                return RedirectToAccessDenied();
            }

            await LoadOsiguriteli();

            // Validate date range
            if (!ValidateDateRange())
            {
                return Page();
            }

            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();

                using (var command = new SqlCommand("IzvestajProvizijaZaBrokerMesecenVlezniFakturiKonKlient", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@DatumOd", DatumOd);
                    command.Parameters.AddWithValue("@DatumDo", DatumDo);
                    command.Parameters.AddWithValue("@KlientiIdOsiguritel", 93);
                    command.Parameters.AddWithValue("@Osiguritel2", SelectedOsiguritel);
                    command.Parameters.AddWithValue("@BrojNaFakturaZaProvizija", string.IsNullOrWhiteSpace(BrojNaFakturaZaProvizijaFilter) ? (object)DBNull.Value : BrojNaFakturaZaProvizijaFilter);
                    command.Parameters.AddWithValue("@ExcludeInvoicedPolicies", ExcludeInvoicedPolicies);
                    
                    using (var adapter = new SqlDataAdapter(command))
                    {
                        var dt = new DataTable();
                        adapter.Fill(dt);

                        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                        using (var package = new ExcelPackage())
                        {
                            var worksheet = package.Workbook.Worksheets.Add("Провизија по брокер");

                            // Add report header
                            worksheet.Cells["A1"].Value = "Месечен извештај за провизија по брокер";
                            worksheet.Cells["A1:H1"].Merge = true;
                            worksheet.Cells["A1"].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                            worksheet.Cells["A1"].Style.Font.Bold = true;

                            worksheet.Cells["A3"].Value = "Период од:";
                            worksheet.Cells["B3"].Value = DatumOd.ToString("dd.MM.yyyy");
                            worksheet.Cells["C3"].Value = "до:";
                            worksheet.Cells["D3"].Value = DatumDo.ToString("dd.MM.yyyy");

                            // Add headers at row 5
                            worksheet.Cells["A5"].Value = "Број на полиса";
                            worksheet.Cells["B5"].Value = "Износ на Премија за Исплата на провизија";
                            worksheet.Cells["C5"].Value = "Датум на уплата";
                            worksheet.Cells["D5"].Value = "Назив";
                            worksheet.Cells["E5"].Value = "Класа";
                            worksheet.Cells["F5"].Value = "Продукт";
                            worksheet.Cells["G5"].Value = "Клиент";
                            worksheet.Cells["H5"].Value = "Процент на провизија";
                            worksheet.Cells["I5"].Value = "Износ на провизија";
                            worksheet.Cells["J5"].Value = "Бр. Фактура за провизија";

                            // Style the headers
                            using (var range = worksheet.Cells["A5:J5"])
                            {
                                range.Style.Font.Bold = true;
                                range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                                range.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                                range.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                                range.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                                range.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                            }

                            // Add data starting from row 6
                            int row = 6;
                            foreach (DataRow dataRow in dt.Rows)
                            {
                                worksheet.Cells[row, 1].Value = dataRow["BrojNaPolisa"];
                                worksheet.Cells[row, 2].Value = dataRow["IznosNaPremijaZaIsplataNaProvizija"];
                                worksheet.Cells[row, 3].Value = ((DateTime)dataRow["DatumNaUplata"]).ToString("dd.MM.yyyy");
                                worksheet.Cells[row, 4].Value = dataRow["Naziv"];
                                worksheet.Cells[row, 5].Value = dataRow["KlasaIme"];
                                worksheet.Cells[row, 6].Value = dataRow["Ime"];
                                worksheet.Cells[row, 7].Value = dataRow["KlientIme"];
                                worksheet.Cells[row, 8].Value = dataRow["ProcentNaProvizija"];
                                worksheet.Cells[row, 9].Value = dataRow["IznosProvizija"];
                                worksheet.Cells[row, 10].Value = dataRow["BrojNaFakturaZaProvizija"];

                                // Format decimal columns
                                worksheet.Cells[row, 2].Style.Numberformat.Format = "#,##0.00";
                                worksheet.Cells[row, 8].Style.Numberformat.Format = "#,##0.00";
                                worksheet.Cells[row, 9].Style.Numberformat.Format = "#,##0.00";

                                row++;
                            }

                            // Add totals row
                            worksheet.Cells[row, 1].Value = "Вкупно:";
                            worksheet.Cells[row, 2].Formula = $"SUM(B6:B{row-1})";
                            worksheet.Cells[row, 9].Formula = $"SUM(I6:I{row-1})";
                            
                            using (var range = worksheet.Cells[row, 1, row, 10])
                            {
                                range.Style.Font.Bold = true;
                                range.Style.Border.Top.Style = ExcelBorderStyle.Double;
                            }

                            // Auto-fit columns
                            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                            // Generate the Excel file
                            var content = package.GetAsByteArray();
                            var fileName = $"Провизија_по_осигурител_{DatumOd:dd.MM.yyyy}-{DatumDo:dd.MM.yyyy}.xlsx";

                            return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                        }
                    }
                }
            }
        }



        [HttpPost]
        public async Task<IActionResult> OnPostSaveInvoiceDataAsync([FromBody] SaveInvoiceRequestVlezniFakturiKonKlient request)
        {
            if (!await HasPageAccess("IzvestajProvizijaBrokerMesecen"))
            {
                return new JsonResult(new { success = false, message = "Access denied" });
            }

            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    
                    using (var command = new SqlCommand(@"
                        INSERT INTO FakturiZaProvizijaKonOsiguritel 
                        (UsernameCreated, BrojNaFaktura, FakturaDo, DatumNaFaktura, RokNaPlakanje, Iznos, DatumOd, DatumDo)
                        VALUES 
                        (@UsernameCreated, @BrojNaFaktura, @FakturaDo, @DatumNaFaktura, @RokNaPlakanje, @Iznos, @DatumOd, @DatumDo)", 
                        connection))
                    {
                        command.Parameters.AddWithValue("@UsernameCreated", HttpContext.Session.GetString("Username"));
                        command.Parameters.AddWithValue("@BrojNaFaktura", request.BrojNaFaktura);
                        command.Parameters.AddWithValue("@FakturaDo", request.FakturaDo);
                        command.Parameters.AddWithValue("@DatumNaFaktura", request.DatumNaFaktura);
                        command.Parameters.AddWithValue("@RokNaPlakanje", request.RokNaPlakanje);
                        command.Parameters.AddWithValue("@Iznos", request.Iznos);
                        command.Parameters.AddWithValue("@DatumOd", request.DatumOd);
                        command.Parameters.AddWithValue("@DatumDo", request.DatumDo);

                        await command.ExecuteNonQueryAsync();
                        
                        return new JsonResult(new { success = true });
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        // Method to upload a file to SFTP server
        private async Task<(string filePath, string fileName)> UploadPdfToSftp(byte[] pdfData, string originalFileName)
        {
            System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Starting SFTP upload for file: {originalFileName}");
            System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] PDF data size: {pdfData.Length} bytes");
            
            try
            {
                var sftpConfig = _configuration.GetSection("SftpConfig");
                var host = sftpConfig["Host"];
                var port = int.Parse(sftpConfig["Port"]);
                var username = sftpConfig["Username"];
                var password = sftpConfig["Password"];
                
                if (string.IsNullOrEmpty(host) || string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    throw new Exception("SFTP configuration is incomplete. Please check the configuration settings.");
                }
                
                System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Connection details - Host: {host}, Port: {port}, Username: {username}");
                
                string fileName = $"{DateTime.Now:yyyyMMddHHmmss}_{originalFileName}";
                string remotePath = "/upload/fakturiprovizija";
                string remoteFilePath = $"{remotePath}/{fileName}";
                
                System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Remote file path: {remoteFilePath}");
                
                using (var client = new SftpClient(host, port, username, password))
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine("[SFTP_DEBUG] Connecting to SFTP server...");
                        client.Connect();
                        System.Diagnostics.Debug.WriteLine("[SFTP_DEBUG] Connected successfully to SFTP server");
                        
                        // Create parent directory if it doesn't exist
                        string parentPath = "/upload";
                        if (!client.Exists(parentPath))
                        {
                            System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Parent directory doesn't exist, creating: {parentPath}");
                            client.CreateDirectory(parentPath);
                        }
                        
                        // Create target directory if it doesn't exist
                        if (!client.Exists(remotePath))
                        {
                            System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Directory doesn't exist, creating: {remotePath}");
                            client.CreateDirectory(remotePath);
                            System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Directory created successfully");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Directory already exists");
                            // List directory contents for debugging
                            var files = client.ListDirectory(remotePath);
                            System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Directory contents: {string.Join(", ", files.Select(f => f.Name))}");
                        }
                        
                        // Upload the file
                        System.Diagnostics.Debug.WriteLine("[SFTP_DEBUG] Starting file upload...");
                        using (var ms = new MemoryStream(pdfData))
                        {
                            try
                            {
                                client.BufferSize = 4 * 1024; // Use smaller buffer size (4KB)
                                client.UploadFile(ms, remoteFilePath, true); // true to overwrite if exists
                                System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] File uploaded successfully to {remoteFilePath}");
                                
                                // Verify the file exists after upload
                                if (client.Exists(remoteFilePath))
                                {
                                    var fileInfo = client.GetAttributes(remoteFilePath);
                                    System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Uploaded file size: {fileInfo.Size} bytes");
                                }
                                else
                                {
                                    throw new Exception("File was not found after upload");
                                }
                            }
                            catch (Exception uploadEx)
                            {
                                throw new Exception($"Error during file upload: {uploadEx.Message}", uploadEx);
                            }
                        }
                    }
                    finally
                    {
                        if (client.IsConnected)
                        {
                            client.Disconnect();
                            System.Diagnostics.Debug.WriteLine("[SFTP_DEBUG] Disconnected from SFTP server");
                        }
                    }
                }
                
                System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] SFTP upload completed successfully");
                return (remoteFilePath, fileName);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] ERROR: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Inner exception: {ex.InnerException.Message}");
                    System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Inner exception stack trace: {ex.InnerException.StackTrace}");
                }
                throw;
            }
        }

        // Method to update the database with file path and name
        private async Task UpdateInvoiceWithFileInfo(SqlConnection connection, string brojNaFaktura, string filePath, string fileName)
        {
            using (var updateCommand = new SqlCommand(@"
                UPDATE FakturiZaProvizijaKonOsiguritel 
                SET FilePath = @FilePath, FileName = @FileName
                WHERE BrojNaFaktura = @BrojNaFaktura", connection))
            {
                updateCommand.Parameters.AddWithValue("@FilePath", filePath);
                updateCommand.Parameters.AddWithValue("@FileName", fileName);
                updateCommand.Parameters.AddWithValue("@BrojNaFaktura", brojNaFaktura);
                
                await updateCommand.ExecuteNonQueryAsync();
            }
        }
        
        // Method to check if policies have existing invoices for the given period
        private async Task<bool> CheckExistingInvoicesForPeriod(SqlConnection connection, List<long> polisiIds, DateTime datumOd, DateTime datumDo)
        {
            if (polisiIds == null || !polisiIds.Any())
                return false;
                
            // Build the SQL with parameters for all IDs
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.Append(@"
                SELECT COUNT(*) 
                FROM PolisiInvoiceHistory 
                WHERE PolisiId IN (");
            
            for (int i = 0; i < polisiIds.Count; i++)
            {
                string paramName = $"@Id{i}";
                sqlBuilder.Append(i > 0 ? ", " : "").Append(paramName);
            }
            sqlBuilder.Append(@") 
                AND (
                    (@DatumOd >= DatumOd AND @DatumOd <= DatumDo) OR 
                    (@DatumDo >= DatumOd AND @DatumDo <= DatumDo) OR 
                    (@DatumOd <= DatumOd AND @DatumDo >= DatumDo)
                )");
            
            using (var command = new SqlCommand(sqlBuilder.ToString(), connection))
            {
                command.Parameters.AddWithValue("@DatumOd", datumOd);
                command.Parameters.AddWithValue("@DatumDo", datumDo);
                
                for (int i = 0; i < polisiIds.Count; i++)
                {
                    command.Parameters.AddWithValue($"@Id{i}", polisiIds[i]);
                }
                
                var count = await command.ExecuteScalarAsync();
                return Convert.ToInt32(count) > 0;
            }
        }

        // Method to get list of policy IDs that already have invoices for the given period
        private async Task<List<long>> GetPoliciesWithInvoicesForPeriod(SqlConnection connection, List<long> polisiIds, DateTime datumOd, DateTime datumDo)
        {
            if (polisiIds == null || !polisiIds.Any())
                return new List<long>();
                
            // Build the SQL with parameters for all IDs
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.Append(@"
                SELECT DISTINCT PolisiId 
                FROM PolisiInvoiceHistory 
                WHERE PolisiId IN (");
            
            for (int i = 0; i < polisiIds.Count; i++)
            {
                string paramName = $"@Id{i}";
                sqlBuilder.Append(i > 0 ? ", " : "").Append(paramName);
            }
            sqlBuilder.Append(@") 
                AND (
                    (@DatumOd >= DatumOd AND @DatumOd <= DatumDo) OR 
                    (@DatumDo >= DatumOd AND @DatumDo <= DatumDo) OR 
                    (@DatumOd <= DatumOd AND @DatumDo >= DatumDo)
                )");
            
            using (var command = new SqlCommand(sqlBuilder.ToString(), connection))
            {
                command.Parameters.AddWithValue("@DatumOd", datumOd);
                command.Parameters.AddWithValue("@DatumDo", datumDo);
                
                for (int i = 0; i < polisiIds.Count; i++)
                {
                    command.Parameters.AddWithValue($"@Id{i}", polisiIds[i]);
                }
                
                var excludeIds = new List<long>();
                using (var reader = await command.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        excludeIds.Add(reader.GetInt64(0));
                    }
                }
                
                return excludeIds;
            }
        }

        // Method to update Polisi records with the invoice number (kept for backward compatibility only)
        // NOTE: This method is kept for backward compatibility but the primary system now uses PolisiInvoiceHistory table
        private async Task UpdatePolisiWithInvoiceNumber(SqlConnection connection, string brojNaFaktura, List<long> polisiIds)
        {
            if (polisiIds == null || !polisiIds.Any())
                return;
                
            // Build the SQL with parameters for all IDs
            StringBuilder sqlBuilder = new StringBuilder();
          //  sqlBuilder.Append("UPDATE Polisi SET BrojNaFakturaZaProvizija = @BrojNaFaktura WHERE Id IN (");
            
            for (int i = 0; i < polisiIds.Count; i++)
            {
                string paramName = $"@Id{i}";
                sqlBuilder.Append(i > 0 ? ", " : "").Append(paramName);
            }
            sqlBuilder.Append(")");
            
            using (var command = new SqlCommand(sqlBuilder.ToString(), connection))
            {
                command.Parameters.AddWithValue("@BrojNaFaktura", brojNaFaktura);
                
                for (int i = 0; i < polisiIds.Count; i++)
                {
                    command.Parameters.AddWithValue($"@Id{i}", polisiIds[i]);
                }
                
                await command.ExecuteNonQueryAsync();
            }
        }

        // PRIMARY METHOD: Save policy-invoice relationships to PolisiInvoiceHistory table with period tracking
        // This is the main method for the new system that allows policies to be part of multiple invoices
        private async Task SavePolisiInvoiceHistory(SqlConnection connection, string brojNaFaktura, List<long> polisiIds, DateTime datumOd, DateTime datumDo, string username)
        {
            if (polisiIds == null || !polisiIds.Any())
                return;
                
            // Build the SQL to insert multiple records
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.Append(@"
                INSERT INTO PolisiInvoiceHistory (PolisiId, BrojNaFaktura, DatumOd, DatumDo, UsernameCreated)
                VALUES ");
            
            for (int i = 0; i < polisiIds.Count; i++)
            {
                if (i > 0) sqlBuilder.Append(", ");
                sqlBuilder.Append($"(@Id{i}, @BrojNaFaktura, @DatumOd, @DatumDo, @Username)");
            }
            
            using (var command = new SqlCommand(sqlBuilder.ToString(), connection))
            {
                command.Parameters.AddWithValue("@BrojNaFaktura", brojNaFaktura);
                command.Parameters.AddWithValue("@DatumOd", datumOd);
                command.Parameters.AddWithValue("@DatumDo", datumDo);
                command.Parameters.AddWithValue("@Username", username ?? "SYSTEM");
                
                for (int i = 0; i < polisiIds.Count; i++)
                {
                    command.Parameters.AddWithValue($"@Id{i}", polisiIds[i]);
                }
                
                await command.ExecuteNonQueryAsync();
            }
        }

        // API Endpoint to upload PDF to SFTP
        [HttpPost]
        [RequestSizeLimit(50 * 1024 * 1024)] // 50 MB limit
        [RequestFormLimits(MultipartBodyLengthLimit = 50 * 1024 * 1024)]
        public async Task<IActionResult> OnPostUploadPdfToSftpAsync([FromBody] UploadPdfRequest request)
        {
            var debugMessages = new List<(string Message, string Type)>();
            void AddDebug(string message, string type = "info") => debugMessages.Add((message, type));

            AddDebug($"Starting upload process", "info");
            
            try
            {
                // Validate request object
                if (request == null)
                {
                    AddDebug("Request object is null", "error");
                    return new JsonResult(new { success = false, message = "Invalid request", debugLog = debugMessages });
                }

                AddDebug($"Received request with invoice number: {request.BrojNaFaktura}", "info");
                AddDebug($"Filename: {request.FileName}", "info");
                
                // Validate required parameters
                if (string.IsNullOrWhiteSpace(request.BrojNaFaktura))
                {
                    AddDebug("Missing invoice number (brojNaFaktura)", "error");
                    return new JsonResult(new { success = false, message = "Invoice number is required", debugLog = debugMessages });
                }
                
                // Check if PDF data is empty
                if (string.IsNullOrWhiteSpace(request.PdfBase64))
                {
                    AddDebug("PDF data is empty or null", "error");
                    return new JsonResult(new { success = false, message = "PDF data is empty or invalid", debugLog = debugMessages });
                }
                
                // Log data length for debugging
                AddDebug($"Received base64 data length: {request.PdfBase64.Length} characters", "info");
                
                // Verify the data has a reasonable length
                if (request.PdfBase64.Length < 100)
                {
                    AddDebug("Base64 data is too short to be valid", "error");
                    return new JsonResult(new { success = false, message = "PDF data is too short to be valid", debugLog = debugMessages });
                }
                
                // Create a standardized filename
                string serverFileName = $"faktura_{request.BrojNaFaktura.Replace("/", "_")}_{DateTime.Now:yyyyMMddHHmmss}.pdf";
                AddDebug($"Generated server filename: {serverFileName}", "info");
                
                // Decode the base64 string to byte array
                byte[] pdfBytes;
                try
                {
                    string base64Data = request.PdfBase64;
                    // Strip data URL prefix if present
                    if (base64Data.StartsWith("data:"))
                    {
                        int commaIndex = base64Data.IndexOf(",");
                        if (commaIndex > 0)
                        {
                            base64Data = base64Data.Substring(commaIndex + 1);
                            AddDebug("Stripped data URL prefix from base64 string", "info");
                        }
                    }
                    
                    pdfBytes = Convert.FromBase64String(base64Data);
                    AddDebug($"Successfully decoded base64 data to {pdfBytes.Length} bytes", "success");
                }
                catch (Exception ex)
                {
                    AddDebug($"Failed to decode base64 data: {ex.Message}", "error");
                    return new JsonResult(new { success = false, message = $"Failed to decode PDF data: {ex.Message}", debugLog = debugMessages });
                }
                
                // Upload to SFTP
                try
                {
                    var sftpConfig = _configuration.GetSection("SftpConfig");
                    var host = sftpConfig["Host"];
                    var port = int.Parse(sftpConfig["Port"]);
                    var username = sftpConfig["Username"];
                    
                    AddDebug($"SFTP Configuration - Host: {host}, Port: {port}, Username: {username}", "info");
                    
                    if (string.IsNullOrEmpty(host) || string.IsNullOrEmpty(username))
                    {
                        AddDebug("SFTP configuration is incomplete", "error");
                        return new JsonResult(new { success = false, message = "SFTP configuration is incomplete", debugLog = debugMessages });
                    }

                    var (filePath, uploadedFileName) = await UploadPdfToSftp(pdfBytes, serverFileName);
                    AddDebug($"SFTP upload completed successfully to path: {filePath}", "success");
                    
                    // Update database with file info
                    using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                    {
                        AddDebug("Opening database connection to update file info", "info");
                        await connection.OpenAsync();
                        
                        try
                        {
                            await UpdateInvoiceWithFileInfo(connection, request.BrojNaFaktura, filePath, uploadedFileName);
                            AddDebug("Successfully updated database with file path and name", "success");
                        }
                        catch (Exception dbEx)
                        {
                            AddDebug($"Failed to update database: {dbEx.Message}", "error");
                            return new JsonResult(new { success = false, message = $"Failed to update database: {dbEx.Message}", debugLog = debugMessages });
                        }
                    }
                    
                    return new JsonResult(new { 
                        success = true, 
                        message = "PDF successfully uploaded to SFTP server and database updated",
                        filePath = filePath,
                        fileName = uploadedFileName,
                        debugLog = debugMessages
                    });
                }
                catch (Exception sftpEx)
                {
                    AddDebug($"SFTP upload error: {sftpEx.Message}", "error");
                    if (sftpEx.InnerException != null)
                    {
                        AddDebug($"Inner exception: {sftpEx.InnerException.Message}", "error");
                    }
                    return new JsonResult(new { success = false, message = $"Failed to upload PDF to SFTP: {sftpEx.Message}", debugLog = debugMessages });
                }
            }
            catch (Exception ex)
            {
                AddDebug($"Unhandled error: {ex.Message}", "error");
                if (ex.InnerException != null)
                {
                    AddDebug($"Inner exception: {ex.InnerException.Message}", "error");
                }
                return new JsonResult(new { success = false, message = $"Failed to upload PDF: {ex.Message}", debugLog = debugMessages });
            }
        }

        public class UploadPdfRequest
        {
            public string BrojNaFaktura { get; set; }
            public string FileName { get; set; }
            public string PdfBase64 { get; set; }
        }
        
        public class UpdatePolisiRequest
        {
            public string BrojNaFaktura { get; set; }
            public List<long> PolisiIds { get; set; }
        }
        
        [HttpPost]
        public async Task<IActionResult> OnPostUpdatePolisiAsync([FromBody] UpdatePolisiRequest request)
        {
            if (!await HasPageAccess("IzvestajProvizijaBrokerMesecen"))
            {
                return new JsonResult(new { success = false, message = "Access denied" });
            }

            var debugMessages = new List<(string Message, string Type)>();
            void AddDebug(string message, string type = "info") => debugMessages.Add((message, type));
            
            try
            {
                if (request == null || string.IsNullOrWhiteSpace(request.BrojNaFaktura) || request.PolisiIds == null || !request.PolisiIds.Any())
                {
                    AddDebug("Invalid request parameters", "error");
                    return new JsonResult(new { success = false, message = "Invalid request parameters", debugLog = debugMessages });
                }
                
                AddDebug($"Updating {request.PolisiIds.Count} polisi records with invoice number: {request.BrojNaFaktura}", "info");
                
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    await UpdatePolisiWithInvoiceNumber(connection, request.BrojNaFaktura, request.PolisiIds);
                    AddDebug("Successfully updated polisi records", "success");
                }
                
                return new JsonResult(new { 
                    success = true, 
                    message = $"Successfully updated {request.PolisiIds.Count} polisi records with invoice number: {request.BrojNaFaktura}",
                    debugLog = debugMessages
                });
            }
            catch (Exception ex)
            {
                AddDebug($"Error updating polisi records: {ex.Message}", "error");
                if (ex.InnerException != null)
                {
                    AddDebug($"Inner exception: {ex.InnerException.Message}", "error");
                }
                
                return new JsonResult(new { 
                    success = false, 
                    message = $"Failed to update polisi records: {ex.Message}", 
                    debugLog = debugMessages 
                });
            }
        }

        public class SavePolisiInvoiceHistoryRequest
        {
            public string BrojNaFaktura { get; set; }
            public List<long> PolisiIds { get; set; }
            public DateTime DatumOd { get; set; }
            public DateTime DatumDo { get; set; }
        }

        [HttpPost]
        public async Task<IActionResult> OnPostSavePolisiInvoiceHistoryAsync([FromBody] SavePolisiInvoiceHistoryRequest request)
        {
            if (!await HasPageAccess("IzvestajProvizijaBrokerMesecen"))
            {
                return new JsonResult(new { success = false, message = "Access denied" });
            }

            var debugMessages = new List<(string Message, string Type)>();
            void AddDebug(string message, string type = "info") => debugMessages.Add((message, type));
            
            try
            {
                if (request == null || string.IsNullOrWhiteSpace(request.BrojNaFaktura) || request.PolisiIds == null || !request.PolisiIds.Any())
                {
                    AddDebug("Invalid request parameters", "error");
                    return new JsonResult(new { success = false, message = "Invalid request parameters", debugLog = debugMessages });
                }
                
                AddDebug($"Saving {request.PolisiIds.Count} polisi records to invoice history with invoice number: {request.BrojNaFaktura}", "info");
                AddDebug($"Period: {request.DatumOd:yyyy-MM-dd} to {request.DatumDo:yyyy-MM-dd}", "info");
                
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    
                    var username = HttpContext.Session.GetString("Username");
                    await SavePolisiInvoiceHistory(connection, request.BrojNaFaktura, request.PolisiIds, request.DatumOd, request.DatumDo, username);
                    AddDebug("Successfully saved polisi records to invoice history", "success");
                }
                
                return new JsonResult(new { 
                    success = true, 
                    message = $"Successfully saved {request.PolisiIds.Count} polisi records to invoice history with invoice number: {request.BrojNaFaktura}",
                    debugLog = debugMessages
                });
            }
            catch (Exception ex)
            {
                AddDebug($"Error saving polisi records to history: {ex.Message}", "error");
                if (ex.InnerException != null)
                {
                    AddDebug($"Inner exception: {ex.InnerException.Message}", "error");
                }
                
                return new JsonResult(new { 
                    success = false, 
                    message = $"Failed to save polisi records to history: {ex.Message}", 
                    debugLog = debugMessages 
                });
            }
        }
    }

    public class SaveInvoiceRequestVlezniFakturiKonKlient
    {
        public string BrojNaFaktura { get; set; }
        public string FakturaDo { get; set; }
        public DateTime DatumNaFaktura { get; set; }
        public DateTime RokNaPlakanje { get; set; }
        public decimal Iznos { get; set; }
        public DateTime DatumOd { get; set; }
        public DateTime DatumDo { get; set; }
    }
}
