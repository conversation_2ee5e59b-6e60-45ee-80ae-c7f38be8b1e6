@page
@model NextBroker.Pages.LoginEnModel
@{
    ViewData["Title"] = "Login";
}

<link rel="stylesheet" href="~/css/login.css" asp-append-version="true" />

<div class="login-container">
    <h2>@ViewData["Title"]</h2>

    <form method="post">
        <div class="form-group">
            <label asp-for="Username">Username</label>
            <input asp-for="Username" class="form-control" />
            <span asp-validation-for="Username" class="text-danger"></span>
        </div>
        <div class="form-group">
            <label asp-for="Password">Password</label>
            <input asp-for="Password" type="password" class="form-control" />
            <span asp-validation-for="Password" class="text-danger"></span>
        </div>

        <button type="submit" class="btn btn-primary">Login</button>
        
        @if (!string.IsNullOrEmpty(Model.Message))
        {
            <p class="message">@Model.Message</p>
        }

        <div class="links">
            <p><a asp-page="/ResetPassword/ResetRequestEn">Forgot Password?</a></p>
            <p><a asp-page="/RegistrationEn">New User?</a></p>
        </div>
    </form>
</div> 