@page
@model ResetRequestEnModel
@{
    ViewData["Title"] = "Reset Password";
}

<link rel="stylesheet" href="~/css/login.css" asp-append-version="true" />

<div class="login-container">
    <h2>@ViewData["Title"]</h2>

    <form method="post">
        <div class="form-group">
            <label for="emb">ID Number</label>
            <input type="text" id="emb" name="EMB" class="form-control" 
                   pattern="^\d+$" title="Enter a valid ID number."
                   required />
            @if (!string.IsNullOrEmpty(Model.ErrorMessage))
            {
                <span class="text-danger">@Model.ErrorMessage</span>
            }
        </div>

        <button type="submit" class="btn btn-primary">Reset Password</button>

        <br />

        @if (Model.ErrorMessage != null)
        {
            <div class="alert alert-danger">@Model.ErrorMessage</div>
        }
        @if (Model.SuccessMessage != null)
        {
            <div class="alert alert-success">@Model.SuccessMessage</div>
        }

        <div class="links">
            <p><a asp-page="/LoginEn">Back to Login</a></p>
        </div>
    </form>
</div> 