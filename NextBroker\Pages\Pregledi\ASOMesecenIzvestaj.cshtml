@page
@model NextBroker.Pages.Pregledi.ASOMesecenIzvestajModel
@{
    ViewData["Title"] = "АСО Месечен Извештај";
}

<div class="container-fluid">
    <h2>@ViewData["Title"]</h2>

    <form method="post" class="mb-4">
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="SelectedMonth">Месец</label>
                    <select asp-for="SelectedMonth" class="form-control" asp-items="@(new SelectList(Model.Months, "Value", "Name"))">
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="Year">Година</label>
                    <input asp-for="Year" class="form-control" type="number" />
                </div>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                @if (Model.ReportData == null)
                {
                    <button type="submit" class="btn btn-primary me-2">Генерирај извештај</button>
                }                
                
                @if (Model.ReportData != null && Model.ReportData.Rows.Count > 0)
                {
                    <button type="submit" asp-page-handler="ExportExcel" class="btn btn-success me-2">
                        <i class="fas fa-file-excel me-1"></i> Export Excel
                    </button>
                    
                    @if (!Model.IsArchived)
                    {
                        <button type="submit" asp-page-handler="Archive" class="btn btn-warning me-2">
                            <i class="fas fa-archive me-1"></i> Потврди и архивирај извештај
                        </button>
                    }
                }
                <a asp-page="./ASOMesecenIzvestajArhiva" class="btn btn-info">
                    <i class="fas fa-history me-1"></i> Архива на извештаи
                </a>
            </div>
        </div>
    </form>

    @if (!string.IsNullOrEmpty(Model.ReportExistsMessage))
    {
        <div class="alert alert-info mt-3">
            <i class="fas fa-info-circle me-2"></i> @Model.ReportExistsMessage
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ArchiveMessage))
    {
        <div class="alert @(Model.ArchiveSuccess ? "alert-success" : "alert-danger") mt-3">
            @Model.ArchiveMessage
        </div>
    }

    @if (Model.ReportData != null && Model.ReportData.Rows.Count > 0)
    {
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        @foreach (System.Data.DataColumn column in Model.ReportData.Columns)
                        {
                            <th>@column.ColumnName</th>
                        }
                    </tr>
                </thead>
                <tbody>
                    @foreach (System.Data.DataRow row in Model.ReportData.Rows)
                    {
                        <tr>
                            @foreach (var item in row.ItemArray)
                            {
                                <td>
                                    @if (item is DateTime date)
                                    {
                                        @date.ToString("dd/MM/yyyy")
                                    }
                                    else if (item is decimal decimalValue)
                                    {
                                        @decimalValue.ToString("N2")
                                    }
                                    else if (item is double doubleValue)
                                    {
                                        @doubleValue.ToString("N2")
                                    }
                                    else if (item is float floatValue)
                                    {
                                        @floatValue.ToString("N2")
                                    }
                                    else
                                    {
                                        @item
                                    }
                                </td>
                            }
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
    else if (Model.ReportData != null)
    {
        <div class="alert alert-info">
            Нема податоци за избраниот период.
        </div>
    }
</div>
