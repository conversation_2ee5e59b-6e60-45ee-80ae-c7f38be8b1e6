using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using Microsoft.Data.SqlClient;
using System.Collections.Generic;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.IO;
using System.Drawing;
using System;

namespace NextBroker.Pages.Finansii
{
    public class PrenosNaNaplatenaPremijaViewEditSpecifikacijaModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public PrenosNaNaplatenaPremijaViewEditSpecifikacijaModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
            Polisi = new List<PolisaViewModel>();
            StavkiIzvod = new List<StavkaIzvodViewModel>();
        }

        [BindProperty(SupportsGet = true)]
        public long? Id { get; set; }

        public SpecifikacijaViewModel Specifikacija { get; set; }
        public List<PolisaViewModel> <PERSON>isi { get; set; }
        public List<StavkaIzvodViewModel> StavkiIzvod { get; set; }

        public class SpecifikacijaViewModel
        {
            public long Id { get; set; }
            public DateTime DateCreated { get; set; }
            public string UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string UsernameModified { get; set; }
            public string Naziv { get; set; }
            public string BrojNaFaktura { get; set; }
            public string TipNaFaktura { get; set; }
            public DateTime? DatumNaVleznaFaktura { get; set; }
            public DateTime? RokNaPlakjanjeFakturaVlezna { get; set; }
            public decimal? IznosNaFaktura { get; set; }
            public decimal? PlatenoOdDogovoruvac { get; set; }
            public decimal? PrenesenoKonOsiguritel { get; set; }
            public decimal? Dolzi { get; set; }
            public string Banka { get; set; }
            public string BrojNaIzvod { get; set; }
            public DateTime? DatumNaIzvod { get; set; }
            public long? StavkaVoIzvod { get; set; }
            public decimal? IznosNaFakturaVoRok { get; set; }
        }

        public class PolisaViewModel
        {
            public long Id { get; set; }
            public string BrojNaPolisa { get; set; }
            public decimal? VkupnaPremijaZaPlakanje { get; set; }
            public decimal? PlatenoOdDogovoruvac { get; set; }
            public decimal? IznosZaPlakanjeVoRok { get; set; }
            public decimal? DolziDogovoruvac { get; set; }
            public decimal? PlatenoPoFaktura { get; set; }
            public string KlasaIme { get; set; }
            public string Ime { get; set; }
            public decimal? IznosOdobruvanjeZadolzuvanje { get; set; }
            public string BrojNaDokumentOdobruvanjeZadolzuvanje { get; set; }
        }

        public class StavkaIzvodViewModel
        {
            public string Banka { get; set; }
            public string BrojNaIzvod { get; set; }
            public DateTime? DatumNaIzvod { get; set; }
            public long StavkaPremijaId { get; set; }
            public decimal Iznos { get; set; }
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("PrenosNaNaplatenaPremijaViewEditSpecifikacija"))
            {
                return RedirectToAccessDenied();
            }

            if (!Id.HasValue)
            {
                TempData["ErrorMessage"] = "Не е избрана спецификација за преглед/измена.";
                return RedirectToPage("./PrenosNaNaplatenaPremijaListaSpecifikacii");
            }

            await LoadSpecifikacija();
            
            if (Specifikacija == null)
            {
                TempData["ErrorMessage"] = "Спецификацијата не е пронајдена.";
                return RedirectToPage("./PrenosNaNaplatenaPremijaListaSpecifikacii");
            }

            return Page();
        }

        private async Task LoadSpecifikacija()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    select
                        pnns.Id,
                        pnns.DateCreated,
                        pnns.UsernameCreated,
                        pnns.DateModified,
                        pnns.UsernameModified,
                        klnt.Naziv,
                        pnns.BrojNaFaktura,
                        dbo.VratiTipNaFakturaPoBrojNaFaktura(pnns.BrojNaFaktura) as TipNaFaktura,
                        pnns.DatumNaVleznaFaktura,
                        pnns.RokNaPlakjanjeFakturaVlezna,
                        pnns.IznosNaFaktura + dbo.VratiOZZbirIznosOZSpecifikacijaPoPolisi(pnns.BrojNaFaktura) + dbo.VraziOZIznosVleznaFaktura(pnns.BrojNaFaktura) as IznosNaFaktura,
                        dbo.VratiPlatenoOdDogovoruvacPoFakturaPenosNaNaplatenaPremija (pnns.BrojNaFaktura, pnns.KlientiIdOsiguritel) as PlatenoOdDogovoruvac,
                        pnns.IznosNaFaktura - dbo.VratiPlatenoOdDogovoruvacPoFakturaPenosNaNaplatenaPremija (
                            pnns.BrojNaFaktura, 
                            pnns.KlientiIdOsiguritel
                        ) AS Dolzi,
                        dbo.VratiZbirNaIznosStavkiPovrzaniSoSpecifikacijaPrenosNaNaplata(pnns.Id) as [PrenesenoKonOsiguritel],
                        dbo.VratiImeNaBankaPoStavkaOdIzvod(pnns.StavkaPremijaId) as Banka,
                        dbo.VratiBrojNaIzvodPoStavka(pnns.StavkaPremijaId) as BrojNaIzvod,
                        dbo.VratiDatumNaIzvodPoStavka(pnns.StavkaPremijaId) as DatumNaIzvod,
                        pnns.StavkaPremijaId as StavkaVoIzvod,
                        pnns.IznosNaFakturaVoRok + dbo.VratiOZZbirIznosOZSpecifikacijaPoPolisi(pnns.BrojNaFaktura) + dbo.VraziOZIznosVleznaFaktura(pnns.BrojNaFaktura) as IznosNaFakturaVoRok
                    from PrenosNaNaplataSpecifikacii pnns
                    left join Klienti klnt on pnns.KlientiIdOsiguritel = klnt.Id
                    where pnns.Id = @Id", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", Id.Value);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    if (await reader.ReadAsync())
                    {
                        Specifikacija = new SpecifikacijaViewModel
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            DateCreated = reader.GetDateTime(reader.GetOrdinal("DateCreated")),
                            UsernameCreated = reader.GetString(reader.GetOrdinal("UsernameCreated")),
                            DateModified = reader.IsDBNull(reader.GetOrdinal("DateModified")) ? null : reader.GetDateTime(reader.GetOrdinal("DateModified")),
                            UsernameModified = reader.IsDBNull(reader.GetOrdinal("UsernameModified")) ? null : reader.GetString(reader.GetOrdinal("UsernameModified")),
                            Naziv = reader.IsDBNull(reader.GetOrdinal("Naziv")) ? null : reader.GetString(reader.GetOrdinal("Naziv")),
                            BrojNaFaktura = reader.IsDBNull(reader.GetOrdinal("BrojNaFaktura")) ? null : reader.GetString(reader.GetOrdinal("BrojNaFaktura")),
                            TipNaFaktura = reader.IsDBNull(reader.GetOrdinal("TipNaFaktura")) ? null : reader.GetString(reader.GetOrdinal("TipNaFaktura")),
                            DatumNaVleznaFaktura = reader.IsDBNull(reader.GetOrdinal("DatumNaVleznaFaktura")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumNaVleznaFaktura")),
                            RokNaPlakjanjeFakturaVlezna = reader.IsDBNull(reader.GetOrdinal("RokNaPlakjanjeFakturaVlezna")) ? null : reader.GetDateTime(reader.GetOrdinal("RokNaPlakjanjeFakturaVlezna")),
                            IznosNaFaktura = reader.IsDBNull(reader.GetOrdinal("IznosNaFaktura")) ? null : reader.GetDecimal(reader.GetOrdinal("IznosNaFaktura")),
                            PlatenoOdDogovoruvac = reader.IsDBNull(reader.GetOrdinal("PlatenoOdDogovoruvac")) ? null : reader.GetDecimal(reader.GetOrdinal("PlatenoOdDogovoruvac")),
                            PrenesenoKonOsiguritel = reader.IsDBNull(reader.GetOrdinal("PrenesenoKonOsiguritel")) ? null : reader.GetDecimal(reader.GetOrdinal("PrenesenoKonOsiguritel")),
                            Dolzi = reader.IsDBNull(reader.GetOrdinal("Dolzi")) ? null : reader.GetDecimal(reader.GetOrdinal("Dolzi")),
                            Banka = reader.IsDBNull(reader.GetOrdinal("Banka")) ? null : reader.GetString(reader.GetOrdinal("Banka")),
                            BrojNaIzvod = reader.IsDBNull(reader.GetOrdinal("BrojNaIzvod")) ? null : reader.GetString(reader.GetOrdinal("BrojNaIzvod")),
                            DatumNaIzvod = reader.IsDBNull(reader.GetOrdinal("DatumNaIzvod")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumNaIzvod")),
                            StavkaVoIzvod = reader.IsDBNull(reader.GetOrdinal("StavkaVoIzvod")) ? null : reader.GetInt64(reader.GetOrdinal("StavkaVoIzvod")),
                            IznosNaFakturaVoRok = reader.IsDBNull(reader.GetOrdinal("IznosNaFakturaVoRok")) ? null : reader.GetDecimal(reader.GetOrdinal("IznosNaFakturaVoRok"))
                        };
                    }
                }

                // Load Polisi data
                using (SqlCommand cmdPolisi = new SqlCommand(@"
                    select
                        pol.Id,
                        pol.BrojNaPolisa,
                        dbo.VratiPolisaVkupnaPremijaZaPlakanjePoPolisaId(pol.id) + dbo.VraziOZIznosPolisa(pol.id) as [VkupnaPremijaZaPlakanje],
                        dbo.VratiPolisaUplatenIznos(pol.Id) as [PlatenoOdDogovoruvac],
                        dbo.VratiPolisaIznosZaPlakanjeVoRokOdFakturaIzlezna(pol.ID) + dbo.VraziOZIznosPolisa(pol.id) as [IznosZaPlakanjeVoRok],
                        dbo.VraziOZIznosPolisa(pol.id) as [IznosOdobruvanjeZadolzuvanje],
                        dbo.VraziOZBrojOZDokumentPolisa(pol.id) as [BrojNaDokumentOdobruvanjeZadolzuvanje],
                        CASE 
                        WHEN dbo.VratiPolisaPosledenDatumNaUplata(pol.id) <= pol.RokNaPlakjanjeFakturaIzlezna 
                        THEN (dbo.VratiPolisaIznosZaPlakanjeVoRokOdFakturaIzlezna(pol.id) + dbo.VraziOZIznosPolisa(pol.id)) - dbo.VratiPolisaUplatenIznos(pol.Id)
                        ELSE (dbo.VratiPolisaIznosZaPlakanjePoPolisaId(pol.id) + dbo.VraziOZIznosPolisa(pol.id)) - dbo.VratiPolisaUplatenIznos(pol.Id)
                        END AS [DolziDogovoruvac],                        
                        dbo.VratiPolisaPrenesenaPremijaKonOsiguritel(pol.Id) as [PlatenoPoFaktura],
                        kls.KlasaIme,
                        prod.Ime
                    from Polisi pol 
                    left join KlasiOsiguruvanje kls on pol.KlasiOsiguruvanjeIdKlasa = kls.Id
                    left join Produkti prod on pol.ProduktiIdProizvod = prod.Id
                    left join OdobruvanjeZadolzuvanje oz on pol.id = oz.PolisaId
                    where BrojNaFakturaVlezna = (
                        select BrojNaFaktura 
                        from dbo.PrenosNaNaplataSpecifikacii 
                        where id = @Id
                    )
                    and (oz.Storno = 0 or oz.Storno is null)", connection)) 
                {
                    cmdPolisi.Parameters.AddWithValue("@Id", Id.Value);

                    using SqlDataReader readerPolisi = await cmdPolisi.ExecuteReaderAsync();
                    while (await readerPolisi.ReadAsync())
                    {
                        Polisi.Add(new PolisaViewModel
                        {
                            Id = readerPolisi.GetInt64(readerPolisi.GetOrdinal("Id")),
                            BrojNaPolisa = readerPolisi.IsDBNull(readerPolisi.GetOrdinal("BrojNaPolisa")) ? null : readerPolisi.GetString(readerPolisi.GetOrdinal("BrojNaPolisa")),
                            VkupnaPremijaZaPlakanje = readerPolisi.IsDBNull(readerPolisi.GetOrdinal("VkupnaPremijaZaPlakanje")) ? null : readerPolisi.GetDecimal(readerPolisi.GetOrdinal("VkupnaPremijaZaPlakanje")),
                            PlatenoOdDogovoruvac = readerPolisi.IsDBNull(readerPolisi.GetOrdinal("PlatenoOdDogovoruvac")) ? null : readerPolisi.GetDecimal(readerPolisi.GetOrdinal("PlatenoOdDogovoruvac")),
                            IznosZaPlakanjeVoRok = readerPolisi.IsDBNull(readerPolisi.GetOrdinal("IznosZaPlakanjeVoRok")) ? null : readerPolisi.GetDecimal(readerPolisi.GetOrdinal("IznosZaPlakanjeVoRok")),
                            IznosOdobruvanjeZadolzuvanje = readerPolisi.IsDBNull(readerPolisi.GetOrdinal("IznosOdobruvanjeZadolzuvanje")) ? null : readerPolisi.GetDecimal(readerPolisi.GetOrdinal("IznosOdobruvanjeZadolzuvanje")),
                            BrojNaDokumentOdobruvanjeZadolzuvanje = readerPolisi.IsDBNull(readerPolisi.GetOrdinal("BrojNaDokumentOdobruvanjeZadolzuvanje")) ? null : readerPolisi.GetString(readerPolisi.GetOrdinal("BrojNaDokumentOdobruvanjeZadolzuvanje")),
                            DolziDogovoruvac = readerPolisi.IsDBNull(readerPolisi.GetOrdinal("DolziDogovoruvac")) ? null : readerPolisi.GetDecimal(readerPolisi.GetOrdinal("DolziDogovoruvac")),
                            PlatenoPoFaktura = readerPolisi.IsDBNull(readerPolisi.GetOrdinal("PlatenoPoFaktura")) ? null : readerPolisi.GetDecimal(readerPolisi.GetOrdinal("PlatenoPoFaktura")),
                            KlasaIme = readerPolisi.IsDBNull(readerPolisi.GetOrdinal("KlasaIme")) ? null : readerPolisi.GetString(readerPolisi.GetOrdinal("KlasaIme")),
                            Ime = readerPolisi.IsDBNull(readerPolisi.GetOrdinal("Ime")) ? null : readerPolisi.GetString(readerPolisi.GetOrdinal("Ime"))
                        });
                    }
                }

                // Load StavkiIzvod data
                using (SqlCommand cmdStavki = new SqlCommand(@"
                    select
                        dbo.VratiImeNaBankaPoStavkaOdIzvod(StavkaPremijaid) as Banka,
                        dbo.VratiBrojNaIzvodPoStavka(StavkaPremijaid) as BrojNaIzvod,
                        dbo.VratiDatumNaIzvodPoStavka(StavkaPremijaid) as DatumNaIzvod,
                        spec.StavkaPremijaId,
                        stav.Iznos
                    from PrenosNaNaplataSpecifikaciiPovrzaniStavki spec
                    left join StavkaPremija stav on spec.StavkaPremijaId = stav.Id
                    where spec.PrenosNaNaplataSpecifikaciiId = @Id", connection))
                {
                    cmdStavki.Parameters.AddWithValue("@Id", Id.Value);

                    using SqlDataReader readerStavki = await cmdStavki.ExecuteReaderAsync();
                    while (await readerStavki.ReadAsync())
                    {
                        StavkiIzvod.Add(new StavkaIzvodViewModel
                        {
                            Banka = readerStavki.IsDBNull(readerStavki.GetOrdinal("Banka")) ? null : readerStavki.GetString(readerStavki.GetOrdinal("Banka")),
                            BrojNaIzvod = readerStavki.IsDBNull(readerStavki.GetOrdinal("BrojNaIzvod")) ? null : readerStavki.GetString(readerStavki.GetOrdinal("BrojNaIzvod")),
                            DatumNaIzvod = readerStavki.IsDBNull(readerStavki.GetOrdinal("DatumNaIzvod")) ? null : readerStavki.GetDateTime(readerStavki.GetOrdinal("DatumNaIzvod")),
                            StavkaPremijaId = readerStavki.GetInt64(readerStavki.GetOrdinal("StavkaPremijaId")),
                            Iznos = readerStavki.IsDBNull(readerStavki.GetOrdinal("Iznos")) ? 0 : readerStavki.GetDecimal(readerStavki.GetOrdinal("Iznos"))
                        });
                    }
                }
            }
        }

        public async Task<IActionResult> OnPostPovrziStavkaAsync(long specifikacijaId, long stavkaVoIzvod, string brojNaIzvod)
        {
            if (!await HasPageAccess("PrenosNaNaplatenaPremijaViewEditSpecifikacija"))
            {
                return RedirectToAccessDenied();
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand("PrenosNaNaplataSpecifikaciiPovrziSpecifikacijaSoStavka", connection))
                {
                    cmd.CommandType = System.Data.CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@PrenosNaNaplataSpecifikaciiId", specifikacijaId);
                    cmd.Parameters.AddWithValue("@StavkaPremijaId", stavkaVoIzvod);

                    await cmd.ExecuteNonQueryAsync();
                }
            }

            // Redirect back to the same page to show the updated data
            return RedirectToPage(new { Id = specifikacijaId });
        }

        public async Task<IActionResult> OnPostOdvrziStavkaAsync(long stavkaPremijaId, long specifikacijaId)
        {
            if (!await HasPageAccess("PrenosNaNaplatenaPremijaViewEditSpecifikacija"))
            {
                return RedirectToAccessDenied();
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand("PrenosNaNaplataSpecifikaciiPovrziSpecifikacijaSoStavkaReverse", connection))
                {
                    cmd.CommandType = System.Data.CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@StavkaPremijaId", stavkaPremijaId);

                    await cmd.ExecuteNonQueryAsync();
                }
            }

            // Set the Id for reloading data
            this.Id = specifikacijaId;
            
            // Reload the data
            await LoadSpecifikacija();

            // Return to the same page
            return Page();
        }

        public async Task<IActionResult> OnGetSearchIzvodAsync(string searchTerm)
        {
            if (!await HasPageAccess("PrenosNaNaplatenaPremijaViewEditSpecifikacija"))
            {
                return new JsonResult(new { error = "Access denied" });
            }

            var results = new List<object>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(
                    "select Id, BrojNaIzvod, DatumNaIzvod from IzvodPremija where BrojNaIzvod like @searchTerm + '%'",
                    connection))
                {
                    cmd.Parameters.AddWithValue("@searchTerm", searchTerm);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader.GetInt64(0),
                            brojNaIzvod = reader.GetString(1),
                            datumNaIzvod = reader.GetDateTime(2).ToString("dd.MM.yyyy")
                        });
                    }
                }
            }

            return new JsonResult(results);
        }

        public async Task<IActionResult> OnGetSearchStavkeAsync(long izvodId)
        {
            if (!await HasPageAccess("PrenosNaNaplatenaPremijaViewEditSpecifikacija"))
            {
                return new JsonResult(new { error = "Access denied" });
            }

            var results = new List<object>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(
                    "select Id, Iznos, CelNaDoznaka from stavkapremija where izvodpremijaid = @izvodpremijaid and (storno = 0 or storno is null) and odliv = 1",
                    connection))
                {
                    cmd.Parameters.AddWithValue("@izvodpremijaid", izvodId);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader.GetInt64(0),
                            iznos = reader.GetDecimal(1),
                            celNaDoznaka = reader.IsDBNull(2) ? "" : reader.GetString(2)
                        });
                    }
                }
            }

            return new JsonResult(results);
        }

        public async Task<IActionResult> OnGetExportToExcelAsync(long id)
        {
            if (!await HasPageAccess("PrenosNaNaplatenaPremijaViewEditSpecifikacija"))
            {
                return RedirectToAccessDenied();
            }

            Id = id;
            await LoadSpecifikacija();
            
            if (Specifikacija == null)
            {
                TempData["ErrorMessage"] = "Спецификацијата не е пронајдена.";
                return RedirectToPage("./PrenosNaNaplatenaPremijaListaSpecifikacii");
            }

            // Set the license context
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using (var package = new ExcelPackage())
            {
                // Create the worksheet
                var worksheet = package.Workbook.Worksheets.Add("Спецификација");
                
                // Add title
                worksheet.Cells[1, 1].Value = "Пренос на наплатена премија - Спецификација";
                worksheet.Cells[1, 1, 1, 6].Merge = true;
                worksheet.Cells[1, 1, 1, 6].Style.Font.Bold = true;
                worksheet.Cells[1, 1, 1, 6].Style.Font.Size = 14;
                worksheet.Cells[1, 1, 1, 6].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                worksheet.Cells[1, 1, 1, 6].Style.Fill.PatternType = ExcelFillStyle.Solid;
                worksheet.Cells[1, 1, 1, 6].Style.Fill.BackgroundColor.SetColor(Color.LightBlue);

                // Add TipNaFaktura section
                worksheet.Cells[2, 1].Value = "Тип на фактура:";
                worksheet.Cells[2, 2].Value = Specifikacija.TipNaFaktura;
                worksheet.Cells[2, 1].Style.Font.Bold = true;
                worksheet.Cells[2, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                
                // Add Basic Information section (now starting at row 4 instead of 3)
                worksheet.Cells[4, 1].Value = "Основни информации";
                worksheet.Cells[4, 1, 4, 6].Merge = true;
                worksheet.Cells[4, 1, 4, 6].Style.Font.Bold = true;
                worksheet.Cells[4, 1, 4, 6].Style.Fill.PatternType = ExcelFillStyle.Solid;
                worksheet.Cells[4, 1, 4, 6].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                
                // Row 5-10: Basic Information (shifted down by 1)
                worksheet.Cells[5, 1].Value = "ID:";
                worksheet.Cells[5, 2].Value = Specifikacija.Id;
                
                worksheet.Cells[6, 1].Value = "Креирано на:";
                worksheet.Cells[6, 2].Value = Specifikacija.DateCreated.ToString("dd.MM.yyyy");
                
                worksheet.Cells[7, 1].Value = "Креирал:";
                worksheet.Cells[7, 2].Value = Specifikacija.UsernameCreated;
                
                if (Specifikacija.DateModified.HasValue)
                {
                    worksheet.Cells[8, 1].Value = "Променето на:";
                    worksheet.Cells[8, 2].Value = Specifikacija.DateModified?.ToString("dd.MM.yyyy");
                    
                    worksheet.Cells[9, 1].Value = "Променил:";
                    worksheet.Cells[9, 2].Value = Specifikacija.UsernameModified;
                }
                
                int currentRow = Specifikacija.DateModified.HasValue ? 10 : 8;
                
                worksheet.Cells[currentRow, 1].Value = "Назив:";
                worksheet.Cells[currentRow, 2].Value = Specifikacija.Naziv;
                
                // Style cells
                for (int i = 5; i <= currentRow; i++)
                {
                    worksheet.Cells[i, 1].Style.Font.Bold = true;
                    worksheet.Cells[i, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                }
                
                currentRow += 2;
                
                // Add Financial Information section
                worksheet.Cells[currentRow, 1].Value = "Финансиски детали";
                worksheet.Cells[currentRow, 1, currentRow, 6].Merge = true;
                worksheet.Cells[currentRow, 1, currentRow, 6].Style.Font.Bold = true;
                worksheet.Cells[currentRow, 1, currentRow, 6].Style.Fill.PatternType = ExcelFillStyle.Solid;
                worksheet.Cells[currentRow, 1, currentRow, 6].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                
                currentRow++;
                
                // Financial details
                worksheet.Cells[currentRow, 1].Value = "Број на фактура:";
                worksheet.Cells[currentRow, 2].Value = Specifikacija.BrojNaFaktura;
                currentRow++;
                
                worksheet.Cells[currentRow, 1].Value = "Датум на фактура:";
                worksheet.Cells[currentRow, 2].Value = Specifikacija.DatumNaVleznaFaktura?.ToString("dd.MM.yyyy");
                currentRow++;
                
                worksheet.Cells[currentRow, 1].Value = "Рок за плаќање:";
                worksheet.Cells[currentRow, 2].Value = Specifikacija.RokNaPlakjanjeFakturaVlezna?.ToString("dd.MM.yyyy");
                currentRow++;
                
                worksheet.Cells[currentRow, 1].Value = "Износ на фактура:";
                worksheet.Cells[currentRow, 2].Value = Specifikacija.IznosNaFaktura;
                worksheet.Cells[currentRow, 2].Style.Numberformat.Format = "#,##0.00";
                currentRow++;
                
                worksheet.Cells[currentRow, 1].Value = "Платено од договорувач:";
                worksheet.Cells[currentRow, 2].Value = Specifikacija.PlatenoOdDogovoruvac;
                if (Specifikacija.PlatenoOdDogovoruvac.HasValue)
                {
                    worksheet.Cells[currentRow, 2].Style.Numberformat.Format = "#,##0.00";
                }
                
                currentRow++;
                worksheet.Cells[currentRow, 1].Value = "Пренесено кон осигурител:";
                worksheet.Cells[currentRow, 2].Value = Specifikacija.PrenesenoKonOsiguritel;
                if (Specifikacija.PrenesenoKonOsiguritel.HasValue)
                {
                    worksheet.Cells[currentRow, 2].Style.Numberformat.Format = "#,##0.00";
                }

                currentRow++;
                worksheet.Cells[currentRow, 1].Value = "Износ на фактура во рок:";
                worksheet.Cells[currentRow, 2].Value = Specifikacija.IznosNaFakturaVoRok;
                if (Specifikacija.IznosNaFakturaVoRok.HasValue)
                {
                    worksheet.Cells[currentRow, 2].Style.Numberformat.Format = "#,##0.00";
                }
                
                // Style cells
                int startFinancialRow = currentRow - 6;
                for (int i = startFinancialRow; i <= currentRow; i++)
                {
                    worksheet.Cells[i, 1].Style.Font.Bold = true;
                    worksheet.Cells[i, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                }
                
                currentRow += 2;
                
                // Add Invoice Information section
                worksheet.Cells[currentRow, 1].Value = "Информации за извод";
                worksheet.Cells[currentRow, 1, currentRow, 6].Merge = true;
                worksheet.Cells[currentRow, 1, currentRow, 6].Style.Font.Bold = true;
                worksheet.Cells[currentRow, 1, currentRow, 6].Style.Fill.PatternType = ExcelFillStyle.Solid;
                worksheet.Cells[currentRow, 1, currentRow, 6].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                
                currentRow++;
                
                // Invoice information headers
                worksheet.Cells[currentRow, 1].Value = "Банка";
                worksheet.Cells[currentRow, 2].Value = "Број на извод";
                worksheet.Cells[currentRow, 3].Value = "Датум на извод";
                worksheet.Cells[currentRow, 4].Value = "Ставка во извод";
                worksheet.Cells[currentRow, 5].Value = "Износ";

                // Style header row
                worksheet.Cells[currentRow, 1, currentRow, 5].Style.Font.Bold = true;
                worksheet.Cells[currentRow, 1, currentRow, 5].Style.Fill.PatternType = ExcelFillStyle.Solid;
                worksheet.Cells[currentRow, 1, currentRow, 5].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                worksheet.Cells[currentRow, 1, currentRow, 5].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                worksheet.Cells[currentRow, 1, currentRow, 5].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                worksheet.Cells[currentRow, 1, currentRow, 5].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                worksheet.Cells[currentRow, 1, currentRow, 5].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                currentRow++;

                // Invoice information data
                foreach (var stavka in StavkiIzvod)
                {
                    worksheet.Cells[currentRow, 1].Value = stavka.Banka;
                    worksheet.Cells[currentRow, 2].Value = stavka.BrojNaIzvod;
                    worksheet.Cells[currentRow, 3].Value = stavka.DatumNaIzvod?.ToString("dd.MM.yyyy");
                    worksheet.Cells[currentRow, 4].Value = stavka.StavkaPremijaId;
                    worksheet.Cells[currentRow, 5].Value = stavka.Iznos;
                    worksheet.Cells[currentRow, 5].Style.Numberformat.Format = "#,##0.00";

                    // Style the row
                    worksheet.Cells[currentRow, 1, currentRow, 5].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    worksheet.Cells[currentRow, 1, currentRow, 5].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    worksheet.Cells[currentRow, 1, currentRow, 5].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    worksheet.Cells[currentRow, 1, currentRow, 5].Style.Border.Right.Style = ExcelBorderStyle.Thin;

                    currentRow++;
                }

                currentRow += 2;
                
                // Add Policies section
                worksheet.Cells[currentRow, 1].Value = "Полиси";
                worksheet.Cells[currentRow, 1, currentRow, 6].Merge = true;
                worksheet.Cells[currentRow, 1, currentRow, 6].Style.Font.Bold = true;
                worksheet.Cells[currentRow, 1, currentRow, 6].Style.Fill.PatternType = ExcelFillStyle.Solid;
                worksheet.Cells[currentRow, 1, currentRow, 6].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                
                currentRow++;
                
                // Policy table headers
                worksheet.Cells[currentRow, 1].Value = "ID";
                worksheet.Cells[currentRow, 2].Value = "Број на полиса";
                worksheet.Cells[currentRow, 3].Value = "Износ за плакање";
                worksheet.Cells[currentRow, 4].Value = "Платено од договорувач";
                worksheet.Cells[currentRow, 5].Value = "Износ за плакање во рок";
                worksheet.Cells[currentRow, 6].Value = "Платено по фактура";
                worksheet.Cells[currentRow, 7].Value = "Класа на осигурување";
                worksheet.Cells[currentRow, 8].Value = "Производ";
                worksheet.Cells[currentRow, 9].Value = "Должи договорувач";
                
                // Style header row
                worksheet.Cells[currentRow, 1, currentRow, 9].Style.Font.Bold = true;
                worksheet.Cells[currentRow, 1, currentRow, 9].Style.Fill.PatternType = ExcelFillStyle.Solid;
                worksheet.Cells[currentRow, 1, currentRow, 9].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                worksheet.Cells[currentRow, 1, currentRow, 9].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                worksheet.Cells[currentRow, 1, currentRow, 9].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                worksheet.Cells[currentRow, 1, currentRow, 9].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                worksheet.Cells[currentRow, 1, currentRow, 9].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                
                currentRow++;
                
                // Policy data
                int policyStartRow = currentRow;
                foreach (var polisa in Polisi)
                {
                    worksheet.Cells[currentRow, 1].Value = polisa.Id;
                    worksheet.Cells[currentRow, 2].Value = polisa.BrojNaPolisa;
                    worksheet.Cells[currentRow, 3].Value = polisa.VkupnaPremijaZaPlakanje;
                    worksheet.Cells[currentRow, 4].Value = polisa.PlatenoOdDogovoruvac;
                    worksheet.Cells[currentRow, 5].Value = polisa.IznosZaPlakanjeVoRok;
                    worksheet.Cells[currentRow, 6].Value = polisa.PlatenoPoFaktura;
                    worksheet.Cells[currentRow, 7].Value = polisa.KlasaIme;
                    worksheet.Cells[currentRow, 8].Value = polisa.Ime;
                    worksheet.Cells[currentRow, 9].Value = polisa.DolziDogovoruvac;
                    
                    // Add number formatting for decimal columns
                    worksheet.Cells[currentRow, 3].Style.Numberformat.Format = "#,##0.00";
                    worksheet.Cells[currentRow, 4].Style.Numberformat.Format = "#,##0.00";
                    worksheet.Cells[currentRow, 5].Style.Numberformat.Format = "#,##0.00";
                    worksheet.Cells[currentRow, 6].Style.Numberformat.Format = "#,##0.00";
                    worksheet.Cells[currentRow, 9].Style.Numberformat.Format = "#,##0.00";
                    
                    // Style the row
                    worksheet.Cells[currentRow, 1, currentRow, 9].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    worksheet.Cells[currentRow, 1, currentRow, 9].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    worksheet.Cells[currentRow, 1, currentRow, 9].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    worksheet.Cells[currentRow, 1, currentRow, 9].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                    
                    currentRow++;
                }
                
                // Auto fit columns for better readability
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
                
                // Add a little file info at the bottom
                currentRow += 2;
                worksheet.Cells[currentRow, 1].Value = "Извештајот е генериран на:";
                worksheet.Cells[currentRow, 2].Value = DateTime.Now.ToString("dd.MM.yyyy HH:mm:ss");
                worksheet.Cells[currentRow, 1].Style.Font.Italic = true;
                
                // Create the file
                var fileContent = package.GetAsByteArray();
                
                // Return the file
                return File(
                    fileContent,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    $"Спецификација_{Specifikacija.Id}_{DateTime.Now:yyyyMMdd}.xlsx");
            }
        }
    }
}
