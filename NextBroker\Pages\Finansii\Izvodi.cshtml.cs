using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using Microsoft.Data.SqlClient;
using System.Data;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace NextBroker.Pages.Finansii
{
    public class IzvodiModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public bool HasAdminAccess { get; private set; }
        public List<IzvodPremija> Izvodi { get; set; } = new();
        public IEnumerable<SelectListItem> Banki { get; set; }
        public IEnumerable<SelectListItem> BankaSmetki { get; set; } = new List<SelectListItem>();
        
        [BindProperty(SupportsGet = true)]
        public DateTime? DatumOd { get; set; } = DateTime.Today;

        [BindProperty(SupportsGet = true)]
        public DateTime? DatumDo { get; set; } = DateTime.Today;

        [BindProperty]
        public IzvodPremija Input { get; set; } = new()
        {
            DatumNaIzvod = DateTime.Today
        };

        [TempData]
        public string DebugInfo { get; set; }

        [TempData]
        public string ErrorMessage { get; set; }

        public class IzvodPremija
        {
            public long Id { get; set; }
            public DateTime? DateCreated { get; set; }
            public string UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string UsernameModified { get; set; }

            [Required(ErrorMessage = "Изберете банка")]
            [Display(Name = "Банка")]
            public int SifrarnikBankiId { get; set; }

            // This is just for display, no validation needed
            public string BankaNaziv { get; set; }

            [Required(ErrorMessage = "Внесете број на извод")]
            [Display(Name = "Број на извод")]
            [StringLength(200, ErrorMessage = "Бројот на извод не може да биде подолг од 200 карактери")]
            public string BrojNaIzvod { get; set; }

            [Display(Name = "Број на сметка")]
            public string BrojNaSmetka { get; set; }

            [Required(ErrorMessage = "Внесете датум на извод")]
            [Display(Name = "Датум на извод")]
            [DataType(DataType.Date)]
            public DateTime DatumNaIzvod { get; set; }

            [Required(ErrorMessage = "Внесете прилив")]
            [Display(Name = "Износ прилив")]
            [Range(0, ***************.9999, ErrorMessage = "Внесете валиден износ")]
            [DisplayFormat(DataFormatString = "{0:N4}")]
            public decimal Priliv { get; set; }

            [Required(ErrorMessage = "Внесете одлив")]
            [Display(Name = "Износ одлив")]
            [Range(0, ***************.9999, ErrorMessage = "Внесете валиден износ")]
            [DisplayFormat(DataFormatString = "{0:N4}")]
            public decimal Odliv { get; set; }

            public bool? Rasknizen { get; set; }
            public bool? RasknizenOdliv { get; set; }
        }

        public IzvodiModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public async Task<IActionResult> OnGetAsync()
        {
            if (!await HasPageAccess("Izvodi"))
            {
                return RedirectToAccessDenied();
            }

            // Set default dates if none provided in query string
            if (!Request.Query.ContainsKey("DatumOd") && !Request.Query.ContainsKey("DatumDo"))
            {
                DatumOd = DateTime.Today;
                DatumDo = DateTime.Today;
            }

            HasAdminAccess = await HasPageAccess("IzvodiAdmin");
            
            await LoadBanki();
            await LoadIzvodi();

            return Page();
        }

        private async Task LoadIzvodi()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT i.*, b.Banka as BankaNaziv
                    FROM IzvodPremija i
                    LEFT JOIN SifrarnikBanki b ON i.SifrarnikBankiId = b.Id
                    WHERE (@DatumOd IS NULL OR i.DatumNaIzvod >= @DatumOd)
                    AND (@DatumDo IS NULL OR i.DatumNaIzvod <= @DatumDo)
                    ORDER BY i.DatumNaIzvod DESC", connection))
                {
                    cmd.Parameters.AddWithValue("@DatumOd", (object)DatumOd ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@DatumDo", (object)DatumDo ?? DBNull.Value);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        Izvodi.Add(new IzvodPremija
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            DateModified = reader["DateModified"] as DateTime?,
                            UsernameModified = reader["UsernameModified"] as string,
                            SifrarnikBankiId = reader.GetInt32(reader.GetOrdinal("SifrarnikBankiId")),
                            BankaNaziv = reader["BankaNaziv"] as string,
                            BrojNaIzvod = reader["BrojNaIzvod"] as string,
                            BrojNaSmetka = reader["BrojNaSmetka"] as string,
                            DatumNaIzvod = reader.GetDateTime(reader.GetOrdinal("DatumNaIzvod")),
                            Priliv = reader.IsDBNull(reader.GetOrdinal("Priliv")) ? 0 : reader.GetDecimal(reader.GetOrdinal("Priliv")),
                            Odliv = reader.IsDBNull(reader.GetOrdinal("Odliv")) ? 0 : reader.GetDecimal(reader.GetOrdinal("Odliv")),
                            Rasknizen = reader["Rasknizen"] as bool?,
                            RasknizenOdliv = reader["RasknizenOdliv"] as bool?
                        });
                    }
                }
            }
        }

        private async Task LoadBanki()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Banka 
                    FROM SifrarnikBanki 
                    ORDER BY Banka", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Banka"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Banki = items;
                }
            }
        }


        public async Task<IActionResult> OnGetBankAccountsAsync(int bankId)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT BrojNaSmetka 
                    FROM SifrarnikBankiSmetki 
                    WHERE SifrarnikBankiId = @BankId 
                    ORDER BY BrojNaSmetka", connection))
                {
                    cmd.Parameters.AddWithValue("@BankId", bankId);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        var brojNaSmetka = reader["BrojNaSmetka"].ToString();
                        items.Add(new SelectListItem(brojNaSmetka, brojNaSmetka));
                    }
                    return new JsonResult(items);
                }
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            try 
            {
                if (!await HasPageAccess("IzvodiAdmin"))
                {
                    return RedirectToAccessDenied();
                }

                // Validate required fields
                if (Input.SifrarnikBankiId == 0 ||
                    string.IsNullOrEmpty(Input.BrojNaIzvod) ||
                    Input.DatumNaIzvod == default)
                {
                    ModelState.Clear(); // Clear existing errors
                    if (Input.SifrarnikBankiId == 0) ModelState.AddModelError("Input.SifrarnikBankiId", "Изберете банка");
                    if (string.IsNullOrEmpty(Input.BrojNaIzvod)) ModelState.AddModelError("Input.BrojNaIzvod", "Внесете број на извод");
                    if (Input.DatumNaIzvod == default) ModelState.AddModelError("Input.DatumNaIzvod", "Внесете датум на извод");

                    ErrorMessage = "Ве молиме пополнете ги сите задолжителни полиња.";
                    await LoadBanki();
                    await LoadIzvodi();
                    return Page();
                }

                string username = HttpContext.Session.GetString("Username");
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Check if BrojNaIzvod already exists
                    using (SqlCommand checkCmd = new SqlCommand("SELECT COUNT(*) FROM IzvodPremija WHERE BrojNaIzvod = @BrojNaIzvod", connection))
                    {
                        checkCmd.Parameters.AddWithValue("@BrojNaIzvod", Input.BrojNaIzvod);
                        int count = (int)await checkCmd.ExecuteScalarAsync();

                        if (count > 0)
                        {
                            ErrorMessage = "Извод со овој број веќе постои.";
                            ModelState.AddModelError("Input.BrojNaIzvod", ErrorMessage);
                            await LoadBanki();
                            await LoadIzvodi();
                            return Page();
                        }
                    }

                    // Proceed with insert if no existing record
                    using (SqlCommand cmd = new SqlCommand(@"
                        INSERT INTO IzvodPremija (
                            SifrarnikBankiId,
                            BrojNaIzvod,
                            DatumNaIzvod,
                            Priliv,
                            Odliv,
                            DateCreated,
                            UsernameCreated,
                            Rasknizen,
                            RasknizenOdliv,
                            BrojNaSmetka
                        ) VALUES (
                            @SifrarnikBankiId,
                            @BrojNaIzvod,
                            @DatumNaIzvod,
                            @Priliv,
                            @Odliv,
                            GETDATE(),
                            @UsernameCreated,
                            @Rasknizen,
                            @RasknizenOdliv,
                            @BrojNaSmetka
                        )", connection))
                    {
                        cmd.Parameters.AddWithValue("@SifrarnikBankiId", Input.SifrarnikBankiId);
                        cmd.Parameters.AddWithValue("@BrojNaIzvod", Input.BrojNaIzvod);
                        cmd.Parameters.AddWithValue("@DatumNaIzvod", Input.DatumNaIzvod);
                        cmd.Parameters.AddWithValue("@Priliv", Input.Priliv);
                        cmd.Parameters.AddWithValue("@Odliv", Input.Odliv);
                        cmd.Parameters.AddWithValue("@UsernameCreated", username);
                        cmd.Parameters.AddWithValue("@Rasknizen", Input.Priliv == 0 ? 1 : 0);
                        cmd.Parameters.AddWithValue("@RasknizenOdliv", Input.Odliv == 0 ? 1 : 0);
                        cmd.Parameters.AddWithValue("@BrojNaSmetka", Input.BrojNaSmetka ?? (object)DBNull.Value);

                        try
                        {
                            await cmd.ExecuteNonQueryAsync();
                            TempData["SuccessMessage"] = "Изводот е успешно додаден.";
                            return RedirectToPage();
                        }
                        catch (Exception ex)
                        {
                            ErrorMessage = $"Се појави грешка при зачувување на изводот: {ex.Message}";
                            ModelState.AddModelError(string.Empty, ErrorMessage);
                            await LoadBanki();
                            await LoadIzvodi();
                            return Page();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Се појави неочекувана грешка: {ex.Message}";
                ModelState.AddModelError(string.Empty, ErrorMessage);
                await LoadBanki();
                await LoadIzvodi();
                return Page();
            }
        }

        public async Task<IActionResult> OnGetCheckBrojNaIzvodAsync(string brojNaIzvod)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM IzvodPremija WHERE BrojNaIzvod = @BrojNaIzvod", connection))
                {
                    cmd.Parameters.AddWithValue("@BrojNaIzvod", brojNaIzvod);
                    int count = (int)await cmd.ExecuteScalarAsync();
                    return new JsonResult(new { exists = count > 0 });
                }
            }
        }
    }
} 