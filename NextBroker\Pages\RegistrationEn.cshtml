@page
@model RazorPortal.Pages.RegistrationEnModel
@{
    ViewData["Title"] = "New User Registration";
}

<link rel="stylesheet" href="~/css/registracija.css" asp-append-version="true" />

<div class="registration-container">
    <h2>@ViewData["Title"]</h2>

    @if (string.IsNullOrEmpty(Model.SuccessMessage))
    {
        <form method="post">
            <div class="form-group">
                <label asp-for="FirstName"></label>
                <input type="text" asp-for="FirstName" class="form-control" />
                <span asp-validation-for="FirstName" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="LastName"></label>
                <input type="text" asp-for="LastName" class="form-control" />
                <span asp-validation-for="LastName" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="EMB"></label>
                <input type="text" asp-for="EMB" class="form-control" pattern="^\d+$" title="Enter a valid ID number." />
                <span asp-validation-for="EMB" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="Email"></label>
                <input type="email" asp-for="Email" class="form-control" required />
                <span asp-validation-for="Email" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="Phone"></label>
                <input type="text" asp-for="Phone" class="form-control" pattern="^\d+$" title="Enter numbers only." />
                <span asp-validation-for="Phone" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="Username"></label>
                <input type="text" asp-for="Username" class="form-control" />
                <span asp-validation-for="Username" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="Password"></label>
                <input type="password" asp-for="Password" class="form-control" 
                       pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\da-zA-Z]).{8,}$" 
                       title="Password must contain at least 8 characters, one uppercase letter, one number, and one special character." />
                <span asp-validation-for="Password" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="ConfirmPassword"></label>
                <input type="password" asp-for="ConfirmPassword" class="form-control" />
                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
            </div>

            <button type="submit" class="btn btn-primary" onclick="this.disabled=true; this.form.submit();">Register</button>

            <br />
            <br />            

            @if (!string.IsNullOrEmpty(Model.ErrorMessage))
            {
                <div class="alert alert-danger mt-3">@Model.ErrorMessage</div>
            }
        </form>
    }
    else
    {
        <div class="alert alert-success mt-3">@Model.SuccessMessage</div>

        <p>Redirecting in <span id="countdown">10</span> seconds... If you are not redirected, click the button below.</p>

        <button onclick="window.location.href='@Url.Page("/UserValidationEn")'" class="btn btn-primary">Validate User!</button>

        <script>
            setTimeout(function () {
                window.location.href = '@Url.Page("/UserValidationEn")';
            }, 10000);
        </script>

        <script>
            let countdown = 10;
            const countdownElement = document.getElementById('countdown');

            const timer = setInterval(() => {
                countdown--;
                countdownElement.textContent = countdown;

                if (countdown <= 0) {
                    clearInterval(timer);
                    window.location.href = '@Url.Page("/UserValidationEn")';
                }
            }, 1000);
        </script>
    }
</div> 