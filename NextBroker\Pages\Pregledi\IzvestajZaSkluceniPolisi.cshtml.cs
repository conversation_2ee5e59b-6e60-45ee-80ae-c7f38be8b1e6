using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.Threading.Tasks;
using System.Data;
using Microsoft.Data.SqlClient;
using System;
using System.ComponentModel.DataAnnotations;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Collections.Generic;
using System.Text;
using Newtonsoft.Json;

namespace NextBroker.Pages.Pregledi
{
    public class IzvestajZaSkluceniPolisiModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public IzvestajZaSkluceniPolisiModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
            
            // Initialize filters
            if (string.IsNullOrEmpty(AppliedFiltersJson))
            {
                AppliedFilters = new List<string>();
            }
            else
            {
                try
                {
                    AppliedFilters = JsonConvert.DeserializeObject<List<string>>(AppliedFiltersJson);
                }
                catch
                {
                    AppliedFilters = new List<string>();
                }
            }
        }

        // Date filters for "Датум на издавање"
        [BindProperty]
        [Display(Name = "Датум на издавање од")]
        [DataType(DataType.Date)]
        public DateTime? IzdavanjeStartDate { get; set; }

        [BindProperty]
        [Display(Name = "до")]
        [DataType(DataType.Date)]
        public DateTime? IzdavanjeEndDate { get; set; }
        
        // Date filters for "Датум важи од"
        [BindProperty]
        [Display(Name = "Датум важи од")]
        [DataType(DataType.Date)]
        public DateTime? VaziOdStartDate { get; set; }

        [BindProperty]
        [Display(Name = "до")]
        [DataType(DataType.Date)]
        public DateTime? VaziOdEndDate { get; set; }
        
        // Tracks which filters are currently active (as JSON string in TempData)
        [TempData]
        public string AppliedFiltersJson { get; set; }
        
        // Store current query and parameters for Excel export
        [TempData]
        public string CurrentQueryJson { get; set; }
        
        // Non-TempData property for working with filters
        public List<string> AppliedFilters { get; set; }
        
        public DataTable ReportData { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            if (!await HasPageAccess("IzvestajZaSkluceniPolisi"))
            {
                return RedirectToAccessDenied();
            }

            return Page();
        }
        
        public async Task<IActionResult> OnPostFilterAsync()
        {
            if (!await HasPageAccess("IzvestajZaSkluceniPolisi"))
            {
                return RedirectToAccessDenied();
            }
            
            // Server-side validation
            if (IzdavanjeEndDate.HasValue && !IzdavanjeStartDate.HasValue)
            {
                ModelState.AddModelError("IzdavanjeStartDate", "Мора да внесете датум 'од' пред да внесете датум 'до'");
                return Page();
            }
            
            // Only validate Datum Vazi Od date range if both dates are provided
            if (VaziOdStartDate.HasValue && VaziOdEndDate.HasValue && VaziOdEndDate < VaziOdStartDate)
            {
                ModelState.AddModelError("VaziOdEndDate", "Датумот 'до' не може да биде пред датумот 'од'");
                return Page();
            }
            
            if (IzdavanjeStartDate.HasValue && IzdavanjeEndDate.HasValue && IzdavanjeEndDate < IzdavanjeStartDate)
            {
                ModelState.AddModelError("IzdavanjeEndDate", "Датумот 'до' не може да биде пред датумот 'од'");
                return Page();
            }
            
            // Reset applied filters
            AppliedFilters = new List<string>();
            
            // Build query
            var queryBuilder = new StringBuilder();
            var parameters = new List<SqlParameter>();
            bool hasCondition = false;
            
            // Check Datum na izdavanje filter based on provided values
            if (IzdavanjeStartDate.HasValue)
            {
                if (IzdavanjeEndDate.HasValue)
                {
                    // If both start and end dates are provided, filter for range
                    queryBuilder.Append(" WHERE p.DatumNaIzdavanje >= @IzdavanjeStartDate AND p.DatumNaIzdavanje <= @IzdavanjeEndDate");
                    parameters.Add(new SqlParameter("@IzdavanjeStartDate", IzdavanjeStartDate.Value));
                    parameters.Add(new SqlParameter("@IzdavanjeEndDate", IzdavanjeEndDate.Value));
                }
                else
                {
                    // If only start date is provided, filter for exact match on that date
                    queryBuilder.Append(" WHERE CONVERT(date, p.DatumNaIzdavanje) = CONVERT(date, @IzdavanjeStartDate)");
                    parameters.Add(new SqlParameter("@IzdavanjeStartDate", IzdavanjeStartDate.Value));
                }
                hasCondition = true;
                AppliedFilters.Add("Izdavanje");
            }
            
            // Check Datum vazi od filter based on provided values
            if (VaziOdStartDate.HasValue || VaziOdEndDate.HasValue)
            {
                // Special case: only the "до" date is provided
                if (!VaziOdStartDate.HasValue && VaziOdEndDate.HasValue)
                {
                    if (hasCondition)
                    {
                        queryBuilder.Append(" AND p.DatumVaziOd <= @VaziOdEndDate");
                    }
                    else
                    {
                        queryBuilder.Append(" WHERE p.DatumVaziOd <= @VaziOdEndDate");
                    }
                    parameters.Add(new SqlParameter("@VaziOdEndDate", VaziOdEndDate.Value));
                }
                // Both start and end dates are provided
                else if (VaziOdStartDate.HasValue && VaziOdEndDate.HasValue)
                {
                    if (hasCondition)
                    {
                        queryBuilder.Append(" AND p.DatumVaziOd >= @VaziOdStartDate AND p.DatumVaziOd <= @VaziOdEndDate");
                    }
                    else
                    {
                        queryBuilder.Append(" WHERE p.DatumVaziOd >= @VaziOdStartDate AND p.DatumVaziOd <= @VaziOdEndDate");
                    }
                    parameters.Add(new SqlParameter("@VaziOdStartDate", VaziOdStartDate.Value));
                    parameters.Add(new SqlParameter("@VaziOdEndDate", VaziOdEndDate.Value));
                }
                // Only start date is provided
                else
                {
                    if (hasCondition)
                    {
                        queryBuilder.Append(" AND CONVERT(date, p.DatumVaziOd) = CONVERT(date, @VaziOdStartDate)");
                    }
                    else
                    {
                        queryBuilder.Append(" WHERE CONVERT(date, p.DatumVaziOd) = CONVERT(date, @VaziOdStartDate)");
                    }
                    parameters.Add(new SqlParameter("@VaziOdStartDate", VaziOdStartDate.Value));
                }
                hasCondition = true;
                AppliedFilters.Add("VaziOd");
            }
            
            // If no filters applied, just show all
            if (AppliedFilters.Count == 0)
            {
                AppliedFilters.Add("All");
            }
            
            // Store applied filters in TempData as JSON
            AppliedFiltersJson = JsonConvert.SerializeObject(AppliedFilters);
            
            // Store the query clause and parameter values for Excel export
            var queryInfo = new QueryInfo
            {
                WhereClause = queryBuilder.ToString(),
                Parameters = parameters.Select(p => new ParameterInfo
                {
                    Name = p.ParameterName,
                    Value = p.Value.ToString(),
                    IsDate = p.Value is DateTime
                }).ToList()
            };
            
            CurrentQueryJson = JsonConvert.SerializeObject(queryInfo);
            
            await LoadReportDataAsync(queryBuilder.ToString(), parameters);
            
            return Page();
        }
        
        public async Task<IActionResult> OnPostClearAsync()
        {
            if (!await HasPageAccess("IzvestajZaSkluceniPolisi"))
            {
                return RedirectToAccessDenied();
            }
            
            // Clear all date filters
            IzdavanjeStartDate = null;
            IzdavanjeEndDate = null;
            VaziOdStartDate = null;
            VaziOdEndDate = null;
            
            // Clear report data
            ReportData = null;
            
            // Clear applied filters
            AppliedFilters = new List<string>();
            AppliedFiltersJson = JsonConvert.SerializeObject(AppliedFilters);
            
            // Clear stored query
            CurrentQueryJson = null;
            
            return Page();
        }
        
        public async Task<IActionResult> OnPostSitePolisiAsync()
        {
            if (!await HasPageAccess("IzvestajZaSkluceniPolisi"))
            {
                return RedirectToAccessDenied();
            }
            
            await LoadReportDataAsync("", new List<SqlParameter>());
            AppliedFilters = new List<string> { "All" };
            AppliedFiltersJson = JsonConvert.SerializeObject(AppliedFilters);
            
            // Store empty query for Excel export
            var queryInfo = new QueryInfo
            {
                WhereClause = "",
                Parameters = new List<ParameterInfo>()
            };
            
            CurrentQueryJson = JsonConvert.SerializeObject(queryInfo);
            
            return Page();
        }
        
        private async Task LoadReportDataAsync(string whereClause, List<SqlParameter> parameters)
        {
            string query = @"
                SELECT 
                    p.BrojNaPolisa AS [Број на полиса],
                    
                    kOsiguritel.Naziv AS [Осигурител],

                    ko.KlasaIme AS [Класа],
                    pr.Ime AS [Продукт],
                    
                    COALESCE(
                        NULLIF(kDogovoruvac.Ime + ' ' + kDogovoruvac.Prezime, ' '),
                        kDogovoruvac.Naziv
                    ) AS [Договорувач],
                    
                    CASE 
                        WHEN kDogovoruvac.EMBG IS NULL THEN N'правно лице'
                        ELSE N'физичко лице'
                    END AS [Физичко/Правно лице],
                    
                    COALESCE(
                        NULLIF(kOsigurenik.Ime + ' ' + kOsigurenik.Prezime, ' '),
                        kOsigurenik.Naziv
                    ) AS [Осигуреник],
                    
                    -- Sorabotnik logic using CROSS APPLY fallback
                    COALESCE(
                        NULLIF(kSorabotnik.Ime + ' ' + kSorabotnik.Prezime, ' '),
                        NULLIF(fallbackClient.Ime + ' ' + fallbackClient.Prezime, ' '),
                        fallbackClient.Naziv
                    ) AS [Соработник],
                    
                    p.DatumVaziOd AS [Датум важи од],
                    p.DatumVaziDo AS [Датум важи до],
                    p.DatumNaIzdavanje AS [Датум на издавање],
                    
                    CASE 
                        WHEN (SELECT COUNT(*) FROM dbo.PolisiRati iR WHERE iR.PolisaId = p.Id) <= 1 
                        THEN N'еднократно'
                        ELSE N'на рати'
                    END AS [Начин на плаќање],
                    
                    p.TipNaFaktura AS [Тип на фактура],
                    p.BrojNaFakturaVlezna AS [Број на влезна фактура],
                    p.BrojNaFakturaIzlezna AS [Број на излезна фактура],
                    
                    dbo.VratiOsnovnaPremijaZaPolisaBroj(p.BrojNaPolisa) AS [Премија за основна класа],
                    
                    CASE 
                        WHEN p.KlasiOsiguruvanjeIdKlasa = 10 THEN pao.PremijaZaNezgoda
                        WHEN p.KlasiOsiguruvanjeIdKlasa = 16 THEN pzk.PremijaZaNezgoda
                        WHEN p.KlasiOsiguruvanjeIdKlasa = 17 THEN pgr.PremijaZaNezgoda
                        ELSE NULL
                    END AS [Дополнително Незгода],
                    
                    CASE 
                        WHEN p.KlasiOsiguruvanjeIdKlasa = 10 THEN pao.PremijaKrsenjeStakloAO
                        WHEN p.KlasiOsiguruvanjeIdKlasa = 16 THEN pzk.PremijaKrsenjeStakloZK
                        WHEN p.KlasiOsiguruvanjeIdKlasa = 17 THEN pgr.PremijaKrsenjeStakloGR
                        ELSE NULL
                    END AS [Дополнително Скршено стакло],
                    
                    dbo.VratiPolisaIznosZaPlakanjePoPolisaId(p.Id) AS [Премија за наплата],
                    dbo.VratiPolisaIznosZaPlakanjeVoRokOdFakturaIzlezna(p.Id) AS [Премија во рок],
                    
                    NULL AS [Платено во рок],
                    
                    CASE 
                        WHEN p.Storno = 1 THEN N'Да'
                        ELSE N'Не'
                    END AS [Сторно],
                    
                    CASE 
                        WHEN p.Storno = 1 THEN p.DatumStorniranje
                        ELSE NULL
                    END AS [Датум на сторно],
                    
                    (
                        SELECT ISNULL(SUM(pr.UplatenIznos), 0)
                        FROM dbo.PolisiRati pr
                        WHERE pr.PolisaId = p.Id
                    ) AS [Вкупни уплати од договорувач],
                    
                    dbo.VratiPolisaVkupenBrojNaRati(p.Id) AS [Вкупен број на рати],
                    dbo.VratiPolisaDolznaPremija(p.BrojNaPolisa) AS [Преостаната должна премија],
                    dbo.VratiPolisaBrojNaNeplateniRati(p.Id) AS [Преостанати рати],
                    dbo.VratiPolisaDospeanDolg(p.Id) AS [Вкупен доспеан долг],
                    dbo.VratiPolisaBrojNaDospeaniNeplateniRati(p.Id) AS [Број на доспеани неплатени рати]
                
                FROM dbo.Polisi p
                
                LEFT JOIN dbo.Klienti kDogovoruvac ON p.KlientiIdDogovoruvac = kDogovoruvac.Id
                LEFT JOIN dbo.Klienti kOsiguritel ON p.KlientiIdOsiguritel = kOsiguritel.Id
                LEFT JOIN dbo.Klienti kOsigurenik ON p.KlientiIdOsigurenik = kOsigurenik.Id
                LEFT JOIN dbo.Klienti kSorabotnik ON p.KlientiIdSorabotnik = kSorabotnik.Id
                
                -- Add LEFT JOINs for the supplementary insurance tables
                LEFT JOIN dbo.PolisiAvtoOdgovornost pao ON pao.PolisaId = p.Id
                LEFT JOIN dbo.PolisiZelenKarton pzk ON pzk.PolisaId = p.Id
                LEFT JOIN dbo.PolisiGranicno pgr ON pgr.PolisaId = p.Id

                -- Add LEFT JOINs for additional class and product info
                LEFT JOIN dbo.KlasiOsiguruvanje ko ON p.KlasiOsiguruvanjeIdKlasa = ko.Id
                LEFT JOIN dbo.Produkti pr ON p.ProduktiIdProizvod = pr.Id
                
                -- CROSS APPLY to find fallback client based on UsernameCreated -> Users -> emb -> Klienti.EMBG
                CROSS APPLY
                (
                    SELECT TOP 1
                        kFallback.Ime,
                        kFallback.Prezime,
                        kFallback.Naziv
                    FROM dbo.Users u
                    INNER JOIN dbo.Klienti kFallback ON u.emb = kFallback.EMBG
                    WHERE u.Username = p.UsernameCreated
                ) fallbackClient";
                
            // Add WHERE clause if provided
            query += whereClause;
            
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                
                using (var command = new SqlCommand(query, connection))
                {
                    // Add parameters if any
                    if (parameters != null && parameters.Count > 0)
                    {
                        command.Parameters.AddRange(parameters.ToArray());
                    }
                    
                    using (var adapter = new SqlDataAdapter(command))
                    {
                        ReportData = new DataTable();
                        adapter.Fill(ReportData);
                    }
                }
            }
        }
        
        public async Task<IActionResult> OnPostExportExcelAsync()
        {
            if (!await HasPageAccess("IzvestajZaSkluceniPolisi"))
            {
                return RedirectToAccessDenied();
            }
            
            // Reconstruct the query and parameters from the stored JSON
            var parameters = new List<SqlParameter>();
            string whereClause = "";
            
            if (!string.IsNullOrEmpty(CurrentQueryJson))
            {
                try
                {
                    var queryInfo = JsonConvert.DeserializeObject<QueryInfo>(CurrentQueryJson);
                    whereClause = queryInfo.WhereClause;
                    
                    foreach (var param in queryInfo.Parameters)
                    {
                        if (param.IsDate)
                        {
                            parameters.Add(new SqlParameter(param.Name, DateTime.Parse(param.Value)));
                        }
                        else
                        {
                            parameters.Add(new SqlParameter(param.Name, param.Value));
                        }
                    }
                }
                catch
                {
                    // If there's any error parsing the saved query, just use an empty query
                    whereClause = "";
                    parameters = new List<SqlParameter>();
                }
            }
            
            // Load data with the stored filters
            await LoadReportDataAsync(whereClause, parameters);
            
            if (ReportData == null || ReportData.Rows.Count == 0)
            {
                return Page();
            }
            
            // Generate Excel file
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("Извештај за склучени полиси");
                
                // Add report header
                worksheet.Cells["A1"].Value = "Извештај за склучени полиси";
                worksheet.Cells["A1:E1"].Merge = true;
                worksheet.Cells["A1"].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                worksheet.Cells["A1"].Style.Font.Bold = true;
                
                // Add filter information if applicable
                int infoRow = 3;
                
                // Display date filters in dd/MM/yyyy format
                if (IzdavanjeStartDate.HasValue || IzdavanjeEndDate.HasValue)
                {
                    worksheet.Cells[$"A{infoRow}"].Value = "Датум на издавање од:";
                    
                    string izdavanjeValue = "";
                    if (IzdavanjeStartDate.HasValue)
                    {
                        izdavanjeValue += IzdavanjeStartDate.Value.ToString("dd/MM/yyyy");
                    }
                    
                    izdavanjeValue += " до ";
                    
                    if (IzdavanjeEndDate.HasValue)
                    {
                        izdavanjeValue += IzdavanjeEndDate.Value.ToString("dd/MM/yyyy");
                    }
                    
                    worksheet.Cells[$"B{infoRow}"].Value = izdavanjeValue;
                    infoRow++;
                }
                
                if (VaziOdStartDate.HasValue || VaziOdEndDate.HasValue)
                {
                    worksheet.Cells[$"A{infoRow}"].Value = "Датум важи од:";
                    
                    string vaziOdValue = "";
                    if (VaziOdStartDate.HasValue)
                    {
                        vaziOdValue += VaziOdStartDate.Value.ToString("dd/MM/yyyy");
                    }
                    
                    vaziOdValue += " до ";
                    
                    if (VaziOdEndDate.HasValue)
                    {
                        vaziOdValue += VaziOdEndDate.Value.ToString("dd/MM/yyyy");
                    }
                    
                    worksheet.Cells[$"B{infoRow}"].Value = vaziOdValue;
                    infoRow++;
                }
                
                // Always show total records count
                worksheet.Cells[$"A{infoRow}"].Value = "Вкупно записи:";
                worksheet.Cells[$"B{infoRow}"].Value = ReportData.Rows.Count.ToString();
                infoRow++;
                
                // Add spacing before the table
                int tableStartRow = infoRow + 1;
                
                // Add headers
                for (int i = 0; i < ReportData.Columns.Count; i++)
                {
                    var cell = worksheet.Cells[tableStartRow, i + 1];
                    cell.Value = ReportData.Columns[i].ColumnName;
                    cell.Style.Font.Bold = true;
                    cell.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    cell.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    cell.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    cell.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                    cell.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    cell.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                }
                
                // Add data
                for (int row = 0; row < ReportData.Rows.Count; row++)
                {
                    for (int col = 0; col < ReportData.Columns.Count; col++)
                    {
                        var cellValue = ReportData.Rows[row][col];
                        var cell = worksheet.Cells[row + tableStartRow + 1, col + 1];
                        
                        if (cellValue is DateTime dateValue)
                        {
                            cell.Style.Numberformat.Format = "dd/mm/yyyy";
                            cell.Value = dateValue;
                        }
                        else if (cellValue is decimal || cellValue is double || cellValue is float)
                        {
                            cell.Style.Numberformat.Format = "#,##0.00";
                            cell.Value = cellValue;
                        }
                        else
                        {
                            cell.Value = cellValue;
                        }
                        
                        // Add borders to data cells
                        cell.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        cell.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        cell.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        cell.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                    }
                }
                
                // Auto-fit columns
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
                
                // Generate the Excel file
                var content = package.GetAsByteArray();
                string fileName = $"IzvestajZaSkluceniPolisi_{DateTime.Now:yyyyMMdd}.xlsx";
                
                return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
        }
    }
    
    // Helper classes for storing query information
    public class QueryInfo
    {
        public string WhereClause { get; set; }
        public List<ParameterInfo> Parameters { get; set; } = new List<ParameterInfo>();
    }
    
    public class ParameterInfo
    {
        public string Name { get; set; }
        public string Value { get; set; }
        public bool IsDate { get; set; }
    }
}
