@page
@model NextBroker.Pages.Finansii.OZListaModel
@{
    ViewData["Title"] = "Одобрување / Задолжување Листа";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">@ViewData["Title"]</h3>
                    <div class="card-tools">
                        <a asp-page="OZDodaj" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Додај нов запис
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Date Filter Form -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-filter"></i> Филтер по датум
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="post" asp-page-handler="Filter">
                                @Html.AntiForgeryToken()
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label asp-for="DatumOd" class="form-label">Датум од:</label>
                                            <input asp-for="DatumOd" type="date" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label asp-for="DatumDo" class="form-label">Датум до:</label>
                                            <input asp-for="DatumDo" type="date" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="form-label">&nbsp;</label>
                                            <div class="d-flex gap-2">
                                                <button type="submit" class="btn btn-info">
                                                    <i class="fas fa-search"></i> Филтрирај
                                                </button>
                                                <a href="@Url.Page("/Finansii/OZLista")" class="btn btn-secondary">
                                                    <i class="fas fa-times"></i> Ресетирај
                                                </a>
                                                <button type="submit" asp-page-handler="ExportExcel" class="btn btn-success">
                                                    <i class="fas fa-file-excel"></i> Excel
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @if (Model.DatumOd.HasValue || Model.DatumDo.HasValue)
                                {
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        Активен филтер: 
                                        @if (Model.DatumOd.HasValue)
                                        {
                                            <strong>од @Model.DatumOd.Value.ToString("dd.MM.yyyy")</strong>
                                        }
                                        @if (Model.DatumOd.HasValue && Model.DatumDo.HasValue)
                                        {
                                            <span> до </span>
                                        }
                                        @if (Model.DatumDo.HasValue)
                                        {
                                            <strong>до @Model.DatumDo.Value.ToString("dd.MM.yyyy")</strong>
                                        }
                                        <span class="ms-2 text-muted">(@Model.OdobruvanjaZadolzuvanja.Count резултат/и)</span>
                                    </div>
                                }
                            </form>
                        </div>
                    </div>
                    <!-- End Date Filter Form -->

                    @Html.AntiForgeryToken()
                    <div class="table-responsive">
                        <table id="ozTable" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Тип на документ</th>
                                    <th>Тип</th>
                                    <th>Премија/Провизија</th>
                                    <th>Клиент</th>
                                    <th>Број на документ</th>
                                    <th>Датум на документ</th>
                                    <th>Износ</th>
                                    <th>Број на влезна фактура</th>
                                    <th>Број на излезна фактура</th>
                                    <th>Број на полиса</th>
                                    <th>Тип на фактура</th>
                                    <th>Број на излезна фактура премија</th>
                                    <th>Опис</th>
                                    <th>Креирано од</th>
                                    <th>Датум на креирање</th>
                                    <th>Променето од</th>
                                    <th>Датум на промена</th>
                                    <th>Сторно</th>
                                    <th>Акции</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.OdobruvanjaZadolzuvanja)
                                {
                                    <tr>
                                        <td>@item.Id</td>
                                        <td>@item.TipNaDokument</td>
                                        <td>@item.Tip</td>
                                        <td>@item.PremijaProvizija</td>
                                        <td>@item.KlientImePrezimeNaziv</td>
                                        <td>@item.BrojNaDokument</td>
                                        <td>@(item.DatumNaDokument?.ToShortDateString() ?? "")</td>
                                        <td>@(item.Iznos?.ToString("N2") ?? "")</td>
                                        <td>@item.BrojNaVleznaFaktura</td>
                                        <td>@item.BrojNaIzleznaFaktura</td>
                                        <td>@item.BrojNaPolisa</td>
                                        <td>@item.TipNafaktura</td>
                                        <td>@item.BrojNaIzleznaFakturaPremija</td>
                                        <td>@item.Opis</td>
                                        <td>@item.UsernameCreated</td>
                                        <td>@item.DateCreated.ToShortDateString()</td>
                                        <td>@item.UsernameModified</td>
                                        <td>@(item.DateModified?.ToShortDateString() ?? "")</td>
                                        <td>
                                            @if (item.Storno)
                                            {
                                                <span class="badge badge-danger">Да</span>
                                            }
                                            else
                                            {
                                                <span class="badge badge-success">Не</span>
                                            }
                                        </td>
                                        <td>
                                            @if (!item.Storno)
                                            {
                                                <button type="button" class="btn btn-warning btn-sm storno-btn" data-id="@item.Id" data-document="@item.BrojNaDokument">
                                                    <i class="fas fa-ban"></i> Сторно
                                                </button>
                                                <button type="button" class="btn btn-primary btn-sm ms-1 download-document-btn" data-id="@item.Id" data-document="@item.BrojNaDokument" @(item.TipNaDokument == "Влезен Документ" ? "hidden" : "")>
                                                    <i class="fas fa-download"></i> Превземи документ
                                                </button>
                                            }
                                            else
                                            {
                                                <span class="text-muted">Сторниран</span>
                                                <button type="button" class="btn btn-primary btn-sm ms-2 download-document-btn" data-id="@item.Id" data-document="@item.BrojNaDokument" @(item.TipNaDokument == "Влезен Документ" ? "hidden" : "")>
                                                    <i class="fas fa-download"></i> Превземи документ
                                                </button>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <!-- Add jsPDF and html2canvas libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <script>
        $(document).ready(function () {
            $('#ozTable').DataTable({
                "paging": true,
                "lengthChange": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "autoWidth": false,
                "responsive": true,
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Macedonian.json"
                }
            });

            // Handle Storno button click
            $(document).on('click', '.storno-btn', function() {
                const id = $(this).data('id');
                const document = $(this).data('document');
                const button = $(this);
                
                console.log('Storno button clicked for ID:', id, 'Document:', document);
                
                // Confirmation dialog
                if (confirm(`Дали сте сигурни дека сакате да го сторнирате документот "${document}" (ID: ${id})?`)) {
                    const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();
                    
                    console.log('Anti-forgery token found:', !!antiForgeryToken);
                    
                    if (!antiForgeryToken) {
                        alert('Грешка: Не може да се пронајде безбедносниот токен. Освежете ја страницата.');
                        return;
                    }
                    
                    // Disable button and show loading
                    button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Обработува...');
                    
                    $.ajax({
                        url: window.location.pathname + '?handler=Storno',
                        type: 'POST',
                        data: { id: id },
                        headers: {
                            "RequestVerificationToken": antiForgeryToken
                        },
                        success: function(response) {
                            console.log('AJAX success response:', response);
                            if (response && response.success) {
                                alert('Успешно: ' + response.message);
                                location.reload();
                            } else {
                                alert('Грешка при сторнирање: ' + (response?.message || 'Непозната грешка'));
                                button.prop('disabled', false).html('<i class="fas fa-ban"></i> Сторно');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.log('AJAX error:', xhr.status, status, error);
                            console.log('Response text:', xhr.responseText);
                            alert(`Настана грешка при сторнирање (ID: ${id}).\nСтатус: ${xhr.status}\nГрешка: ${error}\nВе молиме обидете се повторно.`);
                            button.prop('disabled', false).html('<i class="fas fa-ban"></i> Сторно');
                        }
                    });
                }
            });

            // Handle Download Document button click
            $(document).on('click', '.download-document-btn', function() {
                const id = $(this).data('id');
                const document = $(this).data('document');
                const button = $(this);
                
                console.log('Download document clicked for ID:', id, 'Document:', document);
                
                // Disable button and show loading
                const originalText = button.html();
                button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Генерирање...');
                
                // Get document data and generate PDF
                generateDocumentPDF(id, document, button, originalText);
            });
        });

        function generateDocumentPDF(id, documentNumber, button, originalText) {
            const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();
            
            // Get document data from server
            $.ajax({
                url: window.location.pathname + '?handler=GetDocumentData',
                type: 'GET',
                data: { id: id },
                headers: {
                    "RequestVerificationToken": antiForgeryToken
                },
                                 success: function(data) {
                     if (data && data.success) {
                         console.log('Document data received:', data);
                         console.log('Document object:', data.document);
                         console.log('Amount in words:', data.iznosSoZborovi);
                         
                         // Update button text
                         button.html('<i class="fas fa-file-pdf"></i> Создавање PDF...');
                         
                         // Generate the document HTML
                         const documentHtml = createDocumentHTML(data.document, data.iznosSoZborovi, data.izrabotilName);
                         console.log('Generated HTML length:', documentHtml.length);
                         
                         // Create a temporary container - position off-screen but visible
                         const tempContainer = $('<div>')
                             .html(documentHtml)
                             .css({
                                 'position': 'absolute',
                                 'top': '-9999px',
                                 'left': '-9999px',
                                 'width': '800px',
                                 'background-color': 'white',
                                 'visibility': 'visible'
                             })
                             .appendTo('body');
                         
                         console.log('Temporary container created, size:', tempContainer.width(), 'x', tempContainer.height());
                         
                         // Wait a moment for the DOM to render
                         setTimeout(function() {
                             // Generate PDF from HTML
                             html2canvas(tempContainer[0], {
                                 scale: 1.5,
                                 useCORS: true,
                                 logging: true,
                                 allowTaint: true,
                                 backgroundColor: '#ffffff',
                                 width: 800,
                                 height: tempContainer.height()
                             }).then(canvas => {
                                 console.log('Canvas created, size:', canvas.width, 'x', canvas.height);
                                 
                                 // Remove temporary container
                                 tempContainer.remove();
                                 
                                 // Check if canvas has content
                                 if (canvas.width === 0 || canvas.height === 0) {
                                     console.error('Canvas has zero dimensions');
                                     alert('Грешка: Не може да се генерира PDF - празна содржина');
                                     button.prop('disabled', false).html(originalText);
                                     return;
                                 }
                                 
                                 // Create PDF
                                 const { jsPDF } = window.jspdf;
                                 const doc = new jsPDF('p', 'mm', 'a4', true);
                                 
                                 const imgData = canvas.toDataURL('image/jpeg', 0.8);
                                 console.log('Image data length:', imgData.length);
                                 
                                 const pageWidth = doc.internal.pageSize.getWidth();
                                 const pageHeight = doc.internal.pageSize.getHeight();
                                 
                                 const margin = 5;
                                 const usableWidth = pageWidth - (margin * 2);
                                 const usableHeight = pageHeight - (margin * 2);
                                 
                                 doc.addImage(imgData, 'JPEG', margin, margin, usableWidth, usableHeight);
                                 
                                 // Generate filename
                                 const safeDocumentNumber = (documentNumber || 'nepoznat').toString().replace(/[\/\\]/g, '_');
                                 const fileName = `odobruvanje_zadolzuvanje_${safeDocumentNumber}_${new Date().toISOString().slice(0, 10)}.pdf`;
                                 
                                 // Save PDF
                                 doc.save(fileName);
                                 
                                 // Reset button
                                 button.prop('disabled', false).html(originalText);
                                 
                             }).catch(function(error) {
                                 console.error('Error generating PDF:', error);
                                 alert('Грешка при генерирање на PDF: ' + error);
                                 
                                 // Remove temporary container
                                 tempContainer.remove();
                                 
                                 // Reset button
                                 button.prop('disabled', false).html(originalText);
                             });
                         }, 100); // Wait 100ms for DOM to render
                        
                    } else {
                        alert('Грешка при преземање на податоци: ' + (data?.message || 'Непозната грешка'));
                        button.prop('disabled', false).html(originalText);
                    }
                },
                error: function(xhr, status, error) {
                    console.log('AJAX error:', xhr.status, status, error);
                    alert(`Настана грешка при преземање на податоци (ID: ${id}).\nСтатус: ${xhr.status}\nГрешка: ${error}`);
                    button.prop('disabled', false).html(originalText);
                }
            });
        }

        function createDocumentHTML(document, iznosSoZborovi, izrabotilName) {
            // Format current date
            const today = new Date();
            const formattedDate = today.getDate().toString().padStart(2, '0') + '.' + 
                                (today.getMonth() + 1).toString().padStart(2, '0') + '.' + 
                                today.getFullYear();

                         return `
                 <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; position: relative; padding: 20px 40px; background-color: white;">
                     <!-- Background Logo -->
                     <div style="position: absolute; top: 20px; right: 40px; width: 150px; opacity: 0.15; z-index: 0; pointer-events: none;">
                         <img src="/images/logo/INCO_LOGO_Regular.svg" style="width: 100%; height: auto;" />
                     </div>
                     
                     <!-- Decorative Corner Elements -->
                     <div style="position: absolute; top: 10px; left: 10px; width: 20px; height: 20px; border-left: 1px solid rgba(212, 175, 55, 0.25); border-top: 1px solid rgba(212, 175, 55, 0.25);"></div>
                     <div style="position: absolute; top: 10px; right: 10px; width: 20px; height: 20px; border-right: 1px solid rgba(212, 175, 55, 0.25); border-top: 1px solid rgba(212, 175, 55, 0.25);"></div>
                     <div style="position: absolute; bottom: 10px; left: 10px; width: 20px; height: 20px; border-left: 1px solid rgba(212, 175, 55, 0.25); border-bottom: 1px solid rgba(212, 175, 55, 0.25);"></div>
                     <div style="position: absolute; bottom: 10px; right: 10px; width: 20px; height: 20px; border-right: 1px solid rgba(212, 175, 55, 0.25); border-bottom: 1px solid rgba(212, 175, 55, 0.25);"></div>
                     
                     <div style="position: relative; z-index: 1;">
                         <div style="text-align: left; margin-bottom: 20px; padding-right: 160px;">
                             <h3 style="color: #2F4F4F; margin-bottom: 5px; font-size: 22px; text-shadow: 1px 1px 1px rgba(0,0,0,0.1);">Осигурително Брокерско Друштво ИНКО АД Скопје</h3>
                             <h4 style="color: #000; margin-top: 0; font-size: 18px;">ОДОБРУВАЊЕ / ЗАДОЛЖУВАЊЕ БРОЈ: ${document.brojNaDokument || ''}</h4>
                         </div>
                    
                    <!-- Client Info Section -->
                    <div style="margin-bottom: 25px; background-color: rgba(47, 79, 79, 0.03); padding: 15px; border-radius: 6px; border: 1px solid rgba(212, 175, 55, 0.2);">
                        <h4 style="color: #2F4F4F; margin-bottom: 10px; font-size: 16px;">До: ${document.klientImePrezimeNaziv || 'Не е наведено'}</h4>
                        <div style="display: flex; justify-content: space-between; flex-wrap: wrap;">
                            <div style="flex: 1; min-width: 200px; margin-right: 15px;">
                                <p style="margin: 5px 0; font-size: 13px;"><strong>Тип на документ:</strong> ${document.tipNaDokument || ''}</p>
                                <p style="margin: 5px 0; font-size: 13px;"><strong>Тип:</strong> ${document.tip || ''}</p>
                                <p style="margin: 5px 0; font-size: 13px;"><strong>Премија/Провизија:</strong> ${document.premijaProvizija || ''}</p>
                            </div>
                            <div style="flex: 1; min-width: 200px;">
                                <p style="margin: 5px 0; font-size: 13px;"><strong>Датум на документ:</strong> ${document.datumNaDokument ? new Date(document.datumNaDokument).toLocaleDateString('en-GB') : ''}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Main Table -->
                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 25px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); background-color: white;">
                        <thead>
                            <tr style="background: linear-gradient(90deg, #2F4F4F, #1a2f2f); color: white;">
                                <th style="border: 1px solid #ddd; padding: 12px; text-align: left; font-size: 14px; width: 40%;">Опис</th>
                                <th style="border: 1px solid #ddd; padding: 12px; text-align: left; font-size: 14px; width: 35%;">По основа на фактура/полиса</th>
                                <th style="border: 1px solid #ddd; padding: 12px; text-align: right; font-size: 14px; width: 25%;">Износ во МКД</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="background-color: rgba(47, 79, 79, 0.02);">
                                <td style="border: 1px solid #ddd; padding: 12px; font-size: 13px; vertical-align: top;">
                                    ${document.opis || 'Нема опис'}
                                    <br><br>
                                    <div style="font-size: 11px; color: #666;">
                                        ${document.brojNaVleznaFaktura ? 'Влезна фактура: ' + document.brojNaVleznaFaktura : ''}
                                        ${document.brojNaVleznaFaktura && document.brojNaIzleznaFaktura ? '<br>' : ''}
                                        ${document.brojNaIzleznaFaktura ? 'Излезна фактура: ' + document.brojNaIzleznaFaktura : ''}
                                        ${document.brojNaIzleznaFakturaPremija ? '<br>Излезна фактура премија: ' + document.brojNaIzleznaFakturaPremija : ''}
                                    </div>
                                </td>
                                <td style="border: 1px solid #ddd; padding: 12px; font-size: 13px; vertical-align: top;">
                                    ${document.brojNaPolisa ? 'Полиса бр. ' + document.brojNaPolisa : ''}
                                    ${document.brojNaPolisa && document.tipNafaktura ? '<br>' : ''}
                                    ${document.tipNafaktura ? 'Тип фактура: ' + document.tipNafaktura : ''}
                                </td>
                                <td style="border: 1px solid #ddd; padding: 12px; text-align: right; font-size: 14px; font-weight: bold; vertical-align: top;">
                                    ${document.iznos ? parseFloat(document.iznos).toFixed(2) : '0.00'}
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Amount in Words -->
                    <div style="margin-bottom: 25px; background-color: rgba(47, 79, 79, 0.03); padding: 15px; border-radius: 6px; border: 1px solid rgba(212, 175, 55, 0.2);">
                        <p style="margin: 0; font-size: 13px; color: #2F4F4F;"><strong>Со букви, износ во МКД:</strong></p>
                        <p style="margin: 5px 0 0 0; font-size: 14px; font-style: italic; color: #000;">${iznosSoZborovi || 'Нула'} денари</p>
                    </div>

                                             <!-- Footer Section -->
                         <div style="display: flex; justify-content: space-between; margin: 30px 0; position: relative;">
                             <!-- Gold Accent Line -->
                             <div style="position: absolute; top: -10px; left: 0; right: 0; height: 1px; background: linear-gradient(90deg, #D4AF37, transparent); opacity: 0.3;"></div>
                             
                             <div style="text-align: center; flex: 1; padding: 0 10px;">
                                 <p style="margin-bottom: 20px;"><strong style="color: #2F4F4F; font-size: 13px;">Изработил</strong></p>                                        
                                 <p style="margin-top: 10px; font-size: 12px;">${izrabotilName || document.usernameCreated || ''}</p>
                                 <p style="margin-top: 5px; font-size: 12px;">${formattedDate}</p>
                             </div>
                             <div style="text-align: center; flex: 1; padding: 0 10px;">
                                 <p style="margin-bottom: 20px;"><strong style="color: #2F4F4F; font-size: 13px;">Примил</strong></p>
                                 <p style="border-top: 1px solid rgba(47, 79, 79, 0.3); width: 80%; margin: 0 auto;"></p>
                             </div>
                             <div style="text-align: center; flex: 1; padding: 0 10px;">
                                 <p style="margin-bottom: 20px;"><strong style="color: #2F4F4F; font-size: 13px;">Одговорно лице за потпис на документ</strong></p>
                                 <p style="border-top: 1px solid rgba(47, 79, 79, 0.3); width: 80%; margin: 0 auto;"></p>
                             </div>
                         </div>

                         <!-- Gold Accent Bottom -->
                         <div style="position: relative; margin-top: 20px;">
                             <div style="position: absolute; top: -10px; left: 0; right: 0; height: 1px; background: linear-gradient(90deg, transparent, #D4AF37, transparent); opacity: 0.3;"></div>
                             <div style="position: absolute; top: -8px; left: 0; right: 0; height: 1px; background: linear-gradient(90deg, transparent, #D4AF37, transparent); opacity: 0.2;"></div>
                             
                             <div style="display: flex; justify-content: space-between; font-size: 0.85em; background-color: rgba(47, 79, 79, 0.03); padding: 15px; border-radius: 6px; border: 1px solid rgba(212, 175, 55, 0.2); box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
                                 <div>
                                     <p style="margin: 3px 0;"><strong style="color: #2F4F4F;">ЕДБ 4080025630210</strong></p>
                                     <p style="margin: 3px 0; color: #000;">Сметка: 210 078354340168</p>
                                 </div>
                                 <div>
                                     <p style="margin: 3px 0;"><strong style="color: #2F4F4F;">МБ 7835434</strong></p>
                                     <p style="margin: 3px 0; color: #000;">Банка: НЛБ Банка АД Скопје</p>
                                 </div>
                                 <div>
                                     <p style="margin: 3px 0;"><strong style="color: #2F4F4F;">Контакт маил: <EMAIL></strong></p>
                                 </div>
                             </div>
                         </div>
                     </div>
                 </div>
             `;
        }
    </script>
}
