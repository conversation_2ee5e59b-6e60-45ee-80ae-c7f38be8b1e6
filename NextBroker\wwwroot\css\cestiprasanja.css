body {
    background-color: #f8f9fa;
    font-family: '<PERSON> Sans', '<PERSON> Sans MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS', sans-serif;
    font-size: 18px;
}

main {
    max-width: 600px; /* Slightly wider than login/registration */
    margin: 60px auto;
    padding: 40px;
    background-color: #ffffff;
    border-radius: 20px;
    box-shadow: 0 6px 18px rgba(0, 51, 102, 0.15);
    transition: box-shadow 0.3s ease;
}

main:hover {
    box-shadow: 0 8px 24px rgba(0, 51, 102, 0.2);
}

h2 {
    color: #003366;
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 40px;
    position: relative;
    padding-bottom: 20px;
    font-family: 'Gill Sans', 'Gill Sans MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS', sans-serif !important;
}

h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 60%;
    height: 3px;
    background-color: rgba(76, 175, 80, 0.7);
    transform: translateX(-50%);
    transition: width 0.3s ease, left 0.3s ease;
}

main:hover h2::after {
    width: 70%;
    left: 50%;
}

.form-group {
    margin-bottom: 25px;
}

label {
    color: #003366;
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 8px;
    display: block;
}

.form-control {
    border: 2px solid rgba(0, 51, 102, 0.2);
    border-radius: 12px;
    padding: 12px;
    width: 100%;
    transition: border-color 0.3s ease;
    font-size: 1rem;
}

.form-control:focus {
    border-color: #003366;
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 51, 102, 0.1);
}

textarea.form-control {
    resize: vertical;
}

.btn-primary {
    background-color: #004080; /* Slightly different shade of blue */
    border: none;
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.3s;
    width: 100%;
    font-size: 1.1rem;
    margin-top: 10px;
}

.btn-primary:hover {
    background-color: #005cb2;
    transform: scale(1.05);
}

.btn-primary:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 64, 128, 0.3);
}

.alert {
    margin-top: 20px;
    padding: 15px;
    border-radius: 12px;
    font-size: 1.1rem;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

