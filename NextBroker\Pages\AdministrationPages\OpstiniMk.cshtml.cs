using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using RazorPortal.Services;

namespace NextBroker.Pages.AdministrationPages
{
    public class OpstiniMkModel : SecurePageModel
    {
        public List<OpstiniMk> OpstiniList { get; set; }
        public bool IsEditing { get; set; }

        public OpstiniMkModel(IConfiguration configuration)
            : base(configuration)
        {
            OpstiniList = new List<OpstiniMk>();
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("OpstiniMk"))
            {
                return RedirectToAccessDenied();
            }

            LoadOpstini();
            return Page();
        }

        public IActionResult OnPostToggleEdit()
        {
            if (!IsEditing)
            {
                IsEditing = true;
            }
            else
            {
                IsEditing = false;
            }
            
            LoadOpstini();
            return Page();
        }

        public IActionResult OnPostSaveChanges([FromBody] List<OpstiniMk> updates)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                foreach (var opstina in updates)
                {
                    // First, get the current values from the database
                    string currentValues = "SELECT Opstina, Kod FROM ListaOpstini WHERE Id = @Id";
                    using (SqlCommand getCommand = new SqlCommand(currentValues, connection))
                    {
                        getCommand.Parameters.AddWithValue("@Id", opstina.Id);
                        using (SqlDataReader reader = getCommand.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                string currentOpstina = reader.IsDBNull(0) ? string.Empty : reader.GetString(0);
                                int currentKod = reader.IsDBNull(1) ? 0 : reader.GetInt32(1);

                                // Only update if values have changed
                                if (currentOpstina != opstina.Opstina || currentKod != opstina.Kod)
                                {
                                    reader.Close(); // Close reader before executing update
                                    using (SqlCommand updateCommand = new SqlCommand(
                                        "UPDATE ListaOpstini SET Opstina = @Opstina, Kod = @Kod, DateModified = GETDATE() " +
                                        "WHERE Id = @Id", connection))
                                    {
                                        updateCommand.Parameters.AddWithValue("@Id", opstina.Id);
                                        updateCommand.Parameters.AddWithValue("@Opstina", opstina.Opstina);
                                        updateCommand.Parameters.AddWithValue("@Kod", opstina.Kod);
                                        updateCommand.ExecuteNonQuery();
                                    }
                                }
                            }
                        }
                    }
                }
            }
            IsEditing = false;
            return new JsonResult(new { success = true });
        }

        public IActionResult OnPostAddOpstina(string opstina, int kod)
        {
            if (string.IsNullOrWhiteSpace(opstina))
            {
                return RedirectToPage();
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(
                    "INSERT INTO ListaOpstini (Opstina, Kod, DateCreated) VALUES (@Opstina, @Kod, GETDATE())", 
                    connection))
                {
                    command.Parameters.AddWithValue("@Opstina", opstina);
                    command.Parameters.AddWithValue("@Kod", kod);
                    command.ExecuteNonQuery();
                }
            }
            
            return RedirectToPage();
        }

        public IActionResult OnPostDeleteOpstina([FromBody] DeleteOpstinaMk model)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(
                    "DELETE FROM ListaOpstini WHERE Id = @Id", 
                    connection))
                {
                    command.Parameters.AddWithValue("@Id", model.Id);
                    command.ExecuteNonQuery();
                }
            }
            
            return new JsonResult(new { success = true });
        }

        private void LoadOpstini()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            OpstiniList.Clear();
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand("SELECT Id, DateCreated, DateModified, Opstina, Kod FROM ListaOpstini", connection))
                {
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            OpstiniList.Add(new OpstiniMk
                            {
                                Id = reader.GetInt32(0),
                                DateCreated = reader.IsDBNull(1) ? DateTime.MinValue : reader.GetDateTime(1),
                                DateModified = reader.IsDBNull(2) ? DateTime.MinValue : reader.GetDateTime(2),
                                Opstina = reader.IsDBNull(3) ? string.Empty : reader.GetString(3),
                                Kod = reader.IsDBNull(4) ? 0 : reader.GetInt32(4)
                            });
                        }
                    }
                }
            }
        }
    }

    public class OpstiniMk
    {
        public int Id { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime DateModified { get; set; }
        public string Opstina { get; set; } = string.Empty;
        public int Kod { get; set; }
    }

    public class DeleteOpstinaMk
    {
        public int Id { get; set; }
    }
} 