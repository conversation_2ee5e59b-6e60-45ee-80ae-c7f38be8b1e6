/* Ensure html and body cover the full viewport */
html, body {
    height: 100%;
    margin: 0;
    overflow-x: hidden;
}

/* Apply the gradient background */
body {
    background: white; /* Skyblue to white gradient */
    font-family: 'Arial', sans-serif;
    color: #333; /* Default text color */
    display: flex;
    flex-direction: column;
    min-height: 100vh; /* Full viewport height */
}

/* Header styling */
header {
    background: #FFFFFF; /* White background */
    color: #003366; /* Dark blue text */
    padding: 0; /* Remove padding */
    position: relative;
    z-index: 1000; /* Ensure header is on top */
}

/* Navbar styling */
.navbar {
    background: inherit;
    margin-bottom: 0;
    position: relative;
    z-index: 1001;
    white-space: nowrap;
    padding: 0.2rem 1rem !important; /* Make navbar shorter */
}

/* Navbar links */
.navbar-nav .nav-link {
    color: #003366 !important; /* Dark blue text */
    padding: 8px 10px !important;  /* Reduced padding */
    font-size: 0.9rem;  /* Slightly smaller font */
}

    .navbar-nav .nav-link:hover {
        color: #0056b3 !important; /* Darker blue on hover */
    }

/* Main content area */
main {
    padding: 1px 0 60px 0; /* Add bottom padding of 60px */
    flex: 1;
}

/* Form styling */
form {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    width: 100%;  /* Allow form to take full width */
    max-width: none;  /* Remove max-width limitation */
    margin: auto;
}

/* Textboxes and Inputs */
input[type="text"],
input[type="password"],
input[type="email"],
textarea {
    width: 100%;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ccc;
    box-sizing: border-box;
    margin-bottom: 10px;
}

/* Buttons */
button {
    background-color: #003366; /* Dark blue */
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

    button:hover {
        background-color: #0033666e; /* Slightly darker blue */
    }

/* Fancy Font Example */
h1, h2, h3, h4, h5, h6 {
    color: #003366; /* Dark blue */
    font-family: 'Georgia', serif; /* Customize font-family as needed */
}

/* Additional Styling */
p {
    color: #333; /* Dark grey for text */
}

.hero-section {
    background-image: url('/images/KupiPolisaHome/herobg.jpg');
    background-size: cover; /* Make sure it covers the area */
    background-position: center; /* Center the image */
    height: 30vh; /* Set the height you want */
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    text-align: center;
    position: relative;
    width: 100vw; /* Set to full viewport width */
    margin-left: calc(-50vw + 50%); /* Centering trick */
    overflow: hidden;
}

    .hero-section::before {
        content: ""; /* Required to create a pseudo-element */
        position: absolute; /* Position it absolutely */
        top: 0; /* Align it to the top */
        left: 0; /* Align it to the left */
        right: 0; /* Align it to the right */
        bottom: 0; /* Align it to the bottom */
        background-color: rgba(0, 0, 0, 0.4); /* Dark overlay */
        z-index: 1;
    }

    .hero-section h1 {
        text-align: center;
        margin-bottom: 20px;
        font-size: clamp(10px, 2.5vw, 50px);
        color: aliceblue;
        position: relative;
        z-index: 2;
    }

.ticket-section .card {
    margin: 20px;
    color: steelblue;
}

.highlighted-text {
    color: #E74C3C;
    font-weight: bold;
}

/* Card overlay styles */
.card-overlay {
    position: relative;
    overflow: hidden;
    border: none;
    align-items: center;
    padding-top: 10px;
    height: 300px;
}

    .card-overlay img {
        height: 100%;
        object-fit: cover;
    }

.card-img-overlay {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.5);
    text-align: center;
    padding: 20px;
    transition: transform 0.3s ease, background-color 0.4s ease-in-out;
}

.card-overlay:hover .card-img-overlay {
    background-color: rgba(0, 0, 0, 0.1); /* darker overlay on hover */
    transform: scale(1.05);
}

.card-title {
    font-size: 1.5rem;
    font-weight: bold;
    text-transform: uppercase;
}

/* Container styling */
.container, .container-fluid {
    position: relative;
    z-index: 1;
    padding-left: 0 !important;
    padding-right: 0 !important;
    padding-top: 20px;
    max-width: 95% !important;  /* Much wider container */
    margin: 0 auto;
}

.card_kupi {
    position: relative;
    transition: transform 0.3s, z-index 0.3s, border-radius 0.3s;
    padding: 0;
    margin: -11.5px;
    z-index: 1;
}

    .card_kupi:hover {
        transform: scale(1.09);
        z-index: 10;
        border-radius: 20px;
    }

.styled-separator {
    border: 0;
    height: 4px; /* Thickness of the separator */
    background-image: linear-gradient(to right, #003366 0%, #0056b3 100%); /* Gradient from dark blue to light blue */
    width: 100vw; /* Span the entire viewport width */
    position: relative;
    left: calc(-50vw + 50%); /* Center the separator */
    margin: 20px 0;
}

.kupi {
    margin-top: -50px;
    position: relative;
    top: 50px;
    text-align: center;
    background-color: rgba(173, 216, 230, 0.5); /* Light blue with slight transparency */
    border: 1px solid rgba(255, 255, 255, 0.2); /* Very light white border */
    color: #fff; /* Darker text for contrast */
    font-weight: bold;
    border-radius: 5px; /* Rounded corners */
    transition: background-color 0.3s ease, color 0.3s ease, border 0.3s ease, transform 0.3s ease;
}

    .kupi:hover {
        background-color: rgba(31, 83, 132, 0.9); /* Transparent white on hover */
        color: #fff;
        transform: scale(1.1);
    }

.error-message {
    color: red;
}

.success-message {
    color: green;
}

/* Footer styling */
footer {
    background: #FFFFFF;
    color: #003366;
    padding: 1px 0;
    text-align: center;
    border-top: 1px solid #ddd;
    margin-top: auto;
    position: relative; /* Change to relative */
    width: 100%; /* Ensure it spans the full width */
    bottom: 0; /* Align it to the bottom */
    z-index: 1000; /* Ensure the footer is on top */
}

    footer a {
        color: #003366; /* Dark blue links */
    }

        footer a:hover {
            color: #0056b3; /* Darker blue on hover */
        }

/* Common button styles */
.btn-primary {
    background-color: rgba(0, 51, 102, 0.8); /* Same background color */
    color: white !important; /* Ensure text is white */
    border-radius: 5px; /* Rounded corners */
    padding: 0.5rem 1rem; /* Consistent padding */
    margin-left: 0.5rem; /* Optional: space between buttons */
    transition: background-color 0.3s ease, border-radius 0.3s ease;
    text-align: center; /* Center text */
    border-color: #003366; /* Border matches the background */
    display: inline-block; /* Buttons behave similarly to links */
    flex-grow: 0; /* Prevents the button from stretching */
}

    /* Hover effect */
    .btn-primary:hover {
        background-color: rgba(0, 51, 102, 0.3); /* Darker background on hover */
        border-radius: 8px; /* More rounded corners on hover */
        color: #003366 !important; /* Blue text on hover */
        border-color: #002a50; /* Slightly darker border on hover */
    }

    /* Optional: Add spacing between buttons */
    .btn-primary + .btn-primary {
        border-left: 1px solid rgba(211, 211, 211, 0.6); /* Light gray border */
        padding-left: 1rem; /* Space for the border */
    }

/* Override the carousel caption text color */
.carousel-caption h2,
.carousel-caption p {
    color: white !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6); /* Optional: adds a shadow to make the text more readable */
}



.sticky-top {
    top: 0; /* Keep it at the top */
    z-index: 1030; /* Ensure it is above other content */
}

/* Bottom bar styling */
.bottom-bar {
    position: fixed; /* Fixes the bar at the bottom */
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white; /* White background */
    text-align: center; /* Center-align text */
    padding: 10px; /* Padding for some space */
    border-top: 1px solid #ddd; /* Light top border */
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
    z-index: 1000; /* Ensure it's above other elements */
    color: #003366;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
}
.footer-link{
    color: green !important;
}

.nextbyte_logo {
    height: 45px;
    width: auto;
}

.dropdown-submenu {
    position: relative;
}

.dropdown-submenu .dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -1px;
}

.dropdown-submenu:hover > .dropdown-menu {
    display: block;
}

.dropdown-submenu > a:after {
    display: block;
    content: " ";
    float: right;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 5px 0 5px 5px;
    border-left-color: #cccccc;
    margin-top: 5px;
    margin-right: -10px;
}

.navbar-nav {
    flex-wrap: nowrap;
}

.language-link {
    text-decoration: none;
    color: #666;
    padding: 4px 8px;
}

.language-link:hover {
    color: #333;
}

.language-link.active {
    color: #0d6efd;
    font-weight: 500;
}

/* Add specific styling for DodajPolisa form */
form.dodaj-polisa-form,
form[class*="dodaj-"] {
    width: 85% !important;
    margin: 0 auto !important;
    max-width: 1600px !important;
}
