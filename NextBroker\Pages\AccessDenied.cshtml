@page
@model RazorPortal.Pages.AccessDeniedModel
@{
    ViewData["Title"] = "Забранет пристап";
    Layout = "_Layout";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card error-card">
                <div class="card-body text-center">
                    <div class="error-icon">
                        <i class="fas fa-lock"></i>
                    </div>
                    <h1 class="error-title">Забранет пристап</h1>
                    <h2 class="error-subtitle">Немате привилегија</h2>
                    
                    <p class="error-message">Немате привилегија за пристап до оваа страница. Обратете се до администраторот.</p>

                    <a href="/" class="btn btn-primary mt-3"> 
                        <i class="fas fa-home"></i> Кон почетна страница
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
 
<style>
    .error-card {
        background-color: #f8f9fc;
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 8px rgba(0, 51, 102, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .error-card:hover {
        transform: scale(1.02);
        box-shadow: 0 6px 12px rgba(0, 51, 102, 0.2);
    }

    .error-icon {
        font-size: 5rem;
        color: rgba(0, 51, 102, 0.8);
        margin-bottom: 20px;
    }

    .error-title {
        font-size: 3.5rem;
        color: #003366;
        margin-bottom: 0;
    } 

    .error-subtitle {
        font-size: 1.5rem;
        color: #0056b3;
        margin-top: 0;
    }

    .error-message {
        font-size: 1.1rem;
        color: #333;
        margin: 20px 0;
    }

    .btn-primary {
        background-color: rgba(0, 51, 102, 0.8);
        color: white !important;
        border-radius: 5px;
        padding: 0.5rem 1rem;
        margin-left: 0.5rem;
        transition: background-color 0.3s ease, border-radius 0.3s ease, color 0.3s ease;
        text-align: center;
        border-color: #003366;
        display: inline-block;
        flex-grow: 0;
    }

    .btn-primary:hover {
        background-color: rgba(0, 51, 102, 0.3);
        border-radius: 8px;
        color: #003366 !important;
        border-color: #002a50;
    }
</style>
