using System.Data;
using Microsoft.Data.SqlClient;

namespace NextBroker.Services
{
    public class NotificationService
    {
        private readonly IConfiguration _configuration;

        public NotificationService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public async Task<IEnumerable<NotificationModel>> GetLatestNotifications(string username)
        {
            var notifications = new List<NotificationModel>();
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using var command = new SqlCommand(@"
                    SELECT Id, 
                           DateCreated, 
                           NotificationTitle,
                           NotificationContent,
                           MarkRead 
                    FROM UserNotifications 
                    WHERE Username = @Username
                    ORDER BY DateCreated DESC", 
                    connection);
                
                command.Parameters.AddWithValue("@Username", username);
                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    notifications.Add(new NotificationModel
                    {
                        Id = reader.GetInt64(0),
                        DateCreated = reader.GetDateTime(1),
                        Title = reader.IsDBNull(2) ? string.Empty : reader.GetString(2),
                        Content = reader.IsDBNull(3) ? string.Empty : reader.GetString(3),
                        IsRead = reader.GetBoolean(4)
                    });
                }
            }
            return notifications;
        }

        public async Task<IEnumerable<string>> GetAllUsers()
        {
            var users = new List<string>();
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using var command = new SqlCommand(
                    "SELECT DISTINCT Username FROM UserNotifications", 
                    connection);
                
                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    users.Add(reader.GetString(0));
                }
            }
            return users;
        }

        public async Task<bool> MarkAsRead(long id, string username)
        {
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using var command = new SqlCommand(@"
                    UPDATE UserNotifications 
                    SET MarkRead = 1 
                    WHERE Id = @Id AND Username = @Username", 
                    connection);
                
                command.Parameters.AddWithValue("@Id", id);
                command.Parameters.AddWithValue("@Username", username);
                var rowsAffected = await command.ExecuteNonQueryAsync();
                return rowsAffected > 0;
            }
        }

        public async Task<int> GetUnreadCount(string username)
        {
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using var command = new SqlCommand(
                    "SELECT COUNT(*) FROM UserNotifications WHERE Username = @Username AND MarkRead = 0", 
                    connection);
                
                command.Parameters.AddWithValue("@Username", username);
                return (int)await command.ExecuteScalarAsync();
            }
        }
    }

    public class NotificationModel
    {
        public long Id { get; set; }
        public DateTime DateCreated { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public bool IsRead { get; set; }
    }
} 