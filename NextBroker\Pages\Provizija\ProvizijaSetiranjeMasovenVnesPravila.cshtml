@page
@model NextBroker.Pages.Provizija.ProvizijaSetiranjeMasovenVnesPravilaModel
@{
    ViewData["Title"] = "Масовен внес на правила";
}

@section Styles {
    <link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/css/toastr.min.css">
    <style>
        .checkbox-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            padding: 10px;
        }
        .checkbox-group {
            margin-bottom: 10px;
        }
        .checkbox-group-header {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
            padding: 5px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .checkbox-item {
            margin: 5px 0;
        }
        .results-section {
            margin-top: 20px;
        }
        .results-list {
            max-height: 200px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border-radius: 0.25rem;
            padding: 10px;
        }
    </style>
}

<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">@ViewData["Title"]</h3>
        </div>
        @if (!ModelState.IsValid)
        {
            <div class="alert alert-danger mx-3 mt-3">
                <div asp-validation-summary="All" class="text-danger mb-0"></div>
            </div>
        }
        <div class="card-body">
            <form method="post">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="KlientiIdOsiguritel">Осигурител</label>
                            <select asp-for="KlientiIdOsiguritel" asp-items="Model.Osiguriteli" class="form-control">
                                <option value="">-- Изберете осигурител --</option>
                            </select>
                            <span asp-validation-for="KlientiIdOsiguritel" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Примач на провизија</label>
                            <div class="input-group">
                                <input type="text" id="searchPrimacProvizija" class="form-control" placeholder="Пребарај по МБ, ЕМБГ, ЕДБ, Име, Презиме..." value="@Model.PrimacProvizijaDisplayText" />
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-secondary" onclick="clearPrimacProvizija()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <input type="hidden" asp-for="KlientiIdPrimacProvizija" id="KlientiIdPrimacProvizija" />
                            <input type="hidden" asp-for="PrimacProvizijaDisplayText" id="PrimacProvizijaDisplayText" />
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Тип на клиент</label>
                            <select asp-for="KlientProvizijaId" asp-items="Model.KlientProvizii" class="form-control">
                                <option value="">-- Изберете тип на провизија --</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <div class="form-group">
                            <label>Производи <span class="text-danger">*</span></label>
                            <small class="form-text text-muted">Изберете еден или повеќе производи. Секој производ автоматски ја содржи својата класа на осигурување.</small>
                            <div class="checkbox-list">
                                <div class="checkbox-item">
                                    <label>
                                        <input type="checkbox" id="selectAllProdukti" /> <strong>Избери сите</strong>
                                    </label>
                                </div>
                                <hr />
                                @{
                                    var grupiraniProdukti = Model.Produkti.GroupBy(p => p.Group?.Name).ToList();
                                }
                                @foreach (var grupa in grupiraniProdukti)
                                {
                                    @if (!string.IsNullOrEmpty(grupa.Key))
                                    {
                                        <div class="checkbox-group">
                                            <div class="checkbox-group-header">@grupa.Key</div>
                                            @foreach (var produkt in grupa)
                                            {
                                                <div class="checkbox-item" style="margin-left: 15px;">
                                                    <label>
                                                        <input type="checkbox" name="SelectedProduktiIds" value="@produkt.Value" class="produkt-checkbox" />
                                                        @produkt.Text
                                                    </label>
                                                </div>
                                            }
                                        </div>
                                    }
                                    else
                                    {
                                        @foreach (var produkt in grupa)
                                        {
                                            <div class="checkbox-item">
                                                <label>
                                                    <input type="checkbox" name="SelectedProduktiIds" value="@produkt.Value" class="produkt-checkbox" />
                                                    @produkt.Text
                                                </label>
                                            </div>
                                        }
                                    }
                                }
                            </div>
                            <span asp-validation-for="SelectedProduktiIds" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="ProcentNaProvizijaZaFizickiLica">Процент на провизија за физички лица</label>
                            <input asp-for="ProcentNaProvizijaZaFizickiLica" class="form-control" type="number" step="0.01" />
                            <span asp-validation-for="ProcentNaProvizijaZaFizickiLica" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="ProcentNaProvizijaZaPravniLica">Процент на провизија за правни лица</label>
                            <input asp-for="ProcentNaProvizijaZaPravniLica" class="form-control" type="number" step="0.01" />
                            <span asp-validation-for="ProcentNaProvizijaZaPravniLica" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="SifrarnikNacinNaPresmetkaProvizijaId">Начин на пресметка</label>
                            <select asp-for="SifrarnikNacinNaPresmetkaProvizijaId" asp-items="Model.NaciniNaPresmetka" class="form-control">
                                <option value="">-- Изберете начин на пресметка --</option>
                            </select>
                            <span asp-validation-for="SifrarnikNacinNaPresmetkaProvizijaId" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="SifrarnikBrutoNetoProvizija">Тип на провизија</label>
                            <select asp-for="SifrarnikBrutoNetoProvizija" asp-items="Model.TipoviProvizija" class="form-control">
                                <option value="">-- Изберете тип на провизија --</option>
                            </select>
                            <span asp-validation-for="SifrarnikBrutoNetoProvizija" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="DatumVaziOd">Важи од</label>
                            <input asp-for="DatumVaziOd" class="form-control" />
                            <span asp-validation-for="DatumVaziOd" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="DatumVaziDo">Важи до</label>
                            <input asp-for="DatumVaziDo" class="form-control" />
                            <span asp-validation-for="DatumVaziDo" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">Зачувај правила</button>
                        <a asp-page="./ProvizijaSetiranje" class="btn btn-secondary">Назад</a>
                    </div>
                </div>
            </form>

            <!-- Results Section -->
            @if (Model.SuccessfullyAdded?.Any() == true || Model.AlreadyExisting?.Any() == true)
            {
                <div class="results-section">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Резултати од обработката</h5>
                        </div>
                        <div class="card-body">
                            @if (Model.SuccessfullyAdded?.Any() == true)
                            {
                                <div class="alert alert-success">
                                    <h6><i class="fas fa-check-circle"></i> Успешно додадени правила (@Model.SuccessfullyAdded.Count):</h6>
                                    <div class="results-list">
                                        <ul class="mb-0">
                                            @foreach (var item in Model.SuccessfullyAdded)
                                            {
                                                <li>@item</li>
                                            }
                                        </ul>
                                    </div>
                                </div>
                            }

                            @if (Model.AlreadyExisting?.Any() == true)
                            {
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle"></i> Веќе постоечки правила (@Model.AlreadyExisting.Count):</h6>
                                    <div class="results-list">
                                        <ul class="mb-0">
                                            @foreach (var item in Model.AlreadyExisting)
                                            {
                                                <li>@item</li>
                                            }
                                        </ul>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
    <script>
        // Configure toastr
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "timeOut": "5000"
        };

        // Display status message if exists and we have processing results
        @if (!string.IsNullOrEmpty(Model.StatusMessage) && (Model.SuccessfullyAdded?.Any() == true || Model.AlreadyExisting?.Any() == true))
        {
            if (Model.StatusMessage.Contains("успешно"))
            {
                <text>
                toastr.success('@Model.StatusMessage');
                </text>
            }
            else
            {
                <text>
                toastr.error('@Model.StatusMessage');
                </text>
            }
        }

        // Date validation function
        function validateDateRange() {
            var datumVaziOd = $('#DatumVaziOd').val();
            var datumVaziDo = $('#DatumVaziDo').val();
            
            if (datumVaziOd && datumVaziDo) {
                var odDate = new Date(datumVaziOd);
                var doDate = new Date(datumVaziDo);
                
                if (odDate > doDate) {
                    $('#DatumVaziOd').addClass('is-invalid');
                    $('#DatumVaziDo').addClass('is-invalid');
                    
                    // Show error message
                    if (!$('#dateRangeError').length) {
                        $('#DatumVaziOd').after('<div id="dateRangeError" class="invalid-feedback">Датумот "Важи од" мора да биде пред или еднаков на датумот "Важи до".</div>');
                    }
                    return false;
                } else {
                    $('#DatumVaziOd').removeClass('is-invalid');
                    $('#DatumVaziDo').removeClass('is-invalid');
                    $('#dateRangeError').remove();
                    return true;
                }
            } else {
                $('#DatumVaziOd').removeClass('is-invalid');
                $('#DatumVaziDo').removeClass('is-invalid');
                $('#dateRangeError').remove();
                return true;
            }
        }

        // Setup checkbox functionality
        function setupCheckboxes() {
            // Select All Produkti functionality
            $('#selectAllProdukti').on('change', function() {
                $('.produkt-checkbox').prop('checked', this.checked);
            });

            // Update Select All Produkti when individual checkboxes change
            $('.produkt-checkbox').on('change', function() {
                var allChecked = $('.produkt-checkbox').length === $('.produkt-checkbox:checked').length;
                $('#selectAllProdukti').prop('checked', allChecked);
            });
        }

        // Setup Primac Provizija search functionality
        function setupPrimacProvizijaSearch() {
            var searchInput = $('#searchPrimacProvizija');

            // Initialize autocomplete
            searchInput.autocomplete({
                minLength: 2,
                source: function(request, response) {
                    $.get('?handler=SearchKlienti', { mb: request.term })
                        .done(function(data) {
                            if (!data || data.length === 0) {
                                response([]);
                                return;
                            }

                            var results = data.map(function(item) {
                                var displayText = '';
                                var details = '';
                                if (item.tip === 'F') {
                                    displayText = (item.ime + ' ' + item.prezime).trim();
                                    if (item.embg) details = 'ЕМБГ: ' + item.embg;
                                } else {
                                    displayText = item.naziv;
                                    if (item.mb) details = 'МБ: ' + item.mb;
                                    else if (item.edb) details = 'ЕДБ: ' + item.edb;
                                }
                                return {
                                    label: details ? displayText + ' (' + details + ')' : displayText,
                                    value: displayText,
                                    item: item
                                };
                            });
                            response(results);
                        })
                        .fail(function() {
                            response([]);
                        });
                },
                select: function(event, ui) {
                    $('#KlientiIdPrimacProvizija').val(ui.item.item.id);
                    $('#PrimacProvizijaDisplayText').val(ui.item.value);
                    $('#searchPrimacProvizija').val(ui.item.value);
                    return false;
                }
            });

            // Add request verification token to all AJAX requests
            $.ajaxSetup({
                beforeSend: function(xhr) {
                    var token = $('input:hidden[name="__RequestVerificationToken"]').val();
                    xhr.setRequestHeader("RequestVerificationToken", token);
                }
            });

            // Custom rendering of autocomplete items
            searchInput.autocomplete("instance")._renderItem = function(ul, item) {
                return $("<li>")
                    .append("<div>" + item.label + "</div>")
                    .appendTo(ul);
            };
        }

        // Modify clear functionality
        window.clearPrimacProvizija = function() {
            $('#searchPrimacProvizija').val('');
            $('#KlientiIdPrimacProvizija').val('');
            $('#PrimacProvizijaDisplayText').val('');
        };

        // Initialize all functionality when document is ready
        $(document).ready(function() {
            setupCheckboxes();
            setupPrimacProvizijaSearch();
            
            // Setup date validation
            $('#DatumVaziOd, #DatumVaziDo').on('change', function() {
                validateDateRange();
            });
            
            // Form submission validation
            $('form').on('submit', function(e) {
                if (!validateDateRange()) {
                    e.preventDefault();
                    toastr.error('Ве молиме поправете ги грешките во датумите пред да продолжите.');
                    return false;
                }
            });
        });
    </script>
}
