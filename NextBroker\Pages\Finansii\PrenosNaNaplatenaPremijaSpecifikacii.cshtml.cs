using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using System.Text.Json.Serialization;
using OfficeOpenXml;

namespace NextBroker.Pages.Finansii
{
    public class PrenosNaNaplatenaPremijaSpecifikaciiModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public PrenosNaNaplatenaPremijaSpecifikaciiModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
            TipoviFakturi = new List<SelectListItem>
            {
                new SelectListItem("Влезна фактура кон брокер", "Влезна фактура кон брокер"),
                new SelectListItem("Влезна фактура кон клиент", "Влезна фактура кон клиент")
            };
        }

        [BindProperty]
        public FilterModel Filter { get; set; } = new();
        public IEnumerable<SelectListItem> Osiguriteli { get; set; }
        public IEnumerable<SelectListItem> KlasiOsiguruvanje { get; set; }
        public IEnumerable<SelectListItem> TipoviFakturi { get; set; }
        public List<PolisaModel> FilteredPolisi { get; set; } = new();

        public class FilterModel
        {
            [Required(ErrorMessage = "Осигурител е задолжително поле")]
            [Display(Name = "Осигурител")]
            public long? KlientiIdOsiguritel { get; set; }

            [Display(Name = "Класа на осигурување")]
            public int? KlasiOsiguruvanjeIdKlasa { get; set; }
            
            [Display(Name = "Тип на фактура")]
            public string TipNaFaktura { get; set; }

            [Display(Name = "Датум на издавање од")]
            public DateTime? DatumNaIzdavanjeOd { get; set; }

            [Display(Name = "Датум на издавање до")]
            public DateTime? DatumNaIzdavanjeDo { get; set; }
        }

        public class PolisaModel
        {
            public long Id { get; set; }
            public long KlientiIdOsiguritel { get; set; }
            public string OsiguritelNaziv { get; set; }
            public int KlasiOsiguruvanjeIdKlasa { get; set; }
            public string KlasaIme { get; set; }
            public string BrojNaPolisa { get; set; }
            public decimal? PlatenoOdDogovoruvac { get; set; }
            public decimal? IznosZaPlakanjeVoRok { get; set; }
            public decimal? DolziDogovoruvac { get; set; }
            public string BrojNaFakturaVlezna { get; set; }
            public DateTime? DatumNaFakturaVlezna { get; set; }
            public DateTime? RokNaplakjanjeFakturaVlezna { get; set; }
            public decimal VkupnaPremijaZaPlakanje { get; set; }
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("PrenosNaNaplatenaPremijaSpecifikacii"))
            {
                return RedirectToAccessDenied();
            }

            await LoadOsiguriteli();
            await LoadKlasiOsiguruvanje();
            return Page();
        }

        private async Task LoadOsiguriteli()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Naziv 
                    FROM Klienti 
                    WHERE Osiguritel = 1 
                    ORDER BY Naziv", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Naziv"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Osiguriteli = items;
                }
            }
        }

        private async Task LoadKlasiOsiguruvanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, KlasaIme 
                    FROM KlasiOsiguruvanje 
                    WHERE Id in ('1','2','3','8','9','10','18','19')
                    ORDER BY KlasaIme", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    items.Add(new SelectListItem("Сите", ""));
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["KlasaIme"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    KlasiOsiguruvanje = items;
                }
            }
        }

        private async Task<decimal> GetVkupnaPremijaZaPlakanje(SqlConnection connection, long polisaId)
        {
            using (SqlCommand cmd = new SqlCommand("SELECT dbo.VratiPolisaVkupnaPremijaZaPlakanjePoPolisaId(@PolisaId)", connection))
            {
                cmd.Parameters.AddWithValue("@PolisaId", polisaId);
                var result = await cmd.ExecuteScalarAsync();
                return result != DBNull.Value ? Convert.ToDecimal(result) : 0m;
            }
        }

        public async Task<IActionResult> OnPostFilterPolisiAsync()
        {
            if (!ModelState.IsValid)
            {
                await LoadOsiguriteli();
                await LoadKlasiOsiguruvanje();
                return Page();
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                
                var query = @"
                    SELECT p.Id,
                           p.KlientiIdOsiguritel,
                           k.Naziv as OsiguritelNaziv,
                           p.KlasiOsiguruvanjeIdKlasa,
                           ko.KlasaIme,
                           p.BrojNaPolisa,

                        dbo.VratiPolisaVkupnaPremijaZaPlakanjePoPolisaId(p.id) as [VkupnaPremijaZaPlakanje],
                           dbo.VratiPolisaUplatenIznos(p.Id) as [PlatenoOdDogovoruvac],
                           dbo.VratiPolisaIznosZaPlakanjeVoRokOdFakturaIzlezna(p.ID) as [IznosZaPlakanjeVoRok],
                           CASE 
                           WHEN dbo.VratiPolisaPosledenDatumNaUplata(p.id) <= p.RokNaPlakjanjeFakturaIzlezna 
                           THEN dbo.VratiPolisaIznosZaPlakanjeVoRokOdFakturaIzlezna(p.id) - dbo.VratiPolisaUplatenIznos(p.Id)
                           ELSE dbo.VratiPolisaIznosZaPlakanjePoPolisaId(p.id) - dbo.VratiPolisaUplatenIznos(p.Id)
                           END AS [DolziDogovoruvac],   

                           p.BrojNaFakturaVlezna,
                           p.DatumNaFakturaVlezna,
                           p.RokNaPlakjanjeFakturaVlezna
                    FROM Polisi p
                    LEFT JOIN Klienti k ON p.KlientiIdOsiguritel = k.Id
                    LEFT JOIN KlasiOsiguruvanje ko ON p.KlasiOsiguruvanjeIdKlasa = ko.Id
                    WHERE (p.Storno is null or p.Storno = 0)
                    AND p.BrojNaFakturaVlezna is null
                    AND p.KlientiIdOsiguritel = @KlientiIdOsiguritel
                    AND p.TipNaFaktura = @TipNaFaktura";

                if (Filter.KlasiOsiguruvanjeIdKlasa.HasValue)
                {
                    query += " AND p.KlasiOsiguruvanjeIdKlasa = @KlasiOsiguruvanjeIdKlasa";
                }

                if (Filter.DatumNaIzdavanjeOd.HasValue)
                {
                    query += " AND p.DatumNaIzdavanje >= @DatumNaIzdavanjeOd";
                }

                if (Filter.DatumNaIzdavanjeDo.HasValue)
                {
                    query += " AND p.DatumNaIzdavanje <= @DatumNaIzdavanjeDo";
                }

                using (SqlCommand cmd = new SqlCommand(query, connection))
                {
                    cmd.Parameters.AddWithValue("@KlientiIdOsiguritel", Filter.KlientiIdOsiguritel);
                    cmd.Parameters.AddWithValue("@TipNaFaktura", Filter.TipNaFaktura);
                    if (Filter.KlasiOsiguruvanjeIdKlasa.HasValue)
                    {
                        cmd.Parameters.AddWithValue("@KlasiOsiguruvanjeIdKlasa", Filter.KlasiOsiguruvanjeIdKlasa.Value);
                    }
                    if (Filter.DatumNaIzdavanjeOd.HasValue)
                    {
                        cmd.Parameters.AddWithValue("@DatumNaIzdavanjeOd", Filter.DatumNaIzdavanjeOd.Value);
                    }
                    if (Filter.DatumNaIzdavanjeDo.HasValue)
                    {
                        cmd.Parameters.AddWithValue("@DatumNaIzdavanjeDo", Filter.DatumNaIzdavanjeDo.Value);
                    }

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    FilteredPolisi = new List<PolisaModel>();
                    while (await reader.ReadAsync())
                    {
                        var polisa = new PolisaModel
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            KlientiIdOsiguritel = reader.GetInt64(reader.GetOrdinal("KlientiIdOsiguritel")),
                            OsiguritelNaziv = reader.GetString(reader.GetOrdinal("OsiguritelNaziv")),
                            KlasiOsiguruvanjeIdKlasa = reader.GetInt32(reader.GetOrdinal("KlasiOsiguruvanjeIdKlasa")),
                            KlasaIme = reader.GetString(reader.GetOrdinal("KlasaIme")),
                            BrojNaPolisa = reader.IsDBNull(reader.GetOrdinal("BrojNaPolisa")) ? null : reader.GetString(reader.GetOrdinal("BrojNaPolisa")),
                            PlatenoOdDogovoruvac = reader.IsDBNull(reader.GetOrdinal("PlatenoOdDogovoruvac")) ? null : reader.GetDecimal(reader.GetOrdinal("PlatenoOdDogovoruvac")),
                            IznosZaPlakanjeVoRok = reader.IsDBNull(reader.GetOrdinal("IznosZaPlakanjeVoRok")) ? null : reader.GetDecimal(reader.GetOrdinal("IznosZaPlakanjeVoRok")),
                            DolziDogovoruvac = reader.IsDBNull(reader.GetOrdinal("DolziDogovoruvac")) ? null : reader.GetDecimal(reader.GetOrdinal("DolziDogovoruvac")),
                            BrojNaFakturaVlezna = reader.IsDBNull(reader.GetOrdinal("BrojNaFakturaVlezna")) ? null : reader.GetString(reader.GetOrdinal("BrojNaFakturaVlezna")),
                            DatumNaFakturaVlezna = reader.IsDBNull(reader.GetOrdinal("DatumNaFakturaVlezna")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumNaFakturaVlezna")),
                            RokNaplakjanjeFakturaVlezna = reader.IsDBNull(reader.GetOrdinal("RokNaPlakjanjeFakturaVlezna")) ? null : reader.GetDateTime(reader.GetOrdinal("RokNaPlakjanjeFakturaVlezna"))
                        };
                        FilteredPolisi.Add(polisa);
                    }
                }

                // After getting the basic data, calculate premium for each polisa
                foreach (var polisa in FilteredPolisi)
                {
                    polisa.VkupnaPremijaZaPlakanje = await GetVkupnaPremijaZaPlakanje(connection, polisa.Id);
                }
            }

            await LoadOsiguriteli();
            await LoadKlasiOsiguruvanje();
            return Page();
        }

        public async Task<IActionResult> OnPostSaveSpecifikacijaAsync(string BrojNaFakturaVlezna, DateTime DatumNaFakturaVlezna, 
            DateTime RokNaPlakjanjeFakturaVlezna, string SelectedPolisiIds, decimal? IznosNaFakturaVoRok)
        {
            if (!await HasPageAccess("PrenosNaNaplatenaPremijaSpecifikacii"))
            {
                return RedirectToAccessDenied();
            }

            if (string.IsNullOrEmpty(SelectedPolisiIds))
            {
                TempData["ErrorMessage"] = "Не се избрани полиси за ажурирање.";
                return RedirectToPage();
            }

            var options = new JsonSerializerOptions
            {
                NumberHandling = JsonNumberHandling.AllowReadingFromString
            };
            var polisiIds = JsonSerializer.Deserialize<List<long>>(SelectedPolisiIds, options);
            if (polisiIds == null || !polisiIds.Any())
            {
                TempData["ErrorMessage"] = "Не се избрани полиси за ажурирање.";
                return RedirectToPage();
            }

            string username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                TempData["ErrorMessage"] = "Вашата сесија е истечена. Најавете се повторно.";
                return RedirectToPage("/Account/Login");
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        using (SqlCommand cmd = new SqlCommand(@"
                            UPDATE Polisi 
                            SET BrojNaFakturaVlezna = @BrojNaFakturaVlezna,
                                DatumNaFakturaVlezna = @DatumNaFakturaVlezna,
                                RokNaPlakjanjeFakturaVlezna = @RokNaPlakjanjeFakturaVlezna,
                                DateModified = GETDATE(),
                                UsernameModified = @Username
                            WHERE Id IN (SELECT value FROM OPENJSON(@PolisiIds))", connection, transaction))
                        {
                            cmd.Parameters.AddWithValue("@BrojNaFakturaVlezna", BrojNaFakturaVlezna);
                            cmd.Parameters.AddWithValue("@DatumNaFakturaVlezna", DatumNaFakturaVlezna);
                            cmd.Parameters.AddWithValue("@RokNaPlakjanjeFakturaVlezna", RokNaPlakjanjeFakturaVlezna);
                            cmd.Parameters.AddWithValue("@Username", username);
                            cmd.Parameters.AddWithValue("@PolisiIds", JsonSerializer.Serialize(polisiIds));

                            await cmd.ExecuteNonQueryAsync();
                        }

                        transaction.Commit();
                        TempData["SuccessMessage"] = "Успешно ажурирани полиси.";
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        TempData["ErrorMessage"] = "Настана грешка при ажурирање на полисите.";
                        return RedirectToPage();
                    }
                }

                // Start a new transaction for PrenosNaNaplataSpecifikacii insert
                using (var specifikacijaTransaction = connection.BeginTransaction())
                {
                    try
                    {
                        // First get KlientiIdOsiguritel from the first selected polisa
                        long klientiIdOsiguritel;
                        using (SqlCommand getKlientCmd = new SqlCommand(@"
                            SELECT TOP 1 KlientiIdOsiguritel 
                            FROM Polisi 
                            WHERE Id IN (SELECT value FROM OPENJSON(@PolisiIds))", connection, specifikacijaTransaction))
                        {
                            getKlientCmd.Parameters.AddWithValue("@PolisiIds", JsonSerializer.Serialize(polisiIds));
                            klientiIdOsiguritel = (long)await getKlientCmd.ExecuteScalarAsync();
                        }

                        // Calculate total IznosNaFaktura using the stored function
                        decimal totalIznosNaFaktura = 0;
                        using (SqlCommand sumCmd = new SqlCommand(@"
                            SELECT SUM(Premium)
                            FROM (
                                SELECT dbo.VratiPolisaVkupnaPremijaZaPlakanjePoPolisaId(Id) as Premium
                                FROM Polisi                                
                                WHERE Id IN (SELECT value FROM OPENJSON(@PolisiIds))
                            ) as PremiumCalculations", connection, specifikacijaTransaction))
                        {
                            sumCmd.Parameters.AddWithValue("@PolisiIds", JsonSerializer.Serialize(polisiIds));
                            var result = await sumCmd.ExecuteScalarAsync();
                            totalIznosNaFaktura = result != DBNull.Value ? Convert.ToDecimal(result) : 0m;
                        }

                        // Insert into PrenosNaNaplataSpecifikacii
                        using (SqlCommand insertCmd = new SqlCommand(@"
                            INSERT INTO PrenosNaNaplataSpecifikacii (
                                UsernameCreated,
                                KlientiIdOsiguritel,
                                DatumNaVleznaFaktura,
                                RokNaPlakjanjeFakturaVlezna,
                                IznosNaFaktura,
                                BrojNaFaktura,
                                IznosNaFakturaVoRok
                            )
                            VALUES (
                                @Username,
                                @KlientiIdOsiguritel,
                                @DatumNaVleznaFaktura,
                                @RokNaPlakjanjeFakturaVlezna,
                                @IznosNaFaktura,
                                @BrojNaFaktura,
                                @IznosNaFakturaVoRok
                            )", connection, specifikacijaTransaction))
                        {
                            insertCmd.Parameters.AddWithValue("@Username", username);
                            insertCmd.Parameters.AddWithValue("@KlientiIdOsiguritel", klientiIdOsiguritel);
                            insertCmd.Parameters.AddWithValue("@DatumNaVleznaFaktura", DatumNaFakturaVlezna);
                            insertCmd.Parameters.AddWithValue("@RokNaPlakjanjeFakturaVlezna", RokNaPlakjanjeFakturaVlezna);
                            insertCmd.Parameters.AddWithValue("@IznosNaFaktura", totalIznosNaFaktura);
                            insertCmd.Parameters.AddWithValue("@BrojNaFaktura", BrojNaFakturaVlezna);
                            insertCmd.Parameters.AddWithValue("@IznosNaFakturaVoRok", (object)IznosNaFakturaVoRok ?? DBNull.Value);

                            await insertCmd.ExecuteNonQueryAsync();
                        }

                        specifikacijaTransaction.Commit();
                        TempData["SuccessMessage"] = "Успешно ажурирани полиси и креирана спецификација.";
                    }
                    catch (Exception ex)
                    {
                        specifikacijaTransaction.Rollback();
                        TempData["ErrorMessage"] = $"Полисите се ажурирани, но настана грешка при креирање на спецификација. Детали: {ex.Message}";
                        if (ex.InnerException != null)
                        {
                            TempData["ErrorMessage"] += $" | Inner Exception: {ex.InnerException.Message}";
                        }
                        System.Diagnostics.Debug.WriteLine($"Error details: {ex}");
                    }
                }
            }

            return RedirectToPage();
        }

        public async Task<IActionResult> OnGetExportToExcelAsync(string SelectedPolisiIds, string BrojNaFakturaVlezna,
            DateTime? DatumNaFakturaVlezna, DateTime? RokNaPlakjanjeFakturaVlezna, decimal? IznosNaFakturaVoRok)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Starting Excel export with parameters:");
                System.Diagnostics.Debug.WriteLine($"SelectedPolisiIds: {SelectedPolisiIds}");
                System.Diagnostics.Debug.WriteLine($"BrojNaFakturaVlezna: {BrojNaFakturaVlezna}");
                System.Diagnostics.Debug.WriteLine($"DatumNaFakturaVlezna: {DatumNaFakturaVlezna}");
                System.Diagnostics.Debug.WriteLine($"RokNaPlakjanjeFakturaVlezna: {RokNaPlakjanjeFakturaVlezna}");

                if (!await HasPageAccess("PrenosNaNaplatenaPremijaSpecifikacii"))
                {
                    System.Diagnostics.Debug.WriteLine("Access denied to page");
                    return RedirectToAccessDenied();
                }

                var options = new JsonSerializerOptions
                {
                    NumberHandling = JsonNumberHandling.AllowReadingFromString
                };

                if (string.IsNullOrEmpty(SelectedPolisiIds))
                {
                    System.Diagnostics.Debug.WriteLine("SelectedPolisiIds is null or empty");
                    TempData["ErrorMessage"] = "Не се избрани полиси за експорт.";
                    return RedirectToPage();
                }

                var polisiIds = JsonSerializer.Deserialize<List<long>>(SelectedPolisiIds, options);
                System.Diagnostics.Debug.WriteLine($"Deserialized polisiIds count: {polisiIds?.Count ?? 0}");

                if (polisiIds == null || !polisiIds.Any())
                {
                    System.Diagnostics.Debug.WriteLine("No polisi IDs after deserialization");
                    TempData["ErrorMessage"] = "Не се избрани полиси за експорт.";
                    return RedirectToPage();
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                List<PolisaModel> selectedPolisi = new List<PolisaModel>();
                string tipNaFaktura = "";

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    System.Diagnostics.Debug.WriteLine("Opening database connection");
                    await connection.OpenAsync();
                    var query = @"
                        SELECT p.Id,
                               p.KlientiIdOsiguritel,
                               k.Naziv as OsiguritelNaziv,
                               p.KlasiOsiguruvanjeIdKlasa,
                               ko.KlasaIme,
                               p.BrojNaPolisa,
                               p.BrojNaFakturaVlezna,
                               p.DatumNaFakturaVlezna,
                               p.RokNaPlakjanjeFakturaVlezna,
                               p.TipNaFaktura
                        FROM Polisi p
                        LEFT JOIN Klienti k ON p.KlientiIdOsiguritel = k.Id
                        LEFT JOIN KlasiOsiguruvanje ko ON p.KlasiOsiguruvanjeIdKlasa = ko.Id
                        WHERE p.Id IN (SELECT value FROM OPENJSON(@PolisiIds))";

                    using (SqlCommand cmd = new SqlCommand(query, connection))
                    {
                        cmd.Parameters.AddWithValue("@PolisiIds", JsonSerializer.Serialize(polisiIds));

                        using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                        while (await reader.ReadAsync())
                        {
                            var polisa = new PolisaModel
                            {
                                Id = reader.GetInt64(reader.GetOrdinal("Id")),
                                KlientiIdOsiguritel = reader.GetInt64(reader.GetOrdinal("KlientiIdOsiguritel")),
                                OsiguritelNaziv = reader.GetString(reader.GetOrdinal("OsiguritelNaziv")),
                                KlasiOsiguruvanjeIdKlasa = reader.GetInt32(reader.GetOrdinal("KlasiOsiguruvanjeIdKlasa")),
                                KlasaIme = reader.GetString(reader.GetOrdinal("KlasaIme")),
                                BrojNaPolisa = reader.IsDBNull(reader.GetOrdinal("BrojNaPolisa")) ? null : reader.GetString(reader.GetOrdinal("BrojNaPolisa")),
                                BrojNaFakturaVlezna = reader.IsDBNull(reader.GetOrdinal("BrojNaFakturaVlezna")) ? null : reader.GetString(reader.GetOrdinal("BrojNaFakturaVlezna")),
                                DatumNaFakturaVlezna = reader.IsDBNull(reader.GetOrdinal("DatumNaFakturaVlezna")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumNaFakturaVlezna")),
                                RokNaplakjanjeFakturaVlezna = reader.IsDBNull(reader.GetOrdinal("RokNaPlakjanjeFakturaVlezna")) ? null : reader.GetDateTime(reader.GetOrdinal("RokNaPlakjanjeFakturaVlezna"))
                            };
                            if (!reader.IsDBNull(reader.GetOrdinal("TipNaFaktura")))
                            {
                                tipNaFaktura = reader.GetString(reader.GetOrdinal("TipNaFaktura"));
                            }
                            selectedPolisi.Add(polisa);
                        }
                    }

                    foreach (var polisa in selectedPolisi)
                    {
                        polisa.VkupnaPremijaZaPlakanje = await GetVkupnaPremijaZaPlakanje(connection, polisa.Id);
                    }
                }

                System.Diagnostics.Debug.WriteLine($"Retrieved {selectedPolisi.Count} polisi from database");

                using (var package = new ExcelPackage())
                {
                    System.Diagnostics.Debug.WriteLine("Creating Excel package");
                    var worksheet = package.Workbook.Worksheets.Add("Спецификација");

                    // Header information
                    worksheet.Cells[1, 1].Value = "Осигурител:";
                    worksheet.Cells[1, 2].Value = selectedPolisi.First().OsiguritelNaziv;

                    worksheet.Cells[2, 1].Value = "Тип на фактура:";
                    worksheet.Cells[2, 2].Value = tipNaFaktura;

                    worksheet.Cells[3, 1].Value = "Број на фактура:";
                    worksheet.Cells[3, 2].Value = BrojNaFakturaVlezna;

                    worksheet.Cells[4, 1].Value = "Датум на фактура:";
                    worksheet.Cells[4, 2].Value = DatumNaFakturaVlezna?.ToString("dd.MM.yyyy");

                    worksheet.Cells[5, 1].Value = "Рок на плаќање:";
                    worksheet.Cells[5, 2].Value = RokNaPlakjanjeFakturaVlezna?.ToString("dd.MM.yyyy");

                    worksheet.Cells[6, 1].Value = "Износ на фактура во рок:";
                    worksheet.Cells[6, 2].Value = IznosNaFakturaVoRok;
                    if (IznosNaFakturaVoRok.HasValue)
                    {
                        worksheet.Cells[6, 2].Style.Numberformat.Format = "#,##0.00";
                    }

                    // Table headers
                    int currentRow = 8;
                    worksheet.Cells[currentRow, 1].Value = "Број на полиса";
                    worksheet.Cells[currentRow, 2].Value = "Класа";
                    worksheet.Cells[currentRow, 3].Value = "Премија за плаќање";

                    // Data rows
                    currentRow++;
                    foreach (var polisa in selectedPolisi)
                    {
                        worksheet.Cells[currentRow, 1].Value = polisa.BrojNaPolisa;
                        worksheet.Cells[currentRow, 2].Value = polisa.KlasaIme;
                        worksheet.Cells[currentRow, 3].Value = polisa.VkupnaPremijaZaPlakanje;
                        currentRow++;
                    }

                    // Total
                    worksheet.Cells[currentRow + 1, 2].Value = "Вкупно:";
                    worksheet.Cells[currentRow + 1, 3].Value = selectedPolisi.Sum(p => p.VkupnaPremijaZaPlakanje);

                    // Formatting
                    worksheet.Cells[currentRow + 1, 3].Style.Numberformat.Format = "#,##0.00";
                    var headerRange = worksheet.Cells[8, 1, 8, 3];
                    headerRange.Style.Font.Bold = true;

                    var dataRange = worksheet.Cells[9, 3, currentRow - 1, 3];
                    dataRange.Style.Numberformat.Format = "#,##0.00";

                    worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                    var stream = new MemoryStream();
                    package.SaveAs(stream);
                    stream.Position = 0;

                    string fileName = $"Specifikacija_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                    System.Diagnostics.Debug.WriteLine($"Returning Excel file: {fileName}");
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in Excel export: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                TempData["ErrorMessage"] = $"Грешка при експорт: {ex.Message}";
                return RedirectToPage();
            }
        }
    }
}
