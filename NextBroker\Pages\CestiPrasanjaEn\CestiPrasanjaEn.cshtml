@page
@model NextBroker.Pages.CestiPrasanjaEnModel
@{
    ViewData["Title"] = "Ask us a question and we will get back to you as soon as possible";
}

<link rel="stylesheet" href="~/css/cestiprasanja.css" asp-append-version="true" />

<main>
    <h2>@ViewData["Title"]</h2>

    @if (Model.SuccessMessage != null)
    {
        <div class="alert alert-success">
            @Model.SuccessMessage
        </div>
    }

    @if (Model.ErrorMessage != null)
    {
        <div class="alert alert-danger">
            @Model.ErrorMessage
        </div>
    }

    <form method="post" id="questionForm" style="@(Model.SuccessMessage != null ? "display:none;" : "")">
        <div class="form-group">
            <label asp-for="Name">First Name</label>
            <input asp-for="Name" class="form-control" required />
        </div>
        <div class="form-group">
            <label asp-for="Surname">Last Name</label>
            <input asp-for="Surname" class="form-control" required />
        </div>
        <div class="form-group">
            <label asp-for="Email">Contact Email</label>
            <input asp-for="Email" type="email" class="form-control" required />
        </div>
        <div class="form-group">
            <label asp-for="Phone">Contact Phone</label>
            <input asp-for="Phone" class="form-control" required />
        </div>
        <div class="form-group">
            <label asp-for="Question">Question</label>
            <input asp-for="Question" class="form-control" required />
        </div>
        <div class="form-group">
            <label asp-for="Description">Additional Information</label>
            <textarea asp-for="Description" class="form-control" rows="5" required></textarea>
        </div>
        <button type="submit" class="btn btn-primary" id="submitButton">Send Question</button>
    </form>
</main>

@section Scripts {
    <script>
        document.getElementById("questionForm").onsubmit = function () {
            // Check if any fields are empty
            var requiredFields = document.querySelectorAll("[required]");
            var allFilled = true;

            requiredFields.forEach(function (field) {
                if (!field.value) {
                    allFilled = false;
                }
            });

            if (!allFilled) {
                alert("All fields are required.");
                return false; // Prevent form submission
            }

            // If all fields are filled, disable the button
            document.getElementById("submitButton").disabled = true;
        };

        // Hide the form if the success message is set
        window.onload = function() {
            var successMessage = '@Html.Raw(Model.SuccessMessage != null ? "true" : "false")';
            if (successMessage === "true") {
                document.getElementById("questionForm").style.display = 'none';
            }
        };
    </script>
} 