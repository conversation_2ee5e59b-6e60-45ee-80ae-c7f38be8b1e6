using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.Data;
using Microsoft.Data.SqlClient;
using OfficeOpenXml;


namespace NextBroker.Pages.Pregledi
{
    public class DolgResult
    {
        public string BrojNaPolisa { get; set; }
        public string Osiguritel { get; set; }
        public string Klasa { get; set; }
        public string Proizvod { get; set; }
        public decimal VkupnaPremija { get; set; }
        public int VkupenBrojRati { get; set; }
        public decimal PreostanataDolznaPremija { get; set; }
        public int PreostanatiRati { get; set; }
        public decimal VkupenDospeanDolg { get; set; }
        public int BrojDospeaniNeplateniRati { get; set; }
    }

    public class DolgTotals
    {
        public decimal VkupnaPremija { get; set; }
        public int VkupenBrojRati { get; set; }
        public decimal PreostanataDolznaPremija { get; set; }
        public int PreostanatiRati { get; set; }
        public decimal VkupenDospeanDolg { get; set; }
        public int BrojDospeaniNeplateniRati { get; set; }
    }

    public class SostojbaNaDolgPoDogovoruvacModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        [BindProperty]
        public string MaticenBroj { get; set; }

        public List<DolgResult> Results { get; set; }
        public string ErrorMessage { get; set; }
        public string DebugInfo { get; set; }
        public DolgTotals Totals { get; set; }

        public SostojbaNaDolgPoDogovoruvacModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("SostojbaNaDolgPoDogovoruvac"))
            {
                return RedirectToAccessDenied();
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!await HasPageAccess("SostojbaNaDolgPoDogovoruvac"))
            {
                return RedirectToAccessDenied();
            }

            if (string.IsNullOrWhiteSpace(MaticenBroj))
            {
                ErrorMessage = "Внесете матичен број";
                return Page();
            }

            try
            {
                Results = new List<DolgResult>();
                using (SqlConnection conn = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await conn.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand("PregledSostojbaNaDolgPoDogovoruvac", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@MaticenBroj", MaticenBroj);

                        using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                Results.Add(new DolgResult
                                {
                                    BrojNaPolisa = reader.IsDBNull(0) ? "" : reader.GetString(0),
                                    Osiguritel = reader.IsDBNull(1) ? "" : reader.GetString(1),
                                    Klasa = reader.IsDBNull(2) ? "" : reader.GetString(2),
                                    Proizvod = reader.IsDBNull(3) ? "" : reader.GetString(3),
                                    VkupnaPremija = reader.IsDBNull(4) ? 0 : reader.GetDecimal(4),
                                    VkupenBrojRati = reader.IsDBNull(5) ? 0 : reader.GetInt32(5),
                                    PreostanataDolznaPremija = reader.IsDBNull(6) ? 0 : reader.GetDecimal(6),
                                    PreostanatiRati = reader.IsDBNull(7) ? 0 : reader.GetInt32(7),
                                    VkupenDospeanDolg = reader.IsDBNull(8) ? 0 : reader.GetDecimal(8),
                                    BrojDospeaniNeplateniRati = reader.IsDBNull(9) ? 0 : reader.GetInt32(9)
                                });
                            }
                        }
                    }
                }

                CalculateTotals();
            }
            catch (Exception ex)
            {
                ErrorMessage = "Грешка при генерирање на прегледот: " + ex.Message;
            }

            return Page();
        }

        private void CalculateTotals()
        {
            if (Results == null || !Results.Any())
            {
                Totals = null;
                return;
            }

            Totals = new DolgTotals
            {
                VkupnaPremija = Results.Sum(x => x.VkupnaPremija),
                VkupenBrojRati = Results.Sum(x => x.VkupenBrojRati),
                PreostanataDolznaPremija = Results.Sum(x => x.PreostanataDolznaPremija),
                PreostanatiRati = Results.Sum(x => x.PreostanatiRati),
                VkupenDospeanDolg = Results.Sum(x => x.VkupenDospeanDolg),
                BrojDospeaniNeplateniRati = Results.Sum(x => x.BrojDospeaniNeplateniRati)
            };
        }

        public async Task<IActionResult> OnGetExportExcelAsync(string maticenBroj)
        {
            try
            {
                DebugInfo = "Starting export process...\n";

                if (!await HasPageAccess("SostojbaNaDolgPoDogovoruvac"))
                {
                    DebugInfo += "Access denied\n";
                    return RedirectToAccessDenied();
                }

                if (string.IsNullOrWhiteSpace(maticenBroj))
                {
                    DebugInfo += "MaticenBroj is empty\n";
                    ErrorMessage = "Матичен број е задолжителен";
                    return Page();
                }

                DebugInfo += $"Fetching data for MaticenBroj: {maticenBroj}\n";
                Results = new List<DolgResult>();

                using (SqlConnection conn = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await conn.OpenAsync();
                    DebugInfo += "Database connection opened\n";

                    using (SqlCommand cmd = new SqlCommand("PregledSostojbaNaDolgPoDogovoruvac", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@MaticenBroj", maticenBroj);
                        DebugInfo += "Executing stored procedure...\n";

                        using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                Results.Add(new DolgResult
                                {
                                    BrojNaPolisa = reader.IsDBNull(0) ? "" : reader.GetString(0),
                                    Osiguritel = reader.IsDBNull(1) ? "" : reader.GetString(1),
                                    Klasa = reader.IsDBNull(2) ? "" : reader.GetString(2),
                                    Proizvod = reader.IsDBNull(3) ? "" : reader.GetString(3),
                                    VkupnaPremija = reader.IsDBNull(4) ? 0 : reader.GetDecimal(4),
                                    VkupenBrojRati = reader.IsDBNull(5) ? 0 : reader.GetInt32(5),
                                    PreostanataDolznaPremija = reader.IsDBNull(6) ? 0 : reader.GetDecimal(6),
                                    PreostanatiRati = reader.IsDBNull(7) ? 0 : reader.GetInt32(7),
                                    VkupenDospeanDolg = reader.IsDBNull(8) ? 0 : reader.GetDecimal(8),
                                    BrojDospeaniNeplateniRati = reader.IsDBNull(9) ? 0 : reader.GetInt32(9)
                                });
                            }
                        }
                    }
                }

                DebugInfo += $"Found {Results.Count} records\n";

                CalculateTotals();

                if (!Results.Any())
                {
                    DebugInfo += "No results found\n";
                    ErrorMessage = "Нема податоци за експорт";
                    return Page();
                }

                DebugInfo += "Creating Excel package...\n";
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add("Состојба на долг");
                    DebugInfo += "Worksheet created\n";

                    worksheet.Cells[1, 1].Value = "Полиса број";
                    worksheet.Cells[1, 2].Value = "Осигурител";
                    worksheet.Cells[1, 3].Value = "Класа";
                    worksheet.Cells[1, 4].Value = "Производ";
                    worksheet.Cells[1, 5].Value = "Вкупна Премија за плаќање";
                    worksheet.Cells[1, 6].Value = "Вкупен број на рати";
                    worksheet.Cells[1, 7].Value = "Преостаната Должна премија";
                    worksheet.Cells[1, 8].Value = "Преостанати рати";
                    worksheet.Cells[1, 9].Value = "Вкупен доспеан долг";
                    worksheet.Cells[1, 10].Value = "Број на доспеани неплатени рати";

                    using (var range = worksheet.Cells[1, 1, 1, 10])
                    {
                        range.Style.Font.Bold = true;
                        range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                        range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                    }

                    int row = 2;
                    foreach (var item in Results)
                    {
                        worksheet.Cells[row, 1].Value = item.BrojNaPolisa;
                        worksheet.Cells[row, 2].Value = item.Osiguritel;
                        worksheet.Cells[row, 3].Value = item.Klasa;
                        worksheet.Cells[row, 4].Value = item.Proizvod;
                        worksheet.Cells[row, 5].Value = item.VkupnaPremija;
                        worksheet.Cells[row, 6].Value = item.VkupenBrojRati;
                        worksheet.Cells[row, 7].Value = item.PreostanataDolznaPremija;
                        worksheet.Cells[row, 8].Value = item.PreostanatiRati;
                        worksheet.Cells[row, 9].Value = item.VkupenDospeanDolg;
                        worksheet.Cells[row, 10].Value = item.BrojDospeaniNeplateniRati;
                        row++;
                    }

                    worksheet.Cells[2, 5, row - 1, 5].Style.Numberformat.Format = "#,##0.00";
                    worksheet.Cells[2, 7, row - 1, 7].Style.Numberformat.Format = "#,##0.00";
                    worksheet.Cells[2, 9, row - 1, 9].Style.Numberformat.Format = "#,##0.00";

                    worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                    // After populating all the rows, add the totals row
                    worksheet.Cells[row, 1, row, 4].Merge = true;
                    worksheet.Cells[row, 1].Value = "ВКУПНО:";
                    worksheet.Cells[row, 5].Value = Totals.VkupnaPremija;
                    worksheet.Cells[row, 6].Value = Totals.VkupenBrojRati;
                    worksheet.Cells[row, 7].Value = Totals.PreostanataDolznaPremija;
                    worksheet.Cells[row, 8].Value = Totals.PreostanatiRati;
                    worksheet.Cells[row, 9].Value = Totals.VkupenDospeanDolg;
                    worksheet.Cells[row, 10].Value = Totals.BrojDospeaniNeplateniRati;

                    // Style the totals row
                    using (var range = worksheet.Cells[row, 1, row, 10])
                    {
                        range.Style.Font.Bold = true;
                        range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                        range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                    }

                    // Apply number formatting to the totals row
                    worksheet.Cells[row, 5].Style.Numberformat.Format = "#,##0.00";
                    worksheet.Cells[row, 7].Style.Numberformat.Format = "#,##0.00";
                    worksheet.Cells[row, 9].Style.Numberformat.Format = "#,##0.00";

                    worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                    DebugInfo += "Excel file generated\n";
                    var content = package.GetAsByteArray();
                    string fileName = $"SostojbaNaDolg_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx";

                    DebugInfo += $"Returning file: {fileName}\n";
                    return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
            }
            catch (Exception ex)
            {
                DebugInfo += $"Error occurred: {ex.Message}\n";
                DebugInfo += $"Stack trace: {ex.StackTrace}\n";
                ErrorMessage = "Грешка при генерирање на Excel: " + ex.Message;
                return Page();
            }
        }
    }
}
