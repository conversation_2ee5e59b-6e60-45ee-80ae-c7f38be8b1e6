body {
    background-color: #f8f9fa;
    font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS', sans-serif;
    font-size: 18px;
}

.registration-container {
    max-width: 500px;
    margin: 60px auto;
    padding: 40px;
    background-color: #ffffff;
    border-radius: 20px;
    box-shadow: 0 6px 18px rgba(0, 51, 102, 0.15);
    transition: box-shadow 0.3s ease;
}

.registration-container:hover {
    box-shadow: 0 8px 24px rgba(0, 51, 102, 0.2);
}

h2 {
    color: #003366;
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 40px;
    position: relative;
    padding-bottom: 20px;
    font-family: '<PERSON> Sans', '<PERSON> Sans MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS', sans-serif !important;
}

h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 60%;
    height: 3px;
    background-color: rgba(76, 175, 80, 0.7);
    transform: translateX(-50%);
    transition: width 0.3s ease, left 0.3s ease;
}

.registration-container:hover h2::after {
    width: 70%;
    left: 50%;
}

.form-group {
    margin-bottom: 30px;
}

label {
    color: #003366;
    font-weight: bold;
    font-size: 1.2rem;
    margin-bottom: 10px;
    display: block;
}

.form-control {
    border: 2px solid rgba(0, 51, 102, 0.2);
    border-radius: 12px;
    padding: 15px;
    width: 100%;
    transition: border-color 0.3s ease;
    font-size: 1.1rem;
}

.form-control:focus {
    border-color: #003366;
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 51, 102, 0.1);
}

.text-danger {
    color: #dc3545;
    font-size: 1rem;
    margin-top: 8px;
}

.btn-primary {
    background-color: #003366;
    border: none !important;
    color: white !important;
    padding: 15px 25px;
    border-radius: 30px !important;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    font-size: 1.2rem;
    margin-top: 10px;
}

.btn-primary:hover {
    background-color: #004080 !important;
    transform: scale(1.05);
    color: white !important;
}

.btn-primary:active,
.btn-primary:focus,
.btn-primary.active,
.btn-primary:active:focus,
.btn-primary:focus-visible,
.btn-primary:focus-within,
.btn-primary.focus {
    background-color: #4CAF50 !important;
    border-radius: 30px !important;
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.3) !important;
    border: none !important;
    color: white !important;
    transform: scale(1) !important;
}

.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled).active,
.show > .btn-primary.dropdown-toggle,
.btn-primary[aria-expanded="true"] {
    background-color: #4CAF50 !important;
    border-radius: 30px !important;
    border-color: transparent !important;
    color: white !important;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.3) !important;
}

.btn-primary:focus-visible,
.btn-primary:focus-within,
.btn-primary.clicked,
.btn-primary.active,
.btn-primary:active {
    background-color: #4CAF50 !important;
    color: white !important;
    border-color: transparent !important;
    outline: none !important;
}

.btn-secondary {
    border-radius: 30px !important;
    transition: all 0.3s ease;
}

.btn-secondary:hover,
.btn-secondary:focus,
.btn-secondary:active {
    border-radius: 30px !important;
}

.alert {
    margin-top: 20px;
    padding: 15px;
    border-radius: 12px;
    font-size: 1.1rem;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

