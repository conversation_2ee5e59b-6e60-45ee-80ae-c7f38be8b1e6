@page
@model NextBroker.Pages.Finansii.OZDodajModel
@{
    ViewData["Title"] = "Одобрување / Задолжување Додај";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">@ViewData["Title"]</h3>
                </div>
                <div class="card-body">
                    <form method="post">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Input.SifrarnikOZTipNaDokumentId" class="form-label"></label>
                                    <select asp-for="Input.SifrarnikOZTipNaDokumentId" class="form-control" asp-items="Model.TipoviNaDokument">
                                        <option value="">-- Изберете --</option>
                                    </select>
                                    <span asp-validation-for="Input.SifrarnikOZTipNaDokumentId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Input.SifrarnikOZTipId" class="form-label"></label>
                                    <select asp-for="Input.SifrarnikOZTipId" class="form-control" asp-items="Model.Tipovi">
                                        <option value="">-- Изберете --</option>
                                    </select>
                                    <span asp-validation-for="Input.SifrarnikOZTipId" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Input.SifrarnikOZPremijaProvizijaId" class="form-label"></label>
                                    <select asp-for="Input.SifrarnikOZPremijaProvizijaId" class="form-control" asp-items="Model.PremijaProvizija">
                                        <option value="">-- Изберете --</option>
                                    </select>
                                    <span asp-validation-for="Input.SifrarnikOZPremijaProvizijaId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Клиент (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                                    <div class="input-group">
                                        <button type="button" class="btn btn-outline-secondary btn-sm clear-field" 
                                                data-target="klient" 
                                                style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                            <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                                        </button>
                                        <input type="text" id="klientMBSearch" class="form-control" 
                                               autocomplete="off"
                                               placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                                        <input type="hidden" asp-for="Input.KlientiIdKlient" id="KlientiIdKlient" />
                                    </div>
                                    <div id="klientSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                                         style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                                    </div>
                                    <span asp-validation-for="Input.KlientiIdKlient" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Input.BrojNaDokument" class="form-label"></label>
                                    <input asp-for="Input.BrojNaDokument" class="form-control" />
                                    <span asp-validation-for="Input.BrojNaDokument" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Input.DatumNaDokument" class="form-label"></label>
                                    <input asp-for="Input.DatumNaDokument" class="form-control" type="date" />
                                    <span asp-validation-for="Input.DatumNaDokument" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Input.Iznos" class="form-label"></label>
                                    <input asp-for="Input.Iznos" class="form-control" type="number" step="0.0001" />
                                    <span asp-validation-for="Input.Iznos" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Полиса (пребарувај по број на полиса)</label>
                                    <div class="input-group">
                                        <button type="button" class="btn btn-outline-secondary clear-field" data-target="polisa">
                                            <i class="fas fa-eraser"></i>
                                        </button>
                                        <input type="text" id="polisaSearch" class="form-control" 
                                               autocomplete="off"
                                               placeholder="Пребарувај по број на полиса или фактура..." />
                                        <input type="hidden" asp-for="Input.PolisaId" id="PolisaId" />
                                    </div>
                                    <div id="polisaSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                                         style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                                    </div>
                                    <span asp-validation-for="Input.PolisaId" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Број на влезна фактура (пренос на наплата кон осигурител)</label>
                                    <div class="input-group">
                                        <button type="button" class="btn btn-outline-secondary clear-field" data-target="vleznaFaktura">
                                            <i class="fas fa-eraser"></i>
                                        </button>
                                        <input type="text" id="vleznaFakturaSearch" class="form-control" 
                                               autocomplete="off"
                                               placeholder="Пребарувај по број на фактура или клиент..." />
                                        <input type="hidden" asp-for="Input.BrojNaVleznaFaktura" id="BrojNaVleznaFaktura" />
                                    </div>
                                    <div id="vleznaFakturaSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                                         style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                                    </div>
                                    <span asp-validation-for="Input.BrojNaVleznaFaktura" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Број на излезна фактура за провизија</label>
                                    <div class="input-group">
                                        <button type="button" class="btn btn-outline-secondary clear-field" data-target="izleznaFaktura">
                                            <i class="fas fa-eraser"></i>
                                        </button>
                                        <input type="text" id="izleznaFakturaSearch" class="form-control" 
                                               autocomplete="off"
                                               placeholder="Пребарувај по број на фактура или фактура до..." />
                                        <input type="hidden" asp-for="Input.BrojNaIzleznaFaktura" id="BrojNaIzleznaFaktura" />
                                    </div>
                                    <div id="izleznaFakturaSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                                         style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                                    </div>
                                    <span asp-validation-for="Input.BrojNaIzleznaFaktura" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Input.TipNafaktura" class="form-label"></label>
                                    <input asp-for="Input.TipNafaktura" class="form-control" readonly/>
                                    <span asp-validation-for="Input.TipNafaktura" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Број на излезна фактура премија</label>
                                    <div class="input-group">
                                        <button type="button" class="btn btn-outline-secondary clear-field" data-target="izleznaFakturaPremija">
                                            <i class="fas fa-eraser"></i>
                                        </button>
                                        <input type="text" id="izleznaFakturaPremijaSearch" class="form-control" 
                                               autocomplete="off"
                                               placeholder="Пребарувај по фактура, полиса, осигурител или продукт..." />
                                        <input type="hidden" asp-for="Input.BrojNaIzleznaFakturaPremija" id="BrojNaIzleznaFakturaPremija" />
                                    </div>
                                    <div id="izleznaFakturaPremijaSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                                         style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                                    </div>
                                    <span asp-validation-for="Input.BrojNaIzleznaFakturaPremija" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="form-group">
                                    <label asp-for="Input.Opis" class="form-label"></label>
                                    <textarea asp-for="Input.Opis" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="Input.Opis" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Зачувај
                            </button>
                            <a asp-page="OZLista" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Назад
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Add click handler for clear button
            $('.clear-field').on('click', function() {
                const target = $(this).data('target');
                if (target === 'klient') {
                    $('#klientMBSearch').val('');
                    $('#KlientiIdKlient').val('');
                    $('#klientSearchResults').hide();
                } else if (target === 'polisa') {
                    $('#polisaSearch').val('');
                    $('#PolisaId').val('');
                    $('#polisaSearchResults').hide();
                    // Also clear the TipNafaktura field when policy is cleared
                    $('#Input_TipNafaktura').val('');
                } else if (target === 'vleznaFaktura') {
                    $('#vleznaFakturaSearch').val('');
                    $('#BrojNaVleznaFaktura').val('');
                    $('#vleznaFakturaSearchResults').hide();
                } else if (target === 'izleznaFaktura') {
                    $('#izleznaFakturaSearch').val('');
                    $('#BrojNaIzleznaFaktura').val('');
                    $('#izleznaFakturaSearchResults').hide();
                } else if (target === 'izleznaFakturaPremija') {
                    $('#izleznaFakturaPremijaSearch').val('');
                    $('#BrojNaIzleznaFakturaPremija').val('');
                    $('#izleznaFakturaPremijaSearchResults').hide();
                }
                
                // Re-enable all fields when any field is cleared
                enableAllSearchFields();
            });

            // Function to disable all search fields except the active one
            function disableOtherSearchFields(activeFieldId) {
                const searchFields = [
                    { input: '#polisaSearch', button: 'button[data-target="polisa"]' },
                    { input: '#vleznaFakturaSearch', button: 'button[data-target="vleznaFaktura"]' },
                    { input: '#izleznaFakturaSearch', button: 'button[data-target="izleznaFaktura"]' },
                    { input: '#izleznaFakturaPremijaSearch', button: 'button[data-target="izleznaFakturaPremija"]' }
                ];

                searchFields.forEach(field => {
                    if (field.input !== activeFieldId) {
                        $(field.input).prop('disabled', true).addClass('disabled-search-field');
                        $(field.button).prop('disabled', true).addClass('disabled-clear-button');
                    }
                });
            }

            // Function to enable all search fields
            function enableAllSearchFields() {
                const searchFields = [
                    { input: '#polisaSearch', button: 'button[data-target="polisa"]' },
                    { input: '#vleznaFakturaSearch', button: 'button[data-target="vleznaFaktura"]' },
                    { input: '#izleznaFakturaSearch', button: 'button[data-target="izleznaFaktura"]' },
                    { input: '#izleznaFakturaPremijaSearch', button: 'button[data-target="izleznaFakturaPremija"]' }
                ];

                searchFields.forEach(field => {
                    $(field.input).prop('disabled', false).removeClass('disabled-search-field');
                    $(field.button).prop('disabled', false).removeClass('disabled-clear-button');
                });
            }

            // Function to check if field should be disabled/enabled based on its value
            function handleFieldExclusivity(fieldId) {
                const fieldValue = $(fieldId).val().trim();
                
                if (fieldValue.length > 0) {
                    disableOtherSearchFields(fieldId);
                } else {
                    // Check if any other field has content
                    const otherFields = [
                        '#polisaSearch',
                        '#vleznaFakturaSearch', 
                        '#izleznaFakturaSearch',
                        '#izleznaFakturaPremijaSearch'
                    ].filter(id => id !== fieldId);
                    
                    const hasOtherContent = otherFields.some(id => $(id).val().trim().length > 0);
                    
                    if (!hasOtherContent) {
                        enableAllSearchFields();
                    }
                }
            }

            // Add input event listeners for mutual exclusivity
            $('#polisaSearch').on('input', function() {
                handleFieldExclusivity('#polisaSearch');
            });

            $('#vleznaFakturaSearch').on('input', function() {
                handleFieldExclusivity('#vleznaFakturaSearch');
            });

            $('#izleznaFakturaSearch').on('input', function() {
                handleFieldExclusivity('#izleznaFakturaSearch');
            });

            $('#izleznaFakturaPremijaSearch').on('input', function() {
                handleFieldExclusivity('#izleznaFakturaPremijaSearch');
            });

            // Function to create search functionality for client
            function createKlientSearchFunctionality() {
                let searchTimeout;
                const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();

                $('#klientMBSearch').on('input', function() {
                    clearTimeout(searchTimeout);
                    const searchTerm = $(this).val();
                    const resultsDiv = $('#klientSearchResults');

                    if (searchTerm.length < 1) {
                        resultsDiv.hide();
                        return;
                    }

                    searchTimeout = setTimeout(function() {
                        $.ajax({
                            url: '?handler=SearchKlienti',
                            type: 'GET',
                            data: { mb: searchTerm },
                            headers: {
                                "RequestVerificationToken": antiForgeryToken
                            },
                            success: function(data) {
                                if (data && data.length > 0) {
                                    let html = '<div class="list-group">';
                                    data.forEach(item => {
                                        let displayText = '';
                                        if (item.tip === 'P') {
                                            displayText = `${item.naziv}`;
                                            let identifiers = [];
                                            if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                            if (item.edb) identifiers.push(`ЕДБ: ${item.edb}`);
                                            if (identifiers.length > 0) {
                                                displayText += ` (${identifiers.join(', ')})`;
                                            }
                                        } else {
                                            displayText = `${item.ime} ${item.prezime}`;
                                            let identifiers = [];
                                            if (item.embg) identifiers.push(`ЕМБГ: ${item.embg}`);
                                            if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                            if (identifiers.length > 0) {
                                                displayText += ` (${identifiers.join(', ')})`;
                                            }
                                        }
                                        html += `<a href="#" class="list-group-item list-group-item-action" data-id="${item.id}">
                                                  ${displayText}
                                               </a>`;
                                    });
                                    html += '</div>';
                                    resultsDiv.html(html).show();
                                } else {
                                    resultsDiv.html(`
                                        <div class="list-group">
                                            <div class="list-group-item text-center">
                                                <p class="mb-2">Нема пронајдени резултати</p>
                                            </div>
                                        </div>
                                    `).show();
                                }
                            },
                            error: function() {
                                resultsDiv.html(`
                                    <div class="list-group">
                                        <div class="list-group-item text-center">
                                            <p class="text-danger mb-2">Грешка при пребарување</p>
                                        </div>
                                    </div>
                                `).show();
                            }
                        });
                    }, 300);
                });

                // Handle selection
                $(document).on('click', '#klientSearchResults .list-group-item', function(e) {
                    e.preventDefault();
                    const id = $(this).data('id');
                    const displayText = $(this).text();
                    $('#klientMBSearch').val(displayText.trim());
                    $('#KlientiIdKlient').val(id);
                    $('#klientSearchResults').hide();
                });

                // Hide results when clicking outside
                $(document).on('click', function(e) {
                    if (!$(e.target).closest('#klientMBSearch, #klientSearchResults').length) {
                        $('#klientSearchResults').hide();
                    }
                });
            }

            // Initialize search for client field
            createKlientSearchFunctionality();

            // Function to create search functionality for policy
            function createPolisaSearchFunctionality() {
                let searchTimeout;
                const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();

                $('#polisaSearch').on('input', function() {
                    clearTimeout(searchTimeout);
                    const searchTerm = $(this).val();
                    const resultsDiv = $('#polisaSearchResults');

                    if (searchTerm.length < 1) {
                        resultsDiv.hide();
                        return;
                    }

                    searchTimeout = setTimeout(function() {
                        $.ajax({
                            url: '?handler=SearchPolisi',
                            type: 'GET',
                            data: { searchTerm: searchTerm },
                            headers: {
                                "RequestVerificationToken": antiForgeryToken
                            },
                            success: function(data) {
                                if (data && data.length > 0) {
                                    let html = '<div class="list-group">';
                                    data.forEach(item => {
                                        const dateCreated = new Date(item.dateCreated).toLocaleDateString('mk-MK');
                                        const stornoText = item.storno ? 'Да' : 'Не';
                                        const stornoClass = item.storno ? 'text-danger' : 'text-success';
                                        
                                        const displayText = `Полиса: ${item.brojNaPolisa}${item.brojNaFakturaIzlezna ? ` | Фактура: ${item.brojNaFakturaIzlezna}` : ''}`;
                                        
                                        html += `<a href="#" class="list-group-item list-group-item-action" data-id="${item.id}">
                                                  <div class="d-flex w-100 justify-content-between">
                                                    <h6 class="mb-1">${displayText}</h6>
                                                  </div>
                                                  <p class="mb-1">ID: ${item.id} | <span class="${stornoClass}">Сторно: ${stornoText}</span></p>
                                                  <small>Креирана: ${dateCreated}</small>
                                               </a>`;
                                    });
                                    html += '</div>';
                                    resultsDiv.html(html).show();
                                } else {
                                    resultsDiv.html(`
                                        <div class="list-group">
                                            <div class="list-group-item text-center">
                                                <p class="mb-2">Нема пронајдени резултати</p>
                                            </div>
                                        </div>
                                    `).show();
                                }
                            },
                            error: function() {
                                resultsDiv.html(`
                                    <div class="list-group">
                                        <div class="list-group-item text-center">
                                            <p class="text-danger mb-2">Грешка при пребарување</p>
                                        </div>
                                    </div>
                                `).show();
                            }
                        });
                    }, 300);
                });

                // Handle selection
                $(document).on('click', '#polisaSearchResults .list-group-item', function(e) {
                    e.preventDefault();
                    const id = $(this).data('id');
                    const displayText = $(this).find('h6').text();
                    $('#polisaSearch').val(displayText.trim());
                    $('#PolisaId').val(id);
                    $('#polisaSearchResults').hide();
                    
                    // Auto-fill TipNaFaktura based on selected policy
                    if (id) {
                        $.ajax({
                            url: '?handler=PolisaDetails',
                            type: 'GET',
                            data: { polisaId: id },
                            headers: {
                                "RequestVerificationToken": antiForgeryToken
                            },
                            success: function(data) {
                                if (data && data.tipNaFaktura) {
                                    $('#Input_TipNafaktura').val(data.tipNaFaktura);
                                }
                            },
                            error: function() {
                                console.log('Error loading policy details');
                            }
                        });
                    }
                });

                // Hide results when clicking outside
                $(document).on('click', function(e) {
                    if (!$(e.target).closest('#polisaSearch, #polisaSearchResults').length) {
                        $('#polisaSearchResults').hide();
                    }
                });
            }

            // Initialize search for policy field
            createPolisaSearchFunctionality();

            // Function to create search functionality for vlezna faktura
            function createVleznaFakturaSearchFunctionality() {
                let searchTimeout;
                const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();

                $('#vleznaFakturaSearch').on('input', function() {
                    clearTimeout(searchTimeout);
                    const searchTerm = $(this).val();
                    const resultsDiv = $('#vleznaFakturaSearchResults');

                    if (searchTerm.length < 1) {
                        resultsDiv.hide();
                        return;
                    }

                    searchTimeout = setTimeout(function() {
                        $.ajax({
                            url: '?handler=SearchVleznaFaktura',
                            type: 'GET',
                            data: { searchTerm: searchTerm },
                            headers: {
                                "RequestVerificationToken": antiForgeryToken
                            },
                            success: function(data) {
                                if (data && data.length > 0) {
                                    let html = '<div class="list-group">';
                                    data.forEach(item => {
                                        const datumVlezna = new Date(item.datumNaVleznaFaktura).toLocaleDateString('mk-MK');
                                        const iznosFormatted = parseFloat(item.iznosNaFaktura).toFixed(2);
                                        const iznosVoRokFormatted = parseFloat(item.iznosNaFakturaVoRok).toFixed(2);
                                        
                                        const displayText = `Фактура: ${item.brojNafaktura}`;
                                        
                                        html += `<a href="#" class="list-group-item list-group-item-action" data-broj="${item.brojNafaktura}">
                                                  <div class="d-flex w-100 justify-content-between">
                                                    <h6 class="mb-1">${displayText}</h6>
                                                  </div>
                                                  <p class="mb-1">ID: ${item.id} | Клиент: ${item.klient}</p>
                                                  <p class="mb-1">Износ: ${iznosFormatted} | Во рок: ${iznosVoRokFormatted}</p>
                                                  <small>Датум: ${datumVlezna}</small>
                                               </a>`;
                                    });
                                    html += '</div>';
                                    resultsDiv.html(html).show();
                                } else {
                                    resultsDiv.html(`
                                        <div class="list-group">
                                            <div class="list-group-item text-center">
                                                <p class="mb-2">Нема пронајдени резултати</p>
                                            </div>
                                        </div>
                                    `).show();
                                }
                            },
                            error: function() {
                                resultsDiv.html(`
                                    <div class="list-group">
                                        <div class="list-group-item text-center">
                                            <p class="text-danger mb-2">Грешка при пребарување</p>
                                        </div>
                                    </div>
                                `).show();
                            }
                        });
                    }, 300);
                });

                // Handle selection
                $(document).on('click', '#vleznaFakturaSearchResults .list-group-item', function(e) {
                    e.preventDefault();
                    const brojNafaktura = $(this).data('broj');
                    const displayText = $(this).find('h6').text();
                    $('#vleznaFakturaSearch').val(displayText.trim());
                    $('#BrojNaVleznaFaktura').val(brojNafaktura);
                    $('#vleznaFakturaSearchResults').hide();
                });

                // Hide results when clicking outside
                $(document).on('click', function(e) {
                    if (!$(e.target).closest('#vleznaFakturaSearch, #vleznaFakturaSearchResults').length) {
                        $('#vleznaFakturaSearchResults').hide();
                    }
                });
            }

            // Initialize search for vlezna faktura field
            createVleznaFakturaSearchFunctionality();

            // Function to create search functionality for izlezna faktura
            function createIzleznaFakturaSearchFunctionality() {
                let searchTimeout;
                const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();

                $('#izleznaFakturaSearch').on('input', function() {
                    clearTimeout(searchTimeout);
                    const searchTerm = $(this).val();
                    const resultsDiv = $('#izleznaFakturaSearchResults');

                    if (searchTerm.length < 1) {
                        resultsDiv.hide();
                        return;
                    }

                    searchTimeout = setTimeout(function() {
                        $.ajax({
                            url: '?handler=SearchIzleznaFaktura',
                            type: 'GET',
                            data: { searchTerm: searchTerm },
                            headers: {
                                "RequestVerificationToken": antiForgeryToken
                            },
                            success: function(data) {
                                if (data && data.length > 0) {
                                    let html = '<div class="list-group">';
                                    data.forEach(item => {
                                        const datumNaFaktura = new Date(item.datumNaFaktura).toLocaleDateString('mk-MK');
                                        const datumOd = new Date(item.datumOd).toLocaleDateString('mk-MK');
                                        const datumDo = new Date(item.datumDo).toLocaleDateString('mk-MK');
                                        const iznosFormatted = parseFloat(item.iznos).toFixed(2);
                                        
                                        const displayText = `Фактура: ${item.brojNaFaktura}`;
                                        
                                        html += `<a href="#" class="list-group-item list-group-item-action" data-broj="${item.brojNaFaktura}">
                                                  <div class="d-flex w-100 justify-content-between">
                                                    <h6 class="mb-1">${displayText}</h6>
                                                  </div>
                                                  <p class="mb-1">ID: ${item.id} | Фактура до: ${item.fakturaDo}</p>
                                                  <p class="mb-1">Износ: ${iznosFormatted} | Датум на фактура: ${datumNaFaktura}</p>
                                                  <small>Период: ${datumOd} - ${datumDo}</small>
                                               </a>`;
                                    });
                                    html += '</div>';
                                    resultsDiv.html(html).show();
                                } else {
                                    resultsDiv.html(`
                                        <div class="list-group">
                                            <div class="list-group-item text-center">
                                                <p class="mb-2">Нема пронајдени резултати</p>
                                            </div>
                                        </div>
                                    `).show();
                                }
                            },
                            error: function() {
                                resultsDiv.html(`
                                    <div class="list-group">
                                        <div class="list-group-item text-center">
                                            <p class="text-danger mb-2">Грешка при пребарување</p>
                                        </div>
                                    </div>
                                `).show();
                            }
                        });
                    }, 300);
                });

                // Handle selection
                $(document).on('click', '#izleznaFakturaSearchResults .list-group-item', function(e) {
                    e.preventDefault();
                    const brojNafaktura = $(this).data('broj');
                    const displayText = $(this).find('h6').text();
                    $('#izleznaFakturaSearch').val(displayText.trim());
                    $('#BrojNaIzleznaFaktura').val(brojNafaktura);
                    $('#izleznaFakturaSearchResults').hide();
                });

                // Hide results when clicking outside
                $(document).on('click', function(e) {
                    if (!$(e.target).closest('#izleznaFakturaSearch, #izleznaFakturaSearchResults').length) {
                        $('#izleznaFakturaSearchResults').hide();
                    }
                });
            }

            // Initialize search for izlezna faktura field
            createIzleznaFakturaSearchFunctionality();

            // Function to create search functionality for izlezna faktura premija
            function createIzleznaFakturaPremijaSearchFunctionality() {
                let searchTimeout;
                const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();

                $('#izleznaFakturaPremijaSearch').on('input', function() {
                    clearTimeout(searchTimeout);
                    const searchTerm = $(this).val();
                    const resultsDiv = $('#izleznaFakturaPremijaSearchResults');

                    if (searchTerm.length < 1) {
                        resultsDiv.hide();
                        return;
                    }

                    searchTimeout = setTimeout(function() {
                        $.ajax({
                            url: '?handler=SearchIzleznaFakturaPremija',
                            type: 'GET',
                            data: { searchTerm: searchTerm },
                            headers: {
                                "RequestVerificationToken": antiForgeryToken
                            },
                            success: function(data) {
                                if (data && data.length > 0) {
                                    let html = '<div class="list-group">';
                                    data.forEach(item => {
                                        const datumNaFaktura = new Date(item.datumNaFaktura).toLocaleDateString('mk-MK');
                                        const premijaFormatted = parseFloat(item.premijaZaNaplata).toFixed(2);
                                        
                                        const displayText = `Фактура: ${item.brojNaFaktura}`;
                                        
                                        html += `<a href="#" class="list-group-item list-group-item-action" data-broj="${item.brojNaFaktura}">
                                                  <div class="d-flex w-100 justify-content-between">
                                                    <h6 class="mb-1">${displayText}</h6>
                                                  </div>
                                                  <p class="mb-1">ID: ${item.id} | Полиса: ${item.brojNaPolisa}</p>
                                                  <p class="mb-1">Осигурител: ${item.osiguritel} | Продукт: ${item.produkt}</p>
                                                  <p class="mb-1">Премија: ${premijaFormatted} | Датум: ${datumNaFaktura}</p>
                                               </a>`;
                                    });
                                    html += '</div>';
                                    resultsDiv.html(html).show();
                                } else {
                                    resultsDiv.html(`
                                        <div class="list-group">
                                            <div class="list-group-item text-center">
                                                <p class="mb-2">Нема пронајдени резултати</p>
                                            </div>
                                        </div>
                                    `).show();
                                }
                            },
                            error: function() {
                                resultsDiv.html(`
                                    <div class="list-group">
                                        <div class="list-group-item text-center">
                                            <p class="text-danger mb-2">Грешка при пребарување</p>
                                        </div>
                                    </div>
                                `).show();
                            }
                        });
                    }, 300);
                });

                // Handle selection
                $(document).on('click', '#izleznaFakturaPremijaSearchResults .list-group-item', function(e) {
                    e.preventDefault();
                    const brojNafaktura = $(this).data('broj');
                    const displayText = $(this).find('h6').text();
                    $('#izleznaFakturaPremijaSearch').val(displayText.trim());
                    $('#BrojNaIzleznaFakturaPremija').val(brojNafaktura);
                    $('#izleznaFakturaPremijaSearchResults').hide();
                });

                // Hide results when clicking outside
                $(document).on('click', function(e) {
                    if (!$(e.target).closest('#izleznaFakturaPremijaSearch, #izleznaFakturaPremijaSearchResults').length) {
                        $('#izleznaFakturaPremijaSearchResults').hide();
                    }
                });
            }

            // Initialize search for izlezna faktura premija field
            createIzleznaFakturaPremijaSearchFunctionality();

            // Handle document type dropdown change for ID 2
            $('#Input_SifrarnikOZTipNaDokumentId').on('change', function() {
                const selectedValue = $(this).val();
                const brojNaDokumentField = $('#Input_BrojNaDokument');
                
                if (selectedValue === '2') {
                    // Lock the field and generate automatic number
                    brojNaDokumentField.prop('readonly', true);
                    brojNaDokumentField.addClass('readonly-field');
                    
                    // Get next OZ number from server
                    const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();
                    $.ajax({
                        url: '?handler=NextOZNumber',
                        type: 'GET',
                        headers: {
                            "RequestVerificationToken": antiForgeryToken
                        },
                        success: function(response) {
                            if (response && response.success) {
                                brojNaDokumentField.val(response.documentNumber);
                            } else {
                                alert('Грешка при генерирање на автоматски број: ' + (response?.message || 'Непозната грешка'));
                            }
                        },
                        error: function(xhr, status, error) {
                            console.log('AJAX error:', xhr.status, status, error);
                            alert('Настана грешка при генерирање на автоматски број.');
                        }
                    });
                } else {
                    // Unlock the field for manual entry
                    brojNaDokumentField.prop('readonly', false);
                    brojNaDokumentField.removeClass('readonly-field');
                    if (brojNaDokumentField.val().startsWith('OZ/')) {
                        brojNaDokumentField.val(''); // Clear auto-generated value
                    }
                }
            });
        });
    </script>
}

@section Styles {
    <style>
        #klientSearchResults .list-group-item.text-center,
        #polisaSearchResults .list-group-item.text-center,
        #vleznaFakturaSearchResults .list-group-item.text-center,
        #izleznaFakturaSearchResults .list-group-item.text-center,
        #izleznaFakturaPremijaSearchResults .list-group-item.text-center {
            padding: 1rem;
        }
        #klientSearchResults .list-group-item.text-center p,
        #polisaSearchResults .list-group-item.text-center p,
        #vleznaFakturaSearchResults .list-group-item.text-center p,
        #izleznaFakturaSearchResults .list-group-item.text-center p,
        #izleznaFakturaPremijaSearchResults .list-group-item.text-center p {
            color: #6c757d;
            margin-bottom: 0.75rem;
        }
        #polisaSearchResults .list-group-item,
        #vleznaFakturaSearchResults .list-group-item,
        #izleznaFakturaSearchResults .list-group-item,
        #izleznaFakturaPremijaSearchResults .list-group-item {
            padding: 0.75rem 1rem;
        }
        #polisaSearchResults .list-group-item h6,
        #vleznaFakturaSearchResults .list-group-item h6,
        #izleznaFakturaSearchResults .list-group-item h6,
        #izleznaFakturaPremijaSearchResults .list-group-item h6 {
            font-size: 0.9rem;
            font-weight: 600;
        }
        #polisaSearchResults .list-group-item p,
        #vleznaFakturaSearchResults .list-group-item p,
        #izleznaFakturaSearchResults .list-group-item p,
        #izleznaFakturaPremijaSearchResults .list-group-item p {
            font-size: 0.8rem;
            color: #6c757d;
        }
        #polisaSearchResults .list-group-item small,
        #vleznaFakturaSearchResults .list-group-item small,
        #izleznaFakturaSearchResults .list-group-item small,
        #izleznaFakturaPremijaSearchResults .list-group-item small {
            font-size: 0.75rem;
            color: #6c757d;
        }
        
        .readonly-field {
            background-color: #f8f9fa !important;
            color: #6c757d !important;
            cursor: not-allowed !important;
        }
        
        /* Styles for disabled search fields */
        .disabled-search-field {
            background-color: #f8f9fa !important;
            color: #6c757d !important;
            cursor: not-allowed !important;
            opacity: 0.6 !important;
        }
        
        .disabled-clear-button {
            background-color: #f8f9fa !important;
            color: #6c757d !important;
            cursor: not-allowed !important;
            opacity: 0.6 !important;
            border-color: #dee2e6 !important;
        }
        
        .disabled-clear-button:hover {
            background-color: #f8f9fa !important;
            color: #6c757d !important;
            border-color: #dee2e6 !important;
        }
    </style>
}
