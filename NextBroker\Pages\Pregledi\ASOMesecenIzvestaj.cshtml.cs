using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.Data;
using Microsoft.Data.SqlClient;
using System.ComponentModel.DataAnnotations;
using OfficeOpenXml;
using OfficeOpenXml.Style;

namespace NextBroker.Pages.Pregledi
{
    public class ASOMesecenIzvestajModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public ASOMesecenIzvestajModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
            Year = DateTime.Now.Year;
            SelectedMonth = DateTime.Now.Month;
        }

        [BindProperty]
        public int Year { get; set; }

        [BindProperty]
        public int SelectedMonth { get; set; }

        public DataTable ReportData { get; set; }
        
        public string ArchiveMessage { get; set; }
        
        public bool ArchiveSuccess { get; set; }
        
        public bool IsArchived { get; set; }
        
        public string ReportExistsMessage { get; set; }

        public List<MonthModel> Months { get; } = new List<MonthModel>
        {
            new MonthModel { Value = 1, Name = "Јануари" },
            new MonthModel { Value = 2, Name = "Февруари" },
            new MonthModel { Value = 3, Name = "Март" },
            new MonthModel { Value = 4, Name = "Април" },
            new MonthModel { Value = 5, Name = "Мај" },
            new MonthModel { Value = 6, Name = "Јуни" },
            new MonthModel { Value = 7, Name = "Јули" },
            new MonthModel { Value = 8, Name = "Август" },
            new MonthModel { Value = 9, Name = "Септември" },
            new MonthModel { Value = 10, Name = "Октомври" },
            new MonthModel { Value = 11, Name = "Ноември" },
            new MonthModel { Value = 12, Name = "Декември" }
        };

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("ASOMesecenIzvestaj"))
            {
                return RedirectToAccessDenied();
            }
            
            // Check if we have archived status from previous request
            if (TempData["IsArchived"] != null)
            {
                IsArchived = (bool)TempData["IsArchived"];
                // Persist the flag
                TempData.Keep("IsArchived");
            }
            
            // Clear any existing report exists message
            ReportExistsMessage = null;
            
            return Page();
        }
        
        /// <summary>
        /// Check if a report already exists for the selected month/year
        /// </summary>
        private async Task<bool> ReportExistsAsync(string startDate, string endDate)
        {
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                
                using (var command = new SqlCommand("SELECT COUNT(*) FROM ASOMesecenIzvestajArhivaNaIzvestai WHERE DatumIzvestajStartDate = @StartDate AND DatumIzvestajEndDate = @EndDate", connection))
                {
                    command.Parameters.AddWithValue("@StartDate", startDate);
                    command.Parameters.AddWithValue("@EndDate", endDate);
                    
                    int count = (int)await command.ExecuteScalarAsync();
                    return count > 0;
                }
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!await HasPageAccess("ASOMesecenIzvestaj"))
            {
                return RedirectToAccessDenied();
            }

            var firstDayOfMonth = new DateTime(Year, SelectedMonth, 1);
            var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);

            string startDate = firstDayOfMonth.ToString("yyyyMMdd");
            string endDate = lastDayOfMonth.ToString("yyyyMMdd");
            
            // Check if a report already exists for this period
            if (await ReportExistsAsync(startDate, endDate))
            {
                // Set message that report already exists
                ReportExistsMessage = "Веќе е генериран извештај за овој месец / година, може да го превземете преку архива на извештаи.";
                return Page();
            }

            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();

                using (var command = new SqlCommand("IzvestajASOMesecen", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@StartDate", startDate);
                    command.Parameters.AddWithValue("@EndDate", endDate);

                    using (var adapter = new SqlDataAdapter(command))
                    {
                        ReportData = new DataTable();
                        adapter.Fill(ReportData);
                    }
                }
            }
            
            // Reset the archived status when generating a new report
            IsArchived = false;
            TempData["IsArchived"] = false;
            
            // Clear any existing report exists message
            ReportExistsMessage = null;

            return Page();
        }

        public async Task<IActionResult> OnPostExportExcelAsync()
        {
            if (!await HasPageAccess("ASOMesecenIzvestaj"))
            {
                return RedirectToAccessDenied();
            }

            var firstDayOfMonth = new DateTime(Year, SelectedMonth, 1);
            var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);

            string startDate = firstDayOfMonth.ToString("yyyyMMdd");
            string endDate = lastDayOfMonth.ToString("yyyyMMdd");

            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();

                using (var command = new SqlCommand("IzvestajASOMesecen", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@StartDate", startDate);
                    command.Parameters.AddWithValue("@EndDate", endDate);

                    using (var adapter = new SqlDataAdapter(command))
                    {
                        var dt = new DataTable();
                        adapter.Fill(dt);

                        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                        using (var package = new ExcelPackage())
                        {
                            var worksheet = package.Workbook.Worksheets.Add("АСО Извештај");

                            // Add report header
                            worksheet.Cells["A1"].Value = "Образец обд 4";
                            worksheet.Cells["A1:E1"].Merge = true;
                            worksheet.Cells["A1"].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                            worksheet.Cells["A1"].Style.Font.Bold = true;

                            worksheet.Cells["A3"].Value = "Осигурително брокерско друштво";
                            worksheet.Cells["E3"].Value = "ОБД ИНКО АД СКОПЈЕ";

                            worksheet.Cells["A6"].Value = "Година:";
                            worksheet.Cells["B6"].Value = Year;

                            worksheet.Cells["A7"].Value = "Месец:";
                            worksheet.Cells["B7"].Value = Months.First(m => m.Value == SelectedMonth).Name;

                            // Add some spacing before the table
                            int tableStartRow = 10;

                            // Add headers
                            for (int i = 0; i < dt.Columns.Count; i++)
                            {
                                var cell = worksheet.Cells[tableStartRow, i + 1];
                                cell.Value = dt.Columns[i].ColumnName;
                                cell.Style.Font.Bold = true;
                                cell.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                                cell.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                                cell.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                                cell.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                            }

                            // Add data
                            for (int row = 0; row < dt.Rows.Count; row++)
                            {
                                for (int col = 0; col < dt.Columns.Count; col++)
                                {
                                    var cellValue = dt.Rows[row][col];
                                    var cell = worksheet.Cells[row + tableStartRow + 1, col + 1];

                                    if (cellValue is DateTime dateValue)
                                    {
                                        cell.Style.Numberformat.Format = "dd/mm/yyyy";
                                        cell.Value = dateValue;
                                    }
                                    else if (cellValue is decimal || cellValue is double || cellValue is float)
                                    {
                                        cell.Style.Numberformat.Format = "#,##0.00";
                                        cell.Value = cellValue;
                                    }
                                    else
                                    {
                                        cell.Value = cellValue;
                                    }

                                    // Add borders to data cells
                                    cell.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                                    cell.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                                    cell.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                                    cell.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                                }
                            }

                            // Auto-fit columns
                            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                            // Generate the Excel file
                            var content = package.GetAsByteArray();
                            var monthName = Months.First(m => m.Value == SelectedMonth).Name;
                            var fileName = $"ASO_Izvestaj_{monthName}_{Year}.xlsx";
                            
                            // Get current archived status from TempData if it exists
                            if (TempData["IsArchived"] != null)
                            {
                                IsArchived = (bool)TempData["IsArchived"];
                                TempData.Keep("IsArchived");
                            }
                            
                            return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                        }
                    }
                }
            }
        }
        
        public async Task<IActionResult> OnPostArchiveAsync()
        {
            if (!await HasPageAccess("ASOMesecenIzvestaj"))
            {
                return RedirectToAccessDenied();
            }
            
            var firstDayOfMonth = new DateTime(Year, SelectedMonth, 1);
            var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);

            string startDate = firstDayOfMonth.ToString("yyyyMMdd");
            string endDate = lastDayOfMonth.ToString("yyyyMMdd");
            
            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    
                    // Execute the first stored procedure: ASOMesecenIzvestajArhiviranje
                    using (var command = new SqlCommand("ASOMesecenIzvestajArhiviranje", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);
                        await command.ExecuteNonQueryAsync();
                    }
                    
                    // Execute the second stored procedure: ASOMesecenIzvestajPolnenjeZavrseniPolisi
                    using (var command = new SqlCommand("ASOMesecenIzvestajPolnenjeZavrseniPolisi", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);
                        await command.ExecuteNonQueryAsync();
                    }
                    
                    // Reload the report data to reflect any changes
                    using (var command = new SqlCommand("IzvestajASOMesecen", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);

                        using (var adapter = new SqlDataAdapter(command))
                        {
                            ReportData = new DataTable();
                            adapter.Fill(ReportData);
                        }
                    }
                }
                
                ArchiveMessage = "Архивирањето беше успешно";
                ArchiveSuccess = true;
                
                // Set archived flag to true to hide the button
                IsArchived = true;
                TempData["IsArchived"] = true;
            }
            catch (Exception ex)
            {
                ArchiveMessage = "Архивирањето не беше успешно";
                ArchiveSuccess = false;
                
                // Log the exception (you might want to use a proper logging framework)
                System.Diagnostics.Debug.WriteLine($"Error in archiving: {ex.Message}");
            }
            
            return Page();
        }
    }

    public class MonthModel
    {
        public int Value { get; set; }
        public string Name { get; set; }
    }
}
