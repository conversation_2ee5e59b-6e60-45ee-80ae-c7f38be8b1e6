@page "{id:long?}/{handler?}"
@model NextBroker.Pages.Finansii.UplatiGenerirajKasaPrimiModel
@using NextBroker.Helpers
@{
    ViewData["Title"] = "Генерирај каса прими";
    var formClass = !Model.HasAdminAccess ? "form-disabled" : "";
}

<div class="container mt-4">
    <div class="text-end mb-2">
        <button type="button" class="btn btn-primary" onclick="generatePdf()">
            <i class="fas fa-file-pdf me-1"></i> Генерирај PDF
        </button>
    </div>
</div>

<div class="container mt-4">
    <div class="card">
        <div class="card-body">
            <div id="documentContent" style="width: 21cm; margin: 0 auto; padding: 1cm; border: 2px solid #004080;">
                <div style="text-align: center; margin-bottom: 1cm;">
                    <h4 style="margin-bottom: 0.3cm;">Осигурително Брокерско Друштво ИНКО АД Скопје</h4>
                    <p style="margin-bottom: 0.2cm;">Касата да</p>
                    <h5 style="margin-bottom: 0.5cm;">
                        ПРИМИ бр. @(Model.Uplata.KasaPrimi?.ToString() ?? "______")
                    </h5>
                </div>

                <div style="margin-bottom: 0.5cm;">
                    <p>
                        <span style="margin-right: 10px;">Од:</span>
                        <span style="border-bottom: 1px solid #000;">@Model.Uplata.UplakjacInfo</span>
                    </p>
                </div>

                <div style="margin-bottom: 0.5cm;">
                    <p>
                        <span style="margin-right: 10px;">сумата од:</span>
                        <span style="border-bottom: 1px solid #000;">@Model.Uplata.Iznos.ToString("N2") денари</span>
                    </p>
                </div>

                <div style="margin-bottom: 0.5cm;">
                    <p>
                        <span style="margin-right: 10px;">со букви:</span>
                        <input type="text" 
                               id="soBukvi"
                               class="form-control-plaintext" 
                               style="display: inline-block; width: calc(100% - 120px); border-bottom: 1px solid #000;" 
                               value="@NumberToMacedonianWordsConverter.Convert(Model.Uplata.Iznos)"
                               onchange="this.setAttribute('value', this.value);"
                               oninput="this.style.width = ((this.value.length + 1) * 8) + 'px';">
                    </p>
                </div>

                <div style="margin-bottom: 0.5cm;">
                    <p>
                        <span style="margin-right: 10px;">Цел на уплата:</span>
                        <input type="text" 
                               id="celNaUplata"
                               class="form-control-plaintext" 
                               style="display: inline-block; width: calc(100% - 120px); border-bottom: 1px solid #000;" 
                               value="Уплата по основ полиса број: @(Model.Uplata.PolisaBroj?.ToString() ?? "____")"
                               onchange="this.setAttribute('value', this.value);"
                               oninput="this.style.width = ((this.value.length + 1) * 8) + 'px';">
                    </p>
                </div>

                <div style="margin-bottom: 0.5cm;">
                    <p>
                        <span style="margin-right: 10px;">Издал:</span>
                        <span style="border-bottom: 1px solid #000;">@Model.Izdal</span>
                    </p>
                </div>

                <div style="margin-bottom: 1cm;">
                    <p>
                        <span style="margin-right: 10px;">Место:</span>
                        <span style="border-bottom: 1px solid #000;">@Model.Mesto</span>
                    </p>
                </div>

                <div style="margin-bottom: 1cm;">
                    <p>
                        <span>Датум: </span>
                        <span>@DateTime.Now.ToString("dd.MM.yyyy")</span>
                    </p>
                </div>

                <div style="margin-top: 1cm;">
                    <div class="row">
                        <div class="col-4 text-center">
                            <p>Генерален директор</p>
                            <div style="border-top: 1px solid #000; margin-top: 1cm;"></div>
                        </div>
                        <div class="col-4 text-center">
                            <p>Директор на сектор</p>
                            <div style="border-top: 1px solid #000; margin-top: 1cm;"></div>
                        </div>
                        <div class="col-4 text-center">
                            <p>Благајник</p>
                            <div style="border-top: 1px solid #000; margin-top: 1cm;"></div>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 1cm; text-align: right;">
                    <p>Уплатил</p>
                    <div style="border-top: 1px solid #000; width: 200px; margin-left: auto; margin-top: 0.5cm;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function generatePdf() {
            // Get the modified values from the form
            var soBukvi = document.getElementById('soBukvi').value;
            var celNaUplata = document.getElementById('celNaUplata').value;
            // Create the URL with parameters
            window.location.href = `/Finansii/UplatiGenerirajKasaPrimi/${@Model.Id}/GeneratePdf` + 
                `?soBukvi=${encodeURIComponent(soBukvi)}` +
                `&celNaUplata=${encodeURIComponent(celNaUplata)}` +
                `&opstina=Датум `;
        }
    </script>
} 