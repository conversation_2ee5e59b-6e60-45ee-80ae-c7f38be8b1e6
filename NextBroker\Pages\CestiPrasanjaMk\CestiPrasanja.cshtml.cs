﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RazorPortal.Services;
using System.Threading.Tasks;

namespace RazorPortal.Pages
{
    public class CestiPrasanjaModel : PageModel
    {
        private readonly MailerService _mailerService;

        public CestiPrasanjaModel(MailerService mailerService)
        {
            _mailerService = mailerService;
        }

        [BindProperty]
        public string Name { get; set; }

        [BindProperty]
        public string Surname { get; set; }

        [BindProperty]
        public string Email { get; set; }

        [BindProperty]
        public string Phone { get; set; }

        [BindProperty]
        public string Question { get; set; }

        [BindProperty]
        public string Description { get; set; }

        public string SuccessMessage { get; private set; }
        public string ErrorMessage { get; private set; } // Added for error messages

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                ErrorMessage = "Сите полиња се задолжителни."; // Set error message if validation fails
                return Page(); // Return the page with validation errors
            }

            var subject = Question; // Use the Description as the subject
            var messageBody = $"Име: {Name}<br>Презиме: {Surname}<br>Е-Маил за контакт: {Email}<br>Телефон за контакт: {Phone}<br>Прашање: {Question}<br>Опис: {Description}";

            await _mailerService.SendEmailAsync("<EMAIL>", subject, messageBody);

            SuccessMessage = "Прашањето е испратено, ќе бидете контактирани во најкраток можен рок. Ви благодариме!";
            return Page(); // Return the page to display the success message
        }
    }
}
