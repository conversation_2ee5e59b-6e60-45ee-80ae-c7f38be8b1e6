.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #f8f9fa;
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #0077cc;
    padding: 0.5rem 1rem;
    position: relative;
    transition: color 0.3s ease-in-out, background-color 0.3s ease-in-out;
    display: flex;
    align-items: center;
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

.sidebar .nav-link:hover {
    background-color: rgba(76, 175, 80, 0.2);
    color: steelblue;
    border-radius: 0.5rem;
}

.sidebar .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 1px;
    background-color: #003366;
    transition: width 0.2s ease-in-out, left 0.2s ease-in-out;
    transform: translateX(-50%);
}

.sidebar .nav-link:hover::after {
    width: 100%;
    left: 50%;
}

.sidebar .nav-link.active {
    color: #003366;
    background-color: rgba(76, 175, 80, 0.2);
    border-radius: 0.5rem;
}

.sidebar .nav-link.active::after {
    width: 100%;
}

.sidebar .nav-item {
    margin-bottom: 0.5rem;
}

.sidebar .nav-item:not(:first-child) {
    border-top: 1px solid rgba(211, 211, 211, 0.7);
    padding-top: 0.5rem;
}

main {
    padding-top: 1.5rem;
}

@media (max-width: 767.98px) {
    .sidebar {
        top: 5rem;
    }
}

/* Table Styles */
.admin-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    font-size: 14px;
}

.admin-table thead {
    background-color: #E6F3FF;
}

.admin-table th {
    color: #003366;
    font-weight: bold;
}

.admin-table th, .admin-table td {
    padding: 8px 12px;
    border-bottom: 1px solid #ddd;
    vertical-align: middle;
}

.admin-table tbody tr:nth-child(even) {
    background-color: #E8F5E9;
}

.admin-table tbody tr:nth-child(odd) {
    background-color: #ffffff;
}

.admin-table tbody tr:hover {
    background-color: #C8E6C9;
}

/* Form and Button Styles */
.admin-form-inline {
    display: flex;
    align-items: center;
    gap: 8px;
}

.admin-select {
    width: auto;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #ccc;
    box-sizing: border-box;
    font-size: 14px;
}

.admin-btn {
    background-color: rgba(0, 51, 102, 0.8);
    color: white;
    border-radius: 5px;
    padding: 0.5rem 1rem;
    font-size: 14px;
    transition: background-color 0.3s ease, border-radius 0.3s ease;
    text-align: center;
    border: none;
    white-space: nowrap;
    cursor: pointer;
}

.admin-btn:hover {
    background-color: rgba(0, 51, 102, 0.3);
    border-radius: 8px;
    color: #003366;
}

/* DataTables Custom Styles */
.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.3em 0.8em;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background-color: #003366 !important;
    color: white !important;
    border: none !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: #E6F3FF !important;
    color: #003366 !important;
    border: none !important;
}

.dataTables_wrapper .dataTables_filter input {
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #ccc;
    font-size: 14px;
}

.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    font-size: 14px;
    padding-top: 0.5em;
}

/* Admin Panel Table Design */
.adminpaneltable {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    font-size: 14px;
}

.adminpaneltable thead {
    background-color: #E6F3FF;
}

.adminpaneltable th {
    color: #003366;
    font-weight: bold;
}

.adminpaneltable th, .adminpaneltable td {
    padding: 8px 12px;
    border-bottom: 1px solid #ddd;
    vertical-align: middle;
}

.adminpaneltable tbody tr:nth-child(even) {
    background-color: #E8F5E9;
}

.adminpaneltable tbody tr:nth-child(odd) {
    background-color: #ffffff;
}

.adminpaneltable tbody tr:hover {
    background-color: #C8E6C9;
}

.adminpaneltable .role-action {
    display: flex;
    align-items: center;
    gap: 8px;
}

.adminpaneltable select {
    width: auto;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #ccc;
    box-sizing: border-box;
    font-size: 14px;
}

.adminpaneltable .btn-primary {
    background-color: rgba(0, 51, 102, 0.8);
    color: white !important;
    border-radius: 5px;
    padding: 0.5rem 1rem;
    font-size: 14px;
    transition: background-color 0.3s ease, border-radius 0.3s ease;
    text-align: center;
    border: none;
    white-space: nowrap;
}

.adminpaneltable .btn-primary:hover {
    background-color: rgba(0, 51, 102, 0.3);
    border-radius: 8px;
    color: #003366 !important;
}

/* DataTables Custom Styles for Admin Panel Table */
.adminpaneltable-wrapper .dataTables_paginate .paginate_button {
    padding: 0.3em 0.8em;
}

.adminpaneltable-wrapper .dataTables_paginate .paginate_button.current,
.adminpaneltable-wrapper .dataTables_paginate .paginate_button.current:hover {
    background-color: #003366 !important;
    color: white !important;
    border: none !important;
}

.adminpaneltable-wrapper .dataTables_paginate .paginate_button:hover {
    background-color: #E6F3FF !important;
    color: #003366 !important;
    border: none !important;
}

.adminpaneltable-wrapper .dataTables_filter input {
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #ccc;
    font-size: 14px;
}

.adminpaneltable-wrapper .dataTables_info,
.adminpaneltable-wrapper .dataTables_paginate {
    font-size: 14px;
    padding-top: 0.5em;
}

.add-user-toggle {
    margin-bottom: 1rem;
}

.add-user-section {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 1rem;
    margin-bottom: 1rem;
    overflow: hidden;
    transition: max-height 0.3s ease-out, opacity 0.3s ease-out, margin-bottom 0.3s ease-out;
    max-height: 0;
    opacity: 0;
}

.add-user-section.show {
    max-height: 300px; /* Adjust this value based on your content */
    opacity: 1;
    margin-bottom: 1rem;
}

.add-user-form {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: flex-end;
}

.add-user-form .form-group {
    flex: 1;
    min-width: 200px;
}

.add-user-form input,
.add-user-form select {
    width: 100%;
}

.add-user-form button {
    white-space: nowrap;
}

@media (max-width: 768px) {
    .add-user-form {
        flex-wrap: wrap;
    }

    .add-user-form .form-group {
        flex-basis: 100%;
    }
}

.user-action-form {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.user-action-form .form-group {
    flex: 1 1 calc(50% - 0.5rem); /* Subtracting half of the gap */
    min-width: 200px;
    display: flex;
}

.user-action-form .form-group input,
.user-action-form .form-group select {
    width: 100%;
    height: 38px; /* Adjust this value to match your button height */
}

.user-action-form .submit-button {
    flex-basis: 100%;
    margin-top: 1rem;
}

.user-action-form button {
    width: 100%;
}

@media (max-width: 768px) {
    .user-action-form .form-group {
        flex-basis: 100%;
    }
}

.user-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem; /* Increased from 1rem to 2rem */
}

.user-action-section {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 1rem;
    margin-bottom: 2rem; /* Increased from 1rem to 2rem */
    overflow: hidden;
    transition: max-height 0.3s ease-out, opacity 0.3s ease-out, margin-bottom 0.3s ease-out;
    max-height: 0;
    opacity: 0;
}

.user-action-section.show {
    max-height: 300px;
    opacity: 1;
    margin-bottom: 2rem; /* Increased from 1rem to 2rem */
}

/* Add this new class */
.actions-container {
    margin-bottom: 2rem; /* Add space below the buttons and forms */
}

/* Add these styles to your existing admin.css file */

.list-group {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.list-group-item {
    cursor: pointer;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.list-group-item.active {
    background-color: #007bff;
    color: white;
}

#grantedAccessList .list-group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#grantedAccessList .list-group-item button {
    padding: 2px 5px;
    font-size: 12px;
}

.list-group-item .btn-sm {
    width: 70px;  /* Adjust this value as needed */
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

.list-group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem;
}

.list-group-item span {
    flex-grow: 1;
    margin-right: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.action-btn {
    width: 70px;
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    background-color: #0056b3;  /* Dark blue color */
    border-color: #0056b3;
}

.action-btn:hover {
    background-color: #003d7a;  /* Darker blue for hover state */
    border-color: #003d7a;
}

#userList, #grantedAccessList, #pagesList {
    height: 400px;
    overflow-y: auto;
}
