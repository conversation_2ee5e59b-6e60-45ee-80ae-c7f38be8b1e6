using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Threading.Tasks;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Drawing;

namespace NextBroker.Pages.Finansii
{
    public class Odobruvanje<PERSON>adolzuvanje
    {
        public int Id { get; set; }
        [DisplayName("Датум на креирање")]
        public DateTime DateCreated { get; set; }
        [DisplayName("Креирано од")]
        public string UsernameCreated { get; set; }
        [DisplayName("Датум на промена")]
        public DateTime? DateModified { get; set; }
        [DisplayName("Променето од")]
        public string UsernameModified { get; set; }
        public int? SifrarnikOZTipNaDokumentId { get; set; }
        public int? SifrarnikOZTipId { get; set; }
        public int? SifrarnikOZPremijaProvizijaId { get; set; }
        public int? KlientiIdKlient { get; set; }
        [DisplayName("Тип на документ")]
        public string TipNaDokument { get; set; }
        [DisplayName("Тип")]
        public string Tip { get; set; }
        [DisplayName("Премија/Провизија")]
        public string PremijaProvizija { get; set; }
        [DisplayName("Клиент")]
        public string KlientImePrezimeNaziv { get; set; }
        [DisplayName("Број на документ")]
        public string BrojNaDokument { get; set; }
        [DisplayName("Датум на документ")]
        public DateTime? DatumNaDokument { get; set; }
        [DisplayName("Износ")]
        public decimal? Iznos { get; set; }
        [DisplayName("Број на влезна фактура")]
        public string BrojNaVleznaFaktura { get; set; }
        [DisplayName("Број на излезна фактура")]
        public string BrojNaIzleznaFaktura { get; set; }
        public int? PolisaId { get; set; }
        [DisplayName("Број на полиса")]
        public string BrojNaPolisa { get; set; }
        [DisplayName("Тип на фактура")]
        public string TipNafaktura { get; set; }
        [DisplayName("Број на излезна фактура премија")]
        public string BrojNaIzleznaFakturaPremija { get; set; }
        [DisplayName("Опис")]
        public string Opis { get; set; }
        public bool Storno { get; set; }
        public string FilePath { get; set; }
        public string FileName { get; set; }
    }

    public class OZListaModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public OZListaModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public List<OdobruvanjeZadolzuvanje> OdobruvanjaZadolzuvanja { get; set; }
        
        // Date filter properties
        [BindProperty]
        [DisplayName("Датум од")]
        public DateTime? DatumOd { get; set; }
        
        [BindProperty]
        [DisplayName("Датум до")]
        public DateTime? DatumDo { get; set; }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("OdobruvanjeZadolzuvanje"))
            {
                return RedirectToAccessDenied();
            }

            await LoadOdobruvanjaZadolzuvanja();
            return Page();
        }

        // New POST method for date filtering
        public async Task<IActionResult> OnPostFilter()
        {
            if (!await HasPageAccess("OdobruvanjeZadolzuvanje"))
            {
                return RedirectToAccessDenied();
            }

            await LoadOdobruvanjaZadolzuvanja(DatumOd, DatumDo);
            return Page();
        }

        // New POST method for Excel export
        public async Task<IActionResult> OnPostExportExcel()
        {
            if (!await HasPageAccess("OdobruvanjeZadolzuvanje"))
            {
                return RedirectToAccessDenied();
            }

            // Load data with current filter settings
            await LoadOdobruvanjaZadolzuvanja(DatumOd, DatumDo);

            // Set EPPlus license context
            ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Одобрување Задолжување");

            // Define headers
            var headers = new[]
            {
                "ID", "Тип на документ", "Тип", "Премија/Провизија", "Клиент", 
                "Број на документ", "Датум на документ", "Износ", "Број на влезна фактура",
                "Број на излезна фактура", "Број на полиса", "Тип на фактура", 
                "Број на излезна фактура премија", "Опис", "Креирано од", 
                "Датум на креирање", "Променето од", "Датум на промена", "Сторно"
            };

            // Add headers
            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
            }

            // Style headers
            using (var range = worksheet.Cells[1, 1, 1, headers.Length])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(Color.LightBlue);
                range.Style.Border.BorderAround(ExcelBorderStyle.Thin);
                range.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            }

            // Add data rows
            for (int row = 0; row < OdobruvanjaZadolzuvanja.Count; row++)
            {
                var item = OdobruvanjaZadolzuvanja[row];
                int excelRow = row + 2; // Excel is 1-indexed, and we start from row 2 (after headers)

                worksheet.Cells[excelRow, 1].Value = item.Id;
                worksheet.Cells[excelRow, 2].Value = item.TipNaDokument ?? "";
                worksheet.Cells[excelRow, 3].Value = item.Tip ?? "";
                worksheet.Cells[excelRow, 4].Value = item.PremijaProvizija ?? "";
                worksheet.Cells[excelRow, 5].Value = item.KlientImePrezimeNaziv ?? "";
                worksheet.Cells[excelRow, 6].Value = item.BrojNaDokument ?? "";
                worksheet.Cells[excelRow, 7].Value = item.DatumNaDokument?.ToString("dd.MM.yyyy") ?? "";
                worksheet.Cells[excelRow, 8].Value = item.Iznos?.ToString("N2") ?? "";
                worksheet.Cells[excelRow, 9].Value = item.BrojNaVleznaFaktura ?? "";
                worksheet.Cells[excelRow, 10].Value = item.BrojNaIzleznaFaktura ?? "";
                worksheet.Cells[excelRow, 11].Value = item.BrojNaPolisa ?? "";
                worksheet.Cells[excelRow, 12].Value = item.TipNafaktura ?? "";
                worksheet.Cells[excelRow, 13].Value = item.BrojNaIzleznaFakturaPremija ?? "";
                worksheet.Cells[excelRow, 14].Value = item.Opis ?? "";
                worksheet.Cells[excelRow, 15].Value = item.UsernameCreated ?? "";
                worksheet.Cells[excelRow, 16].Value = item.DateCreated.ToString("dd.MM.yyyy HH:mm");
                worksheet.Cells[excelRow, 17].Value = item.UsernameModified ?? "";
                worksheet.Cells[excelRow, 18].Value = item.DateModified?.ToString("dd.MM.yyyy HH:mm") ?? "";
                worksheet.Cells[excelRow, 19].Value = item.Storno ? "Да" : "Не";

                // Style data rows - alternate row coloring
                if (row % 2 == 0)
                {
                    using (var range = worksheet.Cells[excelRow, 1, excelRow, headers.Length])
                    {
                        range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        range.Style.Fill.BackgroundColor.SetColor(Color.FromArgb(240, 248, 255));
                    }
                }

                // Style numeric columns
                worksheet.Cells[excelRow, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center; // ID
                worksheet.Cells[excelRow, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right; // Износ
                worksheet.Cells[excelRow, 16].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center; // Датум на креирање
                worksheet.Cells[excelRow, 18].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center; // Датум на промена
                worksheet.Cells[excelRow, 19].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center; // Сторно

                // Color storno rows
                if (item.Storno)
                {
                    using (var range = worksheet.Cells[excelRow, 1, excelRow, headers.Length])
                    {
                        range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        range.Style.Fill.BackgroundColor.SetColor(Color.FromArgb(255, 240, 240));
                        range.Style.Font.Color.SetColor(Color.DarkRed);
                    }
                }
            }

            // Auto-fit columns
            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

            // Add borders to all cells with data
            if (OdobruvanjaZadolzuvanja.Count > 0)
            {
                using (var range = worksheet.Cells[1, 1, OdobruvanjaZadolzuvanja.Count + 1, headers.Length])
                {
                    range.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    range.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    range.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                    range.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                }
            }

            // Generate filename with current date and filter info
            string filename = "OdobruvanjaZadolzuvanja_" + DateTime.Now.ToString("yyyy_MM_dd_HH_mm");
            if (DatumOd.HasValue || DatumDo.HasValue)
            {
                filename += "_filtered";
                if (DatumOd.HasValue)
                    filename += "_od_" + DatumOd.Value.ToString("yyyy_MM_dd");
                if (DatumDo.HasValue)
                    filename += "_do_" + DatumDo.Value.ToString("yyyy_MM_dd");
            }
            filename += ".xlsx";

            // Return the Excel file
            var fileBytes = package.GetAsByteArray();
            return File(fileBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", filename);
        }

        private async Task LoadOdobruvanjaZadolzuvanja()
        {
            await LoadOdobruvanjaZadolzuvanja(null, null);
        }

        // Overloaded method with date filtering
        private async Task LoadOdobruvanjaZadolzuvanja(DateTime? datumOd, DateTime? datumDo)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                string baseQuery = @"
                    SELECT 
                        oz.Id,
                        oz.DateCreated,
                        oz.UsernameCreated,
                        oz.DateModified,
                        oz.UsernameModified,
                        soztnd.TipNaDokument,
                        sozt.Tip,
                        sozpp.PremijaProvizija,
                        ISNULL(klnt.ime, '') + ' ' + ISNULL(klnt.prezime, '') + ' ' + ISNULL(klnt.naziv, '') AS KlientImePrezimeNaziv,
                        oz.BrojNaDokument,
                        oz.DatumNaDokument,
                        oz.Iznos,
                        oz.BrojNaVleznaFaktura,
                        oz.BrojNaIzleznaFaktura,
                        oz.PolisaId,
                        pol.BrojNaPolisa,
                        oz.TipNafaktura,
                        oz.BrojNaIzleznaFakturaPremija,
                        oz.Opis,
                        oz.Storno,
                        oz.FilePath,
                        oz.FileName
                    FROM OdobruvanjeZadolzuvanje oz
                    left join Polisi pol on oz.PolisaId = pol.id
                    left join SifrarnikOZTipNaDokument soztnd on oz.SifrarnikOZTipNaDokumentId = soztnd.id
                    left join SifrarnikOZTip sozt on oz.SifrarnikOZTipId = sozt.id
                    left join SifrarnikOZPremijaProvizija sozpp on oz.SifrarnikOZPremijaProvizijaId = sozpp.id
                    left join klienti klnt on oz.KlientiIdKlient = klnt.Id";

                string whereClause = "";
                var parameters = new List<SqlParameter>();

                // Add date filtering if dates are provided
                if (datumOd.HasValue || datumDo.HasValue)
                {
                    var conditions = new List<string>();
                    
                    if (datumOd.HasValue)
                    {
                        conditions.Add("oz.DateCreated >= @DatumOd");
                        parameters.Add(new SqlParameter("@DatumOd", datumOd.Value.Date));
                    }
                    
                    if (datumDo.HasValue)
                    {
                        conditions.Add("oz.DateCreated <= @DatumDo");
                        parameters.Add(new SqlParameter("@DatumDo", datumDo.Value.Date.AddDays(1).AddTicks(-1)));
                    }
                    
                    if (conditions.Count > 0)
                    {
                        whereClause = " WHERE " + string.Join(" AND ", conditions);
                    }
                }

                string fullQuery = baseQuery + whereClause + " ORDER BY oz.DateCreated DESC";

                using (SqlCommand cmd = new SqlCommand(fullQuery, connection))
                {
                    // Add parameters to command
                    foreach (var param in parameters)
                    {
                        cmd.Parameters.Add(param);
                    }

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<OdobruvanjeZadolzuvanje>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new OdobruvanjeZadolzuvanje
                        {
                            Id = Convert.ToInt32(reader["Id"]),
                            DateCreated = Convert.ToDateTime(reader["DateCreated"]),
                            UsernameCreated = reader["UsernameCreated"]?.ToString() ?? string.Empty,
                            DateModified = reader["DateModified"] != DBNull.Value ? Convert.ToDateTime(reader["DateModified"]) : null,
                            UsernameModified = reader["UsernameModified"]?.ToString() ?? string.Empty,
                            TipNaDokument = reader["TipNaDokument"]?.ToString() ?? string.Empty,
                            Tip = reader["Tip"]?.ToString() ?? string.Empty,
                            PremijaProvizija = reader["PremijaProvizija"]?.ToString() ?? string.Empty,
                            KlientImePrezimeNaziv = reader["KlientImePrezimeNaziv"]?.ToString() ?? string.Empty,
                            BrojNaDokument = reader["BrojNaDokument"]?.ToString() ?? string.Empty,
                            DatumNaDokument = reader["DatumNaDokument"] != DBNull.Value ? Convert.ToDateTime(reader["DatumNaDokument"]) : null,
                            Iznos = reader["Iznos"] != DBNull.Value ? Convert.ToDecimal(reader["Iznos"]) : null,
                            BrojNaVleznaFaktura = reader["BrojNaVleznaFaktura"]?.ToString() ?? string.Empty,
                            BrojNaIzleznaFaktura = reader["BrojNaIzleznaFaktura"]?.ToString() ?? string.Empty,
                            PolisaId = reader["PolisaId"] != DBNull.Value ? Convert.ToInt32(reader["PolisaId"]) : null,
                            BrojNaPolisa = reader["BrojNaPolisa"]?.ToString() ?? string.Empty,
                            TipNafaktura = reader["TipNafaktura"]?.ToString() ?? string.Empty,
                            BrojNaIzleznaFakturaPremija = reader["BrojNaIzleznaFakturaPremija"]?.ToString() ?? string.Empty,
                            Opis = reader["Opis"]?.ToString() ?? string.Empty,
                            Storno = reader["Storno"] != DBNull.Value ? Convert.ToBoolean(reader["Storno"]) : false,
                            FilePath = reader["FilePath"]?.ToString() ?? string.Empty,
                            FileName = reader["FileName"]?.ToString() ?? string.Empty
                        });
                    }
                    OdobruvanjaZadolzuvanja = items;
                }
            }
        }

        public async Task<IActionResult> OnPostStorno(int id)
        {
            if (!await HasPageAccess("OdobruvanjeZadolzuvanje"))
            {
                return new JsonResult(new { success = false, message = "Немате дозвола за оваа акција." });
            }

            string username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                return new JsonResult(new { success = false, message = "Вашата сесија е истечена. Најавете се повторно." });
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Check if record exists and is not already storno
                    using (SqlCommand checkCmd = new SqlCommand("SELECT Storno FROM OdobruvanjeZadolzuvanje WHERE Id = @Id", connection))
                    {
                        checkCmd.Parameters.AddWithValue("@Id", id);
                        var result = await checkCmd.ExecuteScalarAsync();
                        
                        if (result == null)
                        {
                            return new JsonResult(new { success = false, message = $"Записот со ID {id} не е пронајден." });
                        }

                        bool isStorno = result != DBNull.Value && Convert.ToBoolean(result);
                        if (isStorno)
                        {
                            return new JsonResult(new { success = false, message = $"Записот со ID {id} веќе е сторниран." });
                        }
                    }

                    // Update the record to set Storno = 1
                    using (SqlCommand updateCmd = new SqlCommand(@"
                        UPDATE OdobruvanjeZadolzuvanje 
                        SET Storno = 1, 
                            DateModified = GETDATE(), 
                            UsernameModified = @Username 
                        WHERE Id = @Id", connection))
                    {
                        updateCmd.Parameters.AddWithValue("@Id", id);
                        updateCmd.Parameters.AddWithValue("@Username", username);

                        int rowsAffected = await updateCmd.ExecuteNonQueryAsync();
                        
                        if (rowsAffected > 0)
                        {
                            return new JsonResult(new { success = true, message = $"Записот со ID {id} е успешно сторниран." });
                        }
                        else
                        {
                            return new JsonResult(new { success = false, message = $"Неуспешно сторнирање на записот со ID {id}." });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = $"Настана грешка при сторнирање на записот со ID {id}: {ex.Message}" });
            }
        }

        public async Task<IActionResult> OnGetGetDocumentData(int id)
        {
            if (!await HasPageAccess("OdobruvanjeZadolzuvanje"))
            {
                return new JsonResult(new { success = false, message = "Немате дозвола за оваа акција." });
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Get document data with all related information
                    using (SqlCommand cmd = new SqlCommand(@"
                        SELECT 
                            oz.Id,
                            oz.DateCreated,
                            oz.UsernameCreated,
                            oz.DateModified,
                            oz.UsernameModified,
                            soztnd.TipNaDokument,
                            sozt.Tip,
                            sozpp.PremijaProvizija,
                            ISNULL(klnt.ime, '') + ' ' + ISNULL(klnt.prezime, '') + ' ' + ISNULL(klnt.naziv, '') AS KlientImePrezimeNaziv,
                            oz.BrojNaDokument,
                            oz.DatumNaDokument,
                            oz.Iznos,
                            oz.BrojNaVleznaFaktura,
                            oz.BrojNaIzleznaFaktura,
                            oz.PolisaId,
                            pol.BrojNaPolisa,
                            oz.TipNafaktura,
                            oz.BrojNaIzleznaFakturaPremija,
                            oz.Opis,
                            oz.Storno,
                            oz.FilePath,
                            oz.FileName
                        FROM OdobruvanjeZadolzuvanje oz
                        left join Polisi pol on oz.PolisaId = pol.id
                        left join SifrarnikOZTipNaDokument soztnd on oz.SifrarnikOZTipNaDokumentId = soztnd.id
                        left join SifrarnikOZTip sozt on oz.SifrarnikOZTipId = sozt.id
                        left join SifrarnikOZPremijaProvizija sozpp on oz.SifrarnikOZPremijaProvizijaId = sozpp.id
                        left join klienti klnt on oz.KlientiIdKlient = klnt.Id                    
                        WHERE oz.Id = @Id", connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", id);

                        using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                        if (await reader.ReadAsync())
                        {
                            var document = new
                            {
                                id = reader["Id"],
                                dateCreated = reader["DateCreated"],
                                usernameCreated = reader["UsernameCreated"]?.ToString() ?? string.Empty,
                                dateModified = reader["DateModified"] != DBNull.Value ? reader["DateModified"] : null,
                                usernameModified = reader["UsernameModified"]?.ToString() ?? string.Empty,
                                tipNaDokument = reader["TipNaDokument"]?.ToString() ?? string.Empty,
                                tip = reader["Tip"]?.ToString() ?? string.Empty,
                                premijaProvizija = reader["PremijaProvizija"]?.ToString() ?? string.Empty,
                                klientImePrezimeNaziv = reader["KlientImePrezimeNaziv"]?.ToString() ?? string.Empty,
                                brojNaDokument = reader["BrojNaDokument"]?.ToString() ?? string.Empty,
                                datumNaDokument = reader["DatumNaDokument"] != DBNull.Value ? reader["DatumNaDokument"] : null,
                                iznos = reader["Iznos"] != DBNull.Value ? reader["Iznos"] : null,
                                brojNaVleznaFaktura = reader["BrojNaVleznaFaktura"]?.ToString() ?? string.Empty,
                                brojNaIzleznaFaktura = reader["BrojNaIzleznaFaktura"]?.ToString() ?? string.Empty,
                                polisaId = reader["PolisaId"] != DBNull.Value ? reader["PolisaId"] : null,
                                brojNaPolisa = reader["BrojNaPolisa"]?.ToString() ?? string.Empty,
                                tipNafaktura = reader["TipNafaktura"]?.ToString() ?? string.Empty,
                                brojNaIzleznaFakturaPremija = reader["BrojNaIzleznaFakturaPremija"]?.ToString() ?? string.Empty,
                                opis = reader["Opis"]?.ToString() ?? string.Empty,
                                storno = reader["Storno"] != DBNull.Value ? Convert.ToBoolean(reader["Storno"]) : false,
                                filePath = reader["FilePath"]?.ToString() ?? string.Empty,
                                fileName = reader["FileName"]?.ToString() ?? string.Empty
                            };

                            // Close the reader first
                            reader.Close();

                            // Convert amount to words using fn_BrojVoZborovi function
                            string iznosSoZborovi = "";
                            if (document.iznos != null && document.iznos != DBNull.Value)
                            {
                                decimal iznos = Convert.ToDecimal(document.iznos);
                                using (SqlCommand cmdText = new SqlCommand("SELECT dbo.fn_BrojVoZborovi(@Number)", connection))
                                {
                                    cmdText.Parameters.AddWithValue("@Number", Convert.ToInt32(iznos));
                                    var result = await cmdText.ExecuteScalarAsync();
                                    iznosSoZborovi = result?.ToString() ?? "";
                                }
                            }

                            // Get the proper name for "Изработил" using VratiImePrezimePoUsername function
                            string izrabotilName = "";
                            if (!string.IsNullOrEmpty(document.usernameCreated))
                            {
                                using (SqlCommand cmdName = new SqlCommand("SELECT dbo.VratiImePrezimePoUsername(@Username)", connection))
                                {
                                    cmdName.Parameters.AddWithValue("@Username", document.usernameCreated);
                                    var result = await cmdName.ExecuteScalarAsync();
                                    izrabotilName = result?.ToString() ?? document.usernameCreated;
                                }
                            }

                            return new JsonResult(new { 
                                success = true, 
                                document = document,
                                iznosSoZborovi = iznosSoZborovi,
                                izrabotilName = izrabotilName
                            });
                        }
                        else
                        {
                            return new JsonResult(new { success = false, message = $"Записот со ID {id} не е пронајден." });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = $"Настана грешка при преземање на податоци за записот со ID {id}: {ex.Message}" });
            }
        }
    }
}
