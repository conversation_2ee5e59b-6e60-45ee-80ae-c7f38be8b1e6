using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using Tesseract;
using System.Text.RegularExpressions;
using System.Text;
using iText.Kernel.Pdf;
using iText.Kernel.Pdf.Canvas.Parser;
using iText.Kernel.Pdf.Canvas.Parser.Listener;
using ImageMagick;
using System.Runtime.InteropServices;

namespace NextBroker.Pages.OCR
{
    public class OCRAvtoOdgovornostModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        private readonly IWebHostEnvironment _environment;

        // Properties for form data
        [BindProperty]
        public IFormFile? ImageFile { get; set; }

        [BindProperty]
        public string Language { get; set; } = "auto"; // Auto word-by-word detection

        [BindProperty]
        public int OcrMode { get; set; } = 3;

        [BindProperty]
        public bool UseCoordinateExtraction { get; set; } = false;

        // Properties for results
        public string? ExtractedText { get; set; }
        public string? ErrorMessage { get; set; }
        public string? ErrorDetails { get; set; }
        public string? ErrorType { get; set; }
        public string? DetectedLanguage { get; set; }
        public int ProcessedWords { get; set; }
        public int EnglishWords { get; set; }
        public int MacedonianWords { get; set; }

        // Properties for structured data extraction
        public string? BrojNaPolisa { get; set; }
        public string? ImeNaDgovoruvac { get; set; }
        public string? EMBG_ME { get; set; }
        public string? Registracija { get; set; }
        public string? Mesto { get; set; }
        public string? Osigurenik { get; set; }
        public string? Adresa { get; set; }
        public bool HasStructuredData { get; set; }

        // Properties for coordinate-based extraction
        public string? CoordinateExtractionResults { get; set; }
        
        [BindProperty]
        public string? DebugInfo { get; set; }
        
        private readonly List<string> _debugMessages = new List<string>();
        
        private void LogDebug(string message)
        {
            _debugMessages.Add($"{DateTime.Now:HH:mm:ss.fff} - {message}");
            System.Diagnostics.Debug.WriteLine(message);
        }
        
        private void FinalizeDebugInfo()
        {
            DebugInfo = string.Join("\n", _debugMessages);
        }

        public OCRAvtoOdgovornostModel(IConfiguration configuration, IWebHostEnvironment environment)
            : base(configuration)
        {
            _configuration = configuration;
            _environment = environment;
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("OCRPolisaAO"))
            {
                return RedirectToAccessDenied();
            }

            return Page();
        }

        public async Task<IActionResult> OnPostProcessOCR()
        {
            if (!await HasPageAccess("OCRPolisaAO"))
            {
                return RedirectToAccessDenied();
            }

            if (ImageFile == null || ImageFile.Length == 0)
            {
                ErrorMessage = "Ве молиме изберете слика за обработка.";
                return Page();
            }

            // Validate file type
            if (!IsValidImageFile(ImageFile))
            {
                ErrorMessage = "Неподдржан формат на датотека. Користете: JPG, PNG, BMP, TIFF, GIF или PDF.";
                return Page();
            }

            try
            {
                if (IsPdfFile(ImageFile))
                {
                    // Process PDF file
                    var result = await ProcessPdfWithOCR(ImageFile, Language, OcrMode);
                    ExtractedText = result.Text;
                    DetectedLanguage = result.DetectedLanguage;
                    ProcessedWords = result.ProcessedWords;
                    EnglishWords = result.EnglishWords;
                    MacedonianWords = result.MacedonianWords;
                }
                else
                {
                    // Process image file
                    if (UseCoordinateExtraction)
                    {
                        // Use coordinate-based extraction
                        var result = await ProcessImageWithCoordinateExtraction(ImageFile, Language, OcrMode);
                        ExtractedText = result.Text;
                        DetectedLanguage = result.DetectedLanguage;
                        ProcessedWords = result.ProcessedWords;
                        EnglishWords = result.EnglishWords;
                        MacedonianWords = result.MacedonianWords;
                    }
                    else
                    {
                        // Use traditional text-based extraction
                        var result = await ProcessImageWithWordByWordDetection(ImageFile, Language, OcrMode);
                        ExtractedText = result.Text;
                        DetectedLanguage = result.DetectedLanguage;
                        ProcessedWords = result.ProcessedWords;
                        EnglishWords = result.EnglishWords;
                        MacedonianWords = result.MacedonianWords;
                    }
                }
                
                if (string.IsNullOrWhiteSpace(ExtractedText))
                {
                    ErrorMessage = "Не е пронајден текст во датотеката. Обидете се со различна датотека.";
                }
                else
                {
                    // Extract structured data from the OCR text
                    ExtractStructuredData(ExtractedText);
                }
            }
            catch (Exception ex)
            {
                // Enhanced error handling with detailed information
                var errorDetails = GetDetailedErrorMessage(ex);
                ErrorMessage = errorDetails;
            }

            return Page();
        }

        private async Task<(string Text, string DetectedLanguage, int ProcessedWords, int EnglishWords, int MacedonianWords)> ProcessPdfWithOCR(IFormFile pdfFile, string language, int ocrMode)
        {
            // Get tessdata path
            var tessDataPath = Path.Combine(_environment.WebRootPath, "tessdata");
            
            if (!Directory.Exists(tessDataPath))
            {
                throw new DirectoryNotFoundException($"Tessdata directory not found at: {tessDataPath}. Please ensure tessdata files are properly installed.");
            }

            // Validate that required language files exist
            var requiredLanguages = new[] { "eng", "mkd" };
            foreach (var lang in requiredLanguages)
            {
                var langFile = Path.Combine(tessDataPath, $"{lang}.traineddata");
                if (!System.IO.File.Exists(langFile))
                {
                    throw new FileNotFoundException($"Language file '{lang}.traineddata' not found in tessdata directory.");
                }
            }

            // Create temporary file for PDF processing
            var tempPdfPath = Path.GetTempFileName();
            var tempImageFiles = new List<string>();
            
            try
            {
                // Save uploaded PDF to temporary file
                using (var stream = new FileStream(tempPdfPath, FileMode.Create))
                {
                    await pdfFile.CopyToAsync(stream);
                }

                // Convert PDF pages to images
                tempImageFiles = await ConvertPdfToImages(tempPdfPath);
                
                if (tempImageFiles.Count == 0)
                {
                    throw new Exception("Could not convert PDF pages to images for OCR processing.");
                }

                // Process all pages
                var allResults = new List<(string Text, string DetectedLanguage, int ProcessedWords, int EnglishWords, int MacedonianWords)>();
                
                for (int i = 0; i < tempImageFiles.Count; i++)
                {
                    var pageResult = await ProcessSingleImageForPdf(tempImageFiles[i], tessDataPath, language, ocrMode, i + 1);
                    allResults.Add(pageResult);
                }

                // Combine results from all pages
                var combinedText = new StringBuilder();
                var totalProcessedWords = 0;
                var totalEnglishWords = 0;
                var totalMacedonianWords = 0;
                var detectedLanguages = new List<string>();

                foreach (var result in allResults)
                {
                    if (!string.IsNullOrWhiteSpace(result.Text))
                    {
                        combinedText.AppendLine($"=== Страна {allResults.IndexOf(result) + 1} ===");
                        combinedText.AppendLine(result.Text);
                        combinedText.AppendLine();
                    }
                    
                    totalProcessedWords += result.ProcessedWords;
                    totalEnglishWords += result.EnglishWords;
                    totalMacedonianWords += result.MacedonianWords;
                    
                    if (!string.IsNullOrEmpty(result.DetectedLanguage))
                    {
                        detectedLanguages.Add(result.DetectedLanguage);
                    }
                }

                // Add PDF summary
                var summary = new StringBuilder();
                summary.AppendLine($"\n\n=== PDF OCR Информации ===");
                summary.AppendLine($"Вкупно страници: {tempImageFiles.Count}");
                summary.AppendLine($"Успешно обработени страници: {allResults.Count(r => !string.IsNullOrWhiteSpace(r.Text))}");
                summary.AppendLine($"Вкупно зборови: {totalProcessedWords}");
                summary.AppendLine($"Латински карактери: {totalEnglishWords}");
                summary.AppendLine($"Кирилски карактери: {totalMacedonianWords}");
                summary.AppendLine($"Јазик/режим: {GetLanguageDisplayName(language)}");
                summary.AppendLine($"OCR режим: {GetOcrModeDisplayName(ocrMode)}");

                var finalText = combinedText.ToString() + summary.ToString();
                var finalDetectedLanguage = detectedLanguages.Count > 0 
                    ? $"PDF - {string.Join(", ", detectedLanguages.Distinct())}" 
                    : $"PDF - {GetLanguageDisplayName(language)}";

                return (finalText, finalDetectedLanguage, totalProcessedWords, totalEnglishWords, totalMacedonianWords);
            }
            finally
            {
                // Clean up temporary files
                if (System.IO.File.Exists(tempPdfPath))
                {
                    System.IO.File.Delete(tempPdfPath);
                }
                
                foreach (var tempImageFile in tempImageFiles)
                {
                    if (System.IO.File.Exists(tempImageFile))
                    {
                        System.IO.File.Delete(tempImageFile);
                    }
                }
            }
        }

        private async Task<(string Text, string DetectedLanguage, int ProcessedWords, int EnglishWords, int MacedonianWords)> ProcessImageWithWordByWordDetection(IFormFile imageFile, string language, int ocrMode)
        {
            // Get tessdata path
            var tessDataPath = Path.Combine(_environment.WebRootPath, "tessdata");
            
            if (!Directory.Exists(tessDataPath))
            {
                throw new DirectoryNotFoundException($"Tessdata directory not found at: {tessDataPath}. Please ensure tessdata files are properly installed.");
            }

            // Additional diagnostic information
            var diagnosticInfo = GetDiagnosticInfo(tessDataPath);
            System.Diagnostics.Debug.WriteLine($"OCR Diagnostic Info: {diagnosticInfo}");

            // Validate that required language files exist
            var requiredLanguages = new[] { "eng", "mkd" };
            foreach (var lang in requiredLanguages)
            {
                var langFile = Path.Combine(tessDataPath, $"{lang}.traineddata");
                if (!System.IO.File.Exists(langFile))
                {
                    throw new FileNotFoundException($"Language file '{lang}.traineddata' not found in tessdata directory.");
                }
            }

            // Create temporary file for image processing
            var tempFilePath = Path.GetTempFileName();
            
            try
            {
                // Save uploaded image to temporary file
                using (var stream = new FileStream(tempFilePath, FileMode.Create))
                {
                    await imageFile.CopyToAsync(stream);
                }

                if (language == "auto")
                {
                    // Word-by-word processing
                    return await ProcessWordByWord(tempFilePath, tessDataPath, ocrMode);
                }
                else
                {
                    // Use specified language for entire document
                    var text = await ProcessWithSingleLanguage(tempFilePath, tessDataPath, language, ocrMode);
                    return (text, GetLanguageDisplayName(language), 0, 0, 0);
                }
            }
            finally
            {
                // Clean up temporary file
                if (System.IO.File.Exists(tempFilePath))
                {
                    System.IO.File.Delete(tempFilePath);
                }
            }
        }

        private async Task<(string Text, string DetectedLanguage, int ProcessedWords, int EnglishWords, int MacedonianWords)> ProcessWordByWord(string imagePath, string tessDataPath, int ocrMode)
        {
            // Advanced approach: Process with multiple engines and combine best results
            var results = await ProcessWithMultipleEngines(imagePath, tessDataPath, ocrMode);
            
            return (results.BestText, results.DetectedLanguage, results.TotalWords, results.EnglishWords, results.MacedonianWords);
        }

        private async Task<(string BestText, string DetectedLanguage, int TotalWords, int EnglishWords, int MacedonianWords)> ProcessWithMultipleEngines(string imagePath, string tessDataPath, int ocrMode)
        {
            var results = new List<OCRResult>();

            // Process with English OCR
            var engResult = await ProcessAndAnalyze(imagePath, tessDataPath, "eng", ocrMode);
            results.Add(new OCRResult { Language = "eng", Text = engResult.Text, Confidence = engResult.Confidence, Words = engResult.Words });

            // Process with Macedonian OCR  
            var mkdResult = await ProcessAndAnalyze(imagePath, tessDataPath, "mkd", ocrMode);
            results.Add(new OCRResult { Language = "mkd", Text = mkdResult.Text, Confidence = mkdResult.Confidence, Words = mkdResult.Words });

            // Process with dual-language OCR
            var dualResult = await ProcessAndAnalyze(imagePath, tessDataPath, "eng+mkd", ocrMode);
            results.Add(new OCRResult { Language = "eng+mkd", Text = dualResult.Text, Confidence = dualResult.Confidence, Words = dualResult.Words });

            // Find the best result based on confidence and text quality
            var bestResult = SelectBestResult(results);

            // Analyze word composition of the best result
            var wordAnalysis = AnalyzeWordsInText(bestResult.Text);

            var statsText = $"\n\n--- OCR Информации (Мулти-енџин анализа) ---\n" +
                           $"Тестирани OCR енџини: English, Macedonian, Dual-language\n" +
                           $"Најдобар резултат: {GetLanguageDisplayName(bestResult.Language)}\n" +
                           $"Точност: {bestResult.Confidence:F2}%\n" +
                           $"Вкупно зборови: {wordAnalysis.TotalWords}\n" +
                           $"Латински карактери: {wordAnalysis.LatinWords}\n" +
                           $"Кирилски карактери: {wordAnalysis.CyrillicWords}\n" +
                           $"OCR режим: {GetOcrModeDisplayName(ocrMode)}";

            var finalText = bestResult.Text + statsText;

            var languageSummary = bestResult.Language == "eng+mkd" 
                ? $"Мешан текст (најдобар резултат од dual-language)"
                : $"{GetLanguageDisplayName(bestResult.Language)} (најдобар резултат - {bestResult.Confidence:F1}% точност)";

            return (finalText, languageSummary, wordAnalysis.TotalWords, wordAnalysis.LatinWords, wordAnalysis.CyrillicWords);
        }

        private async Task<(string Text, double Confidence, List<string> Words)> ProcessAndAnalyze(string imagePath, string tessDataPath, string language, int ocrMode)
        {
            try
            {
                // First try the .NET wrapper approach
                return await ProcessWithDotNetWrapper(imagePath, tessDataPath, language, ocrMode);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"DotNet wrapper failed: {ex.Message}, trying system command approach");
                
                // Fallback to system command approach
                return await ProcessWithSystemCommand(imagePath, tessDataPath, language, ocrMode);
            }
        }

        private async Task<(string Text, double Confidence, List<string> Words)> ProcessWithDotNetWrapper(string imagePath, string tessDataPath, string language, int ocrMode)
        {
            // Set up environment for native library loading
            SetupNativeLibraryEnvironment();
            
            using (var engine = new TesseractEngine(tessDataPath, language, EngineMode.Default))
            {
                ConfigureEngine(engine, ocrMode, language);
                
                using (var img = Pix.LoadFromFile(imagePath))
                {
                    using (var page = engine.Process(img))
                    {
                        var text = page.GetText();
                        var confidence = page.GetMeanConfidence();
                        
                        // Extract words
                        var words = new List<string>();
                        if (!string.IsNullOrWhiteSpace(text))
                        {
                            var wordPattern = new Regex(@"\b\w+\b");
                            var matches = wordPattern.Matches(text);
                            foreach (Match match in matches)
                            {
                                words.Add(match.Value);
                            }
                        }

                        return (text, confidence, words);
                    }
                }
            }
        }

        private async Task<(string Text, double Confidence, List<string> Words)> ProcessWithSystemCommand(string imagePath, string tessDataPath, string language, int ocrMode)
        {
            try
            {
                // Use system tesseract command directly
                var processInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "tesseract",
                    Arguments = $"\"{imagePath}\" stdout -l {language} --psm {ocrMode} --tessdata-dir \"{tessDataPath}\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    WorkingDirectory = Path.GetDirectoryName(imagePath)
                };

                // Set environment variables
                processInfo.Environment["TESSDATA_PREFIX"] = tessDataPath;

                using (var process = new System.Diagnostics.Process())
                {
                    process.StartInfo = processInfo;
                    process.Start();

                    var output = await process.StandardOutput.ReadToEndAsync();
                    var error = await process.StandardError.ReadToEndAsync();
                    
                    await process.WaitForExitAsync();

                    if (process.ExitCode != 0)
                    {
                        throw new Exception($"Tesseract command failed with exit code {process.ExitCode}: {error}");
                    }

                    // Extract words from output
                    var words = new List<string>();
                    if (!string.IsNullOrWhiteSpace(output))
                    {
                        var wordPattern = new Regex(@"\b\w+\b");
                        var matches = wordPattern.Matches(output);
                        foreach (Match match in matches)
                        {
                            words.Add(match.Value);
                        }
                    }

                    // Since we can't get confidence from stdout, estimate it
                    var estimatedConfidence = EstimateConfidence(output);

                    return (output.Trim(), estimatedConfidence, words);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"System command OCR failed: {ex.Message}");
            }
        }

        private double EstimateConfidence(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return 0.0;

            // Simple heuristic: longer text with more words = higher confidence
            var wordCount = text.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length;
            var hasSpecialChars = text.Any(c => !char.IsLetterOrDigit(c) && !char.IsWhiteSpace(c));
            
            var baseConfidence = Math.Min(90.0, 50.0 + (wordCount * 5.0));
            
            // Reduce confidence if there are too many special characters
            if (hasSpecialChars)
                baseConfidence *= 0.9;

            return baseConfidence;
        }

        private OCRResult SelectBestResult(List<OCRResult> results)
        {
            // Score each result based on confidence and other factors
            var bestResult = results.OrderByDescending(r => {
                var score = r.Confidence;
                
                // Boost score for results with more readable text (fewer strange characters)
                var strangeCharCount = CountStrangeCharacters(r.Text);
                score -= strangeCharCount * 2; // Penalty for strange characters
                
                // Slight preference for dual-language for mixed content
                if (r.Language == "eng+mkd" && HasMixedScripts(r.Text))
                    score += 5;
                
                return score;
            }).First();

            return bestResult;
        }

        private bool HasMixedScripts(string text)
        {
            var latinPattern = new Regex(@"[a-zA-Z]");
            var cyrillicPattern = new Regex(@"[\u0400-\u04FF]");
            
            return latinPattern.IsMatch(text) && cyrillicPattern.IsMatch(text);
        }

        private int CountStrangeCharacters(string text)
        {
            // Count characters that are likely OCR errors
            var strangeChars = new[] { "џ", "ѕ", "љ", "њ", "ќ", "ѓ" }; // Common OCR errors
            var count = 0;
            
            foreach (var strange in strangeChars)
            {
                count += text.Split(strange).Length - 1;
            }
            
            return count;
        }

        private (int TotalWords, int LatinWords, int CyrillicWords) AnalyzeWordsInText(string text)
        {
            var words = new List<string>();
            if (!string.IsNullOrWhiteSpace(text))
            {
                var wordPattern = new Regex(@"\b\w+\b");
                var matches = wordPattern.Matches(text);
                foreach (Match match in matches)
                {
                    words.Add(match.Value);
                }
            }

            var latinWords = 0;
            var cyrillicWords = 0;

            foreach (var word in words)
            {
                if (AnalyzeWordLanguage(word) == "eng")
                    latinWords++;
                else
                    cyrillicWords++;
            }

            return (words.Count, latinWords, cyrillicWords);
        }

        private string AnalyzeWordLanguage(string word)
        {
            if (string.IsNullOrEmpty(word))
                return "eng"; // Default to English

            // Check for Cyrillic characters
            var cyrillicPattern = new Regex(@"[\u0400-\u04FF]");
            var latinPattern = new Regex(@"[a-zA-Z]");

            var hasCyrillic = cyrillicPattern.IsMatch(word);
            var hasLatin = latinPattern.IsMatch(word);

            // If word contains Cyrillic characters, use Macedonian
            if (hasCyrillic)
                return "mkd";
            
            // If word contains Latin characters, use English
            if (hasLatin)
                return "eng";

            // For numbers, punctuation, etc., default to English
            return "eng";
        }

        private async Task<string> ProcessWithSingleLanguage(string imagePath, string tessDataPath, string language, int ocrMode)
        {
            try
            {
                // Try .NET wrapper first
                using (var engine = new TesseractEngine(tessDataPath, language, EngineMode.Default))
                {
                    ConfigureEngine(engine, ocrMode, language);
                    
                    using (var img = Pix.LoadFromFile(imagePath))
                    {
                        using (var page = engine.Process(img))
                        {
                            var text = page.GetText();
                            var confidence = page.GetMeanConfidence();
                            
                            if (confidence > 0)
                            {
                                text += $"\n\n--- OCR Информации ---\n" +
                                       $"Точност: {confidence:F2}%\n" +
                                       $"Јазик: {GetLanguageDisplayName(language)}\n" +
                                       $"OCR режим: {GetOcrModeDisplayName(ocrMode)}";
                            }
                            
                            return text;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"DotNet wrapper failed in ProcessWithSingleLanguage: {ex.Message}");
                
                // Fallback to system command
                var result = await ProcessWithSystemCommand(imagePath, tessDataPath, language, ocrMode);
                
                var text = result.Text;
                if (result.Confidence > 0)
                {
                    text += $"\n\n--- OCR Информации (Системска команда) ---\n" +
                           $"Точност: {result.Confidence:F2}%\n" +
                           $"Јазик: {GetLanguageDisplayName(language)}\n" +
                           $"OCR режим: {GetOcrModeDisplayName(ocrMode)}";
                }
                
                return text;
            }
        }

        private void ConfigureEngine(TesseractEngine engine, int ocrMode, string language)
        {
            try
            {
                engine.SetVariable("tessedit_pageseg_mode", ocrMode.ToString());
                
                if (language == "eng")
                {
                    // English-specific optimizations
                    engine.SetVariable("tessedit_char_whitelist", "");
                    engine.SetVariable("classify_bln_numeric_mode", "0");
                }
                else if (language == "mkd")
                {
                    // Macedonian-specific optimizations
                    engine.SetVariable("tessedit_char_whitelist", "");
                    engine.SetVariable("classify_bln_numeric_mode", "0");
                    engine.SetVariable("textord_really_old_xheight", "1");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Warning: Could not configure OCR engine: {ex.Message}");
                // Continue without advanced configuration
            }
        }

        private bool IsValidImageFile(IFormFile file)
        {
            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".tif", ".gif", ".pdf" };
            var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
            
            return allowedExtensions.Contains(fileExtension);
        }

        private bool IsPdfFile(IFormFile file)
        {
            var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
            return fileExtension == ".pdf";
        }

        public string GetLanguageDisplayName(string languageCode)
        {
            return languageCode switch
            {
                "eng" => "English (Англиски)",
                "mkd" => "Macedonian (Македонски)", 
                "eng+mkd" => "English + Macedonian (Двојазично)",
                "auto" => "Автоматска детекција збор-по-збор",
                _ => languageCode
            };
        }

        private string GetOcrModeDisplayName(int mode)
        {
            return mode switch
            {
                3 => "Автоматски (Default)",
                6 => "Единечен блок од текст",
                7 => "Една линија од текст",
                8 => "Една збор",
                13 => "Сурова линија (без речник)",
                _ => $"Режим {mode}"
            };
        }

        private string GetDetailedErrorMessage(Exception ex)
        {
            var errorMessage = "Непозната грешка";
            var errorDetails = "";
            var errorType = "Општа грешка";

            try
            {
                // Check for specific error types and provide user-friendly messages
                if (ex is DirectoryNotFoundException)
                {
                    errorType = "Конфигурациска грешка";
                    errorMessage = "Не се пронајдени потребните OCR датотеки";
                    errorDetails = $"Детали: {ex.Message}";
                }
                else if (ex is FileNotFoundException)
                {
                    errorType = "Недостасуваат датотеки";
                    errorMessage = "Недостасуваат јазични датотеки за OCR";
                    errorDetails = $"Детали: {ex.Message}";
                }
                else if (ex.Message.Contains("libleptonica") || ex.Message.Contains("libtesseract"))
                {
                    errorType = "Системска грешка";
                    errorMessage = "Проблем со OCR библиотеките";
                    errorDetails = "Tesseract OCR библиотеките не се правилно инсталирани или конфигурирани";
                }
                else if (ex.Message.Contains("target of an invocation"))
                {
                    errorType = "OCR Енџин грешка";
                    errorMessage = "Проблем при иницијализација на OCR енџинот";
                    
                    // Try to get inner exception details
                    if (ex.InnerException != null)
                    {
                        if (ex.InnerException.Message.Contains("libleptonica") || ex.InnerException.Message.Contains("libtesseract"))
                        {
                            errorDetails = "OCR библиотеките не се достапни за .NET апликацијата. Потребно е ребилдирање на Docker контејнерот со дополнителни native библиотеки.";
                        }
                        else if (ex.InnerException.Message.Contains("tessdata"))
                        {
                            errorDetails = "Проблем со јазичните датотеки. Проверете дали се правилно поставени tessdata датотеките.";
                        }
                        else if (ex.InnerException.Message.Contains("DllNotFoundException") || ex.InnerException.Message.Contains("unable to load"))
                        {
                            errorDetails = "Native библиотеките не можат да се вчитаат. Проверете дали се инсталирани libtesseract-dev и libleptonica-dev пакетите.";
                        }
                        else
                        {
                            errorDetails = $"Внатрешна грешка: {ex.InnerException.Message}";
                        }
                    }
                    else
                    {
                        errorDetails = "Tesseract command е достапен, но .NET библиотеката не може да се поврзе со native библиотеките. Потребно е ребилдирање на контејнерот.";
                    }
                }
                else if (ex.Message.Contains("OutOfMemory"))
                {
                    errorType = "Меморија";
                    errorMessage = "Сликата е премногу голема за обработка";
                    errorDetails = "Обидете се со помала слика или слика со пониска резолуција";
                }
                else if (ex.Message.Contains("format") || ex.Message.Contains("corrupt"))
                {
                    errorType = "Формат на слика";
                    errorMessage = "Проблем со форматот на сликата";
                    errorDetails = "Обидете се со различна слика или конвертирајте ја во JPG/PNG формат";
                }
                else
                {
                    errorType = "Општа грешка";
                    errorMessage = "Неочекувана грешка при обработка";
                    errorDetails = ex.Message;
                }

                // Store additional error information for detailed display
                ErrorType = errorType;
                ErrorDetails = errorDetails;

                return errorMessage;
            }
            catch
            {
                return "Критична грешка при анализа на проблемот";
            }
        }

        private string GetDiagnosticInfo(string tessDataPath)
        {
            var info = new StringBuilder();
            info.AppendLine($"Tessdata Path: {tessDataPath}");
            info.AppendLine($"Directory Exists: {Directory.Exists(tessDataPath)}");
            
            if (Directory.Exists(tessDataPath))
            {
                var files = Directory.GetFiles(tessDataPath, "*.traineddata");
                info.AppendLine($"Traineddata Files Found: {files.Length}");
                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    info.AppendLine($"  - {Path.GetFileName(file)}: {fileInfo.Length} bytes");
                }
            }
            
            // Check environment variables
            info.AppendLine($"TESSDATA_PREFIX: {Environment.GetEnvironmentVariable("TESSDATA_PREFIX")}");
            
            // Check if Tesseract executable is available (for system diagnosis)
            try
            {
                var processInfo = new System.Diagnostics.ProcessStartInfo("tesseract", "--version")
                {
                    RedirectStandardOutput = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };
                using (var process = System.Diagnostics.Process.Start(processInfo))
                {
                    if (process != null)
                    {
                        process.WaitForExit();
                        info.AppendLine($"Tesseract Command Available: Yes");
                    }
                }
            }
            catch
            {
                info.AppendLine($"Tesseract Command Available: No");
            }
            
            return info.ToString();
        }

        private void SetupNativeLibraryEnvironment()
        {
            try
            {
                // Set environment variables to help .NET find native libraries
                var currentPath = Environment.GetEnvironmentVariable("PATH") ?? "";
                var ldLibraryPath = Environment.GetEnvironmentVariable("LD_LIBRARY_PATH") ?? "";
                
                // Add common library paths for Linux
                var additionalPaths = new[]
                {
                    "/usr/lib/x86_64-linux-gnu",
                    "/usr/lib",
                    "/lib/x86_64-linux-gnu",
                    "/app/runtimes/linux-x64/native"
                };
                
                foreach (var path in additionalPaths)
                {
                    if (Directory.Exists(path))
                    {
                        if (!currentPath.Contains(path))
                        {
                            Environment.SetEnvironmentVariable("PATH", $"{currentPath}:{path}");
                            currentPath = $"{currentPath}:{path}";
                        }
                        
                        if (!ldLibraryPath.Contains(path))
                        {
                            Environment.SetEnvironmentVariable("LD_LIBRARY_PATH", $"{ldLibraryPath}:{path}");
                            ldLibraryPath = $"{ldLibraryPath}:{path}";
                        }
                    }
                }
                
                // Also try to load libraries explicitly
                LoadNativeLibrariesExplicitly();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Warning: Could not setup native library environment: {ex.Message}");
            }
        }

        private void LoadNativeLibrariesExplicitly()
        {
            try
            {
                // Try to load libraries using different approaches
                var libraryPaths = new[]
                {
                    "/usr/lib/x86_64-linux-gnu/libtesseract.so.5",
                    "/usr/lib/x86_64-linux-gnu/libtesseract.so",
                    "/usr/lib/libtesseract.so",
                    "/app/runtimes/linux-x64/native/libtesseract.so"
                };

                foreach (var libPath in libraryPaths)
                {
                    if (System.IO.File.Exists(libPath))
                    {
                        try
                        {
                            // Use P/Invoke to load library
                            var handle = dlopen(libPath, RTLD_NOW);
                            if (handle != IntPtr.Zero)
                            {
                                System.Diagnostics.Debug.WriteLine($"Successfully loaded library: {libPath}");
                                break;
                            }
                        }
                        catch
                        {
                            // Continue to next path
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Warning: Could not load native libraries explicitly: {ex.Message}");
            }
        }

        // P/Invoke declarations for Linux library loading
        [System.Runtime.InteropServices.DllImport("libdl.so.2")]
        private static extern IntPtr dlopen(string filename, int flags);
        
        private const int RTLD_NOW = 2;

        private void ExtractStructuredData(string ocrText)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Starting structured data extraction...");
                
                LogDebug("=== EXTRACTION METHOD DECISION ===");
                LogDebug($"UseCoordinateExtraction: {UseCoordinateExtraction}");
                LogDebug($"CoordinateExtractionResults is null/empty: {string.IsNullOrEmpty(CoordinateExtractionResults)}");
                if (!string.IsNullOrEmpty(CoordinateExtractionResults))
                {
                    LogDebug($"CoordinateExtractionResults length: {CoordinateExtractionResults.Length}");
                    LogDebug($"CoordinateExtractionResults preview: {CoordinateExtractionResults.Substring(0, Math.Min(200, CoordinateExtractionResults.Length))}...");
                }
                
                // If coordinate extraction was used and we have results, use those first
                if (UseCoordinateExtraction && !string.IsNullOrEmpty(CoordinateExtractionResults))
                {
                    LogDebug("Using coordinate extraction method");
                    ExtractFromCoordinateResults();
                }
                else
                {
                    LogDebug("Using traditional text parsing method");
                    // Clean and prepare text for extraction
                    var cleanText = CleanOcrText(ocrText);
                    
                    // Extract the 3 main fields using traditional text parsing
                    BrojNaPolisa = ExtractPolicyNumber(cleanText);
                    ImeNaDgovoruvac = ExtractInsuredName(cleanText);
                    EMBG_ME = ExtractEmbgMe(cleanText);
                    Registracija = ExtractRegistracija(cleanText);
                    Mesto = ExtractMesto(cleanText);
                    Osigurenik = ExtractOsigurenik(cleanText);
                    Adresa = ExtractAdresa(cleanText); // Add this missing line!
                }
                
                // Check if we found any structured data
                HasStructuredData = !string.IsNullOrEmpty(BrojNaPolisa) || 
                                   !string.IsNullOrEmpty(ImeNaDgovoruvac) || 
                                   !string.IsNullOrEmpty(EMBG_ME) ||
                                   !string.IsNullOrEmpty(Registracija) ||
                                   !string.IsNullOrEmpty(Mesto) ||
                                   !string.IsNullOrEmpty(Osigurenik) ||
                                   !string.IsNullOrEmpty(Adresa);
                
                LogDebug($"Structured data extraction completed:");
                LogDebug($"- Policy Number: {BrojNaPolisa ?? "Not found"}");
                LogDebug($"- Договорувач Name: {ImeNaDgovoruvac ?? "Not found"}");
                LogDebug($"- EMBG/ME: {EMBG_ME ?? "Not found"}");
                LogDebug($"- Registracija: {Registracija ?? "Not found"}");
                LogDebug($"- Mesto: {Mesto ?? "Not found"}");
                LogDebug($"- Osigurenik: {Osigurenik ?? "Not found"}");
                LogDebug($"- Adresa: {Adresa ?? "Not found"}");
                
                // Finalize debug information for display
                FinalizeDebugInfo();
            }
            catch (Exception ex)
            {
                LogDebug($"Error in structured data extraction: {ex.Message}");
                HasStructuredData = false;
                FinalizeDebugInfo();
            }
        }

        /// <summary>
        /// Extract structured data from coordinate extraction results
        /// </summary>
        private void ExtractFromCoordinateResults()
        {
            try
            {
                if (string.IsNullOrEmpty(CoordinateExtractionResults))
                {
                    LogDebug("CoordinateExtractionResults is null or empty");
                    return;
                }

                LogDebug("Processing coordinate extraction results:");
                LogDebug($"Results text: {CoordinateExtractionResults}");

                var lines = CoordinateExtractionResults.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                LogDebug($"Found {lines.Length} lines to process");
                
                // Show all lines for debugging
                for (int i = 0; i < lines.Length; i++)
                {
                    LogDebug($"Line {i}: '{lines[i]}'");
                }
                
                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();
                    LogDebug($"Processing line: '{trimmedLine}'");
                    
                    if (trimmedLine.StartsWith("Број на полиса:"))
                    {
                        BrojNaPolisa = ExtractValueFromCoordinateLine(trimmedLine);
                        LogDebug($"Set BrojNaPolisa = '{BrojNaPolisa}'");
                    }
                    else if (trimmedLine.StartsWith("Име на договорувач:"))
                    {
                        ImeNaDgovoruvac = ExtractValueFromCoordinateLine(trimmedLine);
                        LogDebug($"Set ImeNaDgovoruvac = '{ImeNaDgovoruvac}'");
                    }
                    else if (trimmedLine.StartsWith("ЕМБГ/МЕ број:"))
                    {
                        EMBG_ME = ExtractValueFromCoordinateLine(trimmedLine);
                        LogDebug($"Set EMBG_ME = '{EMBG_ME}'");
                    }
                    else if (trimmedLine.StartsWith("Регистрација на возило:"))
                    {
                        Registracija = ExtractValueFromCoordinateLine(trimmedLine);
                        LogDebug($"Set Registracija = '{Registracija}'");
                    }
                    else if (trimmedLine.StartsWith("Место:"))
                    {
                        Mesto = ExtractValueFromCoordinateLine(trimmedLine);
                        LogDebug($"Set Mesto = '{Mesto}'");
                    }
                    else if (trimmedLine.StartsWith("Осигуреник:"))
                    {
                        Osigurenik = ExtractValueFromCoordinateLine(trimmedLine);
                        LogDebug($"Set Osigurenik = '{Osigurenik}'");
                    }
                    else if (trimmedLine.StartsWith("Адреса:"))
                    {
                        Adresa = ExtractValueFromCoordinateLine(trimmedLine);
                        LogDebug($"Set Adresa = '{Adresa}'");
                    }
                    else
                    {
                        LogDebug($"Line didn't match any pattern: '{trimmedLine}'");
                    }
                }
                
                LogDebug("Final values after coordinate extraction:");
                LogDebug($"  BrojNaPolisa: '{BrojNaPolisa}'");
                LogDebug($"  ImeNaDgovoruvac: '{ImeNaDgovoruvac}'");
                LogDebug($"  EMBG_ME: '{EMBG_ME}'");
                LogDebug($"  Registracija: '{Registracija}'");
                LogDebug($"  Mesto: '{Mesto}'");
                LogDebug($"  Osigurenik: '{Osigurenik}'");
                LogDebug($"  Adresa: '{Adresa}'");
                
                // Apply fallback for fields that didn't extract properly from coordinates
                // Use the full OCR text as fallback, similar to how Mesto works
                var fullText = ExtractedText ?? "";
                var cleanText = CleanOcrText(fullText);
                
                LogDebug("Checking for fallback extraction needed...");
                
                if (string.IsNullOrEmpty(Osigurenik))
                {
                    LogDebug("Osigurenik is empty, trying fallback text extraction...");
                    Osigurenik = ExtractOsigurenik(cleanText);
                    LogDebug($"Fallback Osigurenik result: '{Osigurenik ?? "Still not found"}'");
                }
                
                if (string.IsNullOrEmpty(Adresa))
                {
                    LogDebug("Adresa is empty, trying fallback text extraction...");
                    Adresa = ExtractAdresa(cleanText);
                    LogDebug($"Fallback Adresa result: '{Adresa ?? "Still not found"}'");
                }
                
                // Also apply fallback for other fields if needed (like Mesto does)
                if (string.IsNullOrEmpty(Mesto))
                {
                    LogDebug("Mesto is empty, trying fallback text extraction...");
                    Mesto = ExtractMesto(cleanText);
                    LogDebug($"Fallback Mesto result: '{Mesto ?? "Still not found"}'");
                }
                
                if (string.IsNullOrEmpty(Registracija))
                {
                    LogDebug("Registracija is empty, trying fallback text extraction...");
                    Registracija = ExtractRegistracija(cleanText);
                    LogDebug($"Fallback Registracija result: '{Registracija ?? "Still not found"}'");
                }
                
                LogDebug("Final values after fallback extraction:");
                LogDebug($"  BrojNaPolisa: '{BrojNaPolisa}'");
                LogDebug($"  ImeNaDgovoruvac: '{ImeNaDgovoruvac}'");
                LogDebug($"  EMBG_ME: '{EMBG_ME}'");
                LogDebug($"  Registracija: '{Registracija}'");
                LogDebug($"  Mesto: '{Mesto}'");
                LogDebug($"  Osigurenik: '{Osigurenik}'");
                LogDebug($"  Adresa: '{Adresa}'");
                
                LogDebug("Used coordinate extraction results with fallback for structured data");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting from coordinate results: {ex.Message}");
            }
        }

        /// <summary>
        /// Extract value from coordinate result line
        /// </summary>
        private string? ExtractValueFromCoordinateLine(string line)
        {
            var colonIndex = line.IndexOf(':');
            if (colonIndex >= 0 && colonIndex < line.Length - 1)
            {
                var value = line.Substring(colonIndex + 1).Trim();
                LogDebug($"ExtractValueFromCoordinateLine: '{line}' -> value: '{value}' -> result: '{(string.IsNullOrEmpty(value) || value == "N/A" ? "null" : value)}'");
                return string.IsNullOrEmpty(value) || value == "N/A" ? null : value;
            }
            LogDebug($"ExtractValueFromCoordinateLine: '{line}' -> no colon found or invalid format -> null");
            return null;
        }

        private string CleanOcrText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;
                
            // Remove extra whitespace and normalize line breaks
            var lines = text.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
            var cleanedLines = new List<string>();
            
            foreach (var line in lines)
            {
                var cleanLine = line.Trim();
                if (!string.IsNullOrEmpty(cleanLine))
                {
                    cleanedLines.Add(cleanLine);
                }
            }
            
            return string.Join("\n", cleanedLines);
        }

        private string? ExtractPolicyNumber(string text)
        {
            try
            {
                // Look for policy number patterns
                // Pattern 1: "this number az variable BrojNaPolisa" followed by number
                var policyPattern1 = new Regex(@"this\s+number\s+az\s+variable\s+BrojNaPolisa\s*(\d{8,10})", RegexOptions.IgnoreCase);
                var match1 = policyPattern1.Match(text);
                if (match1.Success)
                {
                    return match1.Groups[1].Value;
                }
                
                // Pattern 2: Just look for 8-10 digit numbers that could be policy numbers
                var policyPattern2 = new Regex(@"\b(\d{8,10})\b");
                var matches2 = policyPattern2.Matches(text);
                
                foreach (Match match in matches2)
                {
                    var number = match.Groups[1].Value;
                    // If it's around the expected length for policy number
                    if (number.Length >= 8 && number.Length <= 10)
                    {
                        return number;
                    }
                }
                
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting policy number: {ex.Message}");
                return null;
            }
        }

        private string? ExtractInsuredName(string text)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"=== NAME EXTRACTION DEBUG ===");
                System.Diagnostics.Debug.WriteLine($"Full text length: {text.Length}");
                
                // Pattern 1: Look for name after policy number, specifically after agent info
                // The structure is: PolicyNumber -> AgentCode AgentName -> CompanyInfo -> ДОГОВОРУВАЧ NAME
                var policyNumberPattern = new Regex(@"\b(\d{9})\b");
                var policyMatches = policyNumberPattern.Matches(text);
                
                foreach (Match policyMatch in policyMatches)
                {
                    var policyNumber = policyMatch.Groups[1].Value;
                    System.Diagnostics.Debug.WriteLine($"Found policy number: {policyNumber}");
                    
                    // Look for text after this policy number
                    var afterPolicyIndex = policyMatch.Index + policyMatch.Length;
                    var textAfterPolicy = text.Substring(afterPolicyIndex);
                    
                    // First, skip the agent info (6-digit code + agent name)
                    var agentSkipPattern = new Regex(@"\d{6}\s+[А-ЯЁЂЃЉЊЌЏ]+\s+[А-ЯЁЂЃЉЊЌЏ]+");
                    var agentMatch = agentSkipPattern.Match(textAfterPolicy);
                    
                    string textAfterAgent = textAfterPolicy;
                    if (agentMatch.Success)
                    {
                        textAfterAgent = textAfterPolicy.Substring(agentMatch.Index + agentMatch.Length);
                        System.Diagnostics.Debug.WriteLine($"Skipped agent info: '{agentMatch.Value.Trim()}'");
                    }
                    
                    // Then skip company info (pattern like "100127-ОБД ИНКО АД Скопје")
                    var companySkipPattern = new Regex(@"\d{6}-[А-ЯЁЂЃЉЊЌЏ\s]+(?:АД|ООД|ДООЕЛ)[А-ЯЁЂЃЉЊЌЏ\s]*");
                    var companyMatch = companySkipPattern.Match(textAfterAgent);
                    
                    string textAfterCompany = textAfterAgent;
                    if (companyMatch.Success)
                    {
                        textAfterCompany = textAfterAgent.Substring(companyMatch.Index + companyMatch.Length);
                        System.Diagnostics.Debug.WriteLine($"Skipped company info: '{companyMatch.Value.Trim()}'");
                    }
                    
                    // Now look for the first personal name (this should be the Договорувач)
                    var namePattern = new Regex(@"\b([А-ЯЁЂЃЉЊЌЏ]{4,}\s+[А-ЯЁЂЃЉЊЌЏ]{4,})\b");
                    var nameMatch = namePattern.Match(textAfterCompany);
                    
                    if (nameMatch.Success)
                    {
                        var name = CleanExtractedName(nameMatch.Groups[1].Value);
                        System.Diagnostics.Debug.WriteLine($"Found договорувач name after skipping agent/company: '{name}'");
                        
                        if (!IsCommonWord(name))
                        {
                            System.Diagnostics.Debug.WriteLine($"Selected договорувач name: '{name}'");
                            return name;
                        }
                    }
                }
                
                // Pattern 2: Traditional approach - look near "Договорувач" label
                var containsDogovoruvac = text.Contains("Договорувач");
                System.Diagnostics.Debug.WriteLine($"Contains 'Договорувач': {containsDogovoruvac}");
                
                if (containsDogovoruvac)
                {
                    var lines = text.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                    
                    for (int i = 0; i < lines.Length; i++)
                    {
                        var line = lines[i].Trim();
                        
                        if (line.Contains("Договорувач"))
                        {
                            System.Diagnostics.Debug.WriteLine($"Found Договорувач on line {i}: '{line}'");
                            
                            // Look in surrounding lines for names
                            for (int j = Math.Max(0, i - 3); j <= Math.Min(lines.Length - 1, i + 5); j++)
                            {
                                if (j == i) continue;
                                
                                var surroundingLine = lines[j].Trim();
                                var nameMatch = new Regex(@"^([А-ЯЁЂЃЉЊЌЏ]{4,}\s+[А-ЯЁЂЃЉЊЌЏ]{4,})").Match(surroundingLine);
                                
                                if (nameMatch.Success)
                                {
                                    var name = CleanExtractedName(nameMatch.Groups[1].Value);
                                    System.Diagnostics.Debug.WriteLine($"Found name near Договорувач: '{name}'");
                                    
                                    if (!IsAgentOrCompanyName(name) && !IsCommonWord(name))
                                    {
                                        return name;
                                    }
                                }
                            }
                        }
                    }
                }
                
                // Pattern 3: Look for names that appear after agent codes but before company info
                // Agent codes are like "004701", "004700" - skip names immediately after these
                var agentCodePattern = new Regex(@"\b(\d{6})\s+([А-ЯЁЂЃЉЊЌЏ]{4,}\s+[А-ЯЁЂЃЉЊЌЏ]{4,})");
                var agentMatches = agentCodePattern.Matches(text);
                
                var agentNames = new List<string>();
                foreach (Match agentMatch in agentMatches)
                {
                    var agentName = agentMatch.Groups[2].Value.Trim();
                    agentNames.Add(agentName.ToUpper());
                    System.Diagnostics.Debug.WriteLine($"Found agent name to skip: {agentName}");
                }
                
                // Pattern 4: Broad search for any valid personal name
                var allNameMatches = new Regex(@"\b([А-ЯЁЂЃЉЊЌЏ]{4,}\s+[А-ЯЁЂЃЉЊЌЏ]{4,})\b").Matches(text);
                System.Diagnostics.Debug.WriteLine($"Found {allNameMatches.Count} potential names in total");
                
                foreach (Match nameMatch in allNameMatches)
                {
                    var name = CleanExtractedName(nameMatch.Groups[1].Value);
                    System.Diagnostics.Debug.WriteLine($"Evaluating name: '{name}'");
                    
                    // Skip if it's a detected agent name
                    if (agentNames.Contains(name.ToUpper()))
                    {
                        System.Diagnostics.Debug.WriteLine($"Skipping detected agent name: '{name}'");
                        continue;
                    }
                    
                    if (!IsAgentOrCompanyName(name) && !IsCommonWord(name))
                    {
                        System.Diagnostics.Debug.WriteLine($"Selected broad search name: '{name}'");
                        return name;
                    }
                }
                
                System.Diagnostics.Debug.WriteLine("=== NO NAME FOUND ===");
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting договорувач name: {ex.Message}");
                return null;
            }
        }

        private string CleanExtractedName(string name)
        {
            if (string.IsNullOrEmpty(name))
                return string.Empty;
                
            System.Diagnostics.Debug.WriteLine($"Cleaning name: '{name}'");
            
            // Remove extra whitespace and clean up
            name = Regex.Replace(name, @"\s+", " ").Trim();
            
            // Remove common suffixes that might be captured
            name = Regex.Replace(name, @"\s*(ЕМБГ|ME|Место|Адреса|СКОПЈЕ).*$", "", RegexOptions.IgnoreCase);
            
            // Remove labels if they appear at the start
            name = Regex.Replace(name, @"^(Договорувач|Осигуреник)\s*:?\s*", "", RegexOptions.IgnoreCase);
            
            var cleaned = name.Trim();
            System.Diagnostics.Debug.WriteLine($"Cleaned to: '{cleaned}'");
            
            return cleaned;
        }

        private string? ExtractEmbgMe(string text)
        {
            try
            {
                // Look for EMBG/ME patterns
                // Pattern 1: "EMBG/ME:" followed by number
                var embgPattern1 = new Regex(@"EMBG[/\\]ME:\s*(\d{13})", RegexOptions.IgnoreCase);
                var match1 = embgPattern1.Match(text);
                if (match1.Success)
                {
                    return match1.Groups[1].Value;
                }
                
                // Pattern 2: Just "EMBG" or "ME" followed by 13 digits
                var embgPattern2 = new Regex(@"(?:EMBG|ME)[\s:]*(\d{13})", RegexOptions.IgnoreCase);
                var match2 = embgPattern2.Match(text);
                if (match2.Success)
                {
                    return match2.Groups[1].Value;
                }
                
                // Pattern 3: Look for 13-digit numbers (EMBG format)
                var embgPattern3 = new Regex(@"\b(\d{13})\b");
                var matches3 = embgPattern3.Matches(text);
                
                foreach (Match match in matches3)
                {
                    var number = match.Groups[1].Value;
                    // EMBG should be exactly 13 digits
                    if (number.Length == 13)
                    {
                        return number;
                    }
                }
                
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting EMBG/ME: {ex.Message}");
                return null;
            }
        }

        private bool IsCommonWord(string word)
        {
            if (string.IsNullOrEmpty(word) || word.Length < 3)
                return true;
                
            var upperWord = word.ToUpper();
            
            var commonWords = new[] { 
                "ДОГОВОРУВАЧ", "ОСИГУРЕНИК", "ЕМБГ", "МЕСТО", "АДРЕСА", "СКОПЈЕ", 
                "МАКЕДОНИЈА", "СЕВЕРНА", "РЕПУБЛИКА", "МК", "ME", "УЛИЦА", "БР",
                "БРОЈ", "ПОЛИСА", "ОСИГУРУВАЊЕ", "АВТОМОБИЛСКО", "ОДГОВОРНОСТ",
                "ДОО", "ДООЕЛ", "АД", "ДРУШТВО", "КОМПАНИЈА", "ДАТУМ", "ГРАД",
                "ПАТНИЧКИ", "ВОЗИЛА", "МОЌНОСТ", "МАРКА", "МОДЕЛ", "ОСНОВНА", "ПРЕМИЈА"
            };
            
            // Check if the word is exactly a common word
            if (commonWords.Contains(upperWord))
            {
                System.Diagnostics.Debug.WriteLine($"IsCommonWord('{word}'): TRUE (exact match)");
                return true;
            }
                
            // Be less restrictive - only reject if it's mostly a common word
            var isCommon = commonWords.Any(common => upperWord.Contains(common) && common.Length > upperWord.Length * 0.8);
            
            System.Diagnostics.Debug.WriteLine($"IsCommonWord('{word}'): {isCommon}");
            return isCommon;
        }

        private bool IsAgentOrCompanyName(string name)
        {
            if (string.IsNullOrEmpty(name))
                return true;
                
            var upperName = name.ToUpper();
            
            // Company/organization indicators
            var companyIndicators = new[] { 
                "АД", "ООД", "ДООЕЛ", "ДРУШТВО", "КОМПАНИЈА", "ОБД", "ИНКО", "СКОПЈЕ",
                "ЕВРОИНС", "ЕВРОПА", "МАКЕДОНИЈА"
            };
            
            // Agent/employee name patterns (typically female names in insurance)
            var commonAgentNameParts = new[] {
                "ОЛГА", "ИРЕНА", "МАРИЈА", "АННА", "ЕЛЕНА", "КАТЕРИНА", "СОЊА", "ВЕСНА",
                "НИКОЛОВСКА", "РИСТОВСКА", "ПЕТРОВСКА", "СТОЈАНОВСКА", "ЈОВАНОВСКА"
            };
            
            // Check if it contains company indicators
            if (companyIndicators.Any(indicator => upperName.Contains(indicator)))
            {
                System.Diagnostics.Debug.WriteLine($"IsAgentOrCompanyName('{name}'): TRUE (company indicator: {companyIndicators.FirstOrDefault(i => upperName.Contains(i))})");
                return true;
            }
            
            // Check if it looks like an agent name (common female names + surnames ending in -ска)
            var nameParts = upperName.Split(' ');
            if (nameParts.Length == 2)
            {
                var firstName = nameParts[0];
                var lastName = nameParts[1];
                
                // Check for common agent first names or surnames ending in -ска
                if (commonAgentNameParts.Contains(firstName) || lastName.EndsWith("СКА"))
                {
                    System.Diagnostics.Debug.WriteLine($"IsAgentOrCompanyName('{name}'): TRUE (agent name pattern)");
                    return true;
                }
            }
            
            // Check if it appears to be a short company abbreviation (like "ОБД ИНКО")
            if (nameParts.Length == 2 && nameParts.All(part => part.Length <= 5) && upperName.Length <= 12)
            {
                System.Diagnostics.Debug.WriteLine($"IsAgentOrCompanyName('{name}'): TRUE (short company pattern)");
                return true;
            }
            
            // Check if name appears in a context that suggests it's an agent (after agent codes like "004701")
            // This would need to be checked in the calling method by looking at surrounding text
            
            System.Diagnostics.Debug.WriteLine($"IsAgentOrCompanyName('{name}'): FALSE (appears to be personal name)");
            return false;
        }

        private async Task<List<string>> ConvertPdfToImages(string pdfPath)
        {
            var imageFiles = new List<string>();
            
            try
            {
                // Method 1: Pure .NET text extraction (cross-platform, no dependencies)
                System.Diagnostics.Debug.WriteLine("Using pure .NET PDF text extraction (cross-platform)");
                var textResult = await TryITextPdfExtraction(pdfPath);
                if (textResult.Count > 0)
                {
                    return textResult;
                }
                
                // Method 2: Try using Magick.NET library (if available)
                System.Diagnostics.Debug.WriteLine("Fallback: Trying Magick.NET for PDF to image conversion");
                var magickResult = await ConvertPdfWithMagickNet(pdfPath);
                if (magickResult.Count > 0)
                {
                    return magickResult;
                }
                
                // Method 3: System commands fallback (Linux only, for legacy support)
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    System.Diagnostics.Debug.WriteLine("Final fallback: Trying system commands on Linux");
                    var systemResult = await ConvertPdfWithSystemCommands(pdfPath);
                    if (systemResult.Count > 0)
                    {
                        return systemResult;
                    }
                }
                
                throw new Exception("Не може да се обработи PDF датотеката. Обидете се со слика наместо PDF.");
            }
            catch (Exception ex)
            {
                throw new Exception($"PDF to image conversion failed: {ex.Message}");
            }
        }

        private async Task<List<string>> ConvertPdfWithMagickNet(string pdfPath)
        {
            var imageFiles = new List<string>();
            
            try
            {
                // Check if file exists and is accessible
                if (!System.IO.File.Exists(pdfPath))
                {
                    throw new FileNotFoundException($"PDF file not found: {pdfPath}");
                }

                var fileInfo = new FileInfo(pdfPath);
                System.Diagnostics.Debug.WriteLine($"PDF Info: {fileInfo.Name}, Size: {fileInfo.Length} bytes");

                // Try different approaches for PDF conversion
                return await TryMultipleMagickNetApproaches(pdfPath);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Magick.NET conversion failed: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                
                // Clean up any partial files
                foreach (var file in imageFiles)
                {
                    if (System.IO.File.Exists(file))
                    {
                        try { System.IO.File.Delete(file); } catch { }
                    }
                }
                
                // Try alternative approach with iText7 + System.Drawing
                return await TryITextPdfExtraction(pdfPath);
            }
        }

        private async Task<List<string>> TryMultipleMagickNetApproaches(string pdfPath)
        {
            var imageFiles = new List<string>();
            
            // Check if Ghostscript is available (required for PDF processing with Magick.NET)
            try
            {
                MagickNET.SetGhostscriptDirectory(@"C:\Program Files\gs\gs10.02.1\bin");
            }
            catch
            {
                // Ghostscript path might be different or not installed
                System.Diagnostics.Debug.WriteLine("Ghostscript directory not found at default location");
            }
            
            // Approach 1: Standard approach with high quality
            try
            {
                var settings = new MagickReadSettings
                {
                    Density = new Density(300, 300),
                    Format = MagickFormat.Png,
                    ColorSpace = ColorSpace.sRGB
                };

                using (var images = new MagickImageCollection())
                {
                    await Task.Run(() => images.Read(pdfPath, settings));
                    
                    System.Diagnostics.Debug.WriteLine($"Magick.NET: Read {images.Count} pages from PDF");
                    
                    for (int i = 0; i < images.Count; i++)
                    {
                        var tempImagePath = Path.Combine(Path.GetTempPath(), $"pdf_page_{Guid.NewGuid()}_{i + 1}.png");
                        
                        using (var image = images[i])
                        {
                            image.Format = MagickFormat.Png;
                            image.ColorType = ColorType.Grayscale;
                            
                            await Task.Run(() => image.Write(tempImagePath));
                            imageFiles.Add(tempImagePath);
                        }
                    }
                }
                
                if (imageFiles.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"Magick.NET Approach 1: Successfully converted {imageFiles.Count} pages");
                    return imageFiles;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Magick.NET Approach 1 failed: {ex.Message}");
                CleanupFiles(imageFiles);
                imageFiles.Clear();
            }

            // Approach 2: Lower quality but more compatible
            try
            {
                var settings = new MagickReadSettings
                {
                    Density = new Density(150, 150), // Lower DPI
                    Format = MagickFormat.Png
                };

                using (var images = new MagickImageCollection())
                {
                    await Task.Run(() => images.Read(pdfPath, settings));
                    
                    for (int i = 0; i < images.Count; i++)
                    {
                        var tempImagePath = Path.Combine(Path.GetTempPath(), $"pdf_page_low_{Guid.NewGuid()}_{i + 1}.png");
                        
                        using (var image = images[i])
                        {
                            await Task.Run(() => image.Write(tempImagePath));
                            imageFiles.Add(tempImagePath);
                        }
                    }
                }
                
                if (imageFiles.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"Magick.NET Approach 2: Successfully converted {imageFiles.Count} pages");
                    return imageFiles;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Magick.NET Approach 2 failed: {ex.Message}");
                CleanupFiles(imageFiles);
                imageFiles.Clear();
            }

            return imageFiles;
        }

        private async Task<List<string>> TryITextPdfExtraction(string pdfPath)
        {
            var textFiles = new List<string>();
            
            try
            {
                System.Diagnostics.Debug.WriteLine("Using iText7 PDF text extraction (cross-platform, pure .NET)...");
                
                // Extract text directly from PDF using iText7 - works on all platforms
                using (var pdfReader = new PdfReader(pdfPath))
                using (var pdfDocument = new PdfDocument(pdfReader))
                {
                    var pageCount = pdfDocument.GetNumberOfPages();
                    System.Diagnostics.Debug.WriteLine($"PDF has {pageCount} pages");
                    
                    var successfulPages = 0;
                    var totalCharacters = 0;
                    
                    for (int i = 1; i <= pageCount; i++)
                    {
                        try
                        {
                            var page = pdfDocument.GetPage(i);
                            var strategy = new SimpleTextExtractionStrategy();
                            var text = PdfTextExtractor.GetTextFromPage(page, strategy);
                            
                            if (!string.IsNullOrWhiteSpace(text))
                            {
                                // Create text file for processing (no OCR needed)
                                var tempTextPath = Path.Combine(Path.GetTempPath(), $"pdf_text_{Guid.NewGuid()}_{i}.txt");
                                await System.IO.File.WriteAllTextAsync(tempTextPath, text);
                                textFiles.Add(tempTextPath);
                                
                                successfulPages++;
                                totalCharacters += text.Length;
                                
                                System.Diagnostics.Debug.WriteLine($"Page {i}: {text.Length} characters extracted");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"Page {i}: No text content found");
                            }
                        }
                        catch (Exception pageEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"Failed to extract text from page {i}: {pageEx.Message}");
                            
                            // Create an error placeholder for this page
                            var errorTextPath = Path.Combine(Path.GetTempPath(), $"pdf_error_{Guid.NewGuid()}_{i}.txt");
                            await System.IO.File.WriteAllTextAsync(errorTextPath, $"Грешка при обработка на страна {i}: {pageEx.Message}");
                            textFiles.Add(errorTextPath);
                        }
                    }
                    
                    System.Diagnostics.Debug.WriteLine($"iText7 Summary: {successfulPages}/{pageCount} pages processed, {totalCharacters} total characters");
                }
                
                if (textFiles.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"iText7: Successfully processed {textFiles.Count} pages using pure .NET");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("iText7: No text content found in PDF");
                }
                
                return textFiles;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"iText7 text extraction failed: {ex.Message}");
                CleanupFiles(textFiles);
                return new List<string>();
            }
        }

        private void CleanupFiles(List<string> files)
        {
            foreach (var file in files)
            {
                if (System.IO.File.Exists(file))
                {
                    try { System.IO.File.Delete(file); } catch { }
                }
            }
        }

        private async Task<List<string>> ConvertPdfWithSystemCommands(string pdfPath)
        {
            var imageFiles = new List<string>();
            
            // Try ImageMagick first
            try
            {
                var magickResult = await ConvertPdfWithImageMagickCommand(pdfPath);
                if (magickResult.Count > 0) return magickResult;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ImageMagick command failed: {ex.Message}");
            }
            
            // Try Ghostscript second
            try
            {
                var ghostscriptResult = await ConvertPdfWithGhostscriptCommand(pdfPath);
                if (ghostscriptResult.Count > 0) return ghostscriptResult;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Ghostscript command failed: {ex.Message}");
            }
            
            // Try pdftoppm third
            try
            {
                var pdftoppmResult = await ConvertPdfWithPdftoppmCommand(pdfPath);
                if (pdftoppmResult.Count > 0) return pdftoppmResult;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Pdftoppm command failed: {ex.Message}");
            }
            
            return imageFiles;
        }

        private async Task<List<string>> ConvertPdfWithImageMagickCommand(string pdfPath)
        {
            var imageFiles = new List<string>();
            var outputDir = Path.GetTempPath();
            var outputPrefix = Path.GetFileNameWithoutExtension(Path.GetTempFileName());
            var outputPattern = Path.Combine(outputDir, $"{outputPrefix}-%d.png");
            
            var processInfo = new System.Diagnostics.ProcessStartInfo
            {
                FileName = "magick",
                Arguments = $"convert -density 300 \"{pdfPath}\" \"{outputPattern}\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using (var process = new System.Diagnostics.Process())
            {
                process.StartInfo = processInfo;
                process.Start();
                await process.WaitForExitAsync();

                if (process.ExitCode == 0)
                {
                    var generatedFiles = Directory.GetFiles(outputDir, $"{outputPrefix}-*.png");
                    imageFiles.AddRange(generatedFiles);
                }
            }
            
            return imageFiles;
        }

        private async Task<List<string>> ConvertPdfWithGhostscriptCommand(string pdfPath)
        {
            var imageFiles = new List<string>();
            var outputDir = Path.GetTempPath();
            var outputPrefix = Path.GetFileNameWithoutExtension(Path.GetTempFileName());
            var outputPattern = Path.Combine(outputDir, $"{outputPrefix}-%d.png");
            
            var processInfo = new System.Diagnostics.ProcessStartInfo
            {
                FileName = "gs",
                Arguments = $"-dNOPAUSE -dBATCH -sDEVICE=png256 -r300 -sOutputFile=\"{outputPattern}\" \"{pdfPath}\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using (var process = new System.Diagnostics.Process())
            {
                process.StartInfo = processInfo;
                process.Start();
                await process.WaitForExitAsync();

                if (process.ExitCode == 0)
                {
                    var generatedFiles = Directory.GetFiles(outputDir, $"{outputPrefix}-*.png");
                    imageFiles.AddRange(generatedFiles);
                }
            }
            
            return imageFiles;
        }

        private async Task<List<string>> ConvertPdfWithPdftoppmCommand(string pdfPath)
        {
            var imageFiles = new List<string>();
            var outputDir = Path.GetTempPath();
            var outputPrefix = Path.GetFileNameWithoutExtension(Path.GetTempFileName());
            
            var processInfo = new System.Diagnostics.ProcessStartInfo
            {
                FileName = "pdftoppm",
                Arguments = $"-png -r 300 \"{pdfPath}\" \"{Path.Combine(outputDir, outputPrefix)}\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using (var process = new System.Diagnostics.Process())
            {
                process.StartInfo = processInfo;
                process.Start();
                await process.WaitForExitAsync();

                if (process.ExitCode == 0)
                {
                    var generatedFiles = Directory.GetFiles(outputDir, $"{outputPrefix}-*.png");
                    imageFiles.AddRange(generatedFiles);
                }
            }
            
            return imageFiles;
        }

        private async Task<(string Text, string DetectedLanguage, int ProcessedWords, int EnglishWords, int MacedonianWords)> ProcessSingleImageForPdf(string filePath, string tessDataPath, string language, int ocrMode, int pageNumber)
        {
            try
            {
                // Check if this is a text file (from iText7 fallback) or image file
                if (Path.GetExtension(filePath).ToLowerInvariant() == ".txt")
                {
                    // This is extracted text from PDF, process directly
                    var extractedText = await System.IO.File.ReadAllTextAsync(filePath);
                    var wordAnalysis = AnalyzeWordsInText(extractedText);
                    
                    var pageText = $"--- Страна {pageNumber} (Директно од PDF) ---\n{extractedText}\n";
                    
                    // Determine language based on text analysis
                    var detectedLang = wordAnalysis.CyrillicWords > wordAnalysis.LatinWords ? "Македонски (детектиран)" : "Англиски (детектиран)";
                    
                    return (pageText, detectedLang, wordAnalysis.TotalWords, wordAnalysis.LatinWords, wordAnalysis.CyrillicWords);
                }
                else
                {
                    // This is an image file, process with OCR
                    if (language == "auto")
                    {
                        // Use multi-engine approach for PDF pages
                        var results = await ProcessWithMultipleEngines(filePath, tessDataPath, ocrMode);
                        
                        var pageText = $"--- Страна {pageNumber} ---\n{results.BestText}\n";
                        
                        return (pageText, results.DetectedLanguage, results.TotalWords, results.EnglishWords, results.MacedonianWords);
                    }
                    else
                    {
                        // Use specified language
                        var text = await ProcessWithSingleLanguage(filePath, tessDataPath, language, ocrMode);
                        var pageText = $"--- Страна {pageNumber} ---\n{text}\n";
                        
                        // Simple word analysis for specified language
                        var wordAnalysis = AnalyzeWordsInText(text);
                        
                        return (pageText, GetLanguageDisplayName(language), wordAnalysis.TotalWords, wordAnalysis.LatinWords, wordAnalysis.CyrillicWords);
                    }
                }
            }
            catch (Exception ex)
            {
                // Return error information for this page
                var errorText = $"--- Страна {pageNumber} (Грешка) ---\nГрешка при обработка: {ex.Message}\n";
                return (errorText, "Грешка", 0, 0, 0);
            }
        }

        #region Coordinate-Based OCR Extraction

        /// <summary>
        /// Coordinate-based extraction using measured regions from LibreOffice Draw
        /// </summary>
        private async Task<(string Text, string DetectedLanguage, int ProcessedWords, int EnglishWords, int MacedonianWords)> ProcessImageWithCoordinateExtraction(IFormFile imageFile, string language, int ocrMode)
        {
            var tessDataPath = Path.Combine(_environment.WebRootPath, "tessdata");
            
            if (!Directory.Exists(tessDataPath))
            {
                throw new DirectoryNotFoundException($"Tessdata directory not found at: {tessDataPath}");
            }

            var tempFilePath = Path.GetTempFileName();
            
            try
            {
                using (var stream = new FileStream(tempFilePath, FileMode.Create))
                {
                    await imageFile.CopyToAsync(stream);
                }

                // Extract data from specific coordinate regions
                var coordinateResults = await ExtractFromCoordinateRegions(tempFilePath, tessDataPath, language, ocrMode);
                
                // Build results text
                var resultText = BuildCoordinateExtractionResults(coordinateResults);
                
                // Analyze combined text
                var wordAnalysis = AnalyzeWordsInText(resultText);
                
                // Store coordinate extraction results for display
                CoordinateExtractionResults = FormatCoordinateResultsForDisplay(coordinateResults);
                
                return (resultText, "Coordinate-based extraction", wordAnalysis.TotalWords, wordAnalysis.LatinWords, wordAnalysis.CyrillicWords);
            }
            finally
            {
                if (System.IO.File.Exists(tempFilePath))
                {
                    System.IO.File.Delete(tempFilePath);
                }
            }
        }

        /// <summary>
        /// Extract text from specific coordinate regions based on LibreOffice Draw measurements
        /// </summary>
        private async Task<Dictionary<string, CoordinateExtractionResult>> ExtractFromCoordinateRegions(string imagePath, string tessDataPath, string language, int ocrMode)
        {
            var results = new Dictionary<string, CoordinateExtractionResult>();
            
            // Define coordinate regions based on LibreOffice Draw measurements
            // Converting cm to pixels assuming 300 DPI (300 pixels per inch, 2.54 cm per inch)
            // Formula: pixels = cm * (300 / 2.54)
            var regions = new Dictionary<string, DocumentRegion>
            {
                ["Договорувач"] = new DocumentRegion
                {
                    Name = "Договорувач",
                    X = (int)(2.83 * 300 / 2.54), // 334 pixels
                    Y = (int)(2.31 * 300 / 2.54), // 273 pixels  
                    Width = (int)(3.01 * 300 / 2.54), // 356 pixels
                    Height = (int)(0.35 * 300 / 2.54), // 41 pixels
                    Description = "Име на договорувач"
                },
                ["PolicyNumber"] = new DocumentRegion
                {
                    Name = "PolicyNumber", 
                    X = (int)(10.94 * 300 / 2.54), // 1293 pixels
                    Y = (int)(0.00 * 300 / 2.54), // 0 pixels
                    Width = (int)(2.23 * 300 / 2.54), // 264 pixels
                    Height = (int)(0.35 * 300 / 2.54), // 41 pixels
                    Description = "Број на полиса"
                },
                ["EMBG_ME"] = new DocumentRegion
                {
                    Name = "EMBG_ME",
                    X = (int)(10.77 * 300 / 2.54), // 1273 pixels
                    Y = (int)(2.31 * 300 / 2.54), // 273 pixels
                    Width = (int)(2.07 * 300 / 2.54), // 245 pixels
                    Height = (int)(0.35 * 300 / 2.54), // 41 pixels
                    Description = "ЕМБГ/МЕ број"
                },
                ["Registracija"] = new DocumentRegion
                {
                    Name = "Registracija",
                    X = (int)(7.50 * 300 / 2.54), // ~885 pixels (estimated position)
                    Y = (int)(7.50 * 300 / 2.54), // ~885 pixels (estimated position)
                    Width = (int)(2.50 * 300 / 2.54), // ~295 pixels (estimated width)
                    Height = (int)(0.40 * 300 / 2.54), // ~47 pixels (estimated height)
                    Description = "Регистрација на возило"
                },
                ["Mesto"] = new DocumentRegion
                {
                    Name = "Mesto",
                    X = (int)(17.50 * 300 / 2.54), // 2068 pixels (shifted right to target city value, not label)
                    Y = (int)(4.22 * 300 / 2.54), // 498 pixels (same Y position)
                    Width = (int)(2.50 * 300 / 2.54), // 295 pixels (reduced width to focus on city name)
                    Height = (int)(0.35 * 300 / 2.54), // ~41 pixels (estimated height)
                    Description = "Место"
                },
                ["Осигуреник"] = new DocumentRegion
                {
                    Name = "Осигуреник",
                    X = (int)(2.83 * 300 / 2.54), // 334 pixels (from Properties panel)
                    Y = (int)(3.76 * 300 / 2.54), // 444 pixels (from Properties panel)
                    Width = (int)(3.01 * 300 / 2.54), // 356 pixels (from Properties panel)
                    Height = (int)(0.35 * 300 / 2.54), // 41 pixels (estimated height)
                    Description = "Осигуреник"
                },
                ["Adresa"] = new DocumentRegion
                {
                    Name = "Adresa",
                    X = (int)(2.65 * 300 / 2.54), // 668 pixels (from Properties panel)
                    Y = (int)(0.80 * 300 / 2.54), // 331 pixels (from Properties panel)
                    Width = (int)(7.10 * 300 / 2.54), // 839 pixels (from Properties panel)
                    Height = (int)(0.35 * 300 / 2.54), // 41 pixels (estimated height)
                    Description = "Адреса"
                }
            };

            System.Diagnostics.Debug.WriteLine("=== COORDINATE-BASED EXTRACTION ===");
            
            foreach (var region in regions)
            {
                try
                {
                    var extractionResult = await ExtractFromSingleRegion(imagePath, tessDataPath, region.Value, language, ocrMode);
                    results[region.Key] = extractionResult;
                    
                    System.Diagnostics.Debug.WriteLine($"Region '{region.Key}': '{extractionResult.ExtractedText}' (Confidence: {extractionResult.Confidence:F1}%)");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to extract from region '{region.Key}': {ex.Message}");
                    results[region.Key] = new CoordinateExtractionResult
                    {
                        RegionName = region.Key,
                        ExtractedText = "",
                        Confidence = 0,
                        ErrorMessage = ex.Message
                    };
                }
            }

            return results;
        }

        /// <summary>
        /// Extract text from a single coordinate region using ImageMagick crop + Tesseract
        /// </summary>
        private async Task<CoordinateExtractionResult> ExtractFromSingleRegion(string imagePath, string tessDataPath, DocumentRegion region, string language, int ocrMode)
        {
            var croppedImagePath = Path.GetTempFileName() + ".png";
            
            try
            {
                // Method 1: Try ImageMagick.NET for cropping
                if (await TryCropWithMagickNet(imagePath, croppedImagePath, region))
                {
                    return await ProcessCroppedRegion(croppedImagePath, tessDataPath, region, language, ocrMode);
                }
                
                // Method 2: Try system ImageMagick command
                if (await TryCropWithSystemCommand(imagePath, croppedImagePath, region))
                {
                    return await ProcessCroppedRegion(croppedImagePath, tessDataPath, region, language, ocrMode);
                }
                
                // Method 3: Fallback - use full image with PSM settings for region-like processing
                System.Diagnostics.Debug.WriteLine($"Cropping failed for region '{region.Name}', using full image with region-optimized settings");
                return await ProcessFullImageAsRegion(imagePath, tessDataPath, region, language);
            }
            finally
            {
                if (System.IO.File.Exists(croppedImagePath))
                {
                    System.IO.File.Delete(croppedImagePath);
                }
            }
        }

        /// <summary>
        /// Crop image using ImageMagick.NET
        /// </summary>
        private async Task<bool> TryCropWithMagickNet(string imagePath, string outputPath, DocumentRegion region)
        {
            try
            {
                using (var image = new MagickImage(imagePath))
                {
                    // Define crop geometry
                    var geometry = new MagickGeometry(region.X, region.Y, region.Width, region.Height);
                    
                    // Crop the image
                    image.Crop(geometry);
                    
                    // Enhance for OCR
                    image.ColorType = ColorType.Grayscale;
                    image.Contrast();
                    image.Normalize();
                    
                    // Save cropped image
                    await Task.Run(() => image.Write(outputPath));
                    
                    System.Diagnostics.Debug.WriteLine($"Successfully cropped region '{region.Name}' using ImageMagick.NET");
                    return true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ImageMagick.NET cropping failed for region '{region.Name}': {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Crop image using system ImageMagick command
        /// </summary>
        private async Task<bool> TryCropWithSystemCommand(string imagePath, string outputPath, DocumentRegion region)
        {
            try
            {
                var cropGeometry = $"{region.Width}x{region.Height}+{region.X}+{region.Y}";
                
                var processInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "magick",
                    Arguments = $"convert \"{imagePath}\" -crop {cropGeometry} -colorspace Gray -contrast -normalize \"{outputPath}\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using (var process = new System.Diagnostics.Process())
                {
                    process.StartInfo = processInfo;
                    process.Start();
                    
                    var error = await process.StandardError.ReadToEndAsync();
                    await process.WaitForExitAsync();

                    if (process.ExitCode == 0 && System.IO.File.Exists(outputPath))
                    {
                        System.Diagnostics.Debug.WriteLine($"Successfully cropped region '{region.Name}' using system command");
                        return true;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"System command cropping failed for region '{region.Name}': {error}");
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"System command cropping failed for region '{region.Name}': {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Process a cropped region image with OCR
        /// </summary>
        private async Task<CoordinateExtractionResult> ProcessCroppedRegion(string croppedImagePath, string tessDataPath, DocumentRegion region, string language, int ocrMode)
        {
            try
            {
                // Use appropriate language for the region
                var regionLanguage = DetermineRegionLanguage(region.Name, language);
                
                // Use single word/line PSM for small regions
                var regionOcrMode = DetermineRegionOcrMode(region.Name, ocrMode);
                
                var result = await ProcessAndAnalyze(croppedImagePath, tessDataPath, regionLanguage, regionOcrMode);
                
                // Clean the extracted text for this specific region
                var cleanedText = CleanRegionText(result.Text, region.Name);
                
                return new CoordinateExtractionResult
                {
                    RegionName = region.Name,
                    ExtractedText = cleanedText,
                    Confidence = result.Confidence,
                    RegionDescription = region.Description,
                    CoordinateInfo = $"X:{region.X}, Y:{region.Y}, W:{region.Width}, H:{region.Height}"
                };
            }
            catch (Exception ex)
            {
                return new CoordinateExtractionResult
                {
                    RegionName = region.Name,
                    ExtractedText = "",
                    Confidence = 0,
                    ErrorMessage = ex.Message,
                    RegionDescription = region.Description
                };
            }
        }

        /// <summary>
        /// Fallback: Process full image with region-optimized settings
        /// </summary>
        private async Task<CoordinateExtractionResult> ProcessFullImageAsRegion(string imagePath, string tessDataPath, DocumentRegion region, string language)
        {
            try
            {
                // Use text-based extraction as fallback
                using (var engine = new TesseractEngine(tessDataPath, language == "auto" ? "eng+mkd" : language, EngineMode.Default))
                {
                    engine.SetVariable("tessedit_pageseg_mode", "6"); // Single block
                    
                    using (var img = Pix.LoadFromFile(imagePath))
                    {
                        using (var page = engine.Process(img))
                        {
                            var fullText = page.GetText();
                            var confidence = page.GetMeanConfidence();
                            
                            // Try to extract relevant text for this region from full text
                            var regionText = ExtractRegionFromFullText(fullText, region.Name);
                            
                            return new CoordinateExtractionResult
                            {
                                RegionName = region.Name,
                                ExtractedText = regionText,
                                Confidence = confidence,
                                RegionDescription = region.Description + " (текст екстракција)",
                                CoordinateInfo = "Fallback: Full image processing"
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new CoordinateExtractionResult
                {
                    RegionName = region.Name,
                    ExtractedText = "",
                    Confidence = 0,
                    ErrorMessage = ex.Message,
                    RegionDescription = region.Description
                };
            }
        }

        /// <summary>
        /// Determine appropriate language for specific region
        /// </summary>
        private string DetermineRegionLanguage(string regionName, string defaultLanguage)
        {
            if (defaultLanguage != "auto")
                return defaultLanguage;
                
            return regionName switch
            {
                "PolicyNumber" => "eng", // Policy numbers are typically numeric/alphanumeric
                "EMBG_ME" => "eng", // ID numbers are numeric
                "Договорувач" => "mkd", // Names are typically in Macedonian
                "Registracija" => "eng", // Registration numbers are alphanumeric
                "Mesto" => "mkd", // Place names are typically in Macedonian
                "Осигуреник" => "mkd", // Insured person names are typically in Macedonian
                "Adresa" => "mkd", // Addresses are typically in Macedonian
                _ => "eng+mkd"
            };
        }

        /// <summary>
        /// Determine appropriate OCR mode for specific region
        /// </summary>
        private int DetermineRegionOcrMode(string regionName, int defaultMode)
        {
            return regionName switch
            {
                "PolicyNumber" => 8, // Single word for numbers
                "EMBG_ME" => 8, // Single word for ID numbers  
                "Договорувач" => 7, // Single line for names
                "Registracija" => 7, // Single line for registration numbers
                "Mesto" => 8, // Single word for place names
                "Осигуреник" => 7, // Single line for insured person names
                "Adresa" => 7, // Single line for addresses
                _ => defaultMode
            };
        }

        /// <summary>
        /// Clean extracted text for specific region
        /// </summary>
        private string CleanRegionText(string text, string regionName)
        {
            if (string.IsNullOrWhiteSpace(text))
                return "";
                
            var cleaned = text.Trim();
            
            // Remove common OCR artifacts
            cleaned = Regex.Replace(cleaned, @"[^\w\s\-]", ""); // Remove special chars except word chars, spaces, hyphens
            cleaned = Regex.Replace(cleaned, @"\s+", " "); // Normalize whitespace
            
            return regionName switch
            {
                "PolicyNumber" => Regex.Replace(cleaned, @"[^\d]", ""), // Keep only digits
                "EMBG_ME" => Regex.Replace(cleaned, @"[^\d]", ""), // Keep only digits
                "Договорувач" => Regex.Replace(cleaned, @"[^\p{L}\s]", "").Trim(), // Keep only letters and spaces
                "Registracija" => Regex.Replace(cleaned, @"[^\w\-]", "").Trim(), // Keep alphanumeric and hyphens
                "Mesto" => CleanMestoText(cleaned), // Special cleaning for city names
                "Осигуреник" => Regex.Replace(cleaned, @"[^\p{L}\s]", "").Trim(), // Keep only letters and spaces (same as Договорувач)
                "Adresa" => CleanAdresaText(cleaned), // Special cleaning for addresses
                _ => cleaned.Trim()
            };
        }

        /// <summary>
        /// Special cleaning for Mesto field to extract city name and filter out labels
        /// </summary>
        private string CleanMestoText(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return "";
                
            // Remove common labels that might be extracted instead of the actual city
            var labelsToRemove = new[] { "МЕСТО", "МЕСТО:", "Место", "Место:", "место", "место:" };
            var cleaned = text.Trim();
            
            foreach (var label in labelsToRemove)
            {
                cleaned = cleaned.Replace(label, "").Trim();
            }
            
            // Keep only letters and spaces
            cleaned = Regex.Replace(cleaned, @"[^\p{L}\s]", "").Trim();
            
            // List of common Macedonian cities for validation
            var commonCities = new[] { "СКОПЈЕ", "БИТОЛА", "ПРИЛEP", "ТЕТОВО", "ВЕЛЕС", "ШТИП", "ОХРИД", "ГОСТИВАР", "СТРУМИЦА", "КАВАДАРЦИ" };
            
            // If we find a common city name, return it
            foreach (var city in commonCities)
            {
                if (cleaned.Contains(city))
                    return city;
            }
            
            return cleaned;
        }

        /// <summary>
        /// Clean extracted address text by removing common labels and artifacts
        /// </summary>
        private string CleanAdresaText(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return "";
                
            // Remove common labels that might be extracted instead of the actual address
            var labelsToRemove = new[] { "АДРЕСА", "АДРЕСА:", "Адреса", "Адреса:", "адреса", "адреса:" };
            var cleaned = text.Trim();
            
            foreach (var label in labelsToRemove)
            {
                cleaned = cleaned.Replace(label, "").Trim();
            }
            
            // Keep letters, spaces, numbers, and common address characters (hyphens, slashes, commas)
            cleaned = Regex.Replace(cleaned, @"[^\p{L}\s\d\-\/,]", "").Trim();
            
            // Normalize whitespace
            cleaned = Regex.Replace(cleaned, @"\s+", " ").Trim();
            
            return cleaned;
        }

        /// <summary>
        /// Extract region-specific text from full OCR text (fallback method)
        /// </summary>
        private string ExtractRegionFromFullText(string fullText, string regionName)
        {
            return regionName switch
            {
                "PolicyNumber" => ExtractPolicyNumber(fullText) ?? "",
                "EMBG_ME" => ExtractEmbgMe(fullText) ?? "",
                "Договорувач" => ExtractInsuredName(fullText) ?? "",
                "Registracija" => ExtractRegistracija(fullText) ?? "",
                "Mesto" => ExtractMesto(fullText) ?? "",
                "Осигуреник" => ExtractInsuredName(fullText) ?? "", // Use same logic as Договорувач
                "Adresa" => ExtractAdresa(fullText) ?? "",
                _ => ""
            };
        }

        /// <summary>
        /// Extract insured person name from OCR text
        /// </summary>
        private string? ExtractOsigurenik(string text)
        {
            try
            {
                LogDebug($"ExtractOsigurenik: Starting extraction from text length: {text.Length}");
                
                // Pattern 1: Look for text after "Осигуреник:" or "Осигуреник"
                var osigurenikPattern1 = new Regex(@"Осигуреник\s*:?\s*([А-ЯЁЂЃЉЊЌЏ]{4,}\s+[А-ЯЁЂЃЉЊЌЏ]{4,})", RegexOptions.IgnoreCase);
                var match1 = osigurenikPattern1.Match(text);
                if (match1.Success)
                {
                    var name = match1.Groups[1].Value.Trim();
                    LogDebug($"ExtractOsigurenik Pattern 1 found: '{name}'");
                    if (!IsAgentOrCompanyName(name) && !IsCommonWord(name))
                    {
                        LogDebug($"ExtractOsigurenik Pattern 1 result: '{name.ToUpper()}'");
                        return name.ToUpper();
                    }
                    else
                    {
                        LogDebug($"ExtractOsigurenik Pattern 1 rejected (agent/company/common): '{name}'");
                    }
                }
                
                // Pattern 2: Look for names in lines containing "Осигуреник"
                var lines = text.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                LogDebug($"ExtractOsigurenik: Checking {lines.Length} lines");
                
                for (int i = 0; i < lines.Length; i++)
                {
                    var line = lines[i].Trim();
                    
                    if (line.Contains("Осигуреник", StringComparison.OrdinalIgnoreCase))
                    {
                        LogDebug($"ExtractOsigurenik: Found 'Осигуреник' in line {i}: '{line}'");
                        // Look in surrounding lines for names
                        for (int j = Math.Max(0, i - 2); j <= Math.Min(lines.Length - 1, i + 3); j++)
                        {
                            if (j == i) continue;
                            
                            var surroundingLine = lines[j].Trim();
                            LogDebug($"ExtractOsigurenik: Checking surrounding line {j}: '{surroundingLine}'");
                            var nameMatch = new Regex(@"\b([А-ЯЁЂЃЉЊЌЏ]{4,}\s+[А-ЯЁЂЃЉЊЌЏ]{4,})\b").Match(surroundingLine);
                            
                            if (nameMatch.Success)
                            {
                                var name = nameMatch.Groups[1].Value.Trim();
                                LogDebug($"ExtractOsigurenik Pattern 2 found name: '{name}'");
                                if (!IsAgentOrCompanyName(name) && !IsCommonWord(name))
                                {
                                    LogDebug($"ExtractOsigurenik Pattern 2 result: '{name.ToUpper()}'");
                                    return name.ToUpper();
                                }
                                else
                                {
                                    LogDebug($"ExtractOsigurenik Pattern 2 rejected (agent/company/common): '{name}'");
                                }
                            }
                        }
                    }
                }
                
                // Pattern 3: Since often Osigurenik is the same as Договорувач, let's try to find the name from Договорувач
                // This is a common case in insurance documents
                LogDebug("ExtractOsigurenik: Trying to extract from Договорувач field");
                var dogovoruvacPattern = new Regex(@"Договорувач\s*:?\s*([А-ЯЁЂЃЉЊЌЏ]{4,}\s+[А-ЯЁЂЃЉЊЌЏ]{4,})", RegexOptions.IgnoreCase);
                var dogovoruvacMatch = dogovoruvacPattern.Match(text);
                if (dogovoruvacMatch.Success)
                {
                    var name = dogovoruvacMatch.Groups[1].Value.Trim();
                    LogDebug($"ExtractOsigurenik Pattern 3 (from Договорувач) found: '{name}'");
                    if (!IsAgentOrCompanyName(name) && !IsCommonWord(name))
                    {
                        LogDebug($"ExtractOsigurenik Pattern 3 result: '{name.ToUpper()}'");
                        return name.ToUpper();
                    }
                }
                
                // Pattern 4: Look for any valid personal name that's different from Договорувач
                // This helps distinguish between the two name fields
                var allNameMatches = new Regex(@"\b([А-ЯЁЂЃЉЊЌЏ]{4,}\s+[А-ЯЁЂЃЉЊЌЏ]{4,})\b").Matches(text);
                var foundNames = new List<string>();
                
                LogDebug($"ExtractOsigurenik: Found {allNameMatches.Count} potential name matches");
                foreach (Match nameMatch in allNameMatches)
                {
                    var name = nameMatch.Groups[1].Value.Trim();
                    LogDebug($"ExtractOsigurenik Pattern 4 checking: '{name}'");
                    if (!IsAgentOrCompanyName(name) && !IsCommonWord(name))
                    {
                        foundNames.Add(name.ToUpper());
                        LogDebug($"ExtractOsigurenik Pattern 4 added to list: '{name.ToUpper()}'");
                    }
                    else
                    {
                        LogDebug($"ExtractOsigurenik Pattern 4 rejected: '{name}'");
                    }
                }
                
                LogDebug($"ExtractOsigurenik: Found {foundNames.Count} valid names");
                // If we have multiple names, try to pick the one that's likely the Осигуреник
                // (often the second name found, as Договорувач usually appears first)
                if (foundNames.Count > 1)
                {
                    // Return the second unique name found
                    var uniqueNames = foundNames.Distinct().ToList();
                    LogDebug($"ExtractOsigurenik: {uniqueNames.Count} unique names found");
                    if (uniqueNames.Count > 1)
                    {
                        LogDebug($"ExtractOsigurenik Pattern 4 result (second name): '{uniqueNames[1]}'");
                        return uniqueNames[1];
                    }
                }
                else if (foundNames.Count == 1)
                {
                    LogDebug($"ExtractOsigurenik Pattern 4 result (only name): '{foundNames[0]}'");
                    return foundNames[0];
                }
                
                LogDebug("ExtractOsigurenik: No valid name found");
                return null;
            }
            catch (Exception ex)
            {
                LogDebug($"ExtractOsigurenik error: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error extracting osigurenik: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Extract place/city name from OCR text
        /// </summary>
        private string? ExtractMesto(string text)
        {
            try
            {
                // Pattern 1: Look for text after "Место:" or "Место"
                var mestoPattern1 = new Regex(@"Место\s*:?\s*([А-ЯЁЂЃЉЊЌЏ]+)", RegexOptions.IgnoreCase);
                var match1 = mestoPattern1.Match(text);
                if (match1.Success)
                {
                    var cityName = match1.Groups[1].Value.Trim().ToUpper();
                    // Make sure it's not just the label "МЕСТО"
                    if (cityName != "МЕСТО")
                    {
                        return cityName;
                    }
                }
                
                // Pattern 2: Look for common Macedonian city names
                var commonCities = new[] { "СКОПЈЕ", "БИТОЛА", "ПРИЛЕП", "ТЕТОВО", "ВЕЛЕС", "ШТИП", "ОХРИД", "КУМАНОВО", "ГОСТИВАР", "СТРУМИЦА" };
                foreach (var city in commonCities)
                {
                    if (text.Contains(city, StringComparison.OrdinalIgnoreCase))
                    {
                        return city;
                    }
                }
                
                // Pattern 3: Look for text near "Адреса" (address) field
                var lines = text.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                for (int i = 0; i < lines.Length; i++)
                {
                    if (lines[i].Contains("Адреса", StringComparison.OrdinalIgnoreCase))
                    {
                        // Look in the same line or nearby lines for city names
                        for (int j = Math.Max(0, i - 2); j <= Math.Min(lines.Length - 1, i + 2); j++)
                        {
                            var line = lines[j];
                            foreach (var city in commonCities)
                            {
                                if (line.Contains(city, StringComparison.OrdinalIgnoreCase))
                                {
                                    return city;
                                }
                            }
                        }
                    }
                }
                
                // Pattern 4: Extract any Macedonian word that looks like a city name
                var cityPattern = new Regex(@"\b([А-ЯЁЂЃЉЊЌЏ]{4,})\b");
                var cityMatches = cityPattern.Matches(text);
                
                foreach (Match match in cityMatches)
                {
                    var potentialCity = match.Groups[1].Value;
                    // Filter out common words that are not cities, including the label "МЕСТО"
                    if (!IsCommonNonCityWord(potentialCity) && potentialCity != "МЕСТО")
                    {
                        return potentialCity;
                    }
                }
                
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting mesto: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Extract address from OCR text
        /// </summary>
        private string? ExtractAdresa(string text)
        {
            try
            {
                LogDebug($"ExtractAdresa: Starting address extraction from text length: {text.Length}");
                
                // Pattern 1: Look for text after "Адреса:" or "Адреса"
                var adresaPattern1 = new Regex(@"Адреса\s*:?\s*([А-ЯЁЂЃЉЊЌЏ\s\d\-\/,]+)", RegexOptions.IgnoreCase);
                var match1 = adresaPattern1.Match(text);
                if (match1.Success)
                {
                    var address = match1.Groups[1].Value.Trim();
                    LogDebug($"ExtractAdresa Pattern 1 found: '{address}'");
                    // Make sure it's not just the label "АДРЕСА"
                    if (address.ToUpper() != "АДРЕСА" && address.Length > 3)
                    {
                        LogDebug($"ExtractAdresa Pattern 1 result: '{address}'");
                        return address;
                    }
                }
                
                // Pattern 2: Look for addresses starting with numbers like "8ми март 20/2-3"
                var numberStartAddressPattern = new Regex(@"(\d+[а-яА-ЯЁЂЃЉЊЌЏ]+\s+[А-ЯЁЂЃЉЊЌЏ]+\s+\d+[\/\-]\d+[\-\d]*)", RegexOptions.IgnoreCase);
                var numberStartMatches = numberStartAddressPattern.Matches(text);
                foreach (Match match in numberStartMatches)
                {
                    var address = match.Groups[1].Value.Trim();
                    LogDebug($"ExtractAdresa Pattern 2 (number start) found: '{address}'");
                    if (!IsCommonNonAddressWord(address))
                    {
                        LogDebug($"ExtractAdresa Pattern 2 result: '{address}'");
                        return address;
                    }
                }
                
                // Pattern 2b: Look for general address patterns like "АНКАРСКА 23/21"
                var generalAddressPattern = new Regex(@"\b([А-ЯЁЂЃЉЊЌЏ]+\s+\d+[\/\-]\d+[\-\d]*)\b", RegexOptions.IgnoreCase);
                var generalMatches = generalAddressPattern.Matches(text);
                foreach (Match match in generalMatches)
                {
                    var address = match.Groups[1].Value.Trim();
                    LogDebug($"ExtractAdresa Pattern 2b (general) found: '{address}'");
                    if (!IsCommonNonAddressWord(address))
                    {
                        LogDebug($"ExtractAdresa Pattern 2b result: '{address}'");
                        return address;
                    }
                }
                
                // Pattern 3: Look for addresses in lines containing "Адреса"
                var lines = text.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                LogDebug($"ExtractAdresa: Checking {lines.Length} lines for address patterns");
                
                for (int i = 0; i < lines.Length; i++)
                {
                    var line = lines[i].Trim();
                    LogDebug($"ExtractAdresa Line {i}: '{line}'");
                    
                    if (line.Contains("Адреса", StringComparison.OrdinalIgnoreCase))
                    {
                        LogDebug($"ExtractAdresa: Found 'Адреса' in line {i}");
                        // Look in the same line for address after the label
                        var addressInLine = line.Replace("Адреса:", "").Replace("Адреса", "").Trim();
                        if (!string.IsNullOrEmpty(addressInLine) && addressInLine.Length > 3)
                        {
                            LogDebug($"ExtractAdresa Pattern 3a result: '{addressInLine}'");
                            return addressInLine;
                        }
                        
                        // Look in surrounding lines for address
                        for (int j = Math.Max(0, i - 1); j <= Math.Min(lines.Length - 1, i + 2); j++)
                        {
                            if (j == i) continue;
                            
                            var surroundingLine = lines[j].Trim();
                            LogDebug($"ExtractAdresa: Checking surrounding line {j}: '{surroundingLine}'");
                            // Look for lines that contain typical address patterns
                            if (surroundingLine.Length > 5 && 
                                (surroundingLine.Contains("бр") || surroundingLine.Contains("ул") || 
                                 Regex.IsMatch(surroundingLine, @"\d+") || surroundingLine.Contains("/")))
                            {
                                LogDebug($"ExtractAdresa Pattern 3b result: '{surroundingLine}'");
                                return surroundingLine;
                            }
                        }
                    }
                }
                
                // Pattern 4: Look for typical address patterns (street names with numbers)
                var addressPattern = new Regex(@"\b(\d*[а-яА-ЯЁЂЃЉЊЌЏ]*\s*[А-ЯЁЂЃЉЊЌЏ]+\s+\d+[А-ЯЁЂЃЉЊЌЏ\d\-\/]*)\b", RegexOptions.IgnoreCase);
                var addressMatches = addressPattern.Matches(text);
                
                LogDebug($"ExtractAdresa: Found {addressMatches.Count} potential address matches");
                foreach (Match match in addressMatches)
                {
                    var potentialAddress = match.Groups[1].Value.Trim();
                    LogDebug($"ExtractAdresa Pattern 4 checking: '{potentialAddress}'");
                    if (potentialAddress.Length > 5 && !IsCommonNonAddressWord(potentialAddress))
                    {
                        LogDebug($"ExtractAdresa Pattern 4 result: '{potentialAddress}'");
                        return potentialAddress;
                    }
                }
                
                // Pattern 5: Look for month names with numbers (like "8ми март 20/2-3")
                // Remove word boundaries to catch numbers at the start
                var monthAddressPattern = new Regex(@"(\d+[а-яА-ЯЁЂЃЉЊЌЏ]+\s+(?:јануари|февруари|март|април|мај|јуни|јули|август|септември|октомври|ноември|декември)\s+\d+[\/\-\d]*)", RegexOptions.IgnoreCase);
                var monthMatches = monthAddressPattern.Matches(text);
                
                LogDebug($"ExtractAdresa: Found {monthMatches.Count} month-based address matches");
                foreach (Match match in monthMatches)
                {
                    var monthAddress = match.Groups[1].Value.Trim();
                    LogDebug($"ExtractAdresa Pattern 5 (month) checking: '{monthAddress}'");
                    if (monthAddress.Length > 5)
                    {
                        LogDebug($"ExtractAdresa Pattern 5 result: '{monthAddress}'");
                        return monthAddress;
                    }
                }
                
                // Pattern 6: More aggressive pattern to catch partial matches and expand them
                // Look for "ми март" and try to find the number before it
                var partialMonthPattern = new Regex(@"([а-яА-ЯЁЂЃЉЊЌЏ]*\s*(?:јануари|февруари|март|април|мај|јуни|јули|август|септември|октомври|ноември|декември)\s+\d+[\/\-\d]*)", RegexOptions.IgnoreCase);
                var partialMatches = partialMonthPattern.Matches(text);
                
                LogDebug($"ExtractAdresa: Found {partialMatches.Count} partial month matches");
                foreach (Match match in partialMatches)
                {
                    var partialAddress = match.Groups[1].Value.Trim();
                    LogDebug($"ExtractAdresa Pattern 6 (partial month) found: '{partialAddress}'");
                    
                    // Try to find a number before this match in the text
                    var matchIndex = match.Index;
                    var beforeText = text.Substring(Math.Max(0, matchIndex - 10), Math.Min(10, matchIndex));
                    var numberBeforePattern = new Regex(@"(\d+[а-яА-ЯЁЂЃЉЊЌЏ]*)$");
                    var numberMatch = numberBeforePattern.Match(beforeText);
                    
                    if (numberMatch.Success)
                    {
                        var fullAddress = (numberMatch.Groups[1].Value + " " + partialAddress).Trim();
                        LogDebug($"ExtractAdresa Pattern 6 expanded to: '{fullAddress}'");
                        if (fullAddress.Length > 5)
                        {
                            LogDebug($"ExtractAdresa Pattern 6 result: '{fullAddress}'");
                            return fullAddress;
                        }
                    }
                    else if (partialAddress.Length > 5)
                    {
                        LogDebug($"ExtractAdresa Pattern 6 result (as-is): '{partialAddress}'");
                        return partialAddress;
                    }
                }
                
                LogDebug("ExtractAdresa: No address found");
                return null;
            }
            catch (Exception ex)
            {
                LogDebug($"ExtractAdresa error: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error extracting adresa: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Check if a word is a common non-address word that should be filtered out
        /// </summary>
        private bool IsCommonNonAddressWord(string word)
        {
            var commonWords = new[] { 
                "ДОГОВОРУВАЧ", "ОСИГУРЕНИК", "ЕМБГ", "МЕСТО", "МАКЕДОНИЈА", "СЕВЕРНА", "РЕПУБЛИКА",
                "ПОЛИСА", "ОСИГУРУВАЊЕ", "АВТОМОБИЛСКО", "ОДГОВОРНОСТ", "ДРУШТВО", "КОМПАНИЈА", "ДАТУМ",
                "ВОЗИЛА", "МОЌНОСТ", "МАРКА", "МОДЕЛ", "ОСНОВНА", "ПРЕМИЈА", "БОНУС", "ПОПУСТ", "КАСКО"
            };
            
            return commonWords.Any(cw => word.ToUpper().Contains(cw));
        }

        /// <summary>
        /// Check if a word is a common non-city word that should be filtered out
        /// </summary>
        private bool IsCommonNonCityWord(string word)
        {
            var commonWords = new[] { 
                "ДОГОВОРУВАЧ", "ОСИГУРЕНИК", "ЕМБГ", "АДРЕСА", "МЕСТО", "МАКЕДОНИЈА", "СЕВЕРНА", "РЕПУБЛИКА",
                "ПОЛИСА", "ОСИГУРУВАЊЕ", "АВТОМОБИЛСКО", "ОДГОВОРНОСТ", "ДРУШТВО", "КОМПАНИЈА", "ДАТУМ",
                "ВОЗИЛА", "МОЌНОСТ", "МАРКА", "МОДЕЛ", "ОСНОВНА", "ПРЕМИЈА", "БОНУС", "ПОПУСТ", "КАСКО"
            };
            
            return commonWords.Contains(word.ToUpper());
        }

        /// <summary>
        /// Extract vehicle registration number from OCR text
        /// </summary>
        private string? ExtractRegistracija(string text)
        {
            try
            {
                // Pattern 1: Look for registration pattern like "SK-004-LK", "BT-123-AB", etc.
                var registrationPattern1 = new Regex(@"\b([A-Z]{2}-\d{3}-[A-Z]{2})\b", RegexOptions.IgnoreCase);
                var match1 = registrationPattern1.Match(text);
                if (match1.Success)
                {
                    return match1.Groups[1].Value.ToUpper();
                }
                
                // Pattern 2: Look for other common registration formats
                var registrationPattern2 = new Regex(@"\b([A-Z]{1,3}\s*-?\s*\d{3,4}\s*-?\s*[A-Z]{1,3})\b", RegexOptions.IgnoreCase);
                var match2 = registrationPattern2.Match(text);
                if (match2.Success)
                {
                    var registration = match2.Groups[1].Value.ToUpper();
                    // Clean up spacing and normalize format
                    registration = Regex.Replace(registration, @"\s+", "");
                    return registration;
                }
                
                // Pattern 3: Look for text near "Reg" or registration-related keywords
                var lines = text.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                foreach (var line in lines)
                {
                    if (line.Contains("Reg") || line.Contains("регистрација", StringComparison.OrdinalIgnoreCase))
                    {
                        // Look for registration pattern in this line or nearby lines
                        var regMatch = new Regex(@"\b([A-Z]{2}-\d{3}-[A-Z]{2})\b", RegexOptions.IgnoreCase).Match(line);
                        if (regMatch.Success)
                        {
                            return regMatch.Groups[1].Value.ToUpper();
                        }
                    }
                }
                
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting registration: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Build coordinate extraction results text
        /// </summary>
        private string BuildCoordinateExtractionResults(Dictionary<string, CoordinateExtractionResult> results)
        {
            var sb = new StringBuilder();
            sb.AppendLine("=== COORDINATE-BASED EXTRACTION RESULTS ===");
            sb.AppendLine();
            
            foreach (var result in results.Values)
            {
                sb.AppendLine($"📍 {result.RegionDescription}:");
                if (!string.IsNullOrEmpty(result.ExtractedText))
                {
                    sb.AppendLine($"   ✅ {result.ExtractedText}");
                    sb.AppendLine($"   🎯 Confidence: {result.Confidence:F1}%");
                }
                else if (!string.IsNullOrEmpty(result.ErrorMessage))
                {
                    sb.AppendLine($"   ❌ Error: {result.ErrorMessage}");
                }
                else
                {
                    sb.AppendLine($"   ⚠️ No text found");
                }
                sb.AppendLine($"   📐 {result.CoordinateInfo}");
                sb.AppendLine();
            }
            
            return sb.ToString();
        }

        /// <summary>
        /// Format coordinate results for UI display
        /// </summary>
        private string FormatCoordinateResultsForDisplay(Dictionary<string, CoordinateExtractionResult> results)
        {
            var sb = new StringBuilder();
            
            foreach (var result in results.Values)
            {
                sb.AppendLine($"{result.RegionDescription}: {result.ExtractedText ?? "N/A"}");
            }
            
            return sb.ToString();
        }

        #endregion
    }

    public class OCRResult
    {
        public string Language { get; set; } = "";
        public string Text { get; set; } = "";
        public double Confidence { get; set; }
        public List<string> Words { get; set; } = new List<string>();
    }

    /// <summary>
    /// Represents a document region with coordinates
    /// </summary>
    public class DocumentRegion
    {
        public string Name { get; set; } = "";
        public int X { get; set; }
        public int Y { get; set; }
        public int Width { get; set; }
        public int Height { get; set; }
        public string Description { get; set; } = "";
    }

    /// <summary>
    /// Result of coordinate-based extraction from a specific region
    /// </summary>
    public class CoordinateExtractionResult
    {
        public string RegionName { get; set; } = "";
        public string ExtractedText { get; set; } = "";
        public double Confidence { get; set; }
        public string RegionDescription { get; set; } = "";
        public string CoordinateInfo { get; set; } = "";
        public string? ErrorMessage { get; set; }
    }
}
