using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using System.Web;
using Renci.SshNet;
using System.IO;

namespace NextBroker.Pages.Finansii
{
    public class GenFakturiProvizijaPoSpecifikacijaOdOsiguritelModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public GenFakturiProvizijaPoSpecifikacijaOdOsiguritelModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        [BindProperty]
        public InputModel Input { get; set; } = new();

        public IEnumerable<SelectListItem> Osiguriteli { get; set; }
        public IEnumerable<SelectListItem> KlasiOsiguruvanje { get; set; }
        public bool ShowPreview { get; set; }
        public string OsiguritelNaziv { get; set; }
        public int VkupnoIznos { get; set; }
        public string VkupnoIznosVoZborovi { get; set; }
        public string FakturiralIme { get; set; }

        public class FakturaStavka
        {
            public int RedenBroj { get; set; }

            [Required(ErrorMessage = "Класа е задолжително поле")]
            [Display(Name = "Класа број и опис")]
            public string KlasaBrojSoIme { get; set; }

            [Required(ErrorMessage = "Износ е задолжително поле")]
            [Display(Name = "Износ во МКД")]
            [Range(0, int.MaxValue, ErrorMessage = "Износот мора да биде позитивен број")]
            public int IznosVoMKD { get; set; }
        }

        public class InputModel
        {
            [Required(ErrorMessage = "Осигурител е задолжително поле")]
            [Display(Name = "Осигурител")]
            public long KlientiIdOsiguritel { get; set; }

            [Required(ErrorMessage = "Број на фактура е задолжително поле")]
            [Display(Name = "Број на фактура")]
            public string BrojNaFaktura { get; set; }

            [Required(ErrorMessage = "Датум на фактура е задолжително поле")]
            [Display(Name = "Датум на фактура")]
            public DateTime DatumNaFaktura { get; set; } = DateTime.Today;

            [Required(ErrorMessage = "Рок на плаќање е задолжително поле")]
            [Display(Name = "Рок на плаќање")]
            public DateTime RokNaPlakanje { get; set; } = DateTime.Today.AddDays(30);

            [Required(ErrorMessage = "Внесете барем една ставка")]
            public List<FakturaStavka> Stavki { get; set; } = new List<FakturaStavka>();
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("GenFakturiProvizijaPoSpecifikacijaOdOsiguritel"))
            {
                return RedirectToAccessDenied();
            }

            await LoadOsiguriteli();
            await LoadKlasiOsiguruvanje();
            await GenerateDefaultBrojNaFaktura();
            return Page();
        }

        private async Task LoadOsiguriteli()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Naziv 
                    FROM Klienti 
                    WHERE Osiguritel = 1 
                    ORDER BY Naziv", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Naziv"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Osiguriteli = items;
                }
            }
        }

        private async Task LoadKlasiOsiguruvanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT 
                        CAST(KlasaBroj AS VARCHAR) + ' ' + CAST(KlasaIme AS VARCHAR) AS KlasaBrojSoIme
                    FROM 
                        KlasiOsiguruvanje
                    ORDER BY 
                        KlasaBroj", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        var value = reader["KlasaBrojSoIme"].ToString();
                        items.Add(new SelectListItem(value, value));
                    }
                    KlasiOsiguruvanje = items;
                }
            }
        }

        private async Task GenerateDefaultBrojNaFaktura()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // Get the highest existing invoice number from both tables and increment it
                using (var command = new SqlCommand(@"
                    SELECT TOP 1 InvoiceNumber
                    FROM (
                        SELECT BrojNaFaktura as InvoiceNumber 
                        FROM PolisiInvoiceHistory 
                        WHERE BrojNaFaktura IS NOT NULL AND BrojNaFaktura != ''
                        UNION ALL
                        SELECT BrojNaFaktura as InvoiceNumber 
                        FROM FakturiZaProvizijaKonOsiguritel 
                        WHERE BrojNaFaktura IS NOT NULL AND BrojNaFaktura != ''
                    ) AS AllInvoices
                    WHERE InvoiceNumber LIKE 'O.K %'
                    ORDER BY InvoiceNumber DESC", connection))
                {
                    var lastInvoiceNumber = await command.ExecuteScalarAsync() as string;
                    var twoDigitYear = DateTime.Now.Year % 100; // Gets last 2 digits of year
                    
                    int nextNumber = 1; // Default to 1 if no previous invoices found
                    
                    if (!string.IsNullOrEmpty(lastInvoiceNumber))
                    {
                        // Parse the number from format "O.K XXXX/YY"
                        // Expected format: "O.K 0011/25"
                        try
                        {
                            var parts = lastInvoiceNumber.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                            if (parts.Length >= 2 && parts[0] == "O.K")
                            {
                                var numberPart = parts[1].Split('/')[0]; // Get "0011" part
                                if (int.TryParse(numberPart, out int currentNumber))
                                {
                                    nextNumber = currentNumber + 1;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            // Log the error but continue with default next number
                            System.Diagnostics.Debug.WriteLine($"Error parsing invoice number '{lastInvoiceNumber}': {ex.Message}");
                        }
                    }
                    
                    Input.BrojNaFaktura = $"O.K {nextNumber.ToString("0000")}/{twoDigitYear}";
                }
            }
        }

        public async Task<IActionResult> OnPost()
        {
            if (!await HasPageAccess("GenFakturiProvizijaPoSpecifikacijaOdOsiguritel"))
            {
                return RedirectToAccessDenied();
            }

            if (!ModelState.IsValid)
            {
                await LoadOsiguriteli();
                await LoadKlasiOsiguruvanje();
                return Page();
            }

            // Calculate total
            VkupnoIznos = Input.Stavki.Sum(s => s.IznosVoMKD);

            // Load Osiguritel name for preview
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                
                // Get Osiguritel name
                using (SqlCommand cmd = new SqlCommand("SELECT Naziv FROM Klienti WHERE Id = @Id", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", Input.KlientiIdOsiguritel);
                    OsiguritelNaziv = (string)await cmd.ExecuteScalarAsync();
                }

                // Get amount in words
                using (var cmdText = new SqlCommand("SELECT dbo.fn_BrojVoZborovi(@Number)", connection))
                {
                    cmdText.Parameters.AddWithValue("@Number", VkupnoIznos);
                    VkupnoIznosVoZborovi = (await cmdText.ExecuteScalarAsync())?.ToString();
                }

                // Get the username from session and get the full name
                var username = HttpContext.Session.GetString("Username");
                if (!string.IsNullOrEmpty(username))
                {
                    using (var command = new SqlCommand("SELECT dbo.VratiImePrezimePoUsername(@Username)", connection))
                    {
                        command.Parameters.AddWithValue("@Username", username);
                        FakturiralIme = (await command.ExecuteScalarAsync())?.ToString();
                    }
                }
            }

            ShowPreview = true;
            await LoadOsiguriteli();
            await LoadKlasiOsiguruvanje();
            return Page();
        }



        [HttpPost]
        public async Task<IActionResult> OnPostSaveInvoiceDataAsync([FromBody] SaveInvoiceRequest request)
        {
            if (!await HasPageAccess("GenFakturiProvizijaPoSpecifikacijaOdOsiguritel"))
            {
                return new JsonResult(new { success = false, message = "Access denied" });
            }

            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    
                    // Ensure proper encoding for Cyrillic characters
                    var fakturaDo = System.Web.HttpUtility.HtmlDecode(request.FakturaDo);
                    
                    using (var command = new SqlCommand(@"
                        INSERT INTO FakturiZaProvizijaKonOsiguritel 
                        (UsernameCreated, BrojNaFaktura, FakturaDo, DatumNaFaktura, RokNaPlakanje, Iznos)
                        VALUES 
                        (@UsernameCreated, @BrojNaFaktura, @FakturaDo, @DatumNaFaktura, @RokNaPlakanje, @Iznos)", 
                        connection))
                    {
                        command.Parameters.AddWithValue("@UsernameCreated", HttpContext.Session.GetString("Username"));
                        command.Parameters.AddWithValue("@BrojNaFaktura", request.BrojNaFaktura);
                        command.Parameters.AddWithValue("@FakturaDo", fakturaDo);
                        command.Parameters.AddWithValue("@DatumNaFaktura", request.DatumNaFaktura);
                        command.Parameters.AddWithValue("@RokNaPlakanje", request.RokNaPlakanje);
                        command.Parameters.AddWithValue("@Iznos", request.Iznos);

                        await command.ExecuteNonQueryAsync();
                        
                        return new JsonResult(new { success = true });
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        // Method to upload a file to SFTP server
        private async Task<(string filePath, string fileName)> UploadPdfToSftp(byte[] pdfData, string originalFileName)
        {
            System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Starting SFTP upload for file: {originalFileName}");
            System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] PDF data size: {pdfData.Length} bytes");
            
            try
            {
                var sftpConfig = _configuration.GetSection("SftpConfig");
                var host = sftpConfig["Host"];
                var port = int.Parse(sftpConfig["Port"]);
                var username = sftpConfig["Username"];
                var password = sftpConfig["Password"];
                
                if (string.IsNullOrEmpty(host) || string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    throw new Exception("SFTP configuration is incomplete. Please check the configuration settings.");
                }
                
                System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Connection details - Host: {host}, Port: {port}, Username: {username}");
                
                string fileName = $"{DateTime.Now:yyyyMMddHHmmss}_{originalFileName}";
                string remotePath = "/upload/fakturiprovizija";
                string remoteFilePath = $"{remotePath}/{fileName}";
                
                System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Remote file path: {remoteFilePath}");
                
                using (var client = new SftpClient(host, port, username, password))
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine("[SFTP_DEBUG] Connecting to SFTP server...");
                        client.Connect();
                        System.Diagnostics.Debug.WriteLine("[SFTP_DEBUG] Connected successfully to SFTP server");
                        
                        // Create parent directory if it doesn't exist
                        string parentPath = "/upload";
                        if (!client.Exists(parentPath))
                        {
                            System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Parent directory doesn't exist, creating: {parentPath}");
                            client.CreateDirectory(parentPath);
                        }
                        
                        // Create target directory if it doesn't exist
                        if (!client.Exists(remotePath))
                        {
                            System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Directory doesn't exist, creating: {remotePath}");
                            client.CreateDirectory(remotePath);
                            System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Directory created successfully");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Directory already exists");
                            // List directory contents for debugging
                            var files = client.ListDirectory(remotePath);
                            System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Directory contents: {string.Join(", ", files.Select(f => f.Name))}");
                        }
                        
                        // Upload the file
                        System.Diagnostics.Debug.WriteLine("[SFTP_DEBUG] Starting file upload...");
                        using (var ms = new MemoryStream(pdfData))
                        {
                            try
                            {
                                client.BufferSize = 4 * 1024; // Use smaller buffer size (4KB)
                                client.UploadFile(ms, remoteFilePath, true); // true to overwrite if exists
                                System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] File uploaded successfully to {remoteFilePath}");
                                
                                // Verify the file exists after upload
                                if (client.Exists(remoteFilePath))
                                {
                                    var fileInfo = client.GetAttributes(remoteFilePath);
                                    System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Uploaded file size: {fileInfo.Size} bytes");
                                }
                                else
                                {
                                    throw new Exception("File was not found after upload");
                                }
                            }
                            catch (Exception uploadEx)
                            {
                                throw new Exception($"Error during file upload: {uploadEx.Message}", uploadEx);
                            }
                        }
                    }
                    finally
                    {
                        if (client.IsConnected)
                        {
                            client.Disconnect();
                            System.Diagnostics.Debug.WriteLine("[SFTP_DEBUG] Disconnected from SFTP server");
                        }
                    }
                }
                
                System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] SFTP upload completed successfully");
                return (remoteFilePath, fileName);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] ERROR: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Inner exception: {ex.InnerException.Message}");
                    System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Inner exception stack trace: {ex.InnerException.StackTrace}");
                }
                throw;
            }
        }

        // Method to update the database with file path and name
        private async Task UpdateInvoiceWithFileInfo(SqlConnection connection, string brojNaFaktura, string filePath, string fileName)
        {
            using (var updateCommand = new SqlCommand(@"
                UPDATE FakturiZaProvizijaKonOsiguritel 
                SET FilePath = @FilePath, FileName = @FileName
                WHERE BrojNaFaktura = @BrojNaFaktura", connection))
            {
                updateCommand.Parameters.AddWithValue("@FilePath", filePath);
                updateCommand.Parameters.AddWithValue("@FileName", fileName);
                updateCommand.Parameters.AddWithValue("@BrojNaFaktura", brojNaFaktura);
                
                await updateCommand.ExecuteNonQueryAsync();
            }
        }

        // API Endpoint to upload PDF to SFTP
        [HttpPost]
        [RequestSizeLimit(50 * 1024 * 1024)] // 50 MB limit
        [RequestFormLimits(MultipartBodyLengthLimit = 50 * 1024 * 1024)]
        public async Task<IActionResult> OnPostUploadPdfToSftpAsync([FromBody] UploadPdfRequest request)
        {
            var debugMessages = new List<(string Message, string Type)>();
            void AddDebug(string message, string type = "info") => debugMessages.Add((message, type));

            AddDebug($"Starting upload process", "info");
            
            try
            {
                // Validate request object
                if (request == null)
                {
                    AddDebug("Request object is null", "error");
                    return new JsonResult(new { success = false, message = "Invalid request", debugLog = debugMessages });
                }

                AddDebug($"Received request with invoice number: {request.BrojNaFaktura}", "info");
                AddDebug($"Filename: {request.FileName}", "info");
                
                // Validate required parameters
                if (string.IsNullOrWhiteSpace(request.BrojNaFaktura))
                {
                    AddDebug("Missing invoice number (brojNaFaktura)", "error");
                    return new JsonResult(new { success = false, message = "Invoice number is required", debugLog = debugMessages });
                }
                
                // Check if PDF data is empty
                if (string.IsNullOrWhiteSpace(request.PdfBase64))
                {
                    AddDebug("PDF data is empty or null", "error");
                    return new JsonResult(new { success = false, message = "PDF data is empty or invalid", debugLog = debugMessages });
                }
                
                // Log data length for debugging
                AddDebug($"Received base64 data length: {request.PdfBase64.Length} characters", "info");
                
                // Verify the data has a reasonable length
                if (request.PdfBase64.Length < 100)
                {
                    AddDebug("Base64 data is too short to be valid", "error");
                    return new JsonResult(new { success = false, message = "PDF data is too short to be valid", debugLog = debugMessages });
                }
                
                // Create a standardized filename
                string serverFileName = $"faktura_{request.BrojNaFaktura.Replace("/", "_")}_{DateTime.Now:yyyyMMddHHmmss}.pdf";
                AddDebug($"Generated server filename: {serverFileName}", "info");
                
                // Decode the base64 string to byte array
                byte[] pdfBytes;
                try
                {
                    string base64Data = request.PdfBase64;
                    // Strip data URL prefix if present
                    if (base64Data.StartsWith("data:"))
                    {
                        int commaIndex = base64Data.IndexOf(",");
                        if (commaIndex > 0)
                        {
                            base64Data = base64Data.Substring(commaIndex + 1);
                            AddDebug("Stripped data URL prefix from base64 string", "info");
                        }
                    }
                    
                    pdfBytes = Convert.FromBase64String(base64Data);
                    AddDebug($"Successfully decoded base64 data to {pdfBytes.Length} bytes", "success");
                }
                catch (Exception ex)
                {
                    AddDebug($"Failed to decode base64 data: {ex.Message}", "error");
                    return new JsonResult(new { success = false, message = $"Failed to decode PDF data: {ex.Message}", debugLog = debugMessages });
                }
                
                // Upload to SFTP
                try
                {
                    var sftpConfig = _configuration.GetSection("SftpConfig");
                    var host = sftpConfig["Host"];
                    var port = int.Parse(sftpConfig["Port"]);
                    var username = sftpConfig["Username"];
                    
                    AddDebug($"SFTP Configuration - Host: {host}, Port: {port}, Username: {username}", "info");
                    
                    if (string.IsNullOrEmpty(host) || string.IsNullOrEmpty(username))
                    {
                        AddDebug("SFTP configuration is incomplete", "error");
                        return new JsonResult(new { success = false, message = "SFTP configuration is incomplete", debugLog = debugMessages });
                    }

                    var (filePath, uploadedFileName) = await UploadPdfToSftp(pdfBytes, serverFileName);
                    AddDebug($"SFTP upload completed successfully to path: {filePath}", "success");
                    
                    // Update database with file info
                    using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                    {
                        AddDebug("Opening database connection to update file info", "info");
                        await connection.OpenAsync();
                        
                        try
                        {
                            await UpdateInvoiceWithFileInfo(connection, request.BrojNaFaktura, filePath, uploadedFileName);
                            AddDebug("Successfully updated database with file path and name", "success");
                        }
                        catch (Exception dbEx)
                        {
                            AddDebug($"Failed to update database: {dbEx.Message}", "error");
                            return new JsonResult(new { success = false, message = $"Failed to update database: {dbEx.Message}", debugLog = debugMessages });
                        }
                    }
                    
                    return new JsonResult(new { 
                        success = true, 
                        message = "PDF successfully uploaded to SFTP server and database updated",
                        filePath = filePath,
                        fileName = uploadedFileName,
                        debugLog = debugMessages
                    });
                }
                catch (Exception sftpEx)
                {
                    AddDebug($"SFTP upload error: {sftpEx.Message}", "error");
                    if (sftpEx.InnerException != null)
                    {
                        AddDebug($"Inner exception: {sftpEx.InnerException.Message}", "error");
                    }
                    return new JsonResult(new { success = false, message = $"Failed to upload PDF to SFTP: {sftpEx.Message}", debugLog = debugMessages });
                }
            }
            catch (Exception ex)
            {
                AddDebug($"Unhandled error: {ex.Message}", "error");
                if (ex.InnerException != null)
                {
                    AddDebug($"Inner exception: {ex.InnerException.Message}", "error");
                }
                return new JsonResult(new { success = false, message = $"Failed to upload PDF: {ex.Message}", debugLog = debugMessages });
            }
        }
    }

    public class SaveInvoiceRequest
    {
        public string BrojNaFaktura { get; set; }
        public string FakturaDo { get; set; }
        public DateTime DatumNaFaktura { get; set; }
        public DateTime RokNaPlakanje { get; set; }
        public decimal Iznos { get; set; }
    }

    public class UploadPdfRequest
    {
        public string BrojNaFaktura { get; set; }
        public string FileName { get; set; }
        public string PdfBase64 { get; set; }
    }
}
