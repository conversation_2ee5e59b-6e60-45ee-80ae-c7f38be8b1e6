using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Data;
using System.Runtime.Serialization;
using System.Reflection;

namespace NextBroker.Pages.Polisi
{
    public enum TipNaFakturaEnumKlasa2
    {
        [Display(Name = "Влезна фактура кон клиент")]
        [EnumMember(Value = "Влезна фактура кон клиент")]
        VleznaFakturaKonKlient,
        [Display(Name = "Влезна фактура кон брокер")]
        [EnumMember(Value = "Влезна фактура кон брокер")]
        VleznaFakturaKonBroker
    }

    [IgnoreAntiforgeryToken(Order = 1001)]
    public class DodajPolisaKlasa2Model : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public bool HasAdminAccess { get; private set; }

        public IEnumerable<SelectListItem> Osiguriteli { get; set; }
        public IEnumerable<SelectListItem> KlasiOsiguruvanje { get; set; }
        public IEnumerable<SelectListItem> Produkti { get; set; }
        public IEnumerable<SelectListItem> PriciniZaStorno { get; set; }
        public IEnumerable<SelectListItem> Valuti { get; set; }
        public IEnumerable<SelectListItem> NaciniNaPlakanje { get; set; }
        public IEnumerable<SelectListItem> TipoviNaPlakanje { get; set; }
        public IEnumerable<SelectListItem> Banki { get; set; }
        public IEnumerable<SelectListItem> ListaDejnosti { get; set; }
        public IEnumerable<SelectListItem> TipoviNaFaktura { get; set; }

        [BindProperty]
        public PolisaInputModel Input { get; set; } = new();

        public DodajPolisaKlasa2Model(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public async Task<IActionResult> OnGetAsync()
        {
            if (!await HasPageAccess("DodajPolisaKlasa2"))
            {
                return RedirectToAccessDenied();
            }

            // Check admin access
            HasAdminAccess = await HasPageAccess("DodajPolisaKlasa2Admin");

            await LoadOsiguriteli();
            await LoadKlasiOsiguruvanje();
            await LoadProdukti();
            await LoadPriciniZaStorno();
            await LoadValuti();
            await LoadNaciniNaPlakanje();
            await LoadTipoviNaPlakanje();
            await LoadBanki();
            await LoadListaDejnosti();
            LoadTipoviNaFaktura();

            return Page();
        }

        private async Task LoadOsiguriteli()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Naziv 
                    FROM Klienti 
                    WHERE Osiguritel = 1 
                    AND ZivotNezivot = N'Неживот'
                    AND DogovorVaziDo > Convert(date,GETDATE())
                    ORDER BY Naziv", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Naziv"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Osiguriteli = items;
                }
            }
        }

        private async Task LoadKlasiOsiguruvanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, CONCAT(KlasaBroj, ' - ', KlasaIme) as DisplayName 
                    FROM KlasiOsiguruvanje 
                    WHERE (Disabled = 0 OR Disabled IS NULL)
                    AND Id = '2'
                    ORDER BY KlasaBroj", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayName"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    KlasiOsiguruvanje = items;
                }
            }
        }

        private async Task LoadProdukti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Ime 
                    FROM Produkti 
                    WHERE KlasaOsiguruvanjeId = '2'
                    ORDER BY Ime", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Ime"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Produkti = items;
                }
            }
        }

        private async Task LoadPriciniZaStorno()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, PricinaZaStorno 
                    FROM SifrarnikPricinaZaStorno 
                    ORDER BY PricinaZaStorno", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["PricinaZaStorno"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    PriciniZaStorno = items;
                }
            }
        }

        private async Task LoadValuti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Valuta 
                    FROM SifrarnikValuti 
                    ORDER BY Valuta", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Valuta"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Valuti = items;
                }
            }
        }

        private async Task LoadNaciniNaPlakanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, NacinNaPlakanje 
                    FROM SifrarnikNacinNaPlakanje 
                    WHERE Id not in ('2','3','4')
                    ORDER BY NacinNaPlakanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["NacinNaPlakanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    NaciniNaPlakanje = items;
                }
            }
        }

        private async Task LoadTipoviNaPlakanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, TipNaPlakanje 
                    FROM SifrarnikTipNaPlakanje 
                    ORDER BY TipNaPlakanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["TipNaPlakanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    TipoviNaPlakanje = items;
                }
            }
        }

        private async Task LoadBanki()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Banka 
                    FROM SifrarnikBanki 
                    ORDER BY Banka", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Banka"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Banki = items;
                }
            }
        }

        private async Task LoadListaDejnosti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, CONCAT(SifraDejnost, ' - ', NazivDejnost) as DisplayName 
                    FROM ListaDejnosti 
                    ORDER BY SifraDejnost", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayName"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    ListaDejnosti = items;
                }
            }
        }

        private void LoadTipoviNaFaktura()
        {
            TipoviNaFaktura = Enum.GetValues(typeof(TipNaFakturaEnumKlasa2))
                .Cast<TipNaFakturaEnumKlasa2>()
                .Select(e => new SelectListItem
                {
                    Text = e.GetType()
                        .GetMember(e.ToString())
                        .First()
                        .GetCustomAttribute<DisplayAttribute>()
                        ?.Name ?? e.ToString(),
                    Value = e.GetType()
                        .GetMember(e.ToString())
                        .First()
                        .GetCustomAttribute<EnumMemberAttribute>()
                        ?.Value ?? e.ToString()
                })
                .ToList();
        }

        public class PolisaInputModel
        {
            [Required(ErrorMessage = "Осигурител е задолжително поле")]
            [Display(Name = "Осигурител")]
            public long KlientiIdOsiguritel { get; set; }

            [Required(ErrorMessage = "Класа на осигурување е задолжително поле")]
            [Display(Name = "Класа на осигурување")]
            public int KlasiOsiguruvanjeIdKlasa { get; set; }

            [Display(Name = "Продукт")]
            public int? ProduktiIdProizvod { get; set; }

            [Display(Name = "Број на полиса")]
            public string? BrojNaPolisa { get; set; }

            [Display(Name = "Колективна")]
            public bool Kolektivna { get; set; }

            [Required(ErrorMessage = "Договорувач е задолжително поле")]
            [Display(Name = "Договорувач")]
            public long KlientiIdDogovoruvac { get; set; }

            [Display(Name = "Осигуреник")]
            public long? KlientiIdOsigurenik { get; set; }

            [Display(Name = "Соработник")]
            public long? KlientiIdSorabotnik { get; set; }

            [Display(Name = "Факторинг")]
            public bool Faktoring { get; set; }

            [Display(Name = "Генерирана излезна фактура")]
            public bool GeneriranaFakturaIzlezna { get; set; }

            [Display(Name = "Сторно")]
            public bool Storno { get; set; }

            [Display(Name = "Причина за сторно")]
            public int? SifrarnikPricinaZaStornoId { get; set; }

            [Required(ErrorMessage = "Важи од е задолжително поле")]
            [Display(Name = "Важи од")]
            public DateTime? VaziOd { get; set; }

            [Required(ErrorMessage = "Важи до е задолжително поле")]
            [Display(Name = "Важи до")]
            public DateTime? VaziDo { get; set; }

            [Required(ErrorMessage = "Датум на издавање е задолжително поле")]
            [Display(Name = "Датум на издавање")]
            public DateTime? DatumNaIzdavanje { get; set; }

            [Required(ErrorMessage = "Валута е задолжително поле")]
            [Display(Name = "Валута")]
            public int ValutiId { get; set; }

            [Required(ErrorMessage = "Начин на плаќање е задолжително поле")]
            [Display(Name = "Начин на плаќање")]
            public int NaciniNaPlakanjeId { get; set; }

            [Required(ErrorMessage = "Тип на плаќање е задолжително поле")]
            [Display(Name = "Тип на плаќање")]
            public int TipoviNaPlakanjeId { get; set; }

            [Display(Name = "Банка")]
            public int? BankiId { get; set; }

            [Display(Name = "Тип на фактура")]
            public string TipNaFaktura { get; set; }

            [Display(Name = "Број на влезна фактура")]
            [DisplayFormat(NullDisplayText = "")]
            public string? BrojNaVleznaFaktura { get; set; }

            [Display(Name = "Број на излезна фактура")]
            public string? BrojNaIzleznaFaktura { get; set; }

            [Display(Name = "Датум на влезна фактура")]
            public DateTime? DatumNaVleznaFaktura { get; set; }

            [Display(Name = "Датум на излезна фактура")]
            public DateTime? DatumNaIzleznaFaktura { get; set; }

            [Display(Name = "Рок на плаќање излезна фактура")]
            public DateTime? RokNaPlakanjeIzleznaFaktura { get; set; }

            [Display(Name = "Рок на плаќање влезна фактура")]
            public DateTime? RokNaPlakanjeVleznaFaktura { get; set; }

            [Display(Name = "Процент франшиза")]
            public decimal? ProcentFransiza { get; set; }

            [Display(Name = "Франшиза износ")]            
            public decimal? FranshizaIznos { get; set; }

            [Display(Name = "Корегирана стапка на провизија")]
            public decimal? KoregiranaStapkaNaProvizija { get; set; }

            [Display(Name = "Дејност")]
            public int? ListaDejnostiId { get; set; }

            [Display(Name = "Број на осигуреници")]
            public int? BrojNaOsigurenici { get; set; }

            [Display(Name = "Премија за едно лице")]
            public decimal? PremijaZaEdnoLice { get; set; }

            [Display(Name = "Вкупна премија")]
            public decimal? VkupnaPremija { get; set; }

            [Display(Name = "Дополнителен ризик - Бременост")]
            public decimal? DopolnitelenRizik_Bremenost { get; set; }

            [Display(Name = "Операции")]
            public decimal? Operacii { get; set; }

            [Display(Name = "Операции вон РМ")]
            public decimal? OperaciiVonRM { get; set; }

            [Display(Name = "Трошоци за превоз вон РМ")]
            public decimal? TroshociZaPrevozVonRM { get; set; }

            [Display(Name = "Трошоци за ноќевање вон РМ")]
            public decimal? TrosociZaNokjevanjeVonRM { get; set; }

            [Display(Name = "Стоматологија")]
            public decimal? Stomatologija { get; set; }

            [Display(Name = "Офтамологија")]
            public decimal? Oftamologija { get; set; }

            [Display(Name = "Психијатриски услуги")]
            public decimal? PsihijatriskiUslugi { get; set; }

            [Display(Name = "Итна медицинска интервенција")]
            public decimal? ItnaMedicinskaIntervencija { get; set; }

            [Display(Name = "Дополнителни прегледи")]
            public decimal? DopolnitelniPregledi { get; set; }

            [Display(Name = "Превоз со санитетско возило")]
            public decimal? PrevozSoSanitetskoVozilo { get; set; }

            [Display(Name = "Второ мислење")]
            public decimal? VtoroMislenje { get; set; }

            [Display(Name = "Амбулантна рехабилитација")]
            public decimal? AmbulatnaRehabilitacija { get; set; }

            [Display(Name = "Трошоци за сместување родител")]
            public decimal? TroshociZaSmestuvanjeRoditel { get; set; }

            [Display(Name = "Валута за франшиза")]
            public long? SifrarnikValutiIdFranshizaValuta { get; set; }

            [Display(Name = "Процент на попуст за фактура во рок")]
            public decimal? ProcentNaPopustZaFakturaVoRok { get; set; }

            [Display(Name = "Износ за плаќање во рок")]
            public decimal? IznosZaPlakjanjeVoRok { get; set; }

            [Display(Name = "Процент комерцијален попуст")]
            public decimal? ProcentKomercijalenPopust { get; set; }

            [Display(Name = "Процент финансиски")]
            public decimal? ProcentFinansiski { get; set; }

            [Display(Name = "Премија за наплата")]
            public decimal? PremijaZaNaplata { get; set; }  
        }

        public async Task<IActionResult> OnGetSearchKlientiAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE MB LIKE @Search + '%'
                       OR EDB LIKE @Search + '%'
                       OR EMBG LIKE @Search + '%'
                       OR Naziv LIKE '%' + @Search + '%'
                       OR Ime LIKE '%' + @Search + '%'
                       OR Prezime LIKE '%' + @Search + '%'
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<IActionResult> OnGetSearchSorabotniciAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE (KlientSorabotnik = 1 or KlientVraboten = 1)
                        AND (MB LIKE @Search + '%'
                        OR EDB LIKE @Search + '%'
                        OR EMBG LIKE @Search + '%'
                        OR Naziv LIKE '%' + @Search + '%'
                        OR Ime LIKE '%' + @Search + '%'
                        OR Prezime LIKE '%' + @Search + '%')
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            try
            {
                // Add date validation before model validation
                if (Input.VaziOd.HasValue && Input.VaziDo.HasValue && Input.VaziDo.Value < Input.VaziOd.Value)
                {
                    ModelState.AddModelError("Input.VaziDo", "Датумот 'Важи до' не може да биде пред 'Важи од'");
                    // Reload necessary data for the page
                    await LoadOsiguriteli();
                    await LoadKlasiOsiguruvanje();
                    await LoadProdukti();
                    await LoadPriciniZaStorno();
                    await LoadValuti();
                    await LoadNaciniNaPlakanje();
                    await LoadTipoviNaPlakanje();
                    await LoadBanki();
                    await LoadListaDejnosti();
                    return Page();
                }

                if (!ModelState.IsValid)
                {
                    return Page();
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // Get username from session
                            var username = HttpContext.Session.GetString("Username");

                            // First insert into Polisi table and get the ID
                            long polisaId;
                            using (SqlCommand cmd = new SqlCommand(@"
                                INSERT INTO Polisi (
                                    UsernameCreated,
                                    KlientiIdOsiguritel,
                                    KlasiOsiguruvanjeIdKlasa,
                                    ProduktiIdProizvod,
                                    BrojNaPolisa,
                                    KlientiIdDogovoruvac,
                                    KlientiIdOsigurenik,
                                    Kolektivna,
                                    DatumVaziOd,
                                    DatumVaziDo,
                                    DatumNaIzdavanje,
                                    SifrarnikValutiIdValuta,
                                    KlientiIdSorabotnik,
                                    Faktoring,
                                    SifrarnikValutiIdFranshizaValuta,
                                    ProcentFranshiza,
                                    FranshizaIznos,
                                    KoregiranaStapkaNaProvizija,
                                    SifrarnikNacinNaPlakjanjeId,
                                    TipNaFaktura,
                                    BrojNaFakturaVlezna,
                                    DatumNaFakturaVlezna,
                                    RokNaPlakjanjeFakturaVlezna,
                                    SifrarnikTipNaPlakanjeId,
                                    SifrarnikBankiIdBanka,
                                    GeneriranaFakturaIzlezna,
                                    BrojNaFakturaIzlezna,
                                    DatumNaIzleznaFaktura,
                                    RokNaPlakjanjeFakturaIzlezna,
                                    Storno,
                                    PricinaZaStorno
                                ) VALUES (
                                    @UsernameCreated,
                                    @KlientiIdOsiguritel,
                                    @KlasiOsiguruvanjeIdKlasa,
                                    @ProduktiIdProizvod,
                                    @BrojNaPolisa,
                                    @KlientiIdDogovoruvac,
                                    @KlientiIdOsigurenik,
                                    @Kolektivna,
                                    @DatumVaziOd,
                                    @DatumVaziDo,
                                    @DatumNaIzdavanje,
                                    @SifrarnikValutiIdValuta,
                                    @KlientiIdSorabotnik,
                                    @Faktoring,
                                    @SifrarnikValutiIdFranshizaValuta,
                                    @ProcentFranshiza,
                                    @FranshizaIznos,
                                    @KoregiranaStapkaNaProvizija,
                                    @SifrarnikNacinNaPlakjanjeId,
                                    @TipNaFaktura,
                                    @BrojNaVleznaFaktura,
                                    @DatumNaVleznaFaktura,
                                    @RokNaPlakanjeVleznaFaktura,
                                    @SifrarnikTipNaPlakanjeId,
                                    @SifrarnikBankiIdBanka,
                                    @GeneriranaFakturaIzlezna,
                                    @BrojNaIzleznaFaktura,
                                    @DatumNaIzleznaFaktura,
                                    @RokNaPlakanjeIzleznaFaktura,
                                    @Storno,
                                    @PricinaZaStorno
                                );
                                SELECT SCOPE_IDENTITY();", connection, transaction))
                            {
                                cmd.Parameters.AddWithValue("@UsernameCreated", username);
                                cmd.Parameters.AddWithValue("@KlientiIdOsiguritel", Input.KlientiIdOsiguritel);
                                cmd.Parameters.AddWithValue("@KlasiOsiguruvanjeIdKlasa", Input.KlasiOsiguruvanjeIdKlasa);
                                cmd.Parameters.AddWithValue("@ProduktiIdProizvod", (object)Input.ProduktiIdProizvod ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@BrojNaPolisa", (object)Input.BrojNaPolisa ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@KlientiIdDogovoruvac", Input.KlientiIdDogovoruvac);
                                cmd.Parameters.AddWithValue("@KlientiIdOsigurenik", (object)Input.KlientiIdOsigurenik ?? DBNull.Value);
                                
                                // Set Kolektivna based on ProduktiIdProizvod
                                bool kolektivnaValue = Input.Kolektivna;
                                if (Input.ProduktiIdProizvod == 25 || Input.ProduktiIdProizvod == 26)
                                {
                                    kolektivnaValue = true;
                                }
                                cmd.Parameters.AddWithValue("@Kolektivna", kolektivnaValue);
                                
                                cmd.Parameters.AddWithValue("@DatumVaziOd", Input.VaziOd);
                                cmd.Parameters.AddWithValue("@DatumVaziDo", Input.VaziDo);
                                cmd.Parameters.AddWithValue("@DatumNaIzdavanje", Input.DatumNaIzdavanje);
                                cmd.Parameters.AddWithValue("@SifrarnikValutiIdValuta", Input.ValutiId);
                                cmd.Parameters.AddWithValue("@KlientiIdSorabotnik", (object)Input.KlientiIdSorabotnik ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@Faktoring", Input.Faktoring);
                                cmd.Parameters.AddWithValue("@SifrarnikValutiIdFranshizaValuta", 
                                    Input.SifrarnikValutiIdFranshizaValuta ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@ProcentFranshiza", (object)Input.ProcentFransiza ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@FranshizaIznos", Input.FranshizaIznos.HasValue ? (object)Input.FranshizaIznos.Value : DBNull.Value);
                                cmd.Parameters.AddWithValue("@KoregiranaStapkaNaProvizija", (object)Input.KoregiranaStapkaNaProvizija ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@SifrarnikNacinNaPlakjanjeId", Input.NaciniNaPlakanjeId);
                                cmd.Parameters.AddWithValue("@TipNaFaktura", (object)Input.TipNaFaktura ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@BrojNaVleznaFaktura", (object)Input.BrojNaVleznaFaktura ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@DatumNaVleznaFaktura", (object)Input.DatumNaVleznaFaktura ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@RokNaPlakanjeVleznaFaktura", (object)Input.RokNaPlakanjeVleznaFaktura ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@SifrarnikTipNaPlakanjeId", Input.TipoviNaPlakanjeId);
                                cmd.Parameters.AddWithValue("@SifrarnikBankiIdBanka", (object)Input.BankiId ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@GeneriranaFakturaIzlezna", Input.GeneriranaFakturaIzlezna);
                                cmd.Parameters.AddWithValue("@BrojNaIzleznaFaktura", (object)Input.BrojNaIzleznaFaktura ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@DatumNaIzleznaFaktura", (object)Input.DatumNaIzleznaFaktura ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@RokNaPlakanjeIzleznaFaktura", (object)Input.RokNaPlakanjeIzleznaFaktura ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@Storno", Input.Storno);
                                cmd.Parameters.AddWithValue("@PricinaZaStorno", (object)Input.SifrarnikPricinaZaStornoId ?? DBNull.Value);

                                polisaId = Convert.ToInt64(await cmd.ExecuteScalarAsync());
                            }

                            // Then insert into PolisiKlasa2 table
                            using (SqlCommand cmd = new SqlCommand(@"
                                INSERT INTO PolisiKlasa2 (
                                    UsernameCreated,
                                    PolisaId,
                                    ListaDejnostiId,
                                    BrojNaOsigurenici,
                                    PremijaZaEdnoLice,
                                    VkupnaPremija,
                                    DopolnitelenRizik_Bremenost,
                                    Operacii,
                                    OperaciiVonRM,
                                    TroshociZaPrevozVonRM,
                                    TrosociZaNokjevanjeVonRM,
                                    Stomatologija,
                                    Oftamologija,
                                    PsihijatriskiUslugi,
                                    ItnaMedicinskaIntervencija,
                                    DopolnitelniPregledi,
                                    PrevozSoSanitetskoVozilo,
                                    VtoroMislenje,
                                    AmbulatnaRehabilitacija,
                                    TroshociZaSmestuvanjeRoditel,
                                    ProcentNaPopustZaFakturaVoRok,
                                    IznosZaPlakjanjeVoRok,
                                    ProcentKomercijalenPopust,
                                    ProcentFinansiski,
                                    PremijaZaNaplata
                                ) VALUES (
                                    @UsernameCreated,
                                    @PolisaId,
                                    @ListaDejnostiId,
                                    @BrojNaOsigurenici,
                                    @PremijaZaEdnoLice,
                                    @VkupnaPremija,
                                    @DopolnitelenRizik_Bremenost,
                                    @Operacii,
                                    @OperaciiVonRM,
                                    @TroshociZaPrevozVonRM,
                                    @TrosociZaNokjevanjeVonRM,
                                    @Stomatologija,
                                    @Oftamologija,
                                    @PsihijatriskiUslugi,
                                    @ItnaMedicinskaIntervencija,
                                    @DopolnitelniPregledi,
                                    @PrevozSoSanitetskoVozilo,
                                    @VtoroMislenje,
                                    @AmbulatnaRehabilitacija,
                                    @TroshociZaSmestuvanjeRoditel,
                                    @ProcentNaPopustZaFakturaVoRok,
                                    @IznosZaPlakjanjeVoRok,
                                    @ProcentKomercijalenPopust,
                                    @ProcentFinansiski,
                                    @PremijaZaNaplata
                                )", connection, transaction))
                            {
                                cmd.Parameters.AddWithValue("@UsernameCreated", username);
                                cmd.Parameters.AddWithValue("@PolisaId", polisaId);
                                cmd.Parameters.AddWithValue("@ListaDejnostiId", (object)Input.ListaDejnostiId ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@BrojNaOsigurenici", (object)Input.BrojNaOsigurenici ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@PremijaZaEdnoLice", (object)Input.PremijaZaEdnoLice ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@VkupnaPremija", (object)Input.VkupnaPremija ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@DopolnitelenRizik_Bremenost", (object)Input.DopolnitelenRizik_Bremenost ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@Operacii", (object)Input.Operacii ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@OperaciiVonRM", (object)Input.OperaciiVonRM ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@TroshociZaPrevozVonRM", (object)Input.TroshociZaPrevozVonRM ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@TrosociZaNokjevanjeVonRM", (object)Input.TrosociZaNokjevanjeVonRM ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@Stomatologija", (object)Input.Stomatologija ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@Oftamologija", (object)Input.Oftamologija ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@PsihijatriskiUslugi", (object)Input.PsihijatriskiUslugi ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@ItnaMedicinskaIntervencija", (object)Input.ItnaMedicinskaIntervencija ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@DopolnitelniPregledi", (object)Input.DopolnitelniPregledi ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@PrevozSoSanitetskoVozilo", (object)Input.PrevozSoSanitetskoVozilo ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@VtoroMislenje", (object)Input.VtoroMislenje ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@AmbulatnaRehabilitacija", (object)Input.AmbulatnaRehabilitacija ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@TroshociZaSmestuvanjeRoditel", (object)Input.TroshociZaSmestuvanjeRoditel ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@ProcentNaPopustZaFakturaVoRok", (object)Input.ProcentNaPopustZaFakturaVoRok ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@IznosZaPlakjanjeVoRok", (object)Input.IznosZaPlakjanjeVoRok ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@ProcentKomercijalenPopust", (object)Input.ProcentKomercijalenPopust ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@ProcentFinansiski", (object)Input.ProcentFinansiski ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@PremijaZaNaplata", (object)Input.PremijaZaNaplata ?? DBNull.Value);


                                await cmd.ExecuteNonQueryAsync();
                            }

                            // Add this before await transaction.CommitAsync();
                            // Call the stored procedure to generate payment installments
                            using (SqlCommand cmd = new SqlCommand("GenerirajRatiPolisa", connection, transaction))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;

                                cmd.Parameters.AddWithValue("@PolisaId", polisaId);
                                cmd.Parameters.AddWithValue("@SifrarnikValutiId", Input.ValutiId);
                                cmd.Parameters.AddWithValue("@DatumNaIzdavanje", Input.DatumNaIzdavanje ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@SifrarnikNacinNaPlakanjeId", Input.NaciniNaPlakanjeId);
                                cmd.Parameters.AddWithValue("@Premija", Input.VkupnaPremija ?? (object)DBNull.Value);

                                await cmd.ExecuteNonQueryAsync();
                            }

                            await transaction.CommitAsync();
                            TempData["SuccessMessage"] = "Полисата е успешно зачувана.";
                            return RedirectToPage("/Polisi/ListaPolisi");
                        }
                        catch (Exception ex)
                        {
                            await transaction.RollbackAsync();
                            // Add detailed error information
                            ModelState.AddModelError("", $"Грешка при зачувување: {ex.Message}");
                            if (ex.InnerException != null)
                            {
                                ModelState.AddModelError("", $"Дополнителни информации: {ex.InnerException.Message}");
                            }
                            return Page();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Add detailed error information
                ModelState.AddModelError("", $"Неочекувана грешка: {ex.Message}");
                if (ex.InnerException != null)
                {
                    ModelState.AddModelError("", $"Дополнителни информации: {ex.InnerException.Message}");
                }
                return Page();
            }
        }

        public async Task<JsonResult> OnGetCheckBrojNaPolisaAsync(string brojNaPolisa)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand("SELECT dbo.ProverkaZaPostoenjeNaPolisaBezStorno(@brojNaPolisa)", connection))
                {
                    cmd.Parameters.AddWithValue("@brojNaPolisa", brojNaPolisa);
                    var result = await cmd.ExecuteScalarAsync();
                    return new JsonResult(Convert.ToInt32(result));
                }
            }
        }
    }
} 