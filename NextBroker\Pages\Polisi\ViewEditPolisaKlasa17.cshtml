@page "{id:long}"
@model NextBroker.Pages.Polisi.ViewEditPolisaKlasa17Model
@{
    ViewData["Title"] = "Преглед/Измена на полиса Класа 17";
    var formClass = !Model.HasAdminAccess ? "form-disabled" : "";
}

<h1 style="text-align: center;">@ViewData["Title"]</h1>

@if (!Model.HasAdminAccess)
{
    <div class="alert alert-warning mt-4">Немате администраторски пристап. Страницата е само за преглед.</div>
}

<div class="container mt-4">
    <div class="alert alert-info">Ова е празна страница за Класа 17. Додајте функционалност по потреба.</div>
</div>
