using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using Renci.SshNet;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace NextBroker.Pages.Klienti
{
    public class EditPravnoLiceModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        [BindProperty]
        public InputModel Input { get; set; }

        [BindProperty]
        public long Id { get; set; }

        public List<KlientiFile> Files { get; set; }

        public SelectList Opstini { get; set; }
        public SelectList Banki { get; set; }
        public SelectList NivoaNaRizik { get; set; }
        public SelectList ListaDejnosti { get; set; }

        public class KlientiFile
        {
            public long Id { get; set; }
            public string FileName { get; set; }
            public string FilePath { get; set; }
            public DateTime DateCreated { get; set; }
            public string UsernameCreated { get; set; }
        }

        public class InputModel
        {
            public string? Naziv { get; set; }
            public string? EDB { get; set; }
            public string? MB { get; set; }
            public bool Osiguritel { get; set; }
            public bool BrokerskoDrustvo { get; set; }
            public int? ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija { get; set; }
            public string? UlicaOdDokumentZaIdentifikacija { get; set; }
            public string? BrojOdDokumentZaIdentifikacija { get; set; }
            public int? ListaOpstiniIdOpstinaZaKomunikacija { get; set; }
            public string? UlicaZaKomunikacija { get; set; }
            public string? BrojZaKomunikacija { get; set; }
            public DateTime? DatumNaTekovnaSostojba { get; set; }
            public int? ListaDejnostiIdDejnost { get; set; }
            public string? Email { get; set; }
            public string? Tel { get; set; }
            public string? Webstrana { get; set; }
            public string? VistinskiSopstvenik { get; set; }
            public string? VistinskiSopstvenikIme { get; set; }
            public string? VistinskiSopstvenikPrezime { get; set; }
            public bool NositelNaJF { get; set; }
            public string? OsnovZaNositelNaJF { get; set; }
            public bool ZasilenaAnaliza { get; set; }
            public int? NivoaNaRizikIdNivoNaRizik { get; set; }
            public DateTime? DaumNaDogovor { get; set; }
            public DateTime? DogovorVaziDo { get; set; }
            public string? BrojNaDogovor { get; set; }
            public string? DogovorOpredelenoNeopredeleno { get; set; }
            public string? PlateznaSmetka { get; set; }
            public int? SifrarnikBankiIdBanka { get; set; }
            public bool ZadolzitelnoLicenca { get; set; }
            public string? BrojNaResenieOdASOZaLicenca { get; set; }
            public DateTime? DatumNaResenieOdASOZaLicenca { get; set; }
            public DateTime? DatumNaOdzemenaLicenca { get; set; }
            public string? BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti { get; set; }
            public DateTime? DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti { get; set; }
            public string? ZivotNezivot { get; set; }
            public bool KlientSorabotnik { get; set; }
            public DateTime? DatumNaOvlastuvanje { get; set; }
            public int? RokNaPlakanjeDenovi { get; set; }
            public string? Zabeleska { get; set; }
        }

        public EditPravnoLiceModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public async Task<IActionResult> OnGetAsync(long id)
        {
            try
            {
                if (!await HasPageAccess("DodajKlient"))
                {
                    return RedirectToAccessDenied();
                }

                Id = id;
                await LoadDropdownData();
                await LoadClientData(id);
                await LoadFiles(id);

                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Грешка при вчитување на страницата: {ex.Message}";
                return RedirectToPage("/Error");
            }
        }

        private async Task LoadDropdownData()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // Load Opstini
                using (SqlCommand cmd = new SqlCommand("SELECT Id, Opstina FROM ListaOpstini", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["Opstina"].ToString(), reader["Id"].ToString()));
                    }
                    Opstini = new SelectList(items, "Value", "Text");
                }

                // Load NivoaNaRizik
                using (SqlCommand cmd = new SqlCommand("SELECT Id, OpisNivoRizik FROM NivoaNaRizik", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["OpisNivoRizik"].ToString(), reader["Id"].ToString()));
                    }
                    NivoaNaRizik = new SelectList(items, "Value", "Text");
                }

                // Load Banki
                using (SqlCommand cmd = new SqlCommand("SELECT Id, Banka FROM SifrarnikBanki", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["Banka"].ToString(), reader["Id"].ToString()));
                    }
                    Banki = new SelectList(items, "Value", "Text");
                }

                // Load ListaDejnosti
                using (SqlCommand cmd = new SqlCommand("SELECT Id, NazivDejnost FROM ListaDejnosti", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["NazivDejnost"].ToString(), reader["Id"].ToString()));
                    }
                    ListaDejnosti = new SelectList(items, "Value", "Text");
                }
            }
        }

        private async Task LoadClientData(long id)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                string sql = "SELECT * FROM Klienti WHERE Id = @Id";
                using (SqlCommand cmd = new SqlCommand(sql, connection))
                {
                    cmd.Parameters.AddWithValue("@Id", id);
                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            Input = new InputModel
                            {
                                Naziv = reader["Naziv"].ToString(),
                                EDB = reader["EDB"].ToString(),
                                MB = reader["MB"].ToString(),
                                Osiguritel = !reader.IsDBNull(reader.GetOrdinal("Osiguritel")) && reader.GetBoolean(reader.GetOrdinal("Osiguritel")),
                                BrokerskoDrustvo = !reader.IsDBNull(reader.GetOrdinal("BrokerskoDrustvo")) && reader.GetBoolean(reader.GetOrdinal("BrokerskoDrustvo")),
                                ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija = reader.IsDBNull(reader.GetOrdinal("ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija")),
                                UlicaOdDokumentZaIdentifikacija = reader["UlicaOdDokumentZaIdentifikacija"].ToString(),
                                BrojOdDokumentZaIdentifikacija = reader["BrojOdDokumentZaIdentifikacija"].ToString(),
                                ListaOpstiniIdOpstinaZaKomunikacija = reader.IsDBNull(reader.GetOrdinal("ListaOpstiniIdOpstinaZaKomunikacija")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("ListaOpstiniIdOpstinaZaKomunikacija")),
                                UlicaZaKomunikacija = reader["UlicaZaKomunikacija"].ToString(),
                                BrojZaKomunikacija = reader["BrojZaKomunikacija"].ToString(),
                                DatumNaTekovnaSostojba = reader.IsDBNull(reader.GetOrdinal("DatumNaTekovnaSostojba")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DatumNaTekovnaSostojba")),
                                ListaDejnostiIdDejnost = reader.IsDBNull(reader.GetOrdinal("ListaDejnostiIdDejnost")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("ListaDejnostiIdDejnost")),
                                Email = reader["Email"].ToString(),
                                Tel = reader["Tel"].ToString(),
                                Webstrana = reader["Webstrana"].ToString(),
                                VistinskiSopstvenik = reader["VistinskiSopstvenik"].ToString(),
                                VistinskiSopstvenikIme = reader["VistinskiSopstvenikIme"].ToString(),
                                VistinskiSopstvenikPrezime = reader["VistinskiSopstvenikPrezime"].ToString(),
                                NositelNaJF = !reader.IsDBNull(reader.GetOrdinal("NositelNaJF")) && reader.GetBoolean(reader.GetOrdinal("NositelNaJF")),
                                OsnovZaNositelNaJF = reader["OsnovZaNositelNaJF"].ToString(),
                                ZasilenaAnaliza = !reader.IsDBNull(reader.GetOrdinal("ZasilenaAnaliza")) && reader.GetBoolean(reader.GetOrdinal("ZasilenaAnaliza")),
                                NivoaNaRizikIdNivoNaRizik = reader.IsDBNull(reader.GetOrdinal("NivoaNaRizikIdNivoNaRizik")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("NivoaNaRizikIdNivoNaRizik")),
                                DaumNaDogovor = reader.IsDBNull(reader.GetOrdinal("DaumNaDogovor")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DaumNaDogovor")),
                                DogovorVaziDo = reader.IsDBNull(reader.GetOrdinal("DogovorVaziDo")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DogovorVaziDo")),
                                BrojNaDogovor = reader["BrojNaDogovor"].ToString(),
                                DogovorOpredelenoNeopredeleno = reader["DogovorOpredelenoNeopredeleno"].ToString(),
                                PlateznaSmetka = reader["PlateznaSmetka"].ToString(),
                                SifrarnikBankiIdBanka = reader.IsDBNull(reader.GetOrdinal("SifrarnikBankiIdBanka")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("SifrarnikBankiIdBanka")),
                                ZadolzitelnoLicenca = !reader.IsDBNull(reader.GetOrdinal("ZadolzitelnoLicenca")) && reader.GetBoolean(reader.GetOrdinal("ZadolzitelnoLicenca")),
                                BrojNaResenieOdASOZaLicenca = reader["BrojNaResenieOdASOZaLicenca"].ToString(),
                                DatumNaResenieOdASOZaLicenca = reader.IsDBNull(reader.GetOrdinal("DatumNaResenieOdASOZaLicenca")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DatumNaResenieOdASOZaLicenca")),
                                DatumNaOdzemenaLicenca = reader.IsDBNull(reader.GetOrdinal("DatumNaOdzemenaLicenca")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DatumNaOdzemenaLicenca")),
                                BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti = reader["BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti"].ToString(),
                                DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti = reader.IsDBNull(reader.GetOrdinal("DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti")),
                                ZivotNezivot = reader["ZivotNezivot"].ToString(),
                                KlientSorabotnik = !reader.IsDBNull(reader.GetOrdinal("KlientSorabotnik")) && reader.GetBoolean(reader.GetOrdinal("KlientSorabotnik")),
                                DatumNaOvlastuvanje = reader.IsDBNull(reader.GetOrdinal("DatumNaOvlastuvanje")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DatumNaOvlastuvanje")),
                                RokNaPlakanjeDenovi = reader.IsDBNull(reader.GetOrdinal("RokNaPlakanjeDenovi")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("RokNaPlakanjeDenovi")),
                                Zabeleska = reader["Zabeleska"].ToString()
                            };
                        }
                    }
                }
            }
        }

        private async Task LoadFiles(long clientId)
        {
            try
            {
                Files = new List<KlientiFile>();
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    string sql = @"SELECT Id, FileName, FilePath, DateCreated, UsernameCreated 
                                 FROM KlientiFileSystem 
                                 WHERE KlientId = @KlientId 
                                 ORDER BY DateCreated DESC";

                    using (SqlCommand cmd = new SqlCommand(sql, connection))
                    {
                        cmd.Parameters.AddWithValue("@KlientId", clientId);
                        using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                Files.Add(new KlientiFile
                                {
                                    Id = reader.GetInt64(0),
                                    FileName = reader.GetString(1),
                                    FilePath = reader.GetString(2),
                                    DateCreated = reader.GetDateTime(3),
                                    UsernameCreated = reader.GetString(4)
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't throw - we don't want to break the page load if file listing fails
                System.Diagnostics.Debug.WriteLine($"Error loading files: {ex.Message}");
                Files = new List<KlientiFile>(); // Return empty list
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!await HasPageAccess("ListaKlienti"))
            {
                return RedirectToAccessDenied();
            }

            if (!ModelState.IsValid)
            {
                await LoadDropdownData();
                return Page();
            }

            try
            {
                // Get username from session
                var currentUsername = HttpContext.Session.GetString("Username");
                if (string.IsNullOrEmpty(currentUsername))
                {
                    ModelState.AddModelError("", "Session expired. Please log in again.");
                    return RedirectToPage("/Login");
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // First verify if the client exists
                    string checkSql = "SELECT COUNT(*) FROM Klienti WHERE Id = @Id";
                    using (SqlCommand checkCmd = new SqlCommand(checkSql, connection))
                    {
                        checkCmd.Parameters.AddWithValue("@Id", Id);
                        int count = (int)await checkCmd.ExecuteScalarAsync();
                        System.Diagnostics.Debug.WriteLine($"Found {count} clients with ID {Id}");
                        
                        if (count == 0)
                        {
                            ModelState.AddModelError("", "Client not found in database.");
                            await LoadDropdownData();
                            return Page();
                        }
                    }

                    string sql = @"
                        UPDATE Klienti 
                        SET 
                            Naziv = @Naziv,
                            EDB = @EDB,
                            MB = @MB,
                            Osiguritel = @Osiguritel,
                            BrokerskoDrustvo = @BrokerskoDrustvo,
                            ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija = @ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija,
                            UlicaOdDokumentZaIdentifikacija = @UlicaOdDokumentZaIdentifikacija,
                            BrojOdDokumentZaIdentifikacija = @BrojOdDokumentZaIdentifikacija,
                            ListaOpstiniIdOpstinaZaKomunikacija = @ListaOpstiniIdOpstinaZaKomunikacija,
                            UlicaZaKomunikacija = @UlicaZaKomunikacija,
                            BrojZaKomunikacija = @BrojZaKomunikacija,
                            DatumNaTekovnaSostojba = @DatumNaTekovnaSostojba,
                            ListaDejnostiIdDejnost = @ListaDejnostiIdDejnost,
                            Email = @Email,
                            Tel = @Tel,
                            Webstrana = @Webstrana,
                            VistinskiSopstvenik = @VistinskiSopstvenik,
                            VistinskiSopstvenikIme = @VistinskiSopstvenikIme,
                            VistinskiSopstvenikPrezime = @VistinskiSopstvenikPrezime,
                            NositelNaJF = @NositelNaJF,
                            OsnovZaNositelNaJF = @OsnovZaNositelNaJF,
                            ZasilenaAnaliza = @ZasilenaAnaliza,
                            NivoaNaRizikIdNivoNaRizik = @NivoaNaRizikIdNivoNaRizik,
                            DaumNaDogovor = @DaumNaDogovor,
                            DogovorVaziDo = @DogovorVaziDo,
                            BrojNaDogovor = @BrojNaDogovor,
                            DogovorOpredelenoNeopredeleno = @DogovorOpredelenoNeopredeleno,
                            PlateznaSmetka = @PlateznaSmetka,
                            SifrarnikBankiIdBanka = @SifrarnikBankiIdBanka,
                            ZadolzitelnoLicenca = @ZadolzitelnoLicenca,
                            BrojNaResenieOdASOZaLicenca = @BrojNaResenieOdASOZaLicenca,
                            DatumNaResenieOdASOZaLicenca = @DatumNaResenieOdASOZaLicenca,
                            DatumNaOdzemenaLicenca = @DatumNaOdzemenaLicenca,
                            BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti = @BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti,
                            DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti = @DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti,
                            ZivotNezivot = @ZivotNezivot,
                            KlientSorabotnik = @KlientSorabotnik,
                            DatumNaOvlastuvanje = @DatumNaOvlastuvanje,
                            RokNaPlakanjeDenovi = @RokNaPlakanjeDenovi,
                            Zabeleska = @Zabeleska,
                            DateModified = GETDATE(),
                            UsernameModified = @UsernameModified
                        WHERE Id = @Id";

                    using (SqlCommand cmd = new SqlCommand(sql, connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", Id);
                        cmd.Parameters.AddWithValue("@UsernameModified", currentUsername);
                        cmd.Parameters.AddWithValue("@Naziv", Input.Naziv ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@EDB", Input.EDB ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@MB", Input.MB ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Osiguritel", Input.Osiguritel);
                        cmd.Parameters.AddWithValue("@BrokerskoDrustvo", Input.BrokerskoDrustvo);
                        cmd.Parameters.AddWithValue("@ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija", Input.ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@UlicaOdDokumentZaIdentifikacija", Input.UlicaOdDokumentZaIdentifikacija ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojOdDokumentZaIdentifikacija", Input.BrojOdDokumentZaIdentifikacija ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ListaOpstiniIdOpstinaZaKomunikacija", Input.ListaOpstiniIdOpstinaZaKomunikacija ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@UlicaZaKomunikacija", Input.UlicaZaKomunikacija ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojZaKomunikacija", Input.BrojZaKomunikacija ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumNaTekovnaSostojba", Input.DatumNaTekovnaSostojba ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ListaDejnostiIdDejnost", Input.ListaDejnostiIdDejnost ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Email", Input.Email ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Tel", Input.Tel ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Webstrana", Input.Webstrana ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@VistinskiSopstvenik", Input.VistinskiSopstvenik ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@VistinskiSopstvenikIme", Input.VistinskiSopstvenikIme ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@VistinskiSopstvenikPrezime", Input.VistinskiSopstvenikPrezime ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@NositelNaJF", Input.NositelNaJF);
                        cmd.Parameters.AddWithValue("@OsnovZaNositelNaJF", Input.OsnovZaNositelNaJF ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ZasilenaAnaliza", Input.ZasilenaAnaliza);
                        cmd.Parameters.AddWithValue("@NivoaNaRizikIdNivoNaRizik", Input.NivoaNaRizikIdNivoNaRizik ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DaumNaDogovor", Input.DaumNaDogovor ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DogovorVaziDo", Input.DogovorVaziDo ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaDogovor", Input.BrojNaDogovor ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DogovorOpredelenoNeopredeleno", Input.DogovorOpredelenoNeopredeleno ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@PlateznaSmetka", Input.PlateznaSmetka ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SifrarnikBankiIdBanka", Input.SifrarnikBankiIdBanka ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ZadolzitelnoLicenca", Input.ZadolzitelnoLicenca);
                        cmd.Parameters.AddWithValue("@BrojNaResenieOdASOZaLicenca", Input.BrojNaResenieOdASOZaLicenca ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumNaResenieOdASOZaLicenca", Input.DatumNaResenieOdASOZaLicenca ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumNaOdzemenaLicenca", Input.DatumNaOdzemenaLicenca ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti", Input.BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti", Input.DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ZivotNezivot", Input.ZivotNezivot ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@KlientSorabotnik", Input.KlientSorabotnik);
                        cmd.Parameters.AddWithValue("@DatumNaOvlastuvanje", Input.DatumNaOvlastuvanje ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@RokNaPlakanjeDenovi", Input.RokNaPlakanjeDenovi ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Zabeleska", Input.Zabeleska ?? (object)DBNull.Value);
                        int rowsAffected = await cmd.ExecuteNonQueryAsync();
                        System.Diagnostics.Debug.WriteLine($"Rows affected by update: {rowsAffected}");

                        if (rowsAffected == 0)
                        {
                            System.Diagnostics.Debug.WriteLine("Update failed - no rows affected");
                            ModelState.AddModelError("", "No records were updated. Please check if the client still exists.");
                            await LoadDropdownData();
                            return Page();
                        }
                    }
                }

                TempData["SuccessMessage"] = "Клиентот е успешно ажуриран.";
                return RedirectToPage("/Klienti/ListaKlienti");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", $"Error updating client: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in OnPostAsync: {ex}");
                await LoadDropdownData();
                return Page();
            }
        }

        public async Task<IActionResult> OnPostUploadFileAsync(List<IFormFile> files, long klientId)
        {
            /*var debugInfo = new StringBuilder();
            debugInfo.AppendLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] Starting file upload process");
            debugInfo.AppendLine($"Client ID: {klientId}");
            debugInfo.AppendLine($"Number of files: {files?.Count ?? 0}");*/

            if (!await HasPageAccess("ListaKlienti"))
            {
                //debugInfo.AppendLine("Access denied - user does not have required permissions");
                //TempData["DebugInfo"] = debugInfo.ToString();
                return RedirectToAccessDenied();
            }

            if (files == null || !files.Any())
            {
                //debugInfo.AppendLine("No files selected for upload");
                TempData["ErrorMessage"] = "Не се избрани документи.";
                //TempData["DebugInfo"] = debugInfo.ToString();
                return RedirectToPage(new { id = klientId });
            }

            try
            {
                var username = HttpContext.Session.GetString("Username");
                //debugInfo.AppendLine($"Username from session: {username ?? "not found"}");

                if (string.IsNullOrEmpty(username))
                {
                    //debugInfo.AppendLine("Session expired - redirecting to login");
                    //TempData["DebugInfo"] = debugInfo.ToString();
                    return RedirectToPage("/Login");
                }

                var uploadDir = "/upload/Klienti";
                //debugInfo.AppendLine($"Using upload directory: {uploadDir}");

                using var client = new SftpClient(
                    _configuration.GetValue<string>("SftpConfig:Host"),
                    _configuration.GetValue<int>("SftpConfig:Port"),
                    _configuration.GetValue<string>("SftpConfig:Username"),
                    _configuration.GetValue<string>("SftpConfig:Password")
                );

                try
                {
                    //debugInfo.AppendLine("Attempting to connect to SFTP server...");
                    client.Connect();
                    //debugInfo.AppendLine("Successfully connected to SFTP server");

                    // Create base upload directory if it doesn't exist
                    if (!client.Exists("/upload"))
                    {
                        //debugInfo.AppendLine("Creating base /upload directory");
                        client.CreateDirectory("/upload");
                    }

                    // Create Klienti directory if it doesn't exist
                    if (!client.Exists(uploadDir))
                    {
                        //debugInfo.AppendLine($"Creating directory: {uploadDir}");
                        client.CreateDirectory(uploadDir);
                    }

                    foreach (var file in files)
                    {
                        //debugInfo.AppendLine($"\nProcessing file: {file.FileName}");
                        //debugInfo.AppendLine($"File size: {file.Length} bytes");
                        //debugInfo.AppendLine($"Content type: {file.ContentType}");

                        var fileName = Path.GetFileName(file.FileName);
                        var fileExt = Path.GetExtension(fileName);
                        var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
                        var randomChars = GenerateRandomString(10);
                        var uniqueFileName = $"Klient_{klientId}_{timestamp}_{randomChars}{fileExt}";
                        var filePath = $"{uploadDir}/{uniqueFileName}";

                        //debugInfo.AppendLine($"Generated unique filename: {uniqueFileName}");
                        //debugInfo.AppendLine($"Full file path: {filePath}");

                        using (var ms = new MemoryStream())
                        {
                            await file.CopyToAsync(ms);
                            ms.Position = 0;
                            //debugInfo.AppendLine("File copied to memory stream successfully");

                            //debugInfo.AppendLine("Starting file upload to SFTP...");
                            client.UploadFile(ms, filePath);
                            //debugInfo.AppendLine("File uploaded to SFTP successfully");

                            // Save to database
                            string connectionString = _configuration.GetConnectionString("DefaultConnection");
                            //debugInfo.AppendLine("\nAttempting to save file record to database...");
                            
                            using (SqlConnection connection = new SqlConnection(connectionString))
                            {
                                await connection.OpenAsync();
                                //debugInfo.AppendLine("Database connection opened successfully");

                                using var command = new SqlCommand(@"
                                    INSERT INTO KlientiFileSystem (KlientId, FileName, FilePath, DateCreated, UsernameCreated)
                                    VALUES (@KlientId, @FileName, @FilePath, GETDATE(), @UsernameCreated)", connection);

                                command.Parameters.AddWithValue("@KlientId", klientId);
                                command.Parameters.AddWithValue("@FileName", fileName);
                                command.Parameters.AddWithValue("@FilePath", filePath);
                                command.Parameters.AddWithValue("@UsernameCreated", username);

                                var rowsAffected = await command.ExecuteNonQueryAsync();
                                //debugInfo.AppendLine($"Database record created successfully. Rows affected: {rowsAffected}");
                            }
                        }
                    }

                    TempData["SuccessMessage"] = $"Успешно се прикачени {files.Count} документи.";
                    //debugInfo.AppendLine($"\nUpload process completed successfully for {files.Count} files");
                }
                finally
                {
                    if (client.IsConnected)
                    {
                        //debugInfo.AppendLine("Disconnecting from SFTP server");
                        client.Disconnect();
                        //debugInfo.AppendLine("Successfully disconnected from SFTP server");
                    }
                }
            }
            catch (Exception ex)
            {
                /*debugInfo.AppendLine($"\nFatal error in upload process:");
                debugInfo.AppendLine($"Error message: {ex.Message}");
                debugInfo.AppendLine($"Stack trace: {ex.StackTrace}");*/
                TempData["ErrorMessage"] = $"Грешка при прикачување на документите: {ex.Message}";
            }

            //TempData["DebugInfo"] = debugInfo.ToString();
            return RedirectToPage(new { id = klientId });
        }

        private string GenerateRandomString(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        public async Task<IActionResult> OnGetDownloadFileAsync(long fileId)
        {
            if (!await HasPageAccess("ListaKlienti"))
            {
                return RedirectToAccessDenied();
            }

            try
            {
                // Get file info from database
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                var sql = "SELECT FileName, FilePath, KlientId FROM KlientiFileSystem WHERE Id = @FileId";
                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddWithValue("@FileId", fileId);

                using var reader = await command.ExecuteReaderAsync();
                if (!await reader.ReadAsync())
                {
                    TempData["ErrorMessage"] = "Документот не е пронајден.";
                    return RedirectToPage();
                }

                var fileName = reader.GetString(0);
                var filePath = reader.GetString(1);
                var klientId = reader.GetInt64(2);

                System.Diagnostics.Debug.WriteLine($"Downloading file: {fileName}");
                System.Diagnostics.Debug.WriteLine($"File path: {filePath}");

                // Download file from SFTP
                using var client = new SftpClient(
                    _configuration.GetValue<string>("SftpConfig:Host"),
                    _configuration.GetValue<int>("SftpConfig:Port"),
                    _configuration.GetValue<string>("SftpConfig:Username"),
                    _configuration.GetValue<string>("SftpConfig:Password")
                );
                
                try
                {
                    System.Diagnostics.Debug.WriteLine("Connecting to SFTP server...");
                    client.Connect();
                    System.Diagnostics.Debug.WriteLine("Connected successfully");

                    if (!client.Exists(filePath))
                    {
                        TempData["ErrorMessage"] = "Документот не е пронајден на серверот.";
                        return RedirectToPage(new { id = klientId });
                    }

                    using var ms = new MemoryStream();
                    System.Diagnostics.Debug.WriteLine("Downloading file from SFTP...");
                    client.DownloadFile(filePath, ms);
                    ms.Position = 0;
                    System.Diagnostics.Debug.WriteLine("File downloaded successfully");

                    return File(ms.ToArray(), "application/octet-stream", fileName);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error downloading file: {ex.Message}");
                    throw;
                }
                finally
                {
                    if (client.IsConnected)
                    {
                        System.Diagnostics.Debug.WriteLine("Disconnecting from SFTP server");
                        client.Disconnect();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in OnGetDownloadFileAsync: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                TempData["ErrorMessage"] = $"Грешка при преземање на документот: {ex.Message}";
                return RedirectToPage();
            }
        }

        public async Task<IActionResult> OnPostDeleteFileAsync(long fileId)
        {
            if (!await HasPageAccess("ListaKlienti"))
            {
                return RedirectToAccessDenied();
            }

            try
            {
                // Get file info from database
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                var sql = "SELECT FilePath, KlientId FROM KlientiFileSystem WHERE Id = @FileId";
                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddWithValue("@FileId", fileId);

                using var reader = await command.ExecuteReaderAsync();
                if (!await reader.ReadAsync())
                {
                    TempData["ErrorMessage"] = "Документот не е пронајден.";
                    return RedirectToPage();
                }

                var filePath = reader.GetString(0);
                var klientId = reader.GetInt64(1);
                reader.Close();

                System.Diagnostics.Debug.WriteLine($"Deleting file: {filePath}");

                // Delete file from SFTP
                using var client = new SftpClient(
                    _configuration.GetValue<string>("SftpConfig:Host"),
                    _configuration.GetValue<int>("SftpConfig:Port"),
                    _configuration.GetValue<string>("SftpConfig:Username"),
                    _configuration.GetValue<string>("SftpConfig:Password")
                );

                try
                {
                    System.Diagnostics.Debug.WriteLine("Connecting to SFTP server...");
                    client.Connect();
                    System.Diagnostics.Debug.WriteLine("Connected successfully");
                    
                    if (client.Exists(filePath))
                    {
                        System.Diagnostics.Debug.WriteLine("Deleting file from SFTP...");
                        client.DeleteFile(filePath);
                        System.Diagnostics.Debug.WriteLine("File deleted from SFTP successfully");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("File not found on SFTP server");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error deleting file from SFTP: {ex.Message}");
                    throw;
                }
                finally
                {
                    if (client.IsConnected)
                    {
                        System.Diagnostics.Debug.WriteLine("Disconnecting from SFTP server");
                        client.Disconnect();
                    }
                }

                // Delete record from database
                sql = "DELETE FROM KlientiFileSystem WHERE Id = @FileId";
                command.CommandText = sql;
                await command.ExecuteNonQueryAsync();
                System.Diagnostics.Debug.WriteLine("Database record deleted successfully");

                TempData["SuccessMessage"] = "Документот е успешно избришан.";
                return RedirectToPage(new { id = klientId });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in OnPostDeleteFileAsync: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                TempData["ErrorMessage"] = $"Грешка при бришење на документот: {ex.Message}";
                return RedirectToPage();
            }
        }
    }
} 
