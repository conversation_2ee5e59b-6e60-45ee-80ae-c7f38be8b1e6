@page
@model NextBroker.Pages.Pregledi.IzvestajGeneriraniIzlezniFakturiKonKlientModel
@{
    ViewData["Title"] = "Извештај за генерирани излезни фактури кон клиент";
}

<div class="container-fluid">
    <h4 class="mb-3">@ViewData["Title"]</h4>

    <form method="post">
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="DatumOd" class="control-label"></label>
                    <input asp-for="DatumOd" class="form-control" />
                    <span asp-validation-for="DatumOd" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="DatumDo" class="control-label"></label>
                    <input asp-for="DatumDo" class="form-control" />
                    <span asp-validation-for="DatumDo" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-4 d-flex align-items-end gap-2">
                <button type="submit" class="btn btn-primary">Генерирај извештај</button>
                @if (Model.Data.Any())
                {
                    <button type="submit" asp-page-handler="ExportToCsv" class="btn btn-success">
                        <i class="fas fa-file-csv me-1"></i>Експорт во CSV
                    </button>
                }
            </div>
        </div>
    </form>

    @if (!ModelState.IsValid)
    {
        <div class="alert alert-danger">
            <ul>
                @foreach (var modelState in ViewData.ModelState.Values)
                {
                    foreach (var error in modelState.Errors)
                    {
                        <li>@error.ErrorMessage</li>
                    }
                }
            </ul>
        </div>
    }

    @if (Model.Data.Any())
    {
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        @foreach (var columnName in Model.ColumnNames)
                        {
                            <th>@columnName</th>
                        }
                    </tr>
                </thead>
                <tbody>
                    @foreach (var row in Model.Data)
                    {
                        <tr>
                            @foreach (var item in row)
                            {
                                <td>
                                    @if (item is DateTime date)
                                    {
                                        @date.ToString("dd/MM/yyyy")
                                    }
                                    else if (item is decimal decimalValue)
                                    {
                                        @decimalValue.ToString("N2")
                                    }
                                    else if (item is double doubleValue)
                                    {
                                        @doubleValue.ToString("N2")
                                    }
                                    else if (item is float floatValue)
                                    {
                                        @floatValue.ToString("N2")
                                    }
                                    else
                                    {
                                        @item
                                    }
                                </td>
                            }
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
