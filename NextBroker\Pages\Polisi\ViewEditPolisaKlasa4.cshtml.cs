using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.Threading.Tasks;

namespace NextBroker.Pages.Polisi
{
    public class ViewEditPolisaKlasa4Model : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public bool HasAdminAccess { get; private set; }

        public ViewEditPolisaKlasa4Model(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public async Task<IActionResult> OnGetAsync(long id)
        {
            if (!await HasPageAccess("ViewEditPolisaKlasa4"))
            {
                return RedirectToAccessDenied();
            }

            HasAdminAccess = await HasPageAccess("ViewEditPolisaKlasa4Admin");
            return Page();
        }
    }
}
