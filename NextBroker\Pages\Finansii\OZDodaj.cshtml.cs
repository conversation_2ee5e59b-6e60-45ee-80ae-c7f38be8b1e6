using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;

namespace NextBroker.Pages.Finansii
{
    public class OZDodajModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public OZDodajModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        [BindProperty]
        public OZInputModel Input { get; set; } = new();

        public IEnumerable<SelectListItem> TipoviNaDokument { get; set; }
        public IEnumerable<SelectListItem> Tipovi { get; set; }
        public IEnumerable<SelectListItem> PremijaProvizija { get; set; }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("OdobruvanjeZadolzuvanje"))
            {
                return RedirectToAccessDenied();
            }

            // Set today's date as default for DatumNaDokument
            Input.DatumNaDokument = DateTime.Today;

            await LoadDropdownData();
            return Page();
        }

        public async Task<IActionResult> OnPost()
        {
            if (!await HasPageAccess("OdobruvanjeZadolzuvanje"))
            {
                return RedirectToAccessDenied();
            }

            if (!ModelState.IsValid)
            {
                await LoadDropdownData();
                return Page();
            }

            // Check for duplicates in the database
            await CheckForDuplicates();
            
            if (!ModelState.IsValid)
            {
                await LoadDropdownData();
                return Page();
            }

            string username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                TempData["ErrorMessage"] = "Вашата сесија е истечена. Најавете се повторно.";
                return RedirectToPage("/Account/Login");
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Handle automatic document number generation for SifrarnikOZTipNaDokumentId = 2
                    string documentNumber = Input.BrojNaDokument;
                    if (Input.SifrarnikOZTipNaDokumentId == 2)
                    {
                        // Generate the actual document number using the sequence
                        using (SqlCommand seqCmd = new SqlCommand("SELECT NEXT VALUE FOR VratiSledenBrojOZ", connection))
                        {
                            var seqResult = await seqCmd.ExecuteScalarAsync();
                            int nextNumber = seqResult != null ? Convert.ToInt32(seqResult) : 1;
                            documentNumber = $"OZ/{nextNumber}";
                        }
                    }

                    using (SqlCommand cmd = new SqlCommand(@"
                        INSERT INTO OdobruvanjeZadolzuvanje (
                            UsernameCreated, SifrarnikOZTipNaDokumentId, SifrarnikOZTipId, 
                            SifrarnikOZPremijaProvizijaId, KlientiIdKlient, BrojNaDokument,
                            DatumNaDokument, Iznos, BrojNaVleznaFaktura, BrojNaIzleznaFaktura,
                            PolisaId, TipNafaktura, BrojNaIzleznaFakturaPremija, Opis
                        ) VALUES (
                            @UsernameCreated, @SifrarnikOZTipNaDokumentId, @SifrarnikOZTipId,
                            @SifrarnikOZPremijaProvizijaId, @KlientiIdKlient, @BrojNaDokument,
                            @DatumNaDokument, @Iznos, @BrojNaVleznaFaktura, @BrojNaIzleznaFaktura,
                            @PolisaId, @TipNafaktura, @BrojNaIzleznaFakturaPremija, @Opis
                        )", connection))
                    {
                        cmd.Parameters.AddWithValue("@UsernameCreated", username);
                        cmd.Parameters.AddWithValue("@SifrarnikOZTipNaDokumentId", Input.SifrarnikOZTipNaDokumentId ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SifrarnikOZTipId", Input.SifrarnikOZTipId ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SifrarnikOZPremijaProvizijaId", Input.SifrarnikOZPremijaProvizijaId ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@KlientiIdKlient", Input.KlientiIdKlient ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaDokument", documentNumber ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumNaDokument", Input.DatumNaDokument ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Iznos", Input.Iznos ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaVleznaFaktura", Input.BrojNaVleznaFaktura ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaIzleznaFaktura", Input.BrojNaIzleznaFaktura ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@PolisaId", Input.PolisaId ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@TipNafaktura", Input.TipNafaktura ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaIzleznaFakturaPremija", Input.BrojNaIzleznaFakturaPremija ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Opis", Input.Opis ?? (object)DBNull.Value);

                        await cmd.ExecuteNonQueryAsync();
                    }
                }

                TempData["SuccessMessage"] = "Записот е успешно зачуван.";
                return RedirectToPage("OZLista");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "Настана грешка при зачувување на записот. Ве молиме обидете се повторно.");
                await LoadDropdownData();
                return Page();
            }
        }

        private async Task LoadDropdownData()
        {
            await LoadTipoviNaDokument();
            await LoadTipovi();
            await LoadPremijaProvizija();
        }

        private async Task LoadTipoviNaDokument()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand("SELECT Id, TipNaDokument FROM SifrarnikOZTipNaDokument ORDER BY TipNaDokument", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["TipNaDokument"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    TipoviNaDokument = items;
                }
            }
        }

        private async Task LoadTipovi()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand("SELECT Id, Tip FROM SifrarnikOZTip ORDER BY Tip", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Tip"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Tipovi = items;
                }
            }
        }

        private async Task LoadPremijaProvizija()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand("SELECT Id, PremijaProvizija FROM SifrarnikOZPremijaProvizija ORDER BY PremijaProvizija", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["PremijaProvizija"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    PremijaProvizija = items;
                }
            }
        }

        public class OZInputModel
        {
            [Required(ErrorMessage = "Тип на документ е задолжително поле")]
            [Display(Name = "Тип на документ")]
            public long? SifrarnikOZTipNaDokumentId { get; set; }

            [Required(ErrorMessage = "Тип е задолжително поле")]
            [Display(Name = "Тип")]
            public long? SifrarnikOZTipId { get; set; }

            [Required(ErrorMessage = "Премија/Провизија е задолжително поле")]
            [Display(Name = "Премија/Провизија")]
            public long? SifrarnikOZPremijaProvizijaId { get; set; }

            [Required(ErrorMessage = "Клиент е задолжително поле")]
            [Display(Name = "Клиент ID")]
            public long? KlientiIdKlient { get; set; }

            [Required(ErrorMessage = "Број на документ е задолжително поле")]
            [Display(Name = "Број на документ")]
            public string? BrojNaDokument { get; set; }

            [Required(ErrorMessage = "Датум на документ е задолжително поле")]
            [Display(Name = "Датум на документ")]
            public DateTime? DatumNaDokument { get; set; }

            [Display(Name = "Износ")]
            public decimal? Iznos { get; set; }

            [Display(Name = "Број на влезна фактура")]
            public string? BrojNaVleznaFaktura { get; set; }

            [Display(Name = "Број на излезна фактура")]
            public string? BrojNaIzleznaFaktura { get; set; }

            [Display(Name = "Полиса ID")]
            public long? PolisaId { get; set; }

            [Display(Name = "Тип на фактура")]
            public string? TipNafaktura { get; set; }

            [Display(Name = "Број на излезна фактура премија")]
            public string? BrojNaIzleznaFakturaPremija { get; set; }

            [Display(Name = "Опис")]
            public string? Opis { get; set;             }
        }

        public async Task<IActionResult> OnGetSearchKlientiAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE MB LIKE @Search + '%'
                       OR EDB LIKE @Search + '%'
                       OR EMBG LIKE @Search + '%'
                       OR Naziv LIKE '%' + @Search + '%'
                       OR Ime LIKE '%' + @Search + '%'
                       OR Prezime LIKE '%' + @Search + '%'
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<JsonResult> OnGetSearchPolisiAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 5 
                        Id,
                        BrojNaPolisa,
                        ISNULL(BrojNaFakturaIzlezna, '') as BrojNaFakturaIzlezna,
                        ISNULL(Storno, 0) as Storno,
                        DateCreated
                    FROM Polisi 
                    WHERE (BrojNaPolisa LIKE '%' + @Search + '%'
                       OR BrojNaFakturaIzlezna LIKE '%' + @Search + '%')
                    ORDER BY BrojNaPolisa", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", searchTerm);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            brojNaPolisa = reader["BrojNaPolisa"].ToString(),
                            brojNaFakturaIzlezna = reader["BrojNaFakturaIzlezna"],
                            storno = reader["Storno"],
                            dateCreated = reader["DateCreated"]
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<JsonResult> OnGetPolisaDetailsAsync(long polisaId)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT ISNULL(TipNaFaktura, '') as TipNaFaktura
                    FROM Polisi 
                    WHERE Id = @PolisaId", connection))
                {
                    cmd.Parameters.AddWithValue("@PolisaId", polisaId);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    if (await reader.ReadAsync())
                    {
                        return new JsonResult(new
                        {
                            tipNaFaktura = reader["TipNaFaktura"].ToString()
                        });
                    }
                    else
                    {
                        return new JsonResult(new
                        {
                            tipNaFaktura = ""
                        });
                    }
                }
            }
        }

        public async Task<JsonResult> OnGetSearchVleznaFakturaAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10
                        pnns.Id,
                        CONCAT_WS(' ', COALESCE(klnt.ime, ''), COALESCE(klnt.prezime, ''), COALESCE(klnt.naziv, '')) AS Klient,
                        pnns.BrojNafaktura,
                        pnns.IznosNaFaktura,
                        pnns.IznosNaFakturaVoRok,
                        pnns.DatumNaVleznaFaktura
                    FROM prenosnanaplataspecifikacii pnns
                    LEFT JOIN klienti klnt ON pnns.KlientiIdOsiguritel = klnt.id
                    WHERE pnns.BrojNafaktura LIKE '%' + @Search + '%'
                       OR CONCAT_WS(' ', COALESCE(klnt.ime, ''), COALESCE(klnt.prezime, ''), COALESCE(klnt.naziv, '')) LIKE '%' + @Search + '%'
                    ORDER BY pnns.BrojNafaktura", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", searchTerm);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            klient = reader["Klient"].ToString(),
                            brojNafaktura = reader["BrojNafaktura"].ToString(),
                            iznosNaFaktura = reader["IznosNaFaktura"],
                            iznosNaFakturaVoRok = reader["IznosNaFakturaVoRok"],
                            datumNaVleznaFaktura = reader["DatumNaVleznaFaktura"]
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<JsonResult> OnGetSearchIzleznaFakturaAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10
                        Id,
                        BrojNaFaktura,
                        FakturaDo,
                        DatumNaFaktura,
                        Iznos,
                        DatumOd,
                        DatumDO
                    FROM dbo.FakturiZaProvizijaKonOsiguritel
                    WHERE BrojNaFaktura LIKE '%' + @Search + '%'
                       OR FakturaDo LIKE '%' + @Search + '%'
                    ORDER BY BrojNaFaktura", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", searchTerm);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            brojNaFaktura = reader["BrojNaFaktura"].ToString(),
                            fakturaDo = reader["FakturaDo"].ToString(),
                            datumNaFaktura = reader["DatumNaFaktura"],
                            iznos = reader["Iznos"],
                            datumOd = reader["DatumOd"],
                            datumDo = reader["DatumDO"]
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<JsonResult> OnGetSearchIzleznaFakturaPremijaAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10
                        Id,
                        BrojNaFaktura,
                        DatumNaFaktura,
                        BrojNaPolisa,
                        Osiguritel,
                        Produkt,
                        PremijaZaNaplata
                    FROM dbo.PolisiIzlezniFakturiKonKlient
                    WHERE BrojNaFaktura LIKE '%' + @Search + '%'
                       OR BrojNaPolisa LIKE '%' + @Search + '%'
                       OR Osiguritel LIKE '%' + @Search + '%'
                       OR Produkt LIKE '%' + @Search + '%'
                    ORDER BY BrojNaFaktura", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", searchTerm);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            brojNaFaktura = reader["BrojNaFaktura"].ToString(),
                            datumNaFaktura = reader["DatumNaFaktura"],
                            brojNaPolisa = reader["BrojNaPolisa"].ToString(),
                            osiguritel = reader["Osiguritel"].ToString(),
                            produkt = reader["Produkt"].ToString(),
                            premijaZaNaplata = reader["PremijaZaNaplata"]
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<JsonResult> OnGetNextOZNumberAsync()
        {
            if (!await HasPageAccess("OdobruvanjeZadolzuvanje"))
            {
                return new JsonResult(new { success = false, message = "Немате дозвола за оваа акција." });
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    using (SqlCommand cmd = new SqlCommand(@"
                        SELECT Convert(int,current_value) + 1
                        FROM sys.sequences 
                        WHERE name = 'VratiSledenBrojOZ'", connection))
                    {
                        var result = await cmd.ExecuteScalarAsync();
                        int nextNumber = result != null ? Convert.ToInt32(result) : 1;
                        string documentNumber = $"OZ/{nextNumber}";
                        
                        return new JsonResult(new { success = true, documentNumber = documentNumber });
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = $"Грешка при преземање на следен број: {ex.Message}" });
            }
        }

        private async Task CheckForDuplicates()
        {
            const string duplicateMessage = "Веќе постои запис за оваа фактура/полиса во одобрувања. Ве молиме сторнирајте го пред да внесете нов.";
            
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // Check for duplicate PolisaId for SifrarnikOZPremijaProvizijaId = 1
                if (Input.PolisaId.HasValue && Input.SifrarnikOZPremijaProvizijaId == 1)
                {
                    using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM OdobruvanjeZadolzuvanje WHERE PolisaId = @PolisaId AND ISNULL(Storno, 0) != 1 AND SifrarnikOZPremijaProvizijaId = 1", connection))
                    {
                        cmd.Parameters.AddWithValue("@PolisaId", Input.PolisaId.Value);
                        var count = (int)await cmd.ExecuteScalarAsync();
                        if (count > 0)
                        {
                            ModelState.AddModelError("Input.PolisaId", duplicateMessage);
                        }
                    }
                }

                // Check for duplicate PolisaId for SifrarnikOZPremijaProvizijaId = 2
                if (Input.PolisaId.HasValue && Input.SifrarnikOZPremijaProvizijaId == 2)
                {
                    using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM OdobruvanjeZadolzuvanje WHERE PolisaId = @PolisaId AND ISNULL(Storno, 0) != 1 AND SifrarnikOZPremijaProvizijaId = 2", connection))
                    {
                        cmd.Parameters.AddWithValue("@PolisaId", Input.PolisaId.Value);
                        var count = (int)await cmd.ExecuteScalarAsync();
                        if (count > 0)
                        {
                            ModelState.AddModelError("Input.PolisaId", duplicateMessage);
                        }
                    }
                }

                // Check for duplicate BrojNaVleznaFaktura
                if (!string.IsNullOrWhiteSpace(Input.BrojNaVleznaFaktura))
                {
                    using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM OdobruvanjeZadolzuvanje WHERE BrojNaVleznaFaktura = @BrojNaVleznaFaktura AND ISNULL(Storno, 0) != 1", connection))
                    {
                        cmd.Parameters.AddWithValue("@BrojNaVleznaFaktura", Input.BrojNaVleznaFaktura);
                        var count = (int)await cmd.ExecuteScalarAsync();
                        if (count > 0)
                        {
                            ModelState.AddModelError("Input.BrojNaVleznaFaktura", duplicateMessage);
                        }
                    }
                }

                // Check for duplicate BrojNaIzleznaFaktura
                if (!string.IsNullOrWhiteSpace(Input.BrojNaIzleznaFaktura))
                {
                    using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM OdobruvanjeZadolzuvanje WHERE BrojNaIzleznaFaktura = @BrojNaIzleznaFaktura AND ISNULL(Storno, 0) != 1", connection))
                    {
                        cmd.Parameters.AddWithValue("@BrojNaIzleznaFaktura", Input.BrojNaIzleznaFaktura);
                        var count = (int)await cmd.ExecuteScalarAsync();
                        if (count > 0)
                        {
                            ModelState.AddModelError("Input.BrojNaIzleznaFaktura", duplicateMessage);
                        }
                    }
                }

                // Check for duplicate BrojNaIzleznaFakturaPremija
                if (!string.IsNullOrWhiteSpace(Input.BrojNaIzleznaFakturaPremija))
                {
                    using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM OdobruvanjeZadolzuvanje WHERE BrojNaIzleznaFakturaPremija = @BrojNaIzleznaFakturaPremija AND ISNULL(Storno, 0) != 1", connection))
                    {
                        cmd.Parameters.AddWithValue("@BrojNaIzleznaFakturaPremija", Input.BrojNaIzleznaFakturaPremija);
                        var count = (int)await cmd.ExecuteScalarAsync();
                        if (count > 0)
                        {
                            ModelState.AddModelError("Input.BrojNaIzleznaFakturaPremija", duplicateMessage);
                        }
                    }
                }
            }
        }
    }
}
