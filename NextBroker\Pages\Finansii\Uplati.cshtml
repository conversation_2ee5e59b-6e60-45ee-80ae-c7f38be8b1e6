@page
@model NextBroker.Pages.Finansii.UplatiModel
@{
    ViewData["Title"] = "Уплати";
    var formClass = !Model.HasAdminAccess ? "form-disabled" : "";
    var antiforgery = Html.AntiForgeryToken();
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>


<div class="container-fluid mt-4 px-4">
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Филтри</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label class="form-label">Датум од</label>
                    <input type="date" class="form-control" name="DatumOd" value="@(Model.DatumOd?.ToString("yyyy-MM-dd"))" />
                </div>
                <div class="col-md-3">
                    <label class="form-label">Датум до</label>
                    <input type="date" class="form-control" name="DatumDo" value="@(Model.DatumDo?.ToString("yyyy-MM-dd"))" />
                </div>
                <div class="col-md-3">
                    <label class="form-label">Број на полиса</label>
                    <input type="text" class="form-control" name="SearchPolisaBroj" value="@Model.SearchPolisaBroj" />
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i> Филтрирај
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                        <i class="fas fa-times me-1"></i> Исчисти
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container-fluid mt-4 px-4">
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">@ViewData["Title"]</h5>
                <div class="d-flex gap-2">
                    <a href="/Finansii/UplatiImportExcel" 
                       class="btn btn-primary btn-sm"
                       @(Model.HasAdminAccess ? "" : "disabled")>
                        <i class="fas fa-file-excel me-1"></i> Вчитај Excel
                    </a>
                    <button type="button" class="btn btn-primary btn-sm" 
                            onclick="openAddModal()"
                            @(Model.HasAdminAccess ? "" : "disabled")>
                        <i class="fas fa-plus me-1"></i> Додај уплата
                    </button>
                    <a href="?handler=ExportExcel&DatumOd=@(Model.DatumOd?.ToString("yyyy-MM-dd"))&DatumDo=@(Model.DatumDo?.ToString("yyyy-MM-dd"))&SearchPolisaBroj=@Model.SearchPolisaBroj"
                       class="btn btn-success btn-sm">
                        <i class="fas fa-file-excel me-1"></i> Генерирај Excel
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="uplatiTable" class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ИД</th>
                            <th>Креирано на</th>
                            <th>Креирано од</th>
                            <th>Променето на</th>
                            <th>Променето од</th>
                            <th>Датум на уплата</th>
                            <th>Тип на плаќање</th>
                            <th>Број на извод</th>
                            <th class="text-end">Износ</th>
                            <th>Број на полиса</th>
                            <th class="text-center">Нераспределена</th>
                            <th class="text-center">Поврат на средства</th>
                            <th>Број на касов извештај</th>
                            <th>Банка</th>
                            <th>Каса прими</th>
                            <th>Уплаќач</th>
                            <th>Број на фактура</th>
                            <th class="text-center">Прераспределена</th>
                            <th class="text-center">Средства од прераспределена</th>
                            <th style="width: 220px; min-width: 220px;">Акции</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var uplata in Model.Uplati)
                        {
                            <tr>
                                <td>@uplata.Id</td>
                                <td>@uplata.DateCreated?.ToString("dd.MM.yyyy HH:mm")</td>
                                <td>@uplata.UsernameCreated</td>
                                <td>@uplata.DateModified?.ToString("dd.MM.yyyy HH:mm")</td>
                                <td>@uplata.UsernameModified</td>
                                <td>@uplata.UplataDatum.ToString("dd.MM.yyyy")</td>
                                <td>@uplata.TipNaPlakanje</td>
                                <td>@uplata.BrojIzvod</td>
                                <td class="text-end">@uplata.Iznos.ToString("N2")</td>
                                <td>@uplata.PolisaBroj</td>
                                <td class="text-center">
                                    @if (uplata.Neraspredelena == true)
                                    {
                                        <i class="fas fa-check text-success"></i>
                                    }
                                </td>
                                <td class="text-center">
                                    @if (uplata.PovratNaSredstva == true)
                                    {
                                        <i class="fas fa-check text-success"></i>
                                    }
                                </td>
                                <td>@uplata.BrojNaKasovIzvestaj</td>
                                <td>@uplata.BankaNaziv</td>
                                <td>@uplata.KasaPrimi</td>
                                <td>@uplata.UplakjacNaziv</td>
                                <td>@uplata.BrojNaFaktura</td>
                                <td class="text-center">
                                    @if (uplata.PreraspredelenaUplata == true)
                                    {
                                        <i class="fas fa-check text-success"></i>
                                    }
                                </td>
                                <td class="text-center">
                                    @if (uplata.SredstvaOdPreraspredelenaUplata == true)
                                    {
                                        <i class="fas fa-check text-success"></i>
                                    }
                                </td>
                                <td>
                                    @if (uplata.SifrarnikTipNaPlakanjeId != 5 && uplata.SifrarnikTipNaPlakanjeId != 6)
                                    {
                                        <a href="/Finansii/UplatiGenerirajKasaPrimi/@uplata.Id" 
                                           class="btn btn-outline-info btn-sm px-1 py-0"
                                           @(Model.HasAdminAccess ? "" : "disabled")>
                                            <i class="fas fa-receipt me-1"></i> Каса прими
                                        </a>
                                    }
                                    <button type="button" class="btn btn-outline-danger btn-sm px-1 py-0"
                                            onclick="openStornoModal(@uplata.Id)"
                                            @(Model.StorniranjeUplati ? "" : "disabled")>
                                        <i class="fas fa-ban me-1"></i> Сторнирај
                                    </button>
                                    <button type="button" class="btn btn-outline-success btn-sm px-1 py-0"
                                            onclick="preraspredebaUplata(@uplata.Id)"
                                            @(Model.HasAdminAccess ? "" : "disabled")>
                                        <i class="fas fa-exchange-alt me-1"></i> Прераспредели
                                    </button>
                                    @if (!uplata.ImaPecatenaFiskalnaSmetka && uplata.SifrarnikTipNaPlakanjeId != 2 && uplata.SifrarnikTipNaPlakanjeId != 5 && uplata.SifrarnikTipNaPlakanjeId != 6 && uplata.SifrarnikTipNaPlakanjeId != 7)
                                    {
                                        <button type="button" class="btn btn-outline-primary btn-sm px-1 py-0"
                                                onclick="pechatiFiscalna(@uplata.Id, @uplata.Iznos)"
                                                @(Model.HasAdminAccess ? "" : "disabled")>
                                            <i class="fas fa-print me-1"></i> Печати фискална
                                        </button>
                                    }
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid mt-4 px-4">
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Сторнирани уплати</h5>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="stornoUplatiTable" class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ИД</th>
                            <th>Креирано на</th>
                            <th>Креирано од</th>
                            <th>Променето на</th>
                            <th>Променето од</th>
                            <th>Датум на уплата</th>
                            <th>Тип на плаќање</th>
                            <th>Број на извод</th>
                            <th class="text-end">Износ</th>
                            <th>Број на полиса</th>
                            <th class="text-center">Нераспределена</th>
                            <th class="text-center">Поврат на средства</th>
                            <th>Број на касов извештај</th>
                            <th>Банка</th>
                            <th>Каса прими</th>
                            <th>Уплаќач</th>
                            <th>Број на фактура</th>
                            <th class="text-center">Прераспределена</th>
                            <th class="text-center">Средства од прераспределена</th>
                            <th>Датум на сторно</th>
                            <th>Месец</th>
                            <th>Година</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var uplata in Model.StornoUplati)
                        {
                            <tr>
                                <td>@uplata.Id</td>
                                <td>@uplata.DateCreated?.ToString("dd.MM.yyyy HH:mm")</td>
                                <td>@uplata.UsernameCreated</td>
                                <td>@uplata.DateModified?.ToString("dd.MM.yyyy HH:mm")</td>
                                <td>@uplata.UsernameModified</td>
                                <td>@uplata.UplataDatum.ToString("dd.MM.yyyy")</td>
                                <td>@uplata.TipNaPlakanje</td>
                                <td>@uplata.BrojIzvod</td>
                                <td class="text-end">@uplata.Iznos.ToString("N2")</td>
                                <td>@uplata.PolisaBroj</td>
                                <td class="text-center">
                                    @if (uplata.Neraspredelena == true)
                                    {
                                        <i class="fas fa-check text-success"></i>
                                    }
                                </td>
                                <td class="text-center">
                                    @if (uplata.PovratNaSredstva == true)
                                    {
                                        <i class="fas fa-check text-success"></i>
                                    }
                                </td>
                                <td>@uplata.BrojNaKasovIzvestaj</td>
                                <td>@uplata.BankaNaziv</td>
                                <td>@uplata.KasaPrimi</td>
                                <td>@uplata.UplakjacNaziv</td>
                                <td>@uplata.BrojNaFaktura</td>
                                <td class="text-center">
                                    @if (uplata.PreraspredelenaUplata == true)
                                    {
                                        <i class="fas fa-check text-success"></i>
                                    }
                                </td>
                                <td class="text-center">
                                    @if (uplata.SredstvaOdPreraspredelenaUplata == true)
                                    {
                                        <i class="fas fa-check text-success"></i>
                                    }
                                </td>
                                <td>@uplata.DatumStorno?.ToString("dd.MM.yyyy")</td>
                                <td>@uplata.StornoZaMesec</td>
                                <td>@uplata.StornoZaGodina</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Modal -->
<div class="modal fade" id="addModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post">
                <div class="modal-header">
                    <h5 class="modal-title">Додај нова уплата</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label asp-for="Input.UplataDatum" class="form-label">Датум на уплата</label>
                            <input type="date" class="form-control" asp-for="Input.UplataDatum" required />
                            <span asp-validation-for="Input.UplataDatum" class="text-danger"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="Input.SifrarnikTipNaPlakanjeId" class="form-label">Тип на плаќање</label>
                            <select class="form-select" asp-for="Input.SifrarnikTipNaPlakanjeId" required>
                                <option value="">-- Изберете --</option>
                                @foreach (var tip in Model.TipoviNaPlakanje)
                                {
                                    <option value="@tip.Id">@tip.TipNaPlakanje</option>
                                }
                            </select>
                            <span asp-validation-for="Input.SifrarnikTipNaPlakanjeId" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label asp-for="Input.Iznos" class="form-label">Износ</label>
                            <input type="number" class="form-control" asp-for="Input.Iznos" step="0.0001" required />
                            <span asp-validation-for="Input.Iznos" class="text-danger"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="Input.PolisaBroj" class="form-label">Број на полиса</label>
                            <input type="text" class="form-control" asp-for="Input.PolisaBroj" />
                            <div id="polisaSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                                 style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                            </div>
                            <span asp-validation-for="Input.PolisaBroj" class="text-danger"></span>
                            <label class="form-label mt-2">Број на фактура</label>
                            <input type="text" class="form-control" asp-for="Input.BrojNaFaktura" />
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" asp-for="Input.Neraspredelena" />
                                <label class="form-check-label" asp-for="Input.Neraspredelena">Нераспределена</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" asp-for="Input.PovratNaSredstva" />
                                <label class="form-check-label" asp-for="Input.PovratNaSredstva">Поврат на средства</label>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label">Уплаќач (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                            <div class="input-group">
                                <input type="text" id="uplakjacMBSearch" class="form-control" 
                                       autocomplete="off"
                                       placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                                <input type="hidden" asp-for="Input.KlientIdUplakjac" id="KlientiIdUplakjac" />
                                <button class="btn btn-outline-secondary" type="button" onclick="openAddClientWindow('uplakjac')">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <div id="uplakjacSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                                 style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                            </div>
                            <span asp-validation-for="Input.KlientIdUplakjac" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Откажи</button>
                    <button type="submit" class="btn btn-primary">Зачувај</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Storno Modal -->
<div class="modal fade" id="stornoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Сторнирај уплата</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="stornoUplataId" />
                <div class="row mb-3">
                    <div class="col-md-12">
                        <label class="form-label">Датум на сторно</label>
                        <input type="date" class="form-control" id="datumStorno" required />
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Месец</label>
                        <input type="number" class="form-control" id="stornoZaMesec" min="1" max="12" required />
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Година</label>
                        <input type="number" class="form-control" id="stornoZaGodina" required />
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Откажи</button>
                <button type="button" class="btn btn-danger" onclick="stornoUplata()">Сторнирај</button>
            </div>
        </div>
    </div>
</div>

<!-- Custom Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle text-success me-2"></i>Успех
                </h5>
            </div>
            <div class="modal-body">
                <p id="successMessage">Фискалната сметка е успешно додадена во редот за печатење.</p>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        Можете да кликнете ОК за <span id="countdown">8</span> секунди...
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="successOkBtn" disabled onclick="closeSuccessModal()">
                    ОК (<span id="buttonCountdown">8</span>)
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let addModal;
        let stornoModal;
        
        $(document).ready(function() {
            addModal = new bootstrap.Modal(document.getElementById('addModal'));
            stornoModal = new bootstrap.Modal(document.getElementById('stornoModal'));
            
            $('#uplatiTable').DataTable({
                "order": [[0, "desc"]],
                "language": {
                    "lengthMenu": "Прикажи _MENU_ редови",
                    "zeroRecords": "Не се пронајдени записи",
                    "info": "Прикажани _START_ до _END_ од _TOTAL_ записи",
                    "infoEmpty": "Прикажани 0 до 0 од 0 записи",
                    "infoFiltered": "(филтрирано од вкупно _MAX_ записи)",
                    "search": "Пребарај:",
                    "paginate": {
                        "first": "Прва",
                        "last": "Последна",
                        "next": "Следна",
                        "previous": "Претходна"
                    }
                }
            });
            $('#stornoUplatiTable').DataTable({
                "order": [[0, "desc"]],
                "language": {
                    "lengthMenu": "Прикажи _MENU_ редови",
                    "zeroRecords": "Не се пронајдени записи",
                    "info": "Прикажани _START_ до _END_ од _TOTAL_ записи",
                    "infoEmpty": "Прикажани 0 до 0 од 0 записи",
                    "infoFiltered": "(филтрирано од вкупно _MAX_ записи)",
                    "search": "Пребарај:",
                    "paginate": {
                        "first": "Прва",
                        "last": "Последна",
                        "next": "Следна",
                        "previous": "Претходна"
                    }
                }
            });
        });

        let countdownInterval;
        let successModal;

        function showSuccessModal() {
            successModal = new bootstrap.Modal(document.getElementById('successModal'));
            successModal.show();
            
            let seconds = 8;
            document.getElementById('countdown').textContent = seconds;
            document.getElementById('buttonCountdown').textContent = seconds;
            document.getElementById('successOkBtn').disabled = true;
            
            countdownInterval = setInterval(function() {
                seconds--;
                document.getElementById('countdown').textContent = seconds;
                document.getElementById('buttonCountdown').textContent = seconds;
                
                if (seconds <= 0) {
                    clearInterval(countdownInterval);
                    document.getElementById('successOkBtn').disabled = false;
                    document.getElementById('successOkBtn').innerHTML = 'ОК';
                    document.querySelector('#successModal .modal-body small').style.display = 'none';
                }
            }, 1000);
        }

        function closeSuccessModal() {
            if (countdownInterval) {
                clearInterval(countdownInterval);
            }
            successModal.hide();
            location.reload();
        }
        
        function openAddModal() {
            addModal.show();
        }
        
        function openStornoModal(uplataId) {
            document.getElementById('stornoUplataId').value = uplataId;
            document.getElementById('datumStorno').value = new Date().toISOString().split('T')[0];
            document.getElementById('stornoZaMesec').value = new Date().getMonth() + 1;
            document.getElementById('stornoZaGodina').value = new Date().getFullYear();
            stornoModal.show();
        }
        
        function stornoUplata() {
            const uplataId = document.getElementById('stornoUplataId').value;
            const datumStorno = document.getElementById('datumStorno').value;
            const stornoZaMesec = document.getElementById('stornoZaMesec').value;
            const stornoZaGodina = document.getElementById('stornoZaGodina').value;
            
            if (!datumStorno || !stornoZaMesec || !stornoZaGodina) {
                alert('Пополнете ги сите полиња');
                return;
            }
            
            const data = {
                uplataId: uplataId,
                datumStorno: datumStorno,
                stornoZaMesec: parseInt(stornoZaMesec),
                stornoZaGodina: parseInt(stornoZaGodina)
            };
            
            fetch('?handler=StornoUplata', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    location.reload();
                } else {
                    alert('Грешка при сторнирање на уплатата: ' + result.message);
                }
            })
            .catch(error => {
                alert('Се појави грешка при сторнирање на уплатата');
            });
        }

        function preraspredebaUplata(uplataId) {
            if (confirm('Дали сте сигурни дека сакате да ја прераспределите оваа уплата?')) {
                fetch('?handler=PreraspredbaUplata', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({ uplataId: uplataId })
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        alert('Уплатата е успешно додадена во листата за прераспределба.');
                        location.reload();
                    } else {
                        alert('Грешка при прераспределба на уплатата: ' + result.message);
                    }
                })
                .catch(error => {
                    alert('Се појави грешка при прераспределба на уплатата');
                });
            }
        }

        function pechatiFiscalna(uplataId, iznos) {
            if (confirm('Дали сте сигурни дека сакате да ја печатите фискалната сметка за оваа уплата?')) {
                fetch('?handler=PechatiFiscalna', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({ uplataId: uplataId, iznos: iznos })
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        showSuccessModal();
                    } else {
                        alert('Грешка при додавање на фискалната сметка: ' + result.message);
                    }
                })
                .catch(error => {
                    alert('Се појави грешка при додавање на фискалната сметка');
                });
            }
        }

        function clearFilters() {
            document.querySelector('input[name="DatumOd"]').value = '';
            document.querySelector('input[name="DatumDo"]').value = '';
            document.querySelector('input[name="SearchPolisaBroj"]').value = '';
            document.querySelector('form').submit();
        }

        function openAddClientWindow(sourceField) {
            const width = 800;
            const height = 600;
            const left = (window.screen.width - width) / 2;
            const top = (window.screen.height - height) / 2;
            
            const popup = window.open(`/Klienti/DodajKlient?fromPolisa=true&source=${sourceField}`, 'DodajKlient', 
                `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`);
            
            window.addEventListener('message', function(event) {
                if (event.data.type === 'clientAdded') {
                    let displayText = '';
                    if (event.data.client.tip === 'P') {
                        displayText = `${event.data.client.naziv}`;
                        let identifiers = [];
                        if (event.data.client.mb) identifiers.push(`МБ: ${event.data.client.mb}`);
                        if (event.data.client.edb) identifiers.push(`ЕДБ: ${event.data.client.edb}`);
                        if (identifiers.length > 0) {
                            displayText += ` (${identifiers.join(', ')})`;
                        }
                    } else {
                        displayText = `${event.data.client.ime} ${event.data.client.prezime}`;
                        let identifiers = [];
                        if (event.data.client.embg) identifiers.push(`ЕМБГ: ${event.data.client.embg}`);
                        if (event.data.client.mb) identifiers.push(`МБ: ${event.data.client.mb}`);
                        if (identifiers.length > 0) {
                            displayText += ` (${identifiers.join(', ')})`;
                        }
                    }
                    if (event.data.source === 'uplakjac') {
                        $('#uplakjacMBSearch').val(displayText);
                        $('#KlientiIdUplakjac').val(event.data.client.id);
                        $('#uplakjacSearchResults').hide();
                    }
                }
            });
        }
        
        $(document).ready(function() {
            let searchTimeout;
            $('#uplakjacMBSearch').on('input', function() {
                clearTimeout(searchTimeout);
                const searchText = $(this).val();
                const resultsDiv = $('#uplakjacSearchResults');
                
                if (searchText.length < 2) {
                    resultsDiv.hide();
                    return;
                }
                
                searchTimeout = setTimeout(function() {
                    fetch(`?handler=SearchKlienti&searchTerm=${encodeURIComponent(searchText)}`, {
                        headers: {
                            'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                        }
                    })
                        .then(response => response.json())
                        .then(data => {
                            resultsDiv.empty();
                            
                            data.forEach(client => {
                                let displayText = '';
                                if (client.tip === 'P') {
                                    displayText = `${client.naziv}`;
                                    let identifiers = [];
                                    if (client.mb) identifiers.push(`МБ: ${client.mb}`);
                                    if (client.edb) identifiers.push(`ЕДБ: ${client.edb}`);
                                    if (identifiers.length > 0) {
                                        displayText += ` (${identifiers.join(', ')})`;
                                    }
                                } else {
                                    displayText = `${client.ime} ${client.prezime}`;
                                    let identifiers = [];
                                    if (client.embg) identifiers.push(`ЕМБГ: ${client.embg}`);
                                    if (client.mb) identifiers.push(`МБ: ${client.mb}`);
                                    if (identifiers.length > 0) {
                                        displayText += ` (${identifiers.join(', ')})`;
                                    }
                                }
                                
                                const div = $('<div>')
                                    .addClass('p-2 search-result-item')
                                    .text(displayText)
                                    .css('cursor', 'pointer')
                                    .hover(
                                        function() { $(this).addClass('bg-light'); },
                                        function() { $(this).removeClass('bg-light'); }
                                    )
                                    .click(function() {
                                        $('#uplakjacMBSearch').val(displayText);
                                        $('#KlientiIdUplakjac').val(client.id);
                                        resultsDiv.hide();
                                    });
                                
                                resultsDiv.append(div);
                            });
                            
                            if (data.length > 0) {
                                resultsDiv.show();
                            } else {
                                resultsDiv.hide();
                            }
                        });
                }, 300);
            });
            
            $(document).click(function(e) {
                if (!$(e.target).closest('#uplakjacMBSearch, #uplakjacSearchResults').length) {
                    $('#uplakjacSearchResults').hide();
                }
            });

            // Policy search functionality
            let policySearchTimeout;
            $('#Input_PolisaBroj').on('input', function() {
                clearTimeout(policySearchTimeout);
                const searchText = $(this).val();
                const resultsDiv = $('#polisaSearchResults');
                
                if (!searchText) {
                    resultsDiv.hide();
                    return;
                }
                
                policySearchTimeout = setTimeout(function() {
                    fetch(`?handler=SearchPolisi&searchTerm=${encodeURIComponent(searchText)}`, {
                        headers: {
                            'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        resultsDiv.empty();
                        
                        data.forEach(policy => {
                            const displayText = `Полиса: ${policy.brojNaPolisa}${policy.brojNaFakturaIzlezna ? ` | Фактура: ${policy.brojNaFakturaIzlezna}` : ''}`;
                            
                            const div = $('<div>')
                                .addClass('p-2 search-result-item')
                                .text(displayText)
                                .css('cursor', 'pointer')
                                .hover(
                                    function() { $(this).addClass('bg-light'); },
                                    function() { $(this).removeClass('bg-light'); }
                                )
                                .click(function() {
                                    $('#Input_PolisaBroj').val(policy.brojNaPolisa);
                                    $('#Input_BrojNaFaktura').val(policy.brojNaFakturaIzlezna || '');
                                    resultsDiv.hide();
                                });
                            
                            resultsDiv.append(div);
                        });
                        
                        if (data.length > 0) {
                            resultsDiv.show();
                        } else {
                            resultsDiv.hide();
                        }
                    });
                }, 300);
            });
            
            $(document).click(function(e) {
                if (!$(e.target).closest('#Input_PolisaBroj, #polisaSearchResults').length) {
                    $('#polisaSearchResults').hide();
                }
            });
        });
    </script>
} 