using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.Data;
using Microsoft.Data.SqlClient;
using System.ComponentModel.DataAnnotations;
using OfficeOpenXml;
using OfficeOpenXml.Style;

namespace NextBroker.Pages.Pregledi
{
    public class ASOMesecenIzvestajArhivaModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public ASOMesecenIzvestajArhivaModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
            Year = DateTime.Now.Year;
            SelectedMonth = DateTime.Now.Month;
        }

        [BindProperty]
        public int Year { get; set; }

        [BindProperty]
        public int SelectedMonth { get; set; }

        public DataTable ReportData { get; set; }

        public List<MonthModel> Months { get; } = new List<MonthModel>
        {
            new MonthModel { Value = 1, Name = "Јануари" },
            new MonthModel { Value = 2, Name = "Февруари" },
            new MonthModel { Value = 3, Name = "Март" },
            new MonthModel { Value = 4, Name = "Април" },
            new MonthModel { Value = 5, Name = "Мај" },
            new MonthModel { Value = 6, Name = "Јуни" },
            new MonthModel { Value = 7, Name = "Јули" },
            new MonthModel { Value = 8, Name = "Август" },
            new MonthModel { Value = 9, Name = "Септември" },
            new MonthModel { Value = 10, Name = "Октомври" },
            new MonthModel { Value = 11, Name = "Ноември" },
            new MonthModel { Value = 12, Name = "Декември" }
        };

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("ASOMesecenIzvestaj"))
            {
                return RedirectToAccessDenied();
            }
            
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!await HasPageAccess("ASOMesecenIzvestaj"))
            {
                return RedirectToAccessDenied();
            }

            var firstDayOfMonth = new DateTime(Year, SelectedMonth, 1);
            var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);

            string startDate = firstDayOfMonth.ToString("yyyy-MM-dd");
            string endDate = lastDayOfMonth.ToString("yyyy-MM-dd");

            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();

                var query = @"SELECT 
                    RB as 'Реден број',
                    NazivNaDrustvotoZaOsiguruvanje as 'Назив на друштвото за осигурување',
                    BrojNaPolisa as 'Број на полиса',
                    DatumNaPocetokNaPolisa as 'Датум на почеток на полиса',
                    DatumKrajNaOsiguruvanjeto as 'Датум крај на осигурувањето',
                    IznosNaBrutoPolisiranaPremija as 'Износ на бруто полисирана премија',
                    NacinNaPlakanje as 'Начин на плаќање',
                    IznoaNaNaplatenaPremijaVoMKD as 'Износ на наплатена премија во МКД',
                    DatumNaNaplataNaPremijata as 'Датум на наплата на премијата',
                    IznosNaPremijaKojaEPrenesenaVoDrustvotoZaOsiguruvanjeVoMKD as 'Износ на премија која е пренесена во друштвото за осигурување во МКД',
                    DatumNaPrenosNaPremijataVoDrustvotoZaOsiguruvanje as 'Датум на пренос на премијата во друштвото за осигурување',
                    OsnovZaIzvrseniotPrenosBrNaDokument as 'Основ за извршениот пренос бр. на документ',
                    DatumNaDokumentotZaPrenosNaNaplatenataPremija as 'Датум на документот за пренос на наплатената премија'
                FROM ASOMesecenIzvestajArhivaNaIzvestai 
                WHERE DatumIzvestajStartDate = @StartDate 
                AND DatumIzvestajEndDate = @EndDate
                ORDER BY RB";

                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@StartDate", startDate);
                    command.Parameters.AddWithValue("@EndDate", endDate);

                    using (var adapter = new SqlDataAdapter(command))
                    {
                        ReportData = new DataTable();
                        adapter.Fill(ReportData);
                    }
                }
            }

            return Page();
        }

        public async Task<IActionResult> OnPostExportExcelAsync()
        {
            if (!await HasPageAccess("ASOMesecenIzvestaj"))
            {
                return RedirectToAccessDenied();
            }

            var firstDayOfMonth = new DateTime(Year, SelectedMonth, 1);
            var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);

            string startDate = firstDayOfMonth.ToString("yyyy-MM-dd");
            string endDate = lastDayOfMonth.ToString("yyyy-MM-dd");

            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();

                var query = @"SELECT 
                    RB as 'Реден број',
                    NazivNaDrustvotoZaOsiguruvanje as 'Назив на друштвото за осигурување',
                    BrojNaPolisa as 'Број на полиса',
                    DatumNaPocetokNaPolisa as 'Датум на почеток на полиса',
                    DatumKrajNaOsiguruvanjeto as 'Датум крај на осигурувањето',
                    IznosNaBrutoPolisiranaPremija as 'Износ на бруто полисирана премија',
                    NacinNaPlakanje as 'Начин на плаќање',
                    IznoaNaNaplatenaPremijaVoMKD as 'Износ на наплатена премија во МКД',
                    DatumNaNaplataNaPremijata as 'Датум на наплата на премијата',
                    IznosNaPremijaKojaEPrenesenaVoDrustvotoZaOsiguruvanjeVoMKD as 'Износ на премија која е пренесена во друштвото за осигурување во МКД',
                    DatumNaPrenosNaPremijataVoDrustvotoZaOsiguruvanje as 'Датум на пренос на премијата во друштвото за осигурување',
                    OsnovZaIzvrseniotPrenosBrNaDokument as 'Основ за извршениот пренос бр. на документ',
                    DatumNaDokumentotZaPrenosNaNaplatenataPremija as 'Датум на документот за пренос на наплатената премија'
                FROM ASOMesecenIzvestajArhivaNaIzvestai 
                WHERE DatumIzvestajStartDate = @StartDate 
                AND DatumIzvestajEndDate = @EndDate
                ORDER BY RB";

                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@StartDate", startDate);
                    command.Parameters.AddWithValue("@EndDate", endDate);

                    using (var adapter = new SqlDataAdapter(command))
                    {
                        var dt = new DataTable();
                        adapter.Fill(dt);

                        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                        using (var package = new ExcelPackage())
                        {
                            var worksheet = package.Workbook.Worksheets.Add("АСО Извештај Архива");

                            // Add report header
                            worksheet.Cells["A1"].Value = "Образец обд 4";
                            worksheet.Cells["A1:E1"].Merge = true;
                            worksheet.Cells["A1"].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                            worksheet.Cells["A1"].Style.Font.Bold = true;

                            worksheet.Cells["A3"].Value = "Осигурително брокерско друштво";
                            worksheet.Cells["E3"].Value = "ОБД ИНКО АД СКОПЈЕ";

                            worksheet.Cells["A6"].Value = "Година:";
                            worksheet.Cells["B6"].Value = Year;

                            worksheet.Cells["A7"].Value = "Месец:";
                            worksheet.Cells["B7"].Value = Months.First(m => m.Value == SelectedMonth).Name;

                            // Add some spacing before the table
                            int tableStartRow = 10;

                            // Add headers
                            for (int i = 0; i < dt.Columns.Count; i++)
                            {
                                var cell = worksheet.Cells[tableStartRow, i + 1];
                                cell.Value = dt.Columns[i].ColumnName;
                                cell.Style.Font.Bold = true;
                                cell.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                                cell.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                                cell.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                                cell.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                            }

                            // Add data
                            for (int row = 0; row < dt.Rows.Count; row++)
                            {
                                for (int col = 0; col < dt.Columns.Count; col++)
                                {
                                    var cellValue = dt.Rows[row][col];
                                    var cell = worksheet.Cells[row + tableStartRow + 1, col + 1];

                                    if (cellValue is DateTime dateValue)
                                    {
                                        cell.Style.Numberformat.Format = "dd/mm/yyyy";
                                        cell.Value = dateValue;
                                    }
                                    else if (cellValue is decimal || cellValue is double || cellValue is float)
                                    {
                                        cell.Style.Numberformat.Format = "#,##0.00";
                                        cell.Value = cellValue;
                                    }
                                    else
                                    {
                                        cell.Value = cellValue;
                                    }

                                    // Add borders to data cells
                                    cell.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                                    cell.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                                    cell.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                                    cell.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                                }
                            }

                            // Auto-fit columns
                            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                            // Generate the Excel file
                            var content = package.GetAsByteArray();
                            var monthName = Months.First(m => m.Value == SelectedMonth).Name;
                            var fileName = $"ASO_Izvestaj_Arhiva_{monthName}_{Year}.xlsx";

                            return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                        }
                    }
                }
            }
        }
    }

    public class ArhivaMonthModel
    {
        public int Value { get; set; }
        public string Name { get; set; }
    }
}
