@page
@model NextBroker.Pages.Provizija.ProvizijaSetiranjeTablicaModel
@{
    ViewData["Title"] = "Табличен преглед на провизии";
}

<style>
    .freeze-table-container {
        position: relative;
        overflow: auto;
        max-height: 70vh;
        max-width: 100%;
    }
    
    .freeze-table {
        border-collapse: separate;
        border-spacing: 0;
    }
    
    .freeze-table th,
    .freeze-table td {
        padding: 8px;
        white-space: nowrap;
        border: 1px solid #dee2e6;
    }
    
    .freeze-table thead th {
        position: sticky;
        top: 0;
        z-index: 20;
        background-color: #f8f9fa;
    }
    
    .freeze-table .sticky-col {
        position: sticky;
        left: 0;
        z-index: 10;
        background-color: #f8f9fa;
    }
    
    .freeze-table thead th.sticky-col {
        z-index: 30;
    }
    
    .tick-mark {
        color: #28a745;
        font-size: 1.2em;
        font-weight: bold;
    }
    
    .cross-mark {
        color: #dc3545;
        font-size: 1.2em;
        font-weight: bold;
    }
    
    .freeze-table td {
        text-align: center;
    }
    
    .freeze-table td.sticky-col {
        text-align: left;
    }
    
    #klientSearchResults .list-group-item.text-center {
        padding: 1rem;
    }
    #klientSearchResults .list-group-item.text-center p {
        color: #6c757d;
        margin-bottom: 0.75rem;
    }
    #klientSearchResults .btn-primary {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
</style>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>@ViewData["Title"]</h1>
        <div>
            <a asp-page="./ProvizijaSetiranje" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i> Назад кон сетирање
            </a>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <h5>Пребарување по клиент</h5>
        </div>
        <div class="card-body">
            <form method="post">
                @Html.AntiForgeryToken()
                <div class="form-group row">
                    <label for="klientSearch" class="col-sm-3 col-form-label">Клиент:</label>
                    <div class="col-sm-9">
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" data-target="klient" style="width: 30px; height: 38px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="klientSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                            <input type="hidden" asp-for="KlientiId" id="KlientiId" />
                        </div>
                        <div id="klientSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div class="form-group row mt-3">
                    <div class="col-sm-9 offset-sm-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Пребарај
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    @if (Model.IsDataLoaded)
    {
        <div class="freeze-table-container">
            <table class="table table-striped freeze-table">
                <thead>
                    <tr>
                        @{
                            var isFirstColumn = true;
                            foreach (System.Data.DataColumn column in Model.ResultTable.Columns)
                            {
                                if (isFirstColumn)
                                {
                                    <th class="sticky-col">@column.ColumnName</th>
                                    isFirstColumn = false;
                                }
                                else
                                {
                                    <th>@column.ColumnName</th>
                                }
                            }
                        }
                    </tr>
                </thead>
                <tbody>
                    @foreach (System.Data.DataRow row in Model.ResultTable.Rows)
                    {
                        <tr>
                            @{
                                isFirstColumn = true;
                                int colIndex = 0;
                                foreach (var item in row.ItemArray)
                                {
                                    if (isFirstColumn)
                                    {
                                        <td class="sticky-col">@item</td>
                                        isFirstColumn = false;
                                    }
                                    else
                                    {
                                        string valueStr = item?.ToString() ?? "";
                                        if (valueStr == "0")
                                        {
                                            <td><span class="cross-mark"><i class="fas fa-times"></i></span></td>
                                        }
                                        else if (valueStr == "1")
                                        {
                                            <td><span class="tick-mark"><i class="fas fa-check"></i></span></td>
                                        }
                                        else
                                        {
                                            <td>@item</td>
                                        }
                                    }
                                    colIndex++;
                                }
                            }
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add touch-based scrolling for mobile and tablet users
            const tableContainer = document.querySelector('.freeze-table-container');
            if (tableContainer) {
                let isScrolling = false;
                let startX, startY, scrollLeft, scrollTop;
                
                tableContainer.addEventListener('mousedown', function(e) {
                    isScrolling = true;
                    startX = e.pageX - tableContainer.offsetLeft;
                    startY = e.pageY - tableContainer.offsetTop;
                    scrollLeft = tableContainer.scrollLeft;
                    scrollTop = tableContainer.scrollTop;
                });
                
                tableContainer.addEventListener('mouseleave', function() {
                    isScrolling = false;
                });
                
                tableContainer.addEventListener('mouseup', function() {
                    isScrolling = false;
                });
                
                tableContainer.addEventListener('mousemove', function(e) {
                    if (!isScrolling) return;
                    e.preventDefault();
                    
                    const x = e.pageX - tableContainer.offsetLeft;
                    const y = e.pageY - tableContainer.offsetTop;
                    const walkX = (x - startX) * 1.5;
                    const walkY = (y - startY) * 1.5;
                    
                    tableContainer.scrollLeft = scrollLeft - walkX;
                    tableContainer.scrollTop = scrollTop - walkY;
                });
            }
            
            // Client search functionality
            const searchInput = document.getElementById('klientSearch');
            const resultsDiv = document.getElementById('klientSearchResults');
            const hiddenInput = document.getElementById('KlientiId');
            let searchTimeout;
            
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const searchTerm = this.value;
                
                if (searchTerm.length < 1) {
                    resultsDiv.style.display = 'none';
                    return;
                }
                
                searchTimeout = setTimeout(function() {
                    fetch(`?handler=SearchKlienti&mb=${encodeURIComponent(searchTerm)}`, {
                        headers: {
                            "RequestVerificationToken": document.querySelector('input[name="__RequestVerificationToken"]').value
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data && data.length > 0) {
                            let html = '<div class="list-group">';
                            data.forEach(item => {
                                let displayText = '';
                                if (item.tip === 'P') {
                                    displayText = `${item.naziv}`;
                                    let identifiers = [];
                                    if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                    if (item.edb) identifiers.push(`ЕДБ: ${item.edb}`);
                                    if (identifiers.length > 0) {
                                        displayText += ` (${identifiers.join(', ')})`;
                                    }
                                } else {
                                    displayText = `${item.ime} ${item.prezime}`;
                                    let identifiers = [];
                                    if (item.embg) identifiers.push(`ЕМБГ: ${item.embg}`);
                                    if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                    if (identifiers.length > 0) {
                                        displayText += ` (${identifiers.join(', ')})`;
                                    }
                                }
                                html += `<a href="#" class="list-group-item list-group-item-action" data-id="${item.id}">
                                          ${displayText}
                                       </a>`;
                            });
                            html += '</div>';
                            resultsDiv.innerHTML = html;
                            resultsDiv.style.display = 'block';
                        } else {
                            resultsDiv.innerHTML = `
                                <div class="list-group">
                                    <div class="list-group-item text-center">
                                        <p class="mb-2">Нема пронајдени резултати</p>
                                    </div>
                                </div>
                            `;
                            resultsDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        resultsDiv.innerHTML = `
                            <div class="list-group">
                                <div class="list-group-item text-center">
                                    <p class="text-danger mb-2">Грешка при пребарување</p>
                                </div>
                            </div>
                        `;
                        resultsDiv.style.display = 'block';
                    });
                }, 300);
            });
            
            // Handle selection
            document.addEventListener('click', function(e) {
                if (e.target.closest('#klientSearchResults .list-group-item')) {
                    e.preventDefault();
                    const item = e.target.closest('.list-group-item');
                    const id = item.dataset.id;
                    const displayText = item.textContent.trim();
                    searchInput.value = displayText;
                    hiddenInput.value = id;
                    resultsDiv.style.display = 'none';
                }
            });
            
            // Hide results when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('#klientSearch') && !e.target.closest('#klientSearchResults')) {
                    resultsDiv.style.display = 'none';
                }
            });
            
            // Clear field button
            document.querySelector('.clear-field').addEventListener('click', function() {
                searchInput.value = '';
                hiddenInput.value = '';
                resultsDiv.style.display = 'none';
            });
        });
    </script>
}
