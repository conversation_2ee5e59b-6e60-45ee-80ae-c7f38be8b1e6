using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using Microsoft.Data.SqlClient;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace NextBroker.Pages.AnalizaNaKlient
{
    public class RiziciModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public RiziciModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public List<SelectListItem> Rizici { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> Produkti { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> Osiguriteli { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> KlasiOsiguruvanje { get; set; } = new List<SelectListItem>();

        public class RizikViewModel
        {
            public long Id { get; set; }
            public string Naziv { get; set; }
            public string Tip { get; set; }
            public int? ProduktId { get; set; }
            public string ProduktIme { get; set; }
            public int? KlasiOsiguruvanjeIdRizik { get; set; }
            public string KlasaIme { get; set; }
        }

        public class PravnoEkonomskiOdnosiViewModel
        {
            public long Id { get; set; }
            public long KlientiIdAnalizaDogovoruvac { get; set; }
            public string OsiguritelNaziv { get; set; }
            public string PravnoEkonomskiOdnosi { get; set; }
        }

        public List<RizikViewModel> ListaRizici { get; set; } = new List<RizikViewModel>();
        public List<PravnoEkonomskiOdnosiViewModel> ListaPravnoEkonomskiOdnosi { get; set; } = new List<PravnoEkonomskiOdnosiViewModel>();
        public List<RiziciPoOsiguritelViewModel> ListaRiziciPoOsiguritel { get; set; } = new List<RiziciPoOsiguritelViewModel>();

        public class RiziciPoOsiguritelViewModel
        {
            public long Id { get; set; }
            public string OsiguritelNaziv { get; set; }
            public string RizikNaziv { get; set; }
            public bool Pokrieno { get; set; }
            public string Beleshka { get; set; }
            public long KlientiIdAnalizaDogovoruvac { get; set; }
            public int? ProduktId { get; set; }
            public string ProduktIme { get; set; }
            public string KlasaIme { get; set; }
        }

        public class AnalizaNaKlientRizici
        {
            public long Id { get; set; }
            public long RizikIdRizici { get; set; }
            public long KlientiIdAnalizaDogovoruvac { get; set; }
            public bool Pokrieno { get; set; }
            public string Beleshka { get; set; }
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("Rizici"))
            {
                return RedirectToAccessDenied();
            }

            // Load risks with their types and product names
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                
                var query = @"
                    SELECT r.Id, r.Naziv, r.Tip, r.ProduktId, p.Ime as ProduktIme, 
                           r.KlasiOsiguruvanjeIdRizik, k.KlasaBroj, k.KlasaIme
                    FROM Rizici r
                    LEFT JOIN Produkti p ON r.ProduktId = p.Id
                    LEFT JOIN KlasiOsiguruvanje k ON r.KlasiOsiguruvanjeIdRizik = k.Id
                    ORDER BY r.Naziv";

                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            ListaRizici.Add(new RizikViewModel
                            {
                                Id = reader.GetInt64(0),
                                Naziv = reader.GetString(1),
                                Tip = reader.IsDBNull(2) ? null : reader.GetString(2),
                                ProduktId = reader.IsDBNull(3) ? null : reader.GetInt32(3),
                                ProduktIme = reader.IsDBNull(4) ? null : reader.GetString(4),
                                KlasiOsiguruvanjeIdRizik = reader.IsDBNull(5) ? null : reader.GetInt32(5),
                                KlasaIme = reader.IsDBNull(7) ? null : reader.GetString(7)
                            });
                        }
                    }
                }

                // Load products for dropdown
                query = @"
                    SELECT Id, Ime
                    FROM Produkti
                    ORDER BY Ime";

                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            Produkti.Add(new SelectListItem
                            {
                                Value = reader.GetInt32(0).ToString(),
                                Text = reader.GetString(1)
                            });
                        }
                    }
                }

                // Load KlasiOsiguruvanje for dropdown
                query = @"
                    SELECT Id, KlasaBroj, KlasaIme
                    FROM KlasiOsiguruvanje
                    WHERE Disabled = 0 OR Disabled IS NULL
                    ORDER BY KlasaBroj";

                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            KlasiOsiguruvanje.Add(new SelectListItem
                            {
                                Value = reader.GetInt32(0).ToString(),
                                Text = $"{reader.GetInt32(1)} - {reader.GetString(2)}"
                            });
                        }
                    }
                }

                // Load insurers for the dropdown
                query = @"
                    SELECT Id, 
                           CASE 
                               WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                               WHEN KlientFizickoPravnoLice = 'F' THEN CONCAT(Ime, ' ', Prezime)
                               ELSE CAST(Id as nvarchar(20))
                           END as DisplayName
                    FROM Klienti 
                    WHERE Osiguritel = 1 
                    ORDER BY DisplayName";

                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            Osiguriteli.Add(new SelectListItem
                            {
                                Value = reader.GetInt64(0).ToString(),
                                Text = reader.GetString(1)
                            });
                        }
                    }
                }

                // Load Pravno Ekonomski Odnosi data
                var pravnoEkonomskiQuery = @"
                    SELECT a.Id, a.KlientiIdAnalizaDogovoruvac, a.PravnoEkonomskiOdnosiOsiguritel,
                           CASE 
                               WHEN k.KlientFizickoPravnoLice = 'P' THEN k.Naziv
                               WHEN k.KlientFizickoPravnoLice = 'F' THEN CONCAT(k.Ime, ' ', k.Prezime)
                               ELSE CAST(k.Id as nvarchar(20))
                           END as OsiguritelNaziv
                    FROM AnalizaOsiguritelPravnoEkonomskiOdnosi a
                    LEFT JOIN Klienti k ON a.KlientiIdAnalizaDogovoruvac = k.Id
                    ORDER BY OsiguritelNaziv";

                using (var command = new SqlCommand(pravnoEkonomskiQuery, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            ListaPravnoEkonomskiOdnosi.Add(new PravnoEkonomskiOdnosiViewModel
                            {
                                Id = reader.GetInt64(0),
                                KlientiIdAnalizaDogovoruvac = reader.GetInt64(1),
                                PravnoEkonomskiOdnosi = reader.IsDBNull(2) ? null : reader.GetString(2),
                                OsiguritelNaziv = reader.IsDBNull(3) ? null : reader.GetString(3)
                            });
                        }
                    }
                }
            }

            // Load Rizici po Osiguritel data
            var riziciPoOsiguritelQuery = @"
                SELECT a.Id, 
                       CASE 
                           WHEN k.KlientFizickoPravnoLice = 'P' THEN k.Naziv
                           WHEN k.KlientFizickoPravnoLice = 'F' THEN CONCAT(k.Ime, ' ', k.Prezime)
                           ELSE CAST(k.Id as nvarchar(20))
                       END as OsiguritelNaziv,
                       r.Naziv as RizikNaziv,
                       a.Pokrieno,
                       a.Beleshka,
                       r.ProduktId,
                       a.KlientiIdAnalizaDogovoruvac,
                       p.Ime as ProduktIme,
                       ko.KlasaIme as KlasaIme
                FROM AnalizaNaKlientRizici a
                LEFT JOIN Klienti k ON a.KlientiIdAnalizaDogovoruvac = k.Id
                LEFT JOIN Rizici r ON a.RizikIdRizici = r.Id
                LEFT JOIN Produkti p ON r.ProduktId = p.Id
                LEFT JOIN KlasiOsiguruvanje ko ON r.KlasiOsiguruvanjeIdRizik = ko.Id
                ORDER BY a.Id DESC";

            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using (var command = new SqlCommand(riziciPoOsiguritelQuery, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            ListaRiziciPoOsiguritel.Add(new RiziciPoOsiguritelViewModel
                            {
                                Id = reader.GetInt64(0),
                                OsiguritelNaziv = reader.IsDBNull(1) ? null : reader.GetString(1),
                                RizikNaziv = reader.IsDBNull(2) ? null : reader.GetString(2),
                                Pokrieno = reader.GetBoolean(3),
                                Beleshka = reader.IsDBNull(4) ? null : reader.GetString(4),
                                ProduktId = reader.IsDBNull(5) ? (int?)null : reader.GetInt32(5),
                                KlientiIdAnalizaDogovoruvac = reader.GetInt64(6),
                                ProduktIme = reader.IsDBNull(7) ? null : reader.GetString(7),
                                KlasaIme = reader.IsDBNull(8) ? null : reader.GetString(8)
                            });
                        }
                    }
                }
            }

            return Page();
        }

        public async Task<IActionResult> OnGetRizikAsync(long id)
        {
            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    var sql = @"
                        SELECT r.Id, r.Naziv, r.Tip, r.ProduktId, r.KlasiOsiguruvanjeIdRizik
                        FROM Rizici r
                        WHERE r.Id = @Id";

                    using (var cmd = new SqlCommand(sql, connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", id);
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                return new JsonResult(new
                                {
                                    success = true,
                                    rizik = new
                                    {
                                        id = reader.GetInt64(0),
                                        naziv = reader.GetString(1),
                                        tip = reader.IsDBNull(2) ? null : reader.GetString(2),
                                        produktId = reader.IsDBNull(3) ? 0 : reader.GetInt32(3),
                                        klasaId = reader.IsDBNull(4) ? 0 : reader.GetInt32(4)
                                    }
                                });
                            }
                        }
                    }
                }

                return new JsonResult(new { success = false, message = "Ризикот не е пронајден" });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        public async Task<IActionResult> OnPostAddRizikAsync([FromBody] System.Text.Json.JsonElement data)
        {
            try
            {
                string naziv = data.GetProperty("naziv").GetString();
                string tip = data.GetProperty("tip").GetString();
                string produktIdStr = data.GetProperty("produktId").GetString();
                string klasaIdStr = data.GetProperty("klasaId").GetString();

                if (string.IsNullOrEmpty(naziv))
                {
                    return new JsonResult(new { success = false, message = "Називот на ризикот е задолжителен" });
                }

                if (string.IsNullOrEmpty(tip))
                {
                    return new JsonResult(new { success = false, message = "Типот на ризикот е задолжителен" });
                }

                if (string.IsNullOrEmpty(produktIdStr))
                {
                    return new JsonResult(new { success = false, message = "Продуктот е задолжителен" });
                }

                if (string.IsNullOrEmpty(klasaIdStr))
                {
                    return new JsonResult(new { success = false, message = "Класата е задолжителна" });
                }

                if (!int.TryParse(produktIdStr, out int produktId))
                {
                    return new JsonResult(new { success = false, message = "Невалиден ID на продукт" });
                }

                if (!int.TryParse(klasaIdStr, out int klasaId))
                {
                    return new JsonResult(new { success = false, message = "Невалиден ID на класа" });
                }

                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();

                    // Check if risk with same name exists
                    var checkSql = "SELECT COUNT(*) FROM Rizici WHERE Naziv = @Naziv";
                    using (var cmd = new SqlCommand(checkSql, connection))
                    {
                        cmd.Parameters.AddWithValue("@Naziv", naziv);
                        var exists = (int)await cmd.ExecuteScalarAsync() > 0;
                        if (exists)
                        {
                            return new JsonResult(new { success = false, message = "Ризик со ист назив веќе постои" });
                        }
                    }

                    // Insert new risk
                    var sql = @"
                        INSERT INTO Rizici (Naziv, Tip, ProduktId, KlasiOsiguruvanjeIdRizik)
                        OUTPUT INSERTED.Id, INSERTED.Naziv
                        VALUES (@Naziv, @Tip, @ProduktId, @KlasiOsiguruvanjeIdRizik)";

                    using (var cmd = new SqlCommand(sql, connection))
                    {
                        cmd.Parameters.AddWithValue("@Naziv", naziv);
                        cmd.Parameters.AddWithValue("@Tip", tip);
                        cmd.Parameters.AddWithValue("@ProduktId", produktId);
                        cmd.Parameters.AddWithValue("@KlasiOsiguruvanjeIdRizik", klasaId);
                        
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                return new JsonResult(new { 
                                    success = true, 
                                    id = reader.GetInt64(0).ToString(),
                                    naziv = reader.GetString(1)
                                });
                            }
                        }
                    }
                }

                return new JsonResult(new { success = false, message = "Грешка при зачувување на ризик" });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        public async Task<IActionResult> OnPostDeleteRizikAsync([FromBody] System.Text.Json.JsonElement data)
        {
            try
            {
                if (!data.TryGetProperty("id", out JsonElement idElement))
                {
                    return new JsonResult(new { success = false, message = "ID на ризик е задолжителен" });
                }

                long id = idElement.GetInt64();

                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    
                    // First delete associated rizik po osiguritel records
                    var deleteAssociatedSql = "DELETE FROM AnalizaNaKlientRizici WHERE RizikIdRizici = @Id";
                    using (var cmd = new SqlCommand(deleteAssociatedSql, connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", id);
                        await cmd.ExecuteNonQueryAsync();
                    }

                    // Then delete the risk itself
                    var sql = "DELETE FROM Rizici WHERE Id = @Id";
                    using (var cmd = new SqlCommand(sql, connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", id);
                        int rowsAffected = await cmd.ExecuteNonQueryAsync();
                        if (rowsAffected == 0)
                        {
                            return new JsonResult(new { success = false, message = "Ризикот не е пронајден" });
                        }
                    }
                }

                return new JsonResult(new { success = true, message = "Ризикот и сите поврзани записи се успешно избришани" });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        public async Task<IActionResult> OnPostUpdateRizikAsync([FromBody] System.Text.Json.JsonElement data)
        {
            try
            {
                string idStr = data.GetProperty("id").GetString();
                string naziv = data.GetProperty("naziv").GetString();
                string tip = data.GetProperty("tip").GetString();
                string produktIdStr = data.GetProperty("produktId").GetString();
                string klasaIdStr = data.GetProperty("klasaId").GetString();

                if (string.IsNullOrEmpty(idStr) || !long.TryParse(idStr, out long id))
                {
                    return new JsonResult(new { success = false, message = "Невалиден ID на ризик" });
                }

                if (string.IsNullOrEmpty(naziv))
                {
                    return new JsonResult(new { success = false, message = "Називот на ризикот е задолжителен" });
                }

                if (string.IsNullOrEmpty(tip))
                {
                    return new JsonResult(new { success = false, message = "Типот на ризикот е задолжителен" });
                }

                if (!int.TryParse(produktIdStr, out int produktId))
                {
                    return new JsonResult(new { success = false, message = "Невалиден ID на продукт" });
                }

                if (!int.TryParse(klasaIdStr, out int klasaId))
                {
                    return new JsonResult(new { success = false, message = "Невалиден ID на класа" });
                }

                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();

                    // Check if risk with same name exists (excluding current risk)
                    var checkSql = "SELECT COUNT(*) FROM Rizici WHERE Naziv = @Naziv AND Id != @Id";
                    using (var cmd = new SqlCommand(checkSql, connection))
                    {
                        cmd.Parameters.AddWithValue("@Naziv", naziv);
                        cmd.Parameters.AddWithValue("@Id", id);
                        var exists = (int)await cmd.ExecuteScalarAsync() > 0;
                        if (exists)
                        {
                            return new JsonResult(new { success = false, message = "Ризик со ист назив веќе постои" });
                        }
                    }

                    // Update risk
                    var sql = @"
                        UPDATE Rizici 
                        SET Naziv = @Naziv, 
                            Tip = @Tip, 
                            ProduktId = @ProduktId,
                            KlasiOsiguruvanjeIdRizik = @KlasiOsiguruvanjeIdRizik
                        WHERE Id = @Id";

                    using (var cmd = new SqlCommand(sql, connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", id);
                        cmd.Parameters.AddWithValue("@Naziv", naziv);
                        cmd.Parameters.AddWithValue("@Tip", tip);
                        cmd.Parameters.AddWithValue("@ProduktId", produktId);
                        cmd.Parameters.AddWithValue("@KlasiOsiguruvanjeIdRizik", klasaId);
                        await cmd.ExecuteNonQueryAsync();
                    }
                }

                return new JsonResult(new { success = true });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        public async Task<IActionResult> OnGetPravnoEkonomskiOdnosi([FromQuery] long insurerId)
        {
            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT TOP 1 PravnoEkonomskiOdnosiOsiguritel
                        FROM AnalizaOsiguritelPravnoEkonomskiOdnosi
                        WHERE KlientiIdAnalizaDogovoruvac = @insurerId";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@insurerId", insurerId);
                        var result = await command.ExecuteScalarAsync();
                        
                        return new JsonResult(new
                        {
                            pravnoEkonomskiOdnosi = result?.ToString()
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { error = ex.Message }) { StatusCode = 500 };
            }
        }

        [HttpPost]
        public async Task<IActionResult> OnPostSavePravnoEkonomskiOdnosi([FromBody] SavePravnoEkonomskiOdnosiRequest request)
        {
            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();

                    // Check if record already exists
                    var checkQuery = @"
                        SELECT TOP 1 Id
                        FROM AnalizaOsiguritelPravnoEkonomskiOdnosi
                        WHERE KlientiIdAnalizaDogovoruvac = @insurerId";

                    using (var checkCommand = new SqlCommand(checkQuery, connection))
                    {
                        checkCommand.Parameters.AddWithValue("@insurerId", request.InsurerId);
                        var existingId = await checkCommand.ExecuteScalarAsync();

                        if (existingId != null)
                        {
                            return new JsonResult(new { success = false, message = "Веќе постои запис за овој осигурител" });
                        }
                    }

                    // Insert new record
                    var insertQuery = @"
                        INSERT INTO AnalizaOsiguritelPravnoEkonomskiOdnosi (KlientiIdAnalizaDogovoruvac, PravnoEkonomskiOdnosiOsiguritel)
                        VALUES (@insurerId, @pravnoEkonomskiOdnosi)";

                    using (var command = new SqlCommand(insertQuery, connection))
                    {
                        command.Parameters.AddWithValue("@insurerId", request.InsurerId);
                        command.Parameters.AddWithValue("@pravnoEkonomskiOdnosi", request.PravnoEkonomskiOdnosi);
                        await command.ExecuteNonQueryAsync();
                        return new JsonResult(new { success = true });
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        public class SavePravnoEkonomskiOdnosiRequest
        {
            public long InsurerId { get; set; }
            public string PravnoEkonomskiOdnosi { get; set; }
        }

        public async Task<IActionResult> OnPostDeletePravnoEkonomskiOdnosiAsync([FromBody] System.Text.Json.JsonElement data)
        {
            try
            {
                if (!data.TryGetProperty("id", out JsonElement idElement))
                {
                    return new JsonResult(new { success = false, message = "ID на правно-економски односи е задолжителен" });
                }

                long id = idElement.GetInt64();

                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    var sql = "DELETE FROM AnalizaOsiguritelPravnoEkonomskiOdnosi WHERE Id = @Id";
                    using (var cmd = new SqlCommand(sql, connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", id);
                        int rowsAffected = await cmd.ExecuteNonQueryAsync();
                        if (rowsAffected == 0)
                        {
                            return new JsonResult(new { success = false, message = "Записот не е пронајден" });
                        }
                    }
                }

                return new JsonResult(new { success = true, message = "Записот е успешно избришан" });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        public async Task<IActionResult> OnGetPravnoEkonomskiOdnosiByIdAsync(long id)
        {
            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    var sql = @"
                        SELECT a.Id, a.KlientiIdAnalizaDogovoruvac, a.PravnoEkonomskiOdnosiOsiguritel,
                               CASE 
                                   WHEN k.KlientFizickoPravnoLice = 'P' THEN k.Naziv
                                   WHEN k.KlientFizickoPravnoLice = 'F' THEN CONCAT(k.Ime, ' ', k.Prezime)
                                   ELSE CAST(k.Id as nvarchar(20))
                               END as OsiguritelNaziv
                        FROM AnalizaOsiguritelPravnoEkonomskiOdnosi a
                        LEFT JOIN Klienti k ON a.KlientiIdAnalizaDogovoruvac = k.Id
                        WHERE a.Id = @Id";

                    using (var cmd = new SqlCommand(sql, connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", id);
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                return new JsonResult(new
                                {
                                    success = true,
                                    data = new
                                    {
                                        id = reader.GetInt64(0),
                                        klientiIdAnalizaDogovoruvac = reader.GetInt64(1),
                                        pravnoEkonomskiOdnosi = reader.IsDBNull(2) ? null : reader.GetString(2),
                                        osiguritelNaziv = reader.IsDBNull(3) ? null : reader.GetString(3)
                                    }
                                });
                            }
                        }
                    }
                }

                return new JsonResult(new { success = false, message = "Записот не е пронајден" });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        public async Task<IActionResult> OnPostUpdatePravnoEkonomskiOdnosiAsync([FromBody] System.Text.Json.JsonElement data)
        {
            try
            {
                string idStr = data.GetProperty("id").GetString();
                string klientiIdStr = data.GetProperty("klientiIdAnalizaDogovoruvac").GetString();
                string pravnoEkonomskiOdnosi = data.GetProperty("pravnoEkonomskiOdnosi").GetString();

                if (string.IsNullOrEmpty(idStr) || !long.TryParse(idStr, out long id))
                {
                    return new JsonResult(new { success = false, message = "Невалиден ID на запис" });
                }

                if (string.IsNullOrEmpty(klientiIdStr) || !long.TryParse(klientiIdStr, out long klientiId))
                {
                    return new JsonResult(new { success = false, message = "Невалиден ID на осигурител" });
                }

                if (string.IsNullOrEmpty(pravnoEkonomskiOdnosi))
                {
                    return new JsonResult(new { success = false, message = "Правно-економските односи се задолжителни" });
                }

                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();

                    // Check if record with same insurer exists (excluding current record)
                    var checkSql = "SELECT COUNT(*) FROM AnalizaOsiguritelPravnoEkonomskiOdnosi WHERE KlientiIdAnalizaDogovoruvac = @KlientiId AND Id != @Id";
                    using (var cmd = new SqlCommand(checkSql, connection))
                    {
                        cmd.Parameters.AddWithValue("@KlientiId", klientiId);
                        cmd.Parameters.AddWithValue("@Id", id);
                        var exists = (int)await cmd.ExecuteScalarAsync() > 0;
                        if (exists)
                        {
                            return new JsonResult(new { success = false, message = "Веќе постои запис за овој осигурител" });
                        }
                    }

                    // Update record
                    var sql = @"
                        UPDATE AnalizaOsiguritelPravnoEkonomskiOdnosi 
                        SET KlientiIdAnalizaDogovoruvac = @KlientiId, 
                            PravnoEkonomskiOdnosiOsiguritel = @PravnoEkonomskiOdnosi
                        WHERE Id = @Id";

                    using (var cmd = new SqlCommand(sql, connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", id);
                        cmd.Parameters.AddWithValue("@KlientiId", klientiId);
                        cmd.Parameters.AddWithValue("@PravnoEkonomskiOdnosi", pravnoEkonomskiOdnosi);
                        await cmd.ExecuteNonQueryAsync();
                    }
                }

                return new JsonResult(new { success = true });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        public async Task<IActionResult> OnPostUpdateRizikPoOsiguritelAsync([FromBody] System.Text.Json.JsonElement data)
        {
            try
            {
                long id = data.GetProperty("id").GetInt64();
                long klientiId = data.GetProperty("klientiIdAnalizaDogovoruvac").GetInt64();
                long rizikId = data.GetProperty("rizikIdRizici").GetInt64();
                bool pokrieno = data.GetProperty("pokrieno").GetBoolean();
                string beleshka = data.GetProperty("beleshka").GetString();

                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();

                    // Check if combination already exists (excluding current record)
                    var checkSql = @"
                        SELECT COUNT(*) 
                        FROM AnalizaNaKlientRizici 
                        WHERE KlientiIdAnalizaDogovoruvac = @KlientiId 
                          AND RizikIdRizici = @RizikId 
                          AND Id != @Id";

                    using (var cmd = new SqlCommand(checkSql, connection))
                    {
                        cmd.Parameters.AddWithValue("@KlientiId", klientiId);
                        cmd.Parameters.AddWithValue("@RizikId", rizikId);
                        cmd.Parameters.AddWithValue("@Id", id);
                        var exists = (int)await cmd.ExecuteScalarAsync() > 0;
                        if (exists)
                        {
                            return new JsonResult(new { success = false, message = "Веќе постои запис за оваа комбинација на осигурител и ризик" });
                        }
                    }

                    // Update record
                    var sql = @"
                        UPDATE AnalizaNaKlientRizici 
                        SET KlientiIdAnalizaDogovoruvac = @KlientiId,
                            RizikIdRizici = @RizikId,
                            Pokrieno = @Pokrieno,
                            Beleshka = @Beleshka
                        WHERE Id = @Id";

                    using (var cmd = new SqlCommand(sql, connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", id);
                        cmd.Parameters.AddWithValue("@KlientiId", klientiId);
                        cmd.Parameters.AddWithValue("@RizikId", rizikId);
                        cmd.Parameters.AddWithValue("@Pokrieno", pokrieno);
                        cmd.Parameters.AddWithValue("@Beleshka", (object)beleshka ?? DBNull.Value);
                        await cmd.ExecuteNonQueryAsync();
                    }
                }

                return new JsonResult(new { success = true });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        public async Task<IActionResult> OnPostDeleteRizikPoOsiguritelAsync([FromBody] System.Text.Json.JsonElement data)
        {
            try
            {
                if (!data.TryGetProperty("id", out JsonElement idElement))
                {
                    return new JsonResult(new { success = false, message = "ID на записот е задолжителен" });
                }

                long id = idElement.GetInt64();

                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    var sql = "DELETE FROM AnalizaNaKlientRizici WHERE Id = @Id";
                    using (var cmd = new SqlCommand(sql, connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", id);
                        int rowsAffected = await cmd.ExecuteNonQueryAsync();
                        if (rowsAffected == 0)
                        {
                            return new JsonResult(new { success = false, message = "Записот не е пронајден" });
                        }
                    }
                }

                return new JsonResult(new { success = true, message = "Записот е успешно избришан" });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        public async Task<IActionResult> OnGetRizikPoOsiguritelByIdAsync(long id)
        {
            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    var sql = @"
                        SELECT a.Id, a.KlientiIdAnalizaDogovoruvac, a.RizikIdRizici, a.Pokrieno, a.Beleshka,
                               CASE 
                                   WHEN k.KlientFizickoPravnoLice = 'P' THEN k.Naziv
                                   WHEN k.KlientFizickoPravnoLice = 'F' THEN CONCAT(k.Ime, ' ', k.Prezime)
                                   ELSE CAST(k.Id as nvarchar(20))
                               END as OsiguritelNaziv,
                               r.Naziv as RizikNaziv
                        FROM AnalizaNaKlientRizici a
                        LEFT JOIN Klienti k ON a.KlientiIdAnalizaDogovoruvac = k.Id
                        LEFT JOIN Rizici r ON a.RizikIdRizici = r.Id
                        WHERE a.Id = @Id";

                    using (var cmd = new SqlCommand(sql, connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", id);
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                return new JsonResult(new
                                {
                                    success = true,
                                    data = new
                                    {
                                        id = reader.GetInt64(0),
                                        klientiIdAnalizaDogovoruvac = reader.GetInt64(1),
                                        rizikIdRizici = reader.GetInt64(2),
                                        pokrieno = reader.GetBoolean(3),
                                        beleshka = reader.IsDBNull(4) ? null : reader.GetString(4),
                                        osiguritelNaziv = reader.IsDBNull(5) ? null : reader.GetString(5),
                                        rizikNaziv = reader.IsDBNull(6) ? null : reader.GetString(6)
                                    }
                                });
                            }
                        }
                    }
                }

                return new JsonResult(new { success = false, message = "Записот не е пронајден" });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        public async Task<IActionResult> OnPostAddRizikPoOsiguritel([FromBody] System.Text.Json.JsonElement data)
        {
            try
            {
                if (!data.TryGetProperty("klientiIdAnalizaDogovoruvac", out var klientiIdElement) ||
                    !data.TryGetProperty("rizikIdRizici", out var rizikIdElement))
                {
                    return new JsonResult(new { success = false, message = "Недостасуваат задолжителни податоци" });
                }

                if (!long.TryParse(klientiIdElement.GetString(), out long klientiId) ||
                    !long.TryParse(rizikIdElement.GetString(), out long rizikId))
                {
                    return new JsonResult(new { success = false, message = "Невалидни ID вредности" });
                }

                bool pokrieno = data.GetProperty("pokrieno").GetBoolean();
                string beleshka = data.GetProperty("beleshka").GetString();

                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();

                    // Check if risk exists
                    var checkRiskSql = "SELECT COUNT(*) FROM Rizici WHERE Id = @RizikId";
                    using (var cmd = new SqlCommand(checkRiskSql, connection))
                    {
                        cmd.Parameters.AddWithValue("@RizikId", rizikId);
                        var riskExists = (int)await cmd.ExecuteScalarAsync() > 0;
                        if (!riskExists)
                        {
                            return new JsonResult(new { success = false, message = "Избраниот ризик не постои" });
                        }
                    }

                    // Check if combination already exists
                    var checkSql = @"
                        SELECT COUNT(*) 
                        FROM AnalizaNaKlientRizici 
                        WHERE KlientiIdAnalizaDogovoruvac = @KlientiId 
                          AND RizikIdRizici = @RizikId";

                    using (var cmd = new SqlCommand(checkSql, connection))
                    {
                        cmd.Parameters.AddWithValue("@KlientiId", klientiId);
                        cmd.Parameters.AddWithValue("@RizikId", rizikId);
                        var exists = (int)await cmd.ExecuteScalarAsync() > 0;
                        if (exists)
                        {
                            return new JsonResult(new { success = false, message = "Веќе постои запис за оваа комбинација на осигурител и ризик" });
                        }
                    }

                    // Insert new record
                    var sql = @"
                        INSERT INTO AnalizaNaKlientRizici (RizikIdRizici, KlientiIdAnalizaDogovoruvac, Pokrieno, Beleshka)
                        VALUES (@RizikId, @KlientiId, @Pokrieno, @Beleshka)";

                    using (var cmd = new SqlCommand(sql, connection))
                    {
                        cmd.Parameters.AddWithValue("@RizikId", rizikId);
                        cmd.Parameters.AddWithValue("@KlientiId", klientiId);
                        cmd.Parameters.AddWithValue("@Pokrieno", pokrieno);
                        cmd.Parameters.AddWithValue("@Beleshka", (object)beleshka ?? DBNull.Value);
                        await cmd.ExecuteNonQueryAsync();
                    }
                }

                return new JsonResult(new { success = true });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = "Грешка при зачувување на ризик: " + ex.Message });
            }
        }

        public async Task<IActionResult> OnGetListaRiziciPoOsiguritel()
        {
            try
            {
                var riziciPoOsiguritelQuery = @"
                    SELECT a.Id, 
                           CASE 
                               WHEN k.KlientFizickoPravnoLice = 'P' THEN k.Naziv
                               WHEN k.KlientFizickoPravnoLice = 'F' THEN CONCAT(k.Ime, ' ', k.Prezime)
                               ELSE CAST(k.Id as nvarchar(20))
                           END as OsiguritelNaziv,
                           r.Naziv as RizikNaziv,
                           a.Pokrieno,
                           a.Beleshka,
                           r.ProduktId,
                           a.KlientiIdAnalizaDogovoruvac,
                           p.Ime as ProduktIme,
                           ko.KlasaIme as KlasaIme
                    FROM AnalizaNaKlientRizici a
                    LEFT JOIN Klienti k ON a.KlientiIdAnalizaDogovoruvac = k.Id
                    LEFT JOIN Rizici r ON a.RizikIdRizici = r.Id
                    LEFT JOIN Produkti p ON r.ProduktId = p.Id
                    LEFT JOIN KlasiOsiguruvanje ko ON r.KlasiOsiguruvanjeIdRizik = ko.Id
                    ORDER BY a.Id DESC";

                var data = new List<object>();
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    using (var command = new SqlCommand(riziciPoOsiguritelQuery, connection))
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                data.Add(new
                                {
                                    id = reader.GetInt64(0),
                                    osiguritelNaziv = reader.IsDBNull(1) ? null : reader.GetString(1),
                                    rizikNaziv = reader.IsDBNull(2) ? null : reader.GetString(2),
                                    pokrieno = reader.GetBoolean(3),
                                    beleshka = reader.IsDBNull(4) ? null : reader.GetString(4),
                                    produktId = reader.IsDBNull(5) ? (int?)null : reader.GetInt32(5),
                                    klientiIdAnalizaDogovoruvac = reader.GetInt64(6),
                                    produktIme = reader.IsDBNull(7) ? null : reader.GetString(7),
                                    klasaIme = reader.IsDBNull(8) ? null : reader.GetString(8)
                                });
                            }
                        }
                    }
                }

                return new JsonResult(new { success = true, data = data });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        public async Task<IActionResult> OnGetProduktiByKlasaAsync(int klasaId)
        {
            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    var query = @"
                        SELECT Id, Ime
                        FROM Produkti
                        WHERE KlasaOsiguruvanjeId = @KlasaId
                        ORDER BY Ime";

                    var produkti = new List<object>();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@KlasaId", klasaId);
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                produkti.Add(new
                                {
                                    id = reader.GetInt32(0),
                                    ime = reader.GetString(1)
                                });
                            }
                        }
                    }
                    return new JsonResult(new { success = true, data = produkti });
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }
    }
}
