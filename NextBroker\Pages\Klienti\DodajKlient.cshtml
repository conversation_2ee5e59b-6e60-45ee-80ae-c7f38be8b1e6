@page
@model NextBroker.Pages.Klienti.DodajKlientModel
@{
    ViewData["Title"] = "Додај клиент";
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container mt-4">
    <form method="post" class="dodaj-polisa-form">
        @Html.AntiForgeryToken()
        
        <!-- Add validation summary -->
        <div asp-validation-summary="All" class="text-danger"></div>

        <!-- Basic Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Основни информации</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Тип на клиент</label>
                        <select asp-for="Input.KlientFizickoPravnoLice" 
                                asp-items="Model.KlientFizickoPravnoLiceOptions" 
                                class="form-select"
                                id="klientType">
                            <option value="">-- Избери тип на клиент --</option>
                        </select>
                    </div>
                </div>

                <!-- Hidden fields container -->
                <div id="additionalFields" style="display: none;">
                    <!-- Fields for Physical Person -->
                    <div id="fizickoLiceFields">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Пол</label>
                                <select asp-for="Input.KlientPol" 
                                        asp-items="Model.KlientPolOptions" 
                                        class="form-select">
                                </select>
                                <span id="pol-error" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check mt-4">
                                    <input type="radio" name="fizickoLiceType" class="form-check-input" id="Input_KlientVraboten" onchange="handleFizickoLiceTypeChange(this)" />
                                    <label class="form-check-label" for="Input_KlientVraboten">
                                        Вработен
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input type="radio" name="fizickoLiceType" class="form-check-input" id="Input_KlientSorabotnik" onchange="handleFizickoLiceTypeChange(this)" />
                                    <label class="form-check-label" for="Input_KlientSorabotnik">
                                        Соработник
                                    </label>
                                </div>
                                <!-- Add clear selection button -->
                                <button type="button" class="btn btn-sm btn-outline-secondary mt-2" onclick="clearFizickoLiceTypeSelection()">
                                    Исчисти избор
                                </button>
                                <!-- Hidden inputs to store actual values -->
                                <input type="hidden" asp-for="Input.KlientVraboten" id="Hidden_KlientVraboten" />
                                <input type="hidden" asp-for="Input.KlientSorabotnik" id="Hidden_KlientSorabotnik" />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Име</label>
                                <input asp-for="Input.Ime" class="form-control" />
                                <span id="ime-error" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Презиме</label>
                                <input asp-for="Input.Prezime" class="form-control" />
                                <span id="prezime-error" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">ЕМБГ</label>
                                <input asp-for="Input.EMBG" class="form-control" />
                                <div class="form-check mt-2">
                                    <input type="checkbox" class="form-check-input" id="Input_Stranec" name="Input.Stranec" />
                                    <label class="form-check-label" for="Input_Stranec">Странец</label>
                                </div>
                                <small class="form-text text-muted">ЕМБГ мора да биде единствен во системот</small>
                                <span id="embg-error" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Fields for Legal Entity - initially hidden -->
                    <div id="pravnoLiceFields" style="display: none;">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Назив</label>
                                <input asp-for="Input.Naziv" class="form-control" />
                                <span class="text-danger" id="naziv-error"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">ЕДБ</label>
                                <input asp-for="Input.EDB" class="form-control" maxlength="15" />
                                <small class="form-text text-muted">ЕДБ мора да биде единствен во системот</small>
                                <span class="text-danger" id="edb-error"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">МБ</label>
                                <input asp-for="Input.MB" class="form-control" maxlength="15" />
                                <small class="form-text text-muted">МБ мора да биде единствен во системот</small>
                                <span class="text-danger" id="mb-error"></span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input type="radio" name="clientType" class="form-check-input" id="Input_KlientSorabotnik" onchange="handleClientTypeChange(this)" />
                                    <label class="form-check-label" for="Input_KlientSorabotnik">
                                        Соработник
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input type="radio" name="clientType" class="form-check-input" id="Input_Osiguritel" onchange="handleClientTypeChange(this)" />
                                    <label class="form-check-label" for="Input_Osiguritel">
                                        Осигурител
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input type="radio" name="clientType" class="form-check-input" id="Input_BrokerskoDrustvo" onchange="handleClientTypeChange(this)" />
                                    <label class="form-check-label" for="Input_BrokerskoDrustvo">
                                        Брокерско друштво
                                    </label>
                                </div>
                                <!-- Add ZivotNezivot dropdown for Osiguritel - initially hidden -->
                                <div id="osiguritelZivotNezivotContainer" class="mt-3" style="display: none;">
                                    <label class="form-label">Живот/Неживот</label>
                                    <select asp-for="Input.ZivotNezivot" 
                                            asp-items="Model.ZivotNezivotOptions" 
                                            class="form-select">
                                        <option value="">-- Избери тип --</option>
                                    </select>
                                </div>
                                <!-- Add clear selection button -->
                                <button type="button" class="btn btn-sm btn-outline-secondary mt-2" onclick="clearClientTypeSelection()">
                                    Исчисти избор
                                </button>
                                <!-- Hidden inputs to store actual values -->
                                <input type="hidden" asp-for="Input.KlientSorabotnik" id="Hidden_KlientSorabotnik" />
                                <input type="hidden" asp-for="Input.Osiguritel" id="Hidden_Osiguritel" />
                                <input type="hidden" asp-for="Input.BrokerskoDrustvo" id="Hidden_BrokerskoDrustvo" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Address Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Адресни податоци</h5>
            </div>
            <div class="card-body">
                <div id="addressFields" style="display: none;">
                    <h6 class="mb-3">Адреса од документ за идентификација</h6>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Општина</label>
                            <select asp-for="Input.ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija" 
                                    asp-items="Model.Opstini" 
                                    class="form-select">
                                <option value="">-- Избери општина --</option>
                            </select>
                            <span id="opstina-id-error" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Улица</label>
                            <input asp-for="Input.UlicaOdDokumentZaIdentifikacija" class="form-control" />
                            <span id="ulica-id-error" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Број</label>
                            <input asp-for="Input.BrojOdDokumentZaIdentifikacija" class="form-control" />
                            <span id="broj-id-error" class="text-danger"></span>
                        </div>
                    </div>

                    <h6 class="mb-3 mt-4">Адреса за комуникација</h6>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Општина</label>
                            <select asp-for="Input.ListaOpstiniIdOpstinaZaKomunikacija" 
                                    asp-items="Model.Opstini" 
                                    class="form-select">
                                <option value="">-- Избери општина --</option>
                            </select>
                            <span id="opstina-kom-error" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Улица</label>
                            <input asp-for="Input.UlicaZaKomunikacija" class="form-control" />
                            <span id="ulica-kom-error" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Број</label>
                            <input asp-for="Input.BrojZaKomunikacija" class="form-control" />
                            <span id="broj-kom-error" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="copyAddressToCommunication()">
                                <i class="fas fa-copy"></i> Копирај адреса од документ
                            </button>
                            <small class="form-text text-muted ml-2">Кликнете за да копирате адреса од документ за идентификација</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Document Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Документи</h5>
            </div>
            <div class="card-body">
                <!-- Fields for Legal Entity -->
                <div id="pravnoLiceDocuments">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label">Датум на тековна состојба</label>
                            <input asp-for="Input.DatumNaTekovnaSostojba" type="date" class="form-control" />
                        </div>
                    </div>
                </div>

                <!-- Fields for Physical Person -->
                <div id="fizickoLiceDocuments">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label">Број на пасош/лична карта</label>
                            <input asp-for="Input.BrojPasosLicnaKarta" class="form-control" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Важи од</label>
                            <input asp-for="Input.DatumVaziOdPasosLicnaKarta" type="date" class="form-control" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Важи до</label>
                            <input asp-for="Input.DatumVaziDoPasosLicnaKarta" type="date" class="form-control" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Контакт информации</h5>
            </div>
            <div class="card-body">
                <!-- All contact fields container -->
                <div id="contactFields" style="display: none;">
                    <!-- Dejnost field - only for Legal Entity -->
                    <div id="pravnoLiceContact" class="col-md-4 mb-3">
                        <label class="form-label">Дејност</label>
                        <div class="input-group">
                            <input type="text" id="dejnostSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај дејност..." />
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" data-target="dejnost" style="width: 30px; height: 30px; padding: 0; margin-left: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                        </div>
                        <input type="hidden" asp-for="Input.ListaDejnostiIdDejnost" id="Input_ListaDejnostiIdDejnost" />
                        <div id="dejnostSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 300px; overflow-y: auto;">
                        </div>
                        <small class="form-text text-muted">Внесете го кодот или називот на дејноста или кликнете за да видите сите опции</small>
                    </div>
                    <!-- Common contact fields -->
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Email *</label>
                        <input asp-for="Input.Email" type="email" class="form-control" pattern="@("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}")" required oninvalid="this.setCustomValidity('Ве молиме внесете email адреса')" oninput="this.setCustomValidity('')" />
                        <span asp-validation-for="Input.Email" class="text-danger"></span>
                        <small class="form-text text-muted">Пример: <EMAIL></small>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Телефон</label>
                        <input asp-for="Input.Tel" class="form-control" />
                        <span id="telefon-error" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Веб страна</label>
                        <input asp-for="Input.Webstrana" class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Забелешка</label>
                        <input asp-for="Input.Zabeleska" class="form-control" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Consent Information Card -->
        <div class="card mb-4" id="consentCard">
            <div class="card-header">
                <h5 class="mb-0">Согласности</h5>
            </div>
            <div class="card-body">
                <div id="consentFields" style="display: none;">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input asp-for="Input.SoglasnostZaDirektenMarketing" class="form-check-input" type="checkbox" />
                                <label class="form-check-label">Согласност за директен маркетинг</label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input asp-for="Input.SoglasnostZaEmailKomunikacija" class="form-check-input" type="checkbox" />
                                <label class="form-check-label">Согласност за email комуникација</label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input asp-for="Input.SoglasnostZaTelKomunikacija" class="form-check-input" type="checkbox" />
                                <label class="form-check-label">Согласност за телефонска комуникација</label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Датум на повлечена согласност за директен маркетинг</label>
                            <input asp-for="Input.DatumNaPovlecenaSoglasnostZaDirektenMarketing" type="date" class="form-control" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Real Owner Information Card -->
        <div id="realOwnerCard" class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Вистински сопственик</h5>
            </div>
            <div class="card-body">
                <div id="realOwnerFields" style="display: none;">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Вистински сопственик</label>
                            <input asp-for="Input.VistinskiSopstvenik" class="form-control" />
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Име</label>
                            <input asp-for="Input.VistinskiSopstvenikIme" class="form-control" />
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Презиме</label>
                            <input asp-for="Input.VistinskiSopstvenikPrezime" class="form-control" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Public Function Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Носител на јавна функција</h5>
            </div>
            <div class="card-body">
                <div id="publicFunctionFields" style="display: none;">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input asp-for="Input.NositelNaJF" class="form-check-input" type="checkbox" />
                                <label class="form-check-label">Носител на јавна функција</label>
                            </div>
                        </div>
                        <div class="col-md-8 mb-3">
                            <label class="form-label">Основ за носител на јавна функција</label>
                            <input asp-for="Input.OsnovZaNositelNaJF" class="form-control" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input asp-for="Input.ZasilenaAnaliza" class="form-check-input" type="checkbox" />
                                <label class="form-check-label">Засилена анализа</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Risk Level Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Ниво на ризик</h5>
            </div>
            <div class="card-body">
                <div id="riskLevelFields" style="display: none;">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label">Ниво на ризик</label>
                            <select asp-for="Input.NivoaNaRizikIdNivoNaRizik" 
                                    asp-items="Model.NivoaNaRizik" 
                                    class="form-select">
                                <option value="">-- Избери ниво на ризик --</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contract Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Договор</h5>
            </div>
            <div class="card-body">
                <div id="contractFields" style="display: none;">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Датум на договор</label>
                            <input asp-for="Input.DaumNaDogovor" type="date" class="form-control" />
                            <span id="dogovor-od-error" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Договор важи до</label>
                            <input asp-for="Input.DogovorVaziDo" type="date" class="form-control" />
                            <span id="dogovor-do-error" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Број на договор</label>
                            <input asp-for="Input.BrojNaDogovor" class="form-control" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Договор определено/неопределено</label>
                            <select asp-for="Input.DogovorOpredelenoNeopredeleno" 
                                    asp-items="Model.DogovorOpredelenoNeopredelenoOptions"
                                    class="form-select">
                                <option value="">-- Избери тип --</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Authorization Date Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Датум на овластување</h5>
            </div>
            <div class="card-body">
                <div id="authorizationDateFields" style="display: none;">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label">Датум на овластување</label>
                            <input asp-for="Input.DatumNaOvlastuvanje" type="date" class="form-control" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bank Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Банкарски информации</h5>
            </div>
            <div class="card-body">
                <div id="bankFields" style="display: none;">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Платежна сметка</label>
                            <input asp-for="Input.PlateznaSmetka" class="form-control" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Банка</label>
                            <select asp-for="Input.SifrarnikBankiIdBanka" 
                                    asp-items="Model.Banki" 
                                    class="form-select">
                                <option value="">-- Избери банка --</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Рок на плаќање за фактури кон клиентот (денови)</label>
                            <input asp-for="Input.RokNaPlakanjeDenovi" type="number" class="form-control" value="15" step="1" min="0" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Organizational Structure Card -->
        <div id="organizationCard" class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Организациона структура</h5>
            </div>
            <div class="card-body">
                <div id="organizationFields" style="display: none;">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Организациона единица</label>
                            <select asp-for="Input.SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica" 
                                    asp-items="Model.OrganizacioniEdinici" 
                                    class="form-select">
                                <option value="">-- Избери организациона единица --</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Работна позиција</label>
                            <select asp-for="Input.SifrarnikRabotniPoziciiIdRabotnaPozicija" 
                                    asp-items="Model.RabotniPozicii" 
                                    class="form-select">
                                <option value="">-- Избери работна позиција --</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Експозитура</label>
                            <select asp-for="Input.EkspozituriIdEkspozitura" 
                                    asp-items="Model.EkspozituriIdEkspozituraOptions" 
                                    class="form-select">
                                <option value="">-- Избери експозитура --</option>
                            </select>
                        </div>

                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Надреден</label>
                            <div class="input-group">
                                <button type="button" class="btn btn-outline-secondary btn-sm clear-field" data-target="nadreden" style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                    <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                                </button>
                                <input type="text" id="nadredenSearch" class="form-control" 
                                       autocomplete="off"
                                       placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                                <input type="hidden" asp-for="Input.SifrarnikRabotniPoziciiIdNadreden" id="SifrarnikRabotniPoziciiIdNadreden" />
                            </div>
                            <div id="nadredenSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                                 style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Надреден важи од</label>
                            <input asp-for="Input.NadredenVaziOd" type="date" class="form-control" />
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Надреден до</label>
                            <input asp-for="Input.NadredenDo" type="date" class="form-control" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- License Information Card -->
        <div id="licenseCard" class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Лиценца и дозволи</h5>
            </div>
            <div class="card-body">
                <div id="licenseFields" style="display: none;">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input asp-for="Input.ZadolzitelnoLicenca" class="form-check-input" type="checkbox" />
                                <label class="form-check-label">Задолжително лиценца</label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Број на решение од АСО за лиценца</label>
                            <input asp-for="Input.BrojNaResenieOdASOZaLicenca" class="form-control" />
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Датум на решение од АСО</label>
                            <input asp-for="Input.DatumNaResenieOdASOZaLicenca" type="date" class="form-control" />
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Датум на одземена лиценца</label>
                            <input asp-for="Input.DatumNaOdzemenaLicenca" type="date" class="form-control" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Broker License Card -->
        <div id="brokerLicenseCard" class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Лиценци брокери</h5>
            </div>
            <div class="card-body">
                <div id="brokerLicenseFields" style="display: none;">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Број на дозвола за вршење осигурително брокерски работи</label>
                            <input asp-for="Input.BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti" class="form-control" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Датум на дозвола за вршење осигурително брокерски работи</label>
                            <input asp-for="Input.DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti" type="date" class="form-control" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3" id="zivotNezivotField" style="display: none;">
                            <label class="form-label">Живот/Неживот</label>
                            <select asp-for="Input.ZivotNezivot" asp-items="Model.ZivotNezivotOptions" class="form-select">
                                <option value="">-- Избери --</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="text-center mb-4" id="submitButtonContainer" style="display: none;">
            <button type="submit" class="btn btn-primary">Зачувај</button>
        </div>
    </form>
</div>

@section Scripts {
    <script>
        function handleClientTypeChange(radio) {
            // Reset all hidden inputs to false
            document.getElementById('Hidden_KlientSorabotnik').value = 'false';
            document.getElementById('Hidden_Osiguritel').value = 'false';
            document.getElementById('Hidden_BrokerskoDrustvo').value = 'false';

            // Get the Osiguritel ZivotNezivot container
            const osiguritelZivotNezivotContainer = document.getElementById('osiguritelZivotNezivotContainer');

            // Handle authorization date fields visibility for Pravno Lice
            if ($('#klientType').val() === 'P') {
                if (radio.id === 'Input_Osiguritel' || radio.id === 'Input_BrokerskoDrustvo') {
                    $('#authorizationDateFields').hide();
                } else if (radio.id === 'Input_KlientSorabotnik') {
                    $('#authorizationDateFields').show();
                }

                // Show contract fields for Sorabotnik and Osiguritel
                if (radio.id === 'Input_KlientSorabotnik' || radio.id === 'Input_Osiguritel') {
                    $('#contractFields').slideDown();
                } else {
                    $('#contractFields').slideUp();
                }
            }

            // Set the selected one to true
            if (radio.id === 'Input_KlientSorabotnik') {
                document.getElementById('Hidden_KlientSorabotnik').value = 'true';
                $('#brokerLicenseFields').hide();
                $('#zivotNezivotField').hide();
                osiguritelZivotNezivotContainer.style.display = 'none';
            } else if (radio.id === 'Input_Osiguritel') {
                document.getElementById('Hidden_Osiguritel').value = 'true';
                $('#brokerLicenseFields').hide();
                $('#zivotNezivotField').hide();
                osiguritelZivotNezivotContainer.style.display = 'block';
            } else if (radio.id === 'Input_BrokerskoDrustvo') {
                document.getElementById('Hidden_BrokerskoDrustvo').value = 'true';
                $('#brokerLicenseFields').show();
                $('#zivotNezivotField').hide();
                osiguritelZivotNezivotContainer.style.display = 'none';
            }
        }

        function handleFizickoLiceTypeChange(radio) {
            // Reset all hidden inputs to false
            document.getElementById('Hidden_KlientVraboten').value = 'false';
            document.getElementById('Hidden_KlientSorabotnik').value = 'false';

            // Set the selected one to true
            if (radio.id === 'Input_KlientVraboten') {
                document.getElementById('Hidden_KlientVraboten').value = 'true';
                $('#contractFields').slideDown();
            } else if (radio.id === 'Input_KlientSorabotnik') {
                document.getElementById('Hidden_KlientSorabotnik').value = 'true';
                $('#contractFields').slideDown();
            }

            // Check if neither is selected to hide contract fields
            if (!$('#Input_KlientVraboten').is(':checked') && !$('#Input_KlientSorabotnik').is(':checked')) {
                $('#contractFields').slideUp();
            }
        }

        function clearFizickoLiceTypeSelection() {
            // Uncheck all radio buttons
            document.querySelectorAll('input[name="fizickoLiceType"]').forEach(radio => {
                radio.checked = false;
            });

            // Reset all hidden inputs to false
            document.getElementById('Hidden_KlientVraboten').value = 'false';
            document.getElementById('Hidden_KlientSorabotnik').value = 'false';
            
            // Hide contract fields
            $('#contractFields').slideUp();
        }

        function clearClientTypeSelection() {
            // Uncheck all radio buttons
            document.querySelectorAll('input[name="clientType"]').forEach(radio => {
                radio.checked = false;
            });

            // Reset all hidden inputs to false
            document.getElementById('Hidden_KlientSorabotnik').value = 'false';
            document.getElementById('Hidden_Osiguritel').value = 'false';
            document.getElementById('Hidden_BrokerskoDrustvo').value = 'false';

            // Show authorization date fields when clearing selection
            if ($('#klientType').val() === 'P') {
                $('#authorizationDateFields').show();
                $('#contractFields').slideUp(); // Hide contract fields when selection is cleared
            }

            $('#brokerLicenseFields').hide();
            $('#zivotNezivotField').hide();
            document.getElementById('osiguritelZivotNezivotContainer').style.display = 'none';
        }

        $(document).ready(function () {
            // Initially hide contract fields
            $('#contractFields').hide();

            // Function to check if license fields should be visible for Fizicko Lice
            function checkFizickoLiceLicenseVisibility() {
                if ($('#Input_KlientVraboten').is(':checked') || $('#Input_KlientSorabotnik').is(':checked')) {
                    $('#licenseFields').show();
                    // Show Zivot/Nezivot field only for Sorabotnik
                    if ($('#Input_KlientSorabotnik').is(':checked')) {
                        $('#zivotNezivotField').show();
                    } else {
                        $('#zivotNezivotField').hide();
                    }
                } else {
                    $('#licenseFields').hide();
                    $('#zivotNezivotField').hide();
                }
            }

            // Update the Fizicko Lice radio handler
            $('input[name="fizickoLiceType"]').change(function() {
                if ($('#klientType').val() === 'F') {
                    checkFizickoLiceLicenseVisibility();
                    // Show organization fields for both Vraboten and Sorabotnik
                    if ($('#Input_KlientVraboten').is(':checked') || $('#Input_KlientSorabotnik').is(':checked')) {
                        $('#organizationFields').show();
                    } else {
                        $('#organizationFields').hide();
                    }
                }
            });

            // Update the Pravno Lice Sorabotnik checkbox handler
            $('#pravnoLiceFields .form-check-input[name="Input.KlientSorabotnik"]').change(function() {
                if ($('#klientType').val() === 'P') {
                    if ($(this).is(':checked')) {
                        $('#licenseFields').show();
                        $('#zivotNezivotField').show();
                    } else {
                        $('#licenseFields').hide();
                        $('#zivotNezivotField').hide();
                    }
                }
            });

            // Add handler for Vraboten checkbox to control organization fields
            $('#Input_KlientVraboten').change(function() {
                if ($('#klientType').val() === 'F') {
                    if ($(this).is(':checked')) {
                        $('#organizationFields').show();
                    } else {
                        $('#organizationFields').hide();
                    }
                }
            });

            $('#klientType').change(function() {
                var selectedType = $(this).val();
                
                if (!selectedType) {
                    $('#additionalFields').slideUp();
                    $('#pravnoLiceDocuments').hide();
                    $('#fizickoLiceDocuments').hide();
                    $('#pravnoLiceContact').hide();
                    $('#contactFields').hide();
                    $('#realOwnerFields').hide();
                    $('#riskLevelFields').hide();
                    $('#contractFields').hide();
                    $('#licenseCard').hide();
                    $('#organizationCard').show();
                    $('#organizationFields').hide();
                    $('#consentCard').hide();
                    $('#consentFields').hide();
                    $('#publicFunctionFields').hide();
                    $('#bankFields').hide();
                    $('#authorizationDateFields').hide();
                    $('#brokerLicenseCard').hide();
                    $('#brokerLicenseFields').hide();
                    $('#submitButtonContainer').hide();
                    return;
                }

                $('#submitButtonContainer').show();
                $('#additionalFields').slideDown();
                $('#contactFields').show();
                $('#riskLevelFields').show();
                $('#contractFields').show();
                $('#consentCard').show();
                $('#consentFields').show();
                $('#publicFunctionFields').show();
                $('#bankFields').show();
                $('#authorizationDateFields').show();
                
                if (selectedType === 'F') {
                    $('#fizickoLiceFields').show();
                    $('#pravnoLiceFields').hide();
                    $('#pravnoLiceDocuments').hide();
                    $('#fizickoLiceDocuments').show();
                    $('#pravnoLiceContact').hide();
                    $('#realOwnerCard').hide();
                    $('#licenseCard').show();
                    $('#organizationCard').show();
                    $('#consentCard').show();
                    $('#consentFields').show();
                    checkFizickoLiceLicenseVisibility();
                    validateEMBG(); // Add EMBG validation when type changes to F
                    if ($('#Input_KlientSorabotnik').is(':checked')) {
                        $('#zivotNezivotField').show();
                    } else {
                        $('#zivotNezivotField').hide();
                    }
                    if ($('#Input_KlientVraboten').is(':checked')) {
                        $('#organizationFields').show();
                    } else {
                        $('#organizationFields').hide();
                    }
                    $('#brokerLicenseCard').hide();
                    $('#brokerLicenseFields').hide();

                    // Check contract fields visibility
                    if ($('#Input_KlientVraboten').is(':checked') || $('#Input_KlientSorabotnik').is(':checked')) {
                        $('#contractFields').slideDown();
                    } else {
                        $('#contractFields').slideUp();
                    }
                } else if (selectedType === 'P') {
                    $('#fizickoLiceFields').hide();
                    $('#pravnoLiceFields').show();
                    $('#pravnoLiceDocuments').show();
                    $('#fizickoLiceDocuments').hide();
                    $('#pravnoLiceContact').show();
                    $('#realOwnerCard').show();
                    $('#realOwnerFields').show();
                    $('#licenseCard').hide();
                    $('#consentCard').hide();
                    $('#consentFields').hide();
                    if ($('#pravnoLiceFields .form-check-input[name="Input.KlientSorabotnik"]').is(':checked')) {
                        $('#licenseFields').show();
                        $('#zivotNezivotField').show();
                    } else {
                        $('#licenseFields').hide();
                        $('#zivotNezivotField').hide();
                    }
                    $('#organizationCard').hide();
                    $('#brokerLicenseCard').show();
                    // Check if BrokerskoDrustvo is selected
                    if ($('#Input_BrokerskoDrustvo').is(':checked')) {
                        $('#brokerLicenseFields').show();
                        $('#zivotNezivotField').hide();
                    } else {
                        $('#brokerLicenseFields').hide();
                    }
                    // Check contract fields visibility for Pravno Lice
                    if ($('#Input_KlientSorabotnik').is(':checked') || $('#Input_Osiguritel').is(':checked')) {
                        $('#contractFields').slideDown();
                    } else {
                        $('#contractFields').slideUp();
                    }

                    // Check initial authorization date fields visibility
                    if ($('#Input_Osiguritel').is(':checked') || $('#Input_BrokerskoDrustvo').is(':checked')) {
                        $('#authorizationDateFields').hide();
                    } else {
                        $('#authorizationDateFields').show();
                    }
                }
            });

            // Check initial value on page load
            var initialType = $('#klientType').val();
            if (initialType) {
                $('#submitButtonContainer').show();
                $('#additionalFields').show();
                $('#contactFields').show();
                $('#riskLevelFields').show();
                $('#contractFields').show();
                $('#consentCard').show();
                $('#consentFields').show();
                $('#publicFunctionFields').show();
                $('#bankFields').show();
                $('#authorizationDateFields').show();
                if (initialType === 'F') {
                    $('#fizickoLiceFields').show();
                    $('#pravnoLiceFields').hide();
                    $('#pravnoLiceDocuments').hide();
                    $('#fizickoLiceDocuments').show();
                    $('#pravnoLiceContact').hide();
                    $('#realOwnerCard').hide();
                    $('#licenseCard').show();
                    $('#organizationCard').show();
                    $('#consentCard').show();
                    $('#consentFields').show();
                    checkFizickoLiceLicenseVisibility();
                    if ($('#Input_KlientSorabotnik').is(':checked')) {
                        $('#zivotNezivotField').show();
                    } else {
                        $('#zivotNezivotField').hide();
                    }
                    if ($('#Input_KlientVraboten').is(':checked')) {
                        $('#organizationFields').show();
                    } else {
                        $('#organizationFields').hide();
                    }
                    $('#brokerLicenseCard').hide();
                    $('#brokerLicenseFields').hide();

                    // Check contract fields visibility
                    if ($('#Input_KlientVraboten').is(':checked') || $('#Input_KlientSorabotnik').is(':checked')) {
                        $('#contractFields').slideDown();
                    } else {
                        $('#contractFields').slideUp();
                    }
                } else if (initialType === 'P') {
                    $('#fizickoLiceFields').hide();
                    $('#pravnoLiceFields').show();
                    $('#pravnoLiceDocuments').show();
                    $('#fizickoLiceDocuments').hide();
                    $('#pravnoLiceContact').show();
                    $('#realOwnerCard').show();
                    $('#realOwnerFields').show();
                    $('#licenseCard').hide();
                    $('#consentCard').hide();
                    $('#consentFields').hide();
                    if ($('#pravnoLiceFields .form-check-input[name="Input.KlientSorabotnik"]').is(':checked')) {
                        $('#licenseFields').show();
                        $('#zivotNezivotField').show();
                    } else {
                        $('#licenseFields').hide();
                        $('#zivotNezivotField').hide();
                    }
                    $('#organizationCard').hide();
                    $('#brokerLicenseCard').show();
                    // Check if BrokerskoDrustvo is selected
                    if ($('#Input_BrokerskoDrustvo').is(':checked')) {
                        $('#brokerLicenseFields').show();
                        $('#zivotNezivotField').hide();
                    } else {
                        $('#brokerLicenseFields').hide();
                    }
                    // Check contract fields visibility for Pravno Lice
                    if ($('#Input_KlientSorabotnik').is(':checked') || $('#Input_Osiguritel').is(':checked')) {
                        $('#contractFields').slideDown();
                    } else {
                        $('#contractFields').slideUp();
                    }

                    // Check initial authorization date fields visibility
                    if ($('#Input_Osiguritel').is(':checked') || $('#Input_BrokerskoDrustvo').is(':checked')) {
                        $('#authorizationDateFields').hide();
                    } else {
                        $('#authorizationDateFields').show();
                    }
                }
            } else {
                $('#pravnoLiceDocuments').hide();
                $('#fizickoLiceDocuments').hide();
                $('#pravnoLiceContact').hide();
                $('#contactFields').hide();
                $('#realOwnerFields').hide();
                $('#riskLevelFields').hide();
                $('#contractFields').hide();
                $('#licenseCard').hide();
                $('#organizationCard').show();
                $('#organizationFields').hide();
                $('#consentCard').hide();
                $('#consentFields').hide();
                $('#publicFunctionFields').hide();
                $('#bankFields').hide();
                $('#authorizationDateFields').hide();
                $('#brokerLicenseCard').hide();
                $('#brokerLicenseFields').hide();
                $('#submitButtonContainer').hide();
            }

            // Handle address section visibility
            function toggleAddressSection() {
                var selectedType = $('#klientType').val();
                if (selectedType === 'F' || selectedType === 'P') {
                    $('#addressFields').show();
                } else {
                    $('#addressFields').hide();
                }
            }

            // Check initial state
            toggleAddressSection();

            // Handle dropdown changes
            $('#klientType').change(function() {
                toggleAddressSection();
            });

            // Add EMBG validation function
            function validateEMBG() {
                var embgField = $('#Input_EMBG');
                var embgValue = embgField.val();
                var embgError = $('#embg-error');
                var klientType = $('#klientType').val();
                var isStranec = $('#Input_Stranec').is(':checked');
                
                if (klientType === 'F' && !isStranec) {
                    if (!embgValue) {
                        embgError.text('ЕМБГ е задолжително поле за физичко лице');
                        return false;
                    }
                    if (!/^\d{13}$/.test(embgValue)) {
                        embgError.text('ЕМБГ мора да содржи точно 13 цифри');
                        return false;
                    }
                    
                    // Check for duplicate EMBG
                    checkDuplicateEMBG(embgValue, embgError);
                    return true;
                }
                embgError.text('');
                return true;
            }

            // Add function to check for duplicate EMBG
            function checkDuplicateEMBG(embgValue, embgError) {
                if (!embgValue || embgValue.length !== 13) {
                    embgError.text('');
                    return;
                }

                // Clear any existing timeout
                if (window.embgCheckTimeout) {
                    clearTimeout(window.embgCheckTimeout);
                }

                // Show checking indicator
                embgError.text('Проверувам...');

                // Set a timeout to avoid too many AJAX calls while typing
                window.embgCheckTimeout = setTimeout(function() {
                    $.ajax({
                        url: '?handler=CheckDuplicateEMBG',
                        type: 'GET',
                        data: { embg: embgValue },
                        headers: {
                            "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: function(data) {
                            if (data.exists) {
                                embgError.text('ЕМБГ веќе постои во системот');
                            } else {
                                embgError.text('');
                            }
                        },
                        error: function() {
                            // Don't show error for AJAX failure, just clear any existing error
                            embgError.text('');
                        }
                    });
                }, 500); // Wait 500ms after user stops typing
            }

            // Add function to check for duplicate EDB
            function checkDuplicateEDB(edbValue, edbError) {
                if (!edbValue || edbValue.length === 0) {
                    edbError.text('');
                    return;
                }

                // Clear any existing timeout
                if (window.edbCheckTimeout) {
                    clearTimeout(window.edbCheckTimeout);
                }

                // Show checking indicator
                edbError.text('Проверувам...');

                // Set a timeout to avoid too many AJAX calls while typing
                window.edbCheckTimeout = setTimeout(function() {
                    $.ajax({
                        url: '?handler=CheckDuplicateEDB',
                        type: 'GET',
                        data: { edb: edbValue },
                        headers: {
                            "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: function(data) {
                            if (data.exists) {
                                edbError.text('ЕДБ веќе постои во системот');
                            } else {
                                edbError.text('');
                            }
                        },
                        error: function() {
                            // Don't show error for AJAX failure, just clear any existing error
                            edbError.text('');
                        }
                    });
                }, 500); // Wait 500ms after user stops typing
            }

            // Add function to check for duplicate MB
            function checkDuplicateMB(mbValue, mbError) {
                if (!mbValue || mbValue.length === 0) {
                    mbError.text('');
                    return;
                }

                // Clear any existing timeout
                if (window.mbCheckTimeout) {
                    clearTimeout(window.mbCheckTimeout);
                }

                // Show checking indicator
                mbError.text('Проверувам...');

                // Set a timeout to avoid too many AJAX calls while typing
                window.mbCheckTimeout = setTimeout(function() {
                    $.ajax({
                        url: '?handler=CheckDuplicateMB',
                        type: 'GET',
                        data: { mb: mbValue },
                        headers: {
                            "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: function(data) {
                            if (data.exists) {
                                mbError.text('МБ веќе постои во системот');
                            } else {
                                mbError.text('');
                            }
                        },
                        error: function() {
                            // Don't show error for AJAX failure, just clear any existing error
                            mbError.text('');
                        }
                    });
                }, 500); // Wait 500ms after user stops typing
            }

            // Add validation on input change and checkbox change
            $('#Input_EMBG, #Input_Stranec').on('input change', function() {
                if ($('#klientType').val() === 'F') {
                    validateEMBG();
                }
            });

            // Add phone number validation
            function validatePhoneNumber(phoneNumber) {
                // This pattern allows:
                // - Optional + at start
                // - Optional country code
                // - Area codes
                // - Local numbers
                // - Extensions
                // - Spaces, dots, or hyphens as separators
                var phonePattern = /^\+?([0-9][\s-.]?){6,}[0-9]$/;
                return phonePattern.test(phoneNumber.replace(/[\s.-]/g, ''));
            }

            // Add validation for all required fields when Fizicko Lice is selected
            function validateFizickoLiceFields() {
                var klientType = $('#klientType').val();
                var isValid = true;
                
                if (klientType === 'F') {
                    // Validate EMBG
                    var embgField = $('#Input_EMBG');
                    var embgValue = embgField.val();
                    var embgError = $('#embg-error');
                    var isStranec = $('#Input_Stranec').is(':checked');
                    
                    if (!isStranec) {
                        if (!embgValue) {
                            embgError.text('ЕМБГ е задолжително поле за физичко лице');
                            isValid = false;
                        } else if (!/^\d{13}$/.test(embgValue)) {
                            embgError.text('ЕМБГ мора да содржи точно 13 цифри');
                            isValid = false;
                        } else {
                            embgError.text('');
                        }
                    } else {
                        embgError.text('');
                    }

                    // Validate Ime
                    var imeField = $('#Input_Ime');
                    var imeError = $('#ime-error');
                    if (!imeField.val().trim()) {
                        imeError.text('Име е задолжително поле за физичко лице');
                        isValid = false;
                    } else {
                        imeError.text('');
                    }

                    // Validate Prezime
                    var prezimeField = $('#Input_Prezime');
                    var prezimeError = $('#prezime-error');
                    if (!prezimeField.val().trim()) {
                        prezimeError.text('Презиме е задолжително поле за физичко лице');
                        isValid = false;
                    } else {
                        prezimeError.text('');
                    }

                    // Validate Pol
                    var polField = $('#Input_KlientPol');
                    var polError = $('#pol-error');
                    if (!polField.val() || polField.val() === 'N') {
                        polError.text('Пол е задолжително поле за физичко лице');
                        isValid = false;
                    } else {
                        polError.text('');
                    }

                    // Validate Address from ID
                    var opstinaIdField = $('#Input_ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija');
                    var opstinaIdError = $('#opstina-id-error');
                    if (!opstinaIdField.val()) {
                        opstinaIdError.text('Општина е задолжително поле');
                        isValid = false;
                    } else {
                        opstinaIdError.text('');
                    }

                    var ulicaIdField = $('#Input_UlicaOdDokumentZaIdentifikacija');
                    var ulicaIdError = $('#ulica-id-error');
                    if (!ulicaIdField.val().trim()) {
                        ulicaIdError.text('Улица е задолжително поле');
                        isValid = false;
                    } else {
                        ulicaIdError.text('');
                    }

                    var brojIdField = $('#Input_BrojOdDokumentZaIdentifikacija');
                    var brojIdError = $('#broj-id-error');
                    if (!brojIdField.val().trim()) {
                        brojIdError.text('Број е задолжително поле');
                        isValid = false;
                    } else {
                        brojIdError.text('');
                    }

                    // Validate Communication Address
                    var opstinaKomField = $('#Input_ListaOpstiniIdOpstinaZaKomunikacija');
                    var opstinaKomError = $('#opstina-kom-error');
                    if (!opstinaKomField.val()) {
                        opstinaKomError.text('Општина е задолжително поле');
                        isValid = false;
                    } else {
                        opstinaKomError.text('');
                    }

                    var ulicaKomField = $('#Input_UlicaZaKomunikacija');
                    var ulicaKomError = $('#ulica-kom-error');
                    if (!ulicaKomField.val().trim()) {
                        ulicaKomError.text('Улица е задолжително поле');
                        isValid = false;
                    } else {
                        ulicaKomError.text('');
                    }

                    var brojKomField = $('#Input_BrojZaKomunikacija');
                    var brojKomError = $('#broj-kom-error');
                    if (!brojKomField.val().trim()) {
                        brojKomError.text('Број е задолжително поле');
                        isValid = false;
                    } else {
                        brojKomError.text('');
                    }

                    // Remove document validation - No longer required
                    
                    // Validate Phone
                    var telField = $('#Input_Tel');
                    var telError = $('#telefon-error');
                    var phoneValue = telField.val().trim();
                    
                    if (!phoneValue) {
                        telError.text('Телефон е задолжително поле');
                        isValid = false;
                    } else if (!validatePhoneNumber(phoneValue)) {
                        telError.text('Внесете валиден телефонски број');
                        isValid = false;
                    } else {
                        telError.text('');
                    }
                } else {
                    // Clear all error messages when not Fizicko Lice
                    $('.text-danger').text('');
                }
                
                return isValid;
            }

            // Modify the input fields that trigger validation to remove document fields
            $('#Input_KlientPol, #Input_Ime, #Input_Prezime, #Input_EMBG, ' +
            '#Input_ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija, #Input_UlicaOdDokumentZaIdentifikacija, ' +
            '#Input_BrojOdDokumentZaIdentifikacija, #Input_ListaOpstiniIdOpstinaZaKomunikacija, #Input_UlicaZaKomunikacija, ' +
            '#Input_BrojZaKomunikacija, #Input_Tel').on('input change', function() {
                if ($('#klientType').val() === 'F') {
                    validateFizickoLiceFields();
                }
            });

            // Add specific validation for phone field
            $('#Input_Tel').on('input', function() {
                var telField = $(this);
                var telError = $('#telefon-error');
                var phoneValue = telField.val().trim();
                
                if (phoneValue && !validatePhoneNumber(phoneValue)) {
                    telError.text('Внесете валиден телефонски број');
                } else {
                    telError.text('');
                }
            });

            // Validate fields for Pravno Lice
            function validatePravnoLiceFields() {
                var klientType = $('#klientType').val();
                var isValid = true;
                
                if (klientType === 'P') {
                    // Validate Naziv
                    var nazivField = $('#Input_Naziv');
                    var nazivError = $('#naziv-error');
                    if (!nazivField.val().trim()) {
                        nazivError.text('Назив е задолжително поле за правно лице');
                        isValid = false;
                    } else {
                        nazivError.text('');
                    }

                    // Validate EDB
                    var edbField = $('#Input_EDB');
                    var edbError = $('#edb-error');
                    var edbValue = edbField.val().trim();
                    
                    if (!edbValue) {
                        edbError.text('ЕДБ е задолжително поле за правно лице');
                        isValid = false;
                    } else if (!/^\d+$/.test(edbValue)) {
                        edbError.text('ЕДБ може да содржи само броеви');
                        isValid = false;
                    } else {
                        edbError.text('');
                        // Check for duplicate EDB
                        checkDuplicateEDB(edbValue, edbError);
                    }

                    // Validate MB
                    var mbField = $('#Input_MB');
                    var mbError = $('#mb-error');
                    var mbValue = mbField.val().trim();
                    
                    if (!mbValue) {
                        mbError.text('МБ е задолжително поле за правно лице');
                        isValid = false;
                    } else if (!/^\d+$/.test(mbValue)) {
                        mbError.text('МБ може да содржи само броеви');
                        isValid = false;
                    } else {
                        mbError.text('');
                        // Check for duplicate MB
                        checkDuplicateMB(mbValue, mbError);
                    }

                    // Validate Address from ID
                    var opstinaIdField = $('#Input_ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija');
                    var opstinaIdError = $('#opstina-id-error');
                    if (!opstinaIdField.val()) {
                        opstinaIdError.text('Општина е задолжително поле');
                        isValid = false;
                    } else {
                        opstinaIdError.text('');
                    }

                    var ulicaIdField = $('#Input_UlicaOdDokumentZaIdentifikacija');
                    var ulicaIdError = $('#ulica-id-error');
                    if (!ulicaIdField.val().trim()) {
                        ulicaIdError.text('Улица е задолжително поле');
                        isValid = false;
                    } else {
                        ulicaIdError.text('');
                    }

                    var brojIdField = $('#Input_BrojOdDokumentZaIdentifikacija');
                    var brojIdError = $('#broj-id-error');
                    if (!brojIdField.val().trim()) {
                        brojIdError.text('Број е задолжително поле');
                        isValid = false;
                    } else {
                        brojIdError.text('');
                    }

                    // Validate Communication Address
                    var opstinaKomField = $('#Input_ListaOpstiniIdOpstinaZaKomunikacija');
                    var opstinaKomError = $('#opstina-kom-error');
                    if (!opstinaKomField.val()) {
                        opstinaKomError.text('Општина е задолжително поле');
                        isValid = false;
                    } else {
                        opstinaKomError.text('');
                    }

                    var ulicaKomField = $('#Input_UlicaZaKomunikacija');
                    var ulicaKomError = $('#ulica-kom-error');
                    if (!ulicaKomField.val().trim()) {
                        ulicaKomError.text('Улица е задолжително поле');
                        isValid = false;
                    } else {
                        ulicaKomError.text('');
                    }

                    var brojKomField = $('#Input_BrojZaKomunikacija');
                    var brojKomError = $('#broj-kom-error');
                    if (!brojKomField.val().trim()) {
                        brojKomError.text('Број е задолжително поле');
                        isValid = false;
                    } else {
                        brojKomError.text('');
                    }

                    // Validate Phone
                    var telField = $('#Input_Tel');
                    var telError = $('#telefon-error');
                    var phoneValue = telField.val().trim();
                    
                    if (!phoneValue) {
                        telError.text('Телефон е задолжително поле');
                        isValid = false;
                    } else if (!validatePhoneNumber(phoneValue)) {
                        telError.text('Внесете валиден телефонски број');
                        isValid = false;
                    } else {
                        telError.text('');
                    }
                }
                
                return isValid;
            }

            // Add validation on input change for Pravno Lice fields including address fields
            $('#Input_Naziv, #Input_EDB, #Input_MB, ' +
            '#Input_ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija, #Input_UlicaOdDokumentZaIdentifikacija, ' +
            '#Input_BrojOdDokumentZaIdentifikacija, #Input_ListaOpstiniIdOpstinaZaKomunikacija, ' +
            '#Input_UlicaZaKomunikacija, #Input_BrojZaKomunikacija, #Input_Tel').on('input change', function() {
                if ($('#klientType').val() === 'P') {
                    validatePravnoLiceFields();
                }
            });

            // Add specific event listeners for EDB and MB fields
            $('#Input_EDB').on('input', function() {
                if ($('#klientType').val() === 'P') {
                    var edbValue = $(this).val().trim();
                    var edbError = $('#edb-error');
                    
                    if (edbValue && /^\d+$/.test(edbValue)) {
                        checkDuplicateEDB(edbValue, edbError);
                    }
                }
            });

            $('#Input_MB').on('input', function() {
                if ($('#klientType').val() === 'P') {
                    var mbValue = $(this).val().trim();
                    var mbError = $('#mb-error');
                    
                    if (mbValue && /^\d+$/.test(mbValue)) {
                        checkDuplicateMB(mbValue, mbError);
                    }
                }
            });

            // Allow only numbers in EDB and MB fields
            $('#Input_EDB, #Input_MB').on('keypress', function(e) {
                if (e.which < 48 || e.which > 57) {
                    e.preventDefault();
                }
            }).on('paste', function(e) {
                var pastedData = e.originalEvent.clipboardData.getData('text');
                if (!/^\d*$/.test(pastedData)) {
                    e.preventDefault();
                }
            });

            // Update client type change handler
            $('#klientType').change(function() {
                var type = $(this).val();
                if (type === 'F') {
                    validateFizickoLiceFields();
                    // Clear Pravno Lice errors
                    $('#naziv-error, #edb-error, #mb-error').text('');
                } else if (type === 'P') {
                    validatePravnoLiceFields();
                    // Clear Fizicko Lice errors
                    $('.text-danger').not('#naziv-error, #edb-error, #mb-error').text('');
                }
            });

            // Update form submission validation
            $('form').on('submit', function(e) {
                var klientType = $('#klientType').val();
                var isValid = true;

                if (klientType === 'F') {
                    isValid = validateFizickoLiceFields();
                } else if (klientType === 'P') {
                    isValid = validatePravnoLiceFields();
                }

                // Check for duplicate EMBG before submission
                var embgField = $('#Input_EMBG');
                var embgValue = embgField.val();
                var embgError = $('#embg-error');
                var isStranec = $('#Input_Stranec').is(':checked');
                
                if (klientType === 'F' && !isStranec && embgValue && embgValue.length === 13) {
                    // Perform synchronous check for duplicate EMBG
                    $.ajax({
                        url: '?handler=CheckDuplicateEMBG',
                        type: 'GET',
                        data: { embg: embgValue },
                        headers: {
                            "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                        },
                        async: false, // Make it synchronous for form validation
                        success: function(data) {
                            if (data.exists) {
                                embgError.text('ЕМБГ веќе постои во системот');
                                isValid = false;
                            }
                        },
                        error: function() {
                            // If AJAX fails, still allow submission (server will validate)
                        }
                    });
                }

                // Check for duplicate EDB before submission
                var edbField = $('#Input_EDB');
                var edbValue = edbField.val();
                var edbError = $('#edb-error');
                
                if (klientType === 'P' && edbValue && edbValue.length > 0) {
                    // Perform synchronous check for duplicate EDB
                    $.ajax({
                        url: '?handler=CheckDuplicateEDB',
                        type: 'GET',
                        data: { edb: edbValue },
                        headers: {
                            "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                        },
                        async: false, // Make it synchronous for form validation
                        success: function(data) {
                            if (data.exists) {
                                edbError.text('ЕДБ веќе постои во системот');
                                isValid = false;
                            }
                        },
                        error: function() {
                            // If AJAX fails, still allow submission (server will validate)
                        }
                    });
                }

                // Check for duplicate MB before submission
                var mbField = $('#Input_MB');
                var mbValue = mbField.val();
                var mbError = $('#mb-error');
                
                if (klientType === 'P' && mbValue && mbValue.length > 0) {
                    // Perform synchronous check for duplicate MB
                    $.ajax({
                        url: '?handler=CheckDuplicateMB',
                        type: 'GET',
                        data: { mb: mbValue },
                        headers: {
                            "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                        },
                        async: false, // Make it synchronous for form validation
                        success: function(data) {
                            if (data.exists) {
                                mbError.text('МБ веќе постои во системот');
                                isValid = false;
                            }
                        },
                        error: function() {
                            // If AJAX fails, still allow submission (server will validate)
                        }
                    });
                }

                if (!isValid) {
                    e.preventDefault();
                }
            });

            // Add validation for document date fields
            function validateDocumentDates() {
                var vaziOdField = $('#Input_DatumVaziOdPasosLicnaKarta');
                var vaziDoField = $('#Input_DatumVaziDoPasosLicnaKarta');
                var vaziOdError = $('#vazi-od-error');
                var vaziDoError = $('#vazi-do-error');
                
                // Clear previous error messages
                vaziOdError.text('');
                vaziDoError.text('');
                
                var vaziOdDate = vaziOdField.val() ? new Date(vaziOdField.val()) : null;
                var vaziDoDate = vaziDoField.val() ? new Date(vaziDoField.val()) : null;
                
                // Only validate if both dates are present
                if (vaziOdDate && vaziDoDate && vaziDoDate < vaziOdDate) {
                    // If end date is earlier than start date, set it to the start date
                    vaziDoField.val(vaziOdField.val());
                }
                
                return true;
            }

            // Add event listeners for date fields
            $('#Input_DatumVaziOdPasosLicnaKarta, #Input_DatumVaziDoPasosLicnaKarta').on('change', function() {
                validateDocumentDates();
            });

            // Add validation to form submission
            $('form').on('submit', function(e) {
                if (!validateDocumentDates()) {
                    e.preventDefault();
                }
            });

            // Add validation for contract date fields
            function validateContractDates() {
                var dogovorOdField = $('#Input_DaumNaDogovor');
                var dogovorDoField = $('#Input_DogovorVaziDo');
                var dogovorOdError = $('#dogovor-od-error');
                var dogovorDoError = $('#dogovor-do-error');
                
                // Clear previous error messages
                dogovorOdError.text('');
                dogovorDoError.text('');
                
                var dogovorOdDate = dogovorOdField.val() ? new Date(dogovorOdField.val()) : null;
                var dogovorDoDate = dogovorDoField.val() ? new Date(dogovorDoField.val()) : null;
                
                // Only check if both dates are present
                if (dogovorOdDate && dogovorDoDate && dogovorDoDate < dogovorOdDate) {
                    // If end date is earlier than start date, set it to the start date
                    dogovorDoField.val(dogovorOdField.val());
                }
                
                return true;
            }

            // Add event listeners for contract date fields
            $('#Input_DaumNaDogovor, #Input_DogovorVaziDo').on('change', function() {
                validateContractDates();
            });

            // Update form submission validation to include contract dates
            $('form').on('submit', function(e) {
                var klientType = $('#klientType').val();
                var isValid = true;

                if (klientType === 'F') {
                    isValid = validateFizickoLiceFields();
                } else if (klientType === 'P') {
                    isValid = validatePravnoLiceFields();
                }

                if (!validateDocumentDates() || !validateContractDates()) {
                    isValid = false;
                }

                if (!isValid) {
                    e.preventDefault();
                }
            });

            // Add the search functionality for nadreden field
            let searchTimeout;
            const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();

            // Add functionality to copy address from ID document to communication address
            function copyAddressToCommunication() {
                var opstinaId = $('#Input_ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija').val();
                var ulicaId = $('#Input_UlicaOdDokumentZaIdentifikacija').val();
                var brojId = $('#Input_BrojOdDokumentZaIdentifikacija').val();
                
                // Check if communication address fields have been manually edited
                var opstinaKomEdited = $('#Input_ListaOpstiniIdOpstinaZaKomunikacija').data('manually-edited');
                var ulicaKomEdited = $('#Input_UlicaZaKomunikacija').data('manually-edited');
                var brojKomEdited = $('#Input_BrojZaKomunikacija').data('manually-edited');
                
                // Only copy if the communication address hasn't been manually edited
                if (!opstinaKomEdited && opstinaId) {
                    $('#Input_ListaOpstiniIdOpstinaZaKomunikacija').val(opstinaId);
                }
                if (!ulicaKomEdited && ulicaId) {
                    $('#Input_UlicaZaKomunikacija').val(ulicaId);
                }
                if (!brojKomEdited && brojId) {
                    $('#Input_BrojZaKomunikacija').val(brojId);
                }
            }

            // Add event listeners for ID document address fields
            $('#Input_ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija, #Input_UlicaOdDokumentZaIdentifikacija, #Input_BrojOdDokumentZaIdentifikacija').on('change', function() {
                copyAddressToCommunication();
            });

            // Add event listeners for communication address fields to prevent reverse copying
            $('#Input_ListaOpstiniIdOpstinaZaKomunikacija, #Input_UlicaZaKomunikacija, #Input_BrojZaKomunikacija').on('input change', function() {
                // Mark that communication address has been manually edited
                $(this).data('manually-edited', true);
                
                // Add visual indicator that field has been manually edited
                $(this).addClass('border-warning');
                $(this).attr('title', 'Рачно уредено поле');
                
                // Add a small icon to indicate manual editing
                var fieldContainer = $(this).closest('.mb-3');
                if (!fieldContainer.find('.manual-edit-indicator').length) {
                    fieldContainer.append('<small class="text-warning manual-edit-indicator"><i class="fas fa-edit"></i> Рачно уредено</small>');
                }
            });



            $('#nadredenSearch').on('input', function() {
                clearTimeout(searchTimeout);
                const searchTerm = $(this).val();
                const resultsDiv = $('#nadredenSearchResults');

                if (searchTerm.length < 1) {
                    resultsDiv.hide();
                    return;
                }

                searchTimeout = setTimeout(function() {
                    $.ajax({
                        url: '?handler=SearchNadreden',
                        type: 'GET',
                        data: { mb: searchTerm },
                        headers: {
                            "RequestVerificationToken": antiForgeryToken
                        },
                        success: function(data) {
                            if (data && data.length > 0) {
                                let html = '<div class="list-group">';
                                data.forEach(item => {
                                    let displayText = '';
                                    if (item.tip === 'P') {
                                        displayText = `${item.naziv}`;
                                        let identifiers = [];
                                        if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                        if (item.edb) identifiers.push(`ЕДБ: ${item.edb}`);
                                        if (identifiers.length > 0) {
                                            displayText += ` (${identifiers.join(', ')})`;
                                        }
                                    } else {
                                        displayText = `${item.ime} ${item.prezime}`;
                                        let identifiers = [];
                                        if (item.embg) identifiers.push(`ЕМБГ: ${item.embg}`);
                                        if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                        if (identifiers.length > 0) {
                                            displayText += ` (${identifiers.join(', ')})`;
                                        }
                                    }
                                    html += `<a href="#" class="list-group-item list-group-item-action" data-id="${item.id}">
                                              ${displayText}
                                           </a>`;
                                });
                                html += '</div>';
                                resultsDiv.html(html).show();
                            } else {
                                resultsDiv.html('<div class="list-group"><div class="list-group-item">Нема пронајдени резултати</div></div>').show();
                            }
                        },
                        error: function() {
                            resultsDiv.html('<div class="list-group"><div class="list-group-item text-danger">Грешка при пребарување</div></div>').show();
                        }
                    });
                }, 300);
            });

            // Handle selection
            $(document).on('click', '#nadredenSearchResults .list-group-item', function(e) {
                e.preventDefault();
                const id = $(this).data('id');
                const displayText = $(this).text();
                $('#nadredenSearch').val(displayText.trim());
                $('#SifrarnikRabotniPoziciiIdNadreden').val(id);
                $('#nadredenSearchResults').hide();
            });

            // Hide results when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('#nadredenSearch, #nadredenSearchResults').length) {
                    $('#nadredenSearchResults').hide();
                }
            });

            // Clear field functionality
            $('.clear-field[data-target="nadreden"]').click(function() {
                $('#nadredenSearch').val('');
                $('#SifrarnikRabotniPoziciiIdNadreden').val('');
                $('#nadredenSearchResults').hide();
            });

            // Add search functionality for dejnost field
            let dejnostSearchTimeout;
            let allDejnosti = []; // Store all dejnosti for filtering

            // Load all dejnosti on page load
            function loadAllDejnosti() {
                $.ajax({
                    url: '?handler=GetAllDejnosti',
                    type: 'GET',
                    headers: {
                        "RequestVerificationToken": antiForgeryToken
                    },
                    success: function(data) {
                        allDejnosti = data;
                        displayDejnostiResults(allDejnosti);
                    },
                    error: function() {
                        console.log('Error loading dejnosti');
                    }
                });
            }

            // Display dejnosti results
            function displayDejnostiResults(dejnosti) {
                const resultsDiv = $('#dejnostSearchResults');
                if (dejnosti && dejnosti.length > 0) {
                    let html = '<div class="list-group">';
                    dejnosti.forEach(item => {
                        let displayText = `${item.sifraDejnost} - ${item.nazivDejnost}`;
                        html += `<a href="#" class="list-group-item list-group-item-action" data-id="${item.id}">
                                  ${displayText}
                               </a>`;
                    });
                    html += '</div>';
                    resultsDiv.html(html).show();
                } else {
                    resultsDiv.html('<div class="list-group"><div class="list-group-item">Нема пронајдени резултати</div></div>').show();
                }
            }

            // Filter dejnosti based on search term
            function filterDejnosti(searchTerm) {
                if (!searchTerm) {
                    return allDejnosti;
                }
                
                return allDejnosti.filter(item => 
                    item.sifraDejnost.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    item.nazivDejnost.toLowerCase().includes(searchTerm.toLowerCase())
                );
            }

            // Show dropdown on focus
            $('#dejnostSearch').on('focus', function() {
                if (allDejnosti.length === 0) {
                    loadAllDejnosti();
                } else {
                    displayDejnostiResults(allDejnosti);
                }
            });

            $('#dejnostSearch').on('input', function() {
                clearTimeout(dejnostSearchTimeout);
                const searchTerm = $(this).val();
                const resultsDiv = $('#dejnostSearchResults');

                if (searchTerm.length < 1) {
                    // Show all options when search is empty
                    displayDejnostiResults(allDejnosti);
                    return;
                }

                dejnostSearchTimeout = setTimeout(function() {
                    const filteredDejnosti = filterDejnosti(searchTerm);
                    displayDejnostiResults(filteredDejnosti);
                }, 300);
            });

            // Handle dejnost selection
            $(document).on('click', '#dejnostSearchResults .list-group-item', function(e) {
                e.preventDefault();
                const id = $(this).data('id');
                const displayText = $(this).text();
                $('#dejnostSearch').val(displayText.trim());
                $('#Input_ListaDejnostiIdDejnost').val(id);
                $('#dejnostSearchResults').hide();
            });

            // Hide dejnost results when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('#dejnostSearch, #dejnostSearchResults').length) {
                    $('#dejnostSearchResults').hide();
                }
            });

            // Clear dejnost field functionality
            $('.clear-field[data-target="dejnost"]').click(function() {
                $('#dejnostSearch').val('');
                $('#Input_ListaDejnostiIdDejnost').val('');
                $('#dejnostSearchResults').hide();
            });
        });
    </script>
} 