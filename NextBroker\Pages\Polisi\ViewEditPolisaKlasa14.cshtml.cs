using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.Threading.Tasks;

namespace NextBroker.Pages.Polisi
{
    public class ViewEditPolisaKlasa14Model : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public bool HasAdminAccess { get; private set; }

        public ViewEditPolisaKlasa14Model(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public async Task<IActionResult> OnGetAsync(long id)
        {
            if (!await HasPageAccess("ViewEditPolisaKlasa14"))
            {
                return RedirectToAccessDenied();
            }

            HasAdminAccess = await HasPageAccess("ViewEditPolisaKlasa14Admin");
            return Page();
        }
    }
}
