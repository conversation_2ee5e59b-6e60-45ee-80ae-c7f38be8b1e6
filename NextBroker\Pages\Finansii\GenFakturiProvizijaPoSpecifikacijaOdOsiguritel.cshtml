@page
@model NextBroker.Pages.Finansii.GenFakturiProvizijaPoSpecifikacijaOdOsiguritelModel

@{
    ViewData["Title"] = "Генерирај фактури за провизија по спецификација од осигурител";
}

<div class="container-fluid">
    <h4>@ViewData["Title"]</h4>

    <div class="row">
        <div class="col-md-12">
            <form method="post" id="invoiceForm">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label asp-for="Input.KlientiIdOsiguritel" class="control-label"></label>
                            <select asp-for="Input.KlientiIdOsiguritel" asp-items="Model.Osiguriteli" class="form-select">
                                <option value="">-- Изберете осигурител --</option>
                            </select>
                            <span asp-validation-for="Input.KlientiIdOsiguritel" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Input.BrojNaFaktura" class="control-label"></label>
                            <input asp-for="Input.BrojNaFaktura" class="form-control" readonly />
                            <span asp-validation-for="Input.BrojNaFaktura" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Input.DatumNaFaktura" class="control-label"></label>
                            <input asp-for="Input.DatumNaFaktura" class="form-control" type="date" />
                            <span asp-validation-for="Input.DatumNaFaktura" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Input.RokNaPlakanje" class="control-label"></label>
                            <input asp-for="Input.RokNaPlakanje" class="form-control" type="date" />
                            <span asp-validation-for="Input.RokNaPlakanje" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5>Ставки на фактура</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered" id="stavkiTable">
                                <thead>
                                    <tr>
                                        <th style="width: 80px">Ред. број</th>
                                        <th>Класа број и опис</th>
                                        <th style="width: 200px">Износ во МКД</th>
                                        <th style="width: 80px">Акции</th>
                                    </tr>
                                </thead>
                                <tbody id="stavkiTableBody">
                                    @for (var i = 0; i < Model.Input.Stavki.Count; i++)
                                    {
                                        <tr>
                                            <td>
                                                @(i + 1)
                                                <input type="hidden" name="Input.Stavki[@i].RedenBroj" value="@(i + 1)" />
                                            </td>
                                            <td>
                                                <select name="Input.Stavki[@i].KlasaBrojSoIme" class="form-select" required>
                                                    <option value="">-- Изберете класа --</option>
                                                    @foreach (var klasa in Model.KlasiOsiguruvanje)
                                                    {
                                                        <option value="@klasa.Value" selected="@(klasa.Value == Model.Input.Stavki[i].KlasaBrojSoIme)">@klasa.Text</option>
                                                    }
                                                </select>
                                            </td>
                                            <td>
                                                <input type="number" name="Input.Stavki[@i].IznosVoMKD" value="@Model.Input.Stavki[i].IznosVoMKD" 
                                                       class="form-control iznos-input" min="0" required />
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-danger btn-sm delete-row">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="2" class="text-end"><strong>Вкупно:</strong></td>
                                        <td><strong id="vkupnoIznos">@Model.VkupnoIznos.ToString("N0")</strong></td>
                                        <td></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                        <button type="button" class="btn btn-success" id="addRow">
                            <i class="fas fa-plus"></i> Додади ставка
                        </button>
                    </div>
                </div>

                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-primary">Генерирај фактура</button>
                </div>
            </form>
        </div>
    </div>

    @if (Model.ShowPreview)
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Преглед на фактура</h5>
                    </div>
                    <div class="text-end mb-3 mt-3 pe-3">
                        <button type="button" class="btn btn-success" id="btnSocuvajFaktura">
                            <i class="bi bi-save me-1"></i> Зачувај фактура
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="fakturaPreview" style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; position: relative; padding: 20px 40px;">
                            <!-- Background Logo -->
                            <div style="position: absolute; top: 20px; right: 40px; width: 150px; opacity: 0.15; z-index: 0; pointer-events: none;">
                                <img src="/images/logo/INCO_LOGO_Regular.svg" style="width: 100%; height: auto;" />
                            </div>
                            
                            <!-- Decorative Corner Elements -->
                            <div style="position: absolute; top: 10px; left: 10px; width: 20px; height: 20px; border-left: 1px solid rgba(212, 175, 55, 0.25); border-top: 1px solid rgba(212, 175, 55, 0.25);"></div>
                            <div style="position: absolute; top: 10px; right: 10px; width: 20px; height: 20px; border-right: 1px solid rgba(212, 175, 55, 0.25); border-top: 1px solid rgba(212, 175, 55, 0.25);"></div>
                            <div style="position: absolute; bottom: 10px; left: 10px; width: 20px; height: 20px; border-left: 1px solid rgba(212, 175, 55, 0.25); border-bottom: 1px solid rgba(212, 175, 55, 0.25);"></div>
                            <div style="position: absolute; bottom: 10px; right: 10px; width: 20px; height: 20px; border-right: 1px solid rgba(212, 175, 55, 0.25); border-bottom: 1px solid rgba(212, 175, 55, 0.25);"></div>
                            
                            <div style="position: relative; z-index: 1;">
                                <div style="text-align: left; margin-bottom: 20px; padding-right: 160px;">
                                    <h3 style="color: #2F4F4F; margin-bottom: 5px; font-size: 22px; text-shadow: 1px 1px 1px rgba(0,0,0,0.1);">Осигурително Брокерско Друштво ИНКО АД Скопје</h3>
                                    <h4 style="color: #000; margin-top: 0; font-size: 18px;">ФАКТУРА БРОЈ: @Model.Input.BrojNaFaktura</h4>
                                </div>
                                
                                <div style="margin-bottom: 20px; background-color: rgba(47, 79, 79, 0.03); padding: 15px; border-radius: 6px; border: 1px solid rgba(212, 175, 55, 0.2); box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
                                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F; min-width: 120px; display: inline-block;">До:</strong> @Model.OsiguritelNaziv</p>
                                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F; min-width: 120px; display: inline-block;">Датум на фактура:</strong> @Model.Input.DatumNaFaktura.ToString("dd.MM.yyyy")</p>
                                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F; min-width: 120px; display: inline-block;">Рок на плаќање:</strong> @Model.Input.RokNaPlakanje.ToString("dd.MM.yyyy")</p>
                                </div>

                                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); background-color: white;">
                                    <thead>
                                        <tr style="background: linear-gradient(90deg, #2F4F4F, #1a2f2f); color: white;">
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 13px;">Реден број</th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 13px;">Класа</th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: right; font-size: 13px;">Износ во МКД</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var stavka in Model.Input.Stavki)
                                        {
                                            <tr style="background-color: rgba(47, 79, 79, 0.02);">
                                                <td style="border: 1px solid #ddd; padding: 8px; font-size: 13px;">@stavka.RedenBroj</td>
                                                <td style="border: 1px solid #ddd; padding: 8px; font-size: 13px;">@stavka.KlasaBrojSoIme</td>
                                                <td style="border: 1px solid #ddd; padding: 8px; text-align: right; font-size: 13px;">@stavka.IznosVoMKD.ToString("N2")</td>
                                            </tr>
                                        }
                                        <tr>
                                            <td colspan="2" style="border: 1px solid #ddd; padding: 8px; text-align: right; font-weight: bold; font-size: 13px;">Вкупно:</td>
                                            <td style="border: 1px solid #ddd; padding: 8px; text-align: right; font-weight: bold; font-size: 13px;">@Model.VkupnoIznos.ToString("N2")</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <div style="margin-bottom: 20px; background-color: rgba(47, 79, 79, 0.03); padding: 15px; border-radius: 6px; border: 1px solid rgba(212, 175, 55, 0.2); box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
                                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F;">Со букви, износ на фактура:</strong> @Model.VkupnoIznosVoZborovi</p>
                                </div>

                                <div style="display: flex; justify-content: space-between; margin: 30px 0; position: relative;">
                                    <!-- Gold Accent Line -->
                                    <div style="position: absolute; top: -10px; left: 0; right: 0; height: 1px; background: linear-gradient(90deg, #D4AF37, transparent); opacity: 0.3;"></div>
                                    
                                    <div style="text-align: center; flex: 1; padding: 0 10px;">
                                        <p style="margin-bottom: 20px;"><strong style="color: #2F4F4F; font-size: 13px;">Фактурирал</strong></p>
                                        <p style="margin-top: 10px; font-size: 12px;">@Model.FakturiralIme</p>
                                    </div>
                                    <div style="text-align: center; flex: 1; padding: 0 10px;">
                                        <p style="margin-bottom: 20px;"><strong style="color: #2F4F4F; font-size: 13px;">Примил</strong></p>
                                        <p style="border-top: 1px solid rgba(47, 79, 79, 0.3); width: 80%; margin: 0 auto;"></p>
                                    </div>
                                    <div style="text-align: center; flex: 1; padding: 0 10px;">
                                        <p style="margin-bottom: 20px;"><strong style="color: #2F4F4F; font-size: 13px;">Одговорно лице за потпис на фактура</strong></p>
                                        <p style="border-top: 1px solid rgba(47, 79, 79, 0.3); width: 80%; margin: 0 auto;"></p>
                                    </div>
                                </div>

                                <!-- Gold Accent Bottom -->
                                <div style="position: relative; margin-top: 20px;">
                                    <div style="position: absolute; top: -10px; left: 0; right: 0; height: 1px; background: linear-gradient(90deg, transparent, #D4AF37, transparent); opacity: 0.3;"></div>
                                    <div style="position: absolute; top: -8px; left: 0; right: 0; height: 1px; background: linear-gradient(90deg, transparent, #D4AF37, transparent); opacity: 0.2;"></div>
                                    
                                    <div style="display: flex; justify-content: space-between; font-size: 0.85em; background-color: rgba(47, 79, 79, 0.03); padding: 15px; border-radius: 6px; border: 1px solid rgba(212, 175, 55, 0.2); box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
                                        <div>
                                            <p style="margin: 3px 0;"><strong style="color: #2F4F4F;">ЕДБ 4080025630210</strong></p>
                                            <p style="margin: 3px 0; color: #000;">Сметка: 210 078354340168</p>
                                        </div>
                                        <div>
                                            <p style="margin: 3px 0;"><strong style="color: #2F4F4F;">МБ 7835434</strong></p>
                                            <p style="margin: 3px 0; color: #000;">Банка: НЛБ Банка АД Скопје</p>
                                        </div>
                                        <div>
                                            <p style="margin: 3px 0;"><strong style="color: #2F4F4F;">Контакт маил: <EMAIL></strong></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add Debug Panel -->
        <div class="card mt-3" style="display: none;">
            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Debug Information</h5>
                <button type="button" class="btn btn-sm btn-outline-light" onclick="clearDebugLog()">Clear Log</button>
            </div>
            <div class="card-body">
                <div id="debugLog" style="max-height: 300px; overflow-y: auto; font-family: monospace;">
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <!-- Add jsPDF and html2canvas libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <script>
        // Add debug logging function
        function addDebugLog(message, type = 'info') {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-danger' : 
                         type === 'success' ? 'text-success' : 
                         type === 'warning' ? 'text-warning' : 'text-info';
            
            debugLog.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            debugLog.scrollTop = debugLog.scrollHeight;
            
            // Also log to console
            console.log(`[${type}] ${message}`);
        }

        function clearDebugLog() {
            document.getElementById('debugLog').innerHTML = '';
        }

        $(document).ready(function () {
            function updateRowNumbers() {
                $('#stavkiTableBody tr').each(function (index) {
                    $(this).find('td:first').text(index + 1);
                    $(this).find('input[name$=".RedenBroj"]').val(index + 1);
                    
                    // Update the name attributes for all inputs in this row
                    $(this).find('select, input').each(function () {
                        var name = $(this).attr('name');
                        if (name) {
                            var newName = name.replace(/\[\d+\]/, '[' + index + ']');
                            $(this).attr('name', newName);
                        }
                    });
                });
            }

            function calculateTotal() {
                var total = 0;
                $('.iznos-input').each(function () {
                    var value = parseInt($(this).val()) || 0;
                    total += value;
                });
                $('#vkupnoIznos').text(total.toLocaleString('mk-MK'));
            }

            $('#addRow').click(function () {
                var rowCount = $('#stavkiTableBody tr').length;
                var klasiOptions = '@Html.Raw(string.Join("", Model.KlasiOsiguruvanje.Select(k => $"<option value=\"{k.Value}\">{k.Text}</option>")))';
                
                var newRow = `
                    <tr>
                        <td>
                            ${rowCount + 1}
                            <input type="hidden" name="Input.Stavki[${rowCount}].RedenBroj" value="${rowCount + 1}" />
                        </td>
                        <td>
                            <select name="Input.Stavki[${rowCount}].KlasaBrojSoIme" class="form-select" required>
                                <option value="">-- Изберете класа --</option>
                                ${klasiOptions}
                            </select>
                        </td>
                        <td>
                            <input type="number" name="Input.Stavki[${rowCount}].IznosVoMKD" 
                                   class="form-control iznos-input" min="0" required />
                        </td>
                        <td>
                            <button type="button" class="btn btn-danger btn-sm delete-row">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
                
                $('#stavkiTableBody').append(newRow);
                updateRowNumbers();
            });

            $(document).on('click', '.delete-row', function () {
                $(this).closest('tr').remove();
                updateRowNumbers();
                calculateTotal();
            });

            $(document).on('input', '.iznos-input', function () {
                calculateTotal();
            });

            // Add initial row if table is empty
            if ($('#stavkiTableBody tr').length === 0) {
                $('#addRow').click();
            }

            // Get anti-forgery token
            const token = $('input[name="__RequestVerificationToken"]').val();
            
            $('#btnSocuvajFaktura').on('click', function() {
                // Disable the button immediately to prevent double-clicking
                $(this).prop('disabled', true);
                
                // Change text to show processing
                $(this).html('<i class="bi bi-hourglass-split me-1"></i> Зачувување...');
                
                // Start the PDF generation
                generatePDF(this);
            });

            function generatePDF(saveBtn) {
                // Get the invoice preview element
                const element = document.getElementById('fakturaPreview');
                
                // Make temporary adjustments to improve PDF appearance
                const originalStyle = element.getAttribute('style') || '';
                element.setAttribute('style', originalStyle + '; width: 794px; padding: 20px;');
                
                // Use html2canvas to capture the element as an image
                html2canvas(element, {
                    scale: 1.5,  // Higher scale for better quality
                    useCORS: true,
                    logging: false,
                    allowTaint: true,
                    backgroundColor: '#ffffff'
                }).then(canvas => {
                    // Restore original styling
                    element.setAttribute('style', originalStyle);
                    
                    // Initialize jsPDF
                    const { jsPDF } = window.jspdf;
                    const doc = new jsPDF('p', 'mm', 'a4', true); // The 'true' enables compression
                    
                    // Calculate dimensions to fill the page
                    const imgData = canvas.toDataURL('image/jpeg', 0.7); // Use JPEG format with lower quality to reduce size
                    
                    const pageWidth = doc.internal.pageSize.getWidth();
                    const pageHeight = doc.internal.pageSize.getHeight();
                    
                    // Use larger margins to fill the page better
                    const margin = 5; // 5mm margin
                    const usableWidth = pageWidth - (margin * 2);
                    const usableHeight = pageHeight - (margin * 2);
                    
                    // Add the image to the PDF with custom dimensions to fit the page
                    doc.addImage(imgData, 'JPEG', margin, margin, usableWidth, usableHeight);
                    
                    // Generate filename based on invoice number
                    const invoiceNumber = '@Model.Input.BrojNaFaktura';
                    const fileName = `faktura_${invoiceNumber.replace('/', '_')}_${new Date().toISOString().slice(0, 10)}.pdf`;
                    
                    // Save the PDF
                    doc.save(fileName);

                    // Get PDF as binary data for SFTP upload
                    const pdfBlob = doc.output('blob');
                    const reader = new FileReader();
                    
                    addDebugLog('Starting PDF to base64 conversion...', 'info');
                    
                    reader.onloadend = function() {
                        // This contains the base64 data
                        const base64data = reader.result;
                        const sizeKB = Math.round(base64data.length / 1024);
                        addDebugLog(`PDF converted to base64, size: ${sizeKB} KB`, 'success');
                        
                        // Upload PDF to SFTP server
                        addDebugLog('Starting SFTP upload...', 'info');
                        
                        // Get the invoice number and encode it properly
                        addDebugLog(`Invoice number for upload: ${invoiceNumber}`, 'info');
                        
                        const uploadData = {
                            brojNaFaktura: invoiceNumber,
                            fileName: fileName,
                            pdfBase64: base64data
                        };
                        
                        addDebugLog('Preparing upload request with data:', 'info');
                        addDebugLog(`- brojNaFaktura: ${uploadData.brojNaFaktura}`, 'info');
                        addDebugLog(`- fileName: ${uploadData.fileName}`, 'info');
                        addDebugLog(`- pdfBase64 length: ${uploadData.pdfBase64.length} chars`, 'info');
                        
                        $.ajax({
                            url: '?handler=UploadPdfToSftp',
                            type: 'POST',
                            contentType: 'application/json; charset=utf-8',
                            data: JSON.stringify(uploadData),
                            headers: {
                                'RequestVerificationToken': token
                            }
                        }).then(function(response) {
                            if (response.success) {
                                addDebugLog(`PDF uploaded to SFTP successfully: ${response.filePath}`, 'success');
                                addDebugLog(`Saved with filename: ${response.fileName}`, 'success');
                            } else {
                                addDebugLog(`Failed to upload PDF to SFTP: ${response.message}`, 'error');
                            }

                            // Display server debug messages if available
                            if (response.debugLog && Array.isArray(response.debugLog)) {
                                addDebugLog('--- Server Debug Log ---', 'info');
                                response.debugLog.forEach(log => {
                                    addDebugLog(log.Message, log.Type);
                                });
                                addDebugLog('--- End Server Debug Log ---', 'info');
                            }
                        }).catch(function(error) {
                            addDebugLog(`Error uploading PDF to SFTP: ${error.statusText || error.message}`, 'error');
                            if (error.responseJSON) {
                                addDebugLog(`Server error details: ${error.responseJSON.message}`, 'error');
                            }
                        });
                    };
                    
                    reader.onerror = function(error) {
                        addDebugLog(`Error converting PDF to base64: ${error}`, 'error');
                    };
                    
                    reader.readAsDataURL(pdfBlob);
                    
                    // Save invoice data to database
                    const invoiceData = {
                        brojNaFaktura: '@Model.Input.BrojNaFaktura',
                        fakturaDo: '@Html.Raw(Model.OsiguritelNaziv)',
                        datumNaFaktura: '@Model.Input.DatumNaFaktura.ToString("yyyy-MM-dd")',
                        rokNaPlakanje: '@Model.Input.RokNaPlakanje.ToString("yyyy-MM-dd")',
                        iznos: @Model.VkupnoIznos.ToString(System.Globalization.CultureInfo.InvariantCulture),
                      //  datumOd: '@DateTime.Today.AddMonths(-1).ToString("yyyy-MM-dd")',
                      //  datumDo: '@DateTime.Today.ToString("yyyy-MM-dd")'
                    };

                    // Save invoice data to database
                    $.ajax({
                        url: '?handler=SaveInvoiceData',
                        type: 'POST',
                        contentType: 'application/json; charset=utf-8',
                        data: JSON.stringify(invoiceData),
                        headers: {
                            'RequestVerificationToken': token
                        }
                    }).then(function(response) {
                        if (!response.success) {
                            console.error('Failed to save invoice data:', response.message);
                        }
                    }).catch(function(error) {
                        console.error('Error saving invoice data:', error);
                    });
                    
                    // Update button state
                    $(saveBtn).html('<i class="bi bi-check-circle me-1"></i> Зачувано');
                    $(saveBtn).removeClass('btn-success').addClass('btn-outline-success');
                    
                    // Add a new button to generate another invoice if needed
                    const resetButton = $('<button type="button" class="btn btn-outline-primary ms-2"><i class="bi bi-arrow-repeat me-1"></i> Нова фактура</button>');
                    resetButton.on('click', function() {
                        location.reload();
                    });
                    $(saveBtn).after(resetButton);
                    
                }).catch(function(error) {
                    console.error('Error generating PDF:', error);
                    $(saveBtn).html('<i class="bi bi-x-circle me-1"></i> Грешка');
                    $(saveBtn).removeClass('btn-success').addClass('btn-danger');
                    alert('Грешка при генерирање на PDF: ' + error);
                });
            }
        });
    </script>
}
