using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using System.Data;
using System.Runtime.Serialization;

namespace NextBroker.Pages.Polisi
{
    public class DodajPolisaKlasa1Model : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public DodajPolisaKlasa1Model(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        [BindProperty]
        public PolisaInputModel Input { get; set; } = new();

        public IEnumerable<SelectListItem> Osiguriteli { get; set; }
        public IEnumerable<SelectListItem> KlasiOsiguruvanje { get; set; }
        public IEnumerable<SelectListItem> Produkti { get; set; }
        public IEnumerable<SelectListItem> PriciniZaStorno { get; set; }
        public IEnumerable<SelectListItem> TipoviNaPlakanje { get; set; }
        public IEnumerable<SelectListItem> Banki { get; set; }
        public IEnumerable<SelectListItem> TipoviNaFaktura { get; set; }
        public IEnumerable<SelectListItem> Valuti { get; set; }
        public IEnumerable<SelectListItem> NaciniNaPlakanje { get; set; }
        public IEnumerable<SelectListItem> ListaDejnosti { get; set; }
        public IEnumerable<SelectListItem> TipoviVozila { get; set; }

        public enum TipNaFakturaEnum
        {
            [Display(Name = "Влезна фактура кон клиент")]
            [EnumMember(Value = "Влезна фактура кон клиент")]
            VleznaFakturaKonKlient,
            
            [Display(Name = "Влезна фактура кон брокер")]
            [EnumMember(Value = "Влезна фактура кон брокер")]
            VleznaFakturaKonBroker
        }

        public async Task<IActionResult> OnGetAsync()
        {
            if (!await HasPageAccess("DodajPolisaKlasa1"))
            {
                return RedirectToAccessDenied();
            }

            await LoadOsiguriteli();
            await LoadKlasiOsiguruvanje();
            await LoadProdukti();
            await LoadPriciniZaStorno();
            await LoadTipoviNaPlakanje();
            await LoadBanki();
            await LoadValuti();
            await LoadNaciniNaPlakanje();
            await LoadListaDejnosti();
            LoadTipoviNaFaktura();
            await LoadTipoviVozila();
            return Page();
        }

        private async Task LoadOsiguriteli()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Naziv 
                    FROM Klienti 
                    WHERE Osiguritel = 1 
                    AND ZivotNezivot = N'Неживот'
                    AND DogovorVaziDo > Convert(date,GETDATE())
                    ORDER BY Naziv", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Naziv"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Osiguriteli = items;
                }
            }
        }

        private async Task LoadKlasiOsiguruvanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, CONCAT(KlasaBroj, ' - ', KlasaIme) as DisplayName 
                    FROM KlasiOsiguruvanje 
                    WHERE (Disabled = 0 OR Disabled IS NULL)
                    AND Id = '1'
                    ORDER BY KlasaBroj", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayName"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    KlasiOsiguruvanje = items;
                }
            }
        }

        private async Task LoadProdukti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Ime, Kategorija 
                    FROM Produkti
                    WHERE KlasaOsiguruvanjeId = '1'
                    ORDER BY Kategorija, Ime", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    var groups = new Dictionary<string, SelectListGroup>();

                    while (await reader.ReadAsync())
                    {
                        var kategorija = reader["Kategorija"].ToString();
                        if (!groups.ContainsKey(kategorija))
                        {
                            groups[kategorija] = new SelectListGroup { Name = kategorija };
                        }

                        items.Add(new SelectListItem
                        {
                            Value = reader["Id"].ToString(),
                            Text = reader["Ime"].ToString(),
                            Group = groups[kategorija]
                        });
                    }
                    Produkti = items;
                }
            }
        }

        private async Task LoadPriciniZaStorno()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, PricinaZaStorno 
                    FROM SifrarnikPricinaZaStorno 
                    ORDER BY PricinaZaStorno", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["PricinaZaStorno"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    PriciniZaStorno = items;
                }
            }
        }

        private async Task LoadTipoviNaPlakanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, TipNaPlakanje 
                    FROM SifrarnikTipNaPlakanje 
                    ORDER BY TipNaPlakanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["TipNaPlakanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    TipoviNaPlakanje = items;
                }
            }
        }

        private async Task LoadBanki()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Banka 
                    FROM SifrarnikBanki 
                    ORDER BY Banka", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Banka"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Banki = items;
                }
            }
        }

        private void LoadTipoviNaFaktura()
        {
            TipoviNaFaktura = Enum.GetValues(typeof(TipNaFakturaEnum))
                .Cast<TipNaFakturaEnum>()
                .Select(e => new SelectListItem
                {
                    Text = e.GetType()
                        .GetMember(e.ToString())
                        .First()
                        .GetCustomAttribute<DisplayAttribute>()
                        ?.Name ?? e.ToString(),
                    Value = e.GetType()
                        .GetMember(e.ToString())
                        .First()
                        .GetCustomAttribute<EnumMemberAttribute>()
                        ?.Value ?? e.ToString()
                })
                .ToList();
        }

        private async Task LoadValuti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, CONCAT(Valuta, ' - ', ImeValuta) as DisplayName 
                    FROM SifrarnikValuti 
                    ORDER BY Valuta", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayName"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Valuti = items;
                }
            }
        }

        private async Task LoadNaciniNaPlakanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, NacinNaPlakanje 
                    FROM SifrarnikNacinNaPlakanje 
                    WHERE Id not in ('2','3','4')
                    ORDER BY NacinNaPlakanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["NacinNaPlakanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    NaciniNaPlakanje = items;
                }
            }
        }

        private async Task LoadListaDejnosti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, CONCAT(SifraDejnost, ' - ', NazivDejnost) as DisplayName 
                    FROM ListaDejnosti 
                    ORDER BY SifraDejnost", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayName"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    ListaDejnosti = items;
                }
            }
        }

        private async Task LoadTipoviVozila()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, TipNaVozilo 
                    FROM SifrarnikTipNaVozilo 
                    ORDER BY TipNaVozilo", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["TipNaVozilo"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    TipoviVozila = items;
                }
            }
        }

        public async Task<IActionResult> OnGetSearchKlientiAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE MB LIKE @Search + '%'
                       OR EDB LIKE @Search + '%'
                       OR EMBG LIKE @Search + '%'
                       OR Naziv LIKE '%' + @Search + '%'
                       OR Ime LIKE '%' + @Search + '%'
                       OR Prezime LIKE '%' + @Search + '%'
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<IActionResult> OnGetSearchSorabotniciAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE (KlientSorabotnik = 1 or KlientVraboten = 1)
                        AND (MB LIKE @Search + '%'
                        OR EDB LIKE @Search + '%'
                        OR EMBG LIKE @Search + '%'
                        OR Naziv LIKE '%' + @Search + '%'
                        OR Ime LIKE '%' + @Search + '%'
                        OR Prezime LIKE '%' + @Search + '%')
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<IActionResult> OnGetLoadOpstiniAsync()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Opstina 
                    FROM ListaOpstini 
                    ORDER BY Opstina", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            opstina = reader["Opstina"]
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }
        
         public async Task<JsonResult> OnGetCheckBrojNaPolisaAsync(string brojNaPolisa)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT COUNT(*) 
                    FROM Polisi 
                    WHERE BrojNaPolisa = @BrojNaPolisa 
                    AND (Storno = 0 OR Storno IS NULL)", connection))
                {
                    cmd.Parameters.AddWithValue("@BrojNaPolisa", brojNaPolisa);
                    int count = Convert.ToInt32(await cmd.ExecuteScalarAsync());
                    return new JsonResult(new { exists = count > 0 });
                }
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            // Check for duplicate policy number
            if (!string.IsNullOrEmpty(Input.BrojNaPolisa))
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        SELECT COUNT(*) 
                        FROM Polisi 
                        WHERE BrojNaPolisa = @BrojNaPolisa 
                        AND (Storno = 0 OR Storno IS NULL)", connection))
                    {
                        cmd.Parameters.AddWithValue("@BrojNaPolisa", Input.BrojNaPolisa);
                        int count = Convert.ToInt32(await cmd.ExecuteScalarAsync());
                        if (count > 0)
                        {
                            return new JsonResult(new { 
                                success = false, 
                                errorMessage = "Веќе постои полиса со овој број која не е сторнирана!" 
                            });
                        }
                    }
                }
            }
            var debugInfo = new Dictionary<string, object>();
            try
            {
                if (!ModelState.IsValid)
                {
                    // Remove any validation errors for these fields
                    ModelState.Remove("Input.BrojNaVleznaFaktura");
                    ModelState.Remove("Input.BrojNaIzleznaFaktura");
                    
                    // Check if there are any other validation errors
                    if (!ModelState.IsValid)
                    {
                        var errors = ModelState.Values
                            .SelectMany(v => v.Errors)
                            .Select(e => e.ErrorMessage);
                        debugInfo["validationErrors"] = errors;
                        return new JsonResult(new { success = false, message = "Validation errors: " + string.Join(", ", errors), debug = debugInfo });
                    }
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlTransaction transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            long polisaId;

                            // Insert into Polisi table
                            using (SqlCommand cmd = new SqlCommand(@"
                                INSERT INTO Polisi (
                                    UsernameCreated,
                                    KlientiIdOsiguritel,
                                    KlasiOsiguruvanjeIdKlasa,
                                    ProduktiIdProizvod,
                                    BrojNaPolisa,
                                    KlientiIdDogovoruvac,
                                    KlientiIdOsigurenik,
                                    Kolektivna,
                                    KolektivnaNeodredenBrOsigurenici,
                                    DatumVaziOd,
                                    DatumVaziDo,
                                    DatumNaIzdavanje,
                                    SifrarnikValutiIdValuta,
                                    KlientiIdSorabotnik,
                                    Faktoring,
                                    ProcentFranshiza,
                                    FranshizaIznos,
                                    SifrarnikValutiIdFranshizaValuta,
                                    KoregiranaStapkaNaProvizija,
                                    SifrarnikNacinNaPlakjanjeId,
                                    TipNaFaktura,
                                    BrojNaFakturaVlezna,
                                    DatumNaFakturaVlezna,
                                    RokNaPlakjanjeFakturaVlezna,
                                    SifrarnikTipNaPlakanjeId,
                                    SifrarnikBankiIdBanka,
                                    GeneriranaFakturaIzlezna,
                                    BrojNaFakturaIzlezna,
                                    DatumNaIzleznaFaktura,
                                    RokNaPlakjanjeFakturaIzlezna,
                                    Storno,
                                    PricinaZaStorno
                                ) 
                                VALUES (
                                    @UsernameCreated,
                                    @KlientiIdOsiguritel,
                                    @KlasiOsiguruvanjeIdKlasa,
                                    @ProduktiIdProizvod,
                                    @BrojNaPolisa,
                                    @KlientiIdDogovoruvac,
                                    @KlientiIdOsigurenik,
                                    @Kolektivna,
                                    @KolektivnaNeodredenBrOsigurenici,
                                    @DatumVaziOd,
                                    @DatumVaziDo,
                                    @DatumNaIzdavanje,
                                    @SifrarnikValutiIdValuta,
                                    @KlientiIdSorabotnik,
                                    @Faktoring,
                                    @ProcentFranshiza,
                                    @FranshizaIznos,
                                    @SifrarnikValutiIdFranshizaValuta,
                                    @KoregiranaStapkaNaProvizija,
                                    @SifrarnikNacinNaPlakjanjeId,
                                    @TipNaFaktura,
                                    @BrojNaFakturaVlezna,
                                    @DatumNaFakturaVlezna,
                                    @RokNaPlakjanjeFakturaVlezna,
                                    @SifrarnikTipNaPlakanjeId,
                                    @SifrarnikBankiIdBanka,
                                    @GeneriranaFakturaIzlezna,
                                    @BrojNaFakturaIzlezna,
                                    @DatumNaIzleznaFaktura,
                                    @RokNaPlakjanjeFakturaIzlezna,
                                    @Storno,
                                    @PricinaZaStorno
                                );
                                SELECT SCOPE_IDENTITY();", connection, transaction))
                            {
                                cmd.Parameters.AddWithValue("@UsernameCreated", HttpContext.Session.GetString("Username"));
                                cmd.Parameters.AddWithValue("@KlientiIdOsiguritel", Input.KlientiIdOsiguritel);
                                cmd.Parameters.AddWithValue("@KlasiOsiguruvanjeIdKlasa", Input.KlasiOsiguruvanjeIdKlasa);
                                cmd.Parameters.AddWithValue("@ProduktiIdProizvod", Input.ProduktiIdProizvod ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@BrojNaPolisa", Input.BrojNaPolisa ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@KlientiIdDogovoruvac", Input.KlientiIdDogovoruvac ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@KlientiIdOsigurenik", Input.KlientiIdOsigurenik ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@Kolektivna", Input.Kolektivna);
                                cmd.Parameters.AddWithValue("@KolektivnaNeodredenBrOsigurenici", Input.KolektivnaNeodredenBrOsigurenici);
                                cmd.Parameters.AddWithValue("@DatumVaziOd", Input.DatumVaziOd ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@DatumVaziDo", Input.DatumVaziDo ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@DatumNaIzdavanje", Input.DatumNaIzdavanje ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@SifrarnikValutiIdValuta", Input.SifrarnikValutiIdValuta ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@KlientiIdSorabotnik", Input.KlientiIdSorabotnik ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@Faktoring", Input.Faktoring);
                                cmd.Parameters.AddWithValue("@ProcentFranshiza", Input.ProcentFranshiza ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@FranshizaIznos", Input.FranshizaIznos.HasValue ? (object)Input.FranshizaIznos.Value : DBNull.Value);
                                cmd.Parameters.AddWithValue("@SifrarnikValutiIdFranshizaValuta", Input.SifrarnikValutiIdFranshizaValuta ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@KoregiranaStapkaNaProvizija", Input.KoregiranaStapkaNaProvizija ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@SifrarnikNacinNaPlakjanjeId", Input.SifrarnikNacinNaPlakjanjeId ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TipNaFaktura", Input.TipNaFaktura?.ToString() ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@BrojNaFakturaVlezna", Input.BrojNaVleznaFaktura ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@DatumNaFakturaVlezna", Input.DatumNaVleznaFaktura ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@RokNaPlakjanjeFakturaVlezna", Input.RokNaPlakanjeVleznaFaktura ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@SifrarnikTipNaPlakanjeId", Input.SifrarnikTipNaPlakanjeId ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@SifrarnikBankiIdBanka", Input.SifrarnikBankiIdBanka ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@GeneriranaFakturaIzlezna", Input.GeneriranaFakturaIzlezna);
                                cmd.Parameters.AddWithValue("@BrojNaFakturaIzlezna", Input.BrojNaIzleznaFaktura ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@DatumNaIzleznaFaktura", Input.DatumNaIzleznaFaktura ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@RokNaPlakjanjeFakturaIzlezna", Input.RokNaPlakanjeIzleznaFaktura ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@Storno", Input.Storno);
                                cmd.Parameters.AddWithValue("@PricinaZaStorno", Input.SifrarnikPricinaZaStornoId?.ToString() ?? (object)DBNull.Value);

                                // Execute and get the new Polisi ID
                                polisaId = Convert.ToInt64(await cmd.ExecuteScalarAsync());
                            }

                            debugInfo["polisaId"] = polisaId;

                            // Handle osigurenici data
                            if (Input.Kolektivna)
                            {
                                var osigureniciData = Request.Form["osigurenici"].ToString();
                                debugInfo["receivedOsigureniciData"] = osigureniciData;

                                if (!string.IsNullOrEmpty(osigureniciData))
                                {
                                    var options = new JsonSerializerOptions
                                    {
                                        PropertyNameCaseInsensitive = true
                                    };
                                    var osigurenici = JsonSerializer.Deserialize<List<OsigurenikModel>>(osigureniciData, options);
                                    debugInfo["deserializedOsigurenici"] = osigurenici;

                                    foreach (var osigurenik in osigurenici)
                                    {
                                        using (SqlCommand cmd = new SqlCommand(@"
                                            INSERT INTO PolisiOsigureniciKolektivno (
                                                PolisaId,
                                                Ime,
                                                Prezime,
                                                EMBG,
                                                BrojNaLicnaKarta,
                                                BrojNaPasos,
                                                ListaOpstiniId,
                                                Adresa,
                                                Broj,
                                                Telefon,
                                                Email,
                                                UsernameCreated
                                            ) 
                                            VALUES (
                                                @PolisaId,
                                                @Ime,
                                                @Prezime,
                                                @EMBG,
                                                @BrojNaLicnaKarta,
                                                @BrojNaPasos,
                                                @ListaOpstiniId,
                                                @Adresa,
                                                @Broj,
                                                @Telefon,
                                                @Email,
                                                @UsernameCreated
                                            )", connection, transaction))
                                        {
                                            cmd.Parameters.AddWithValue("@PolisaId", polisaId);
                                            cmd.Parameters.AddWithValue("@Ime", osigurenik.Ime ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@Prezime", osigurenik.Prezime ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@EMBG", osigurenik.EMBG ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@BrojNaLicnaKarta", osigurenik.BrojNaLicnaKarta ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@BrojNaPasos", osigurenik.BrojNaPasos ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@ListaOpstiniId", osigurenik.ListaOpstiniId ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@Adresa", osigurenik.Adresa ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@Broj", osigurenik.Broj ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@Telefon", osigurenik.Telefon ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@Email", osigurenik.Email ?? (object)DBNull.Value);
                                            cmd.Parameters.AddWithValue("@UsernameCreated", HttpContext.Session.GetString("Username"));

                                            await cmd.ExecuteNonQueryAsync();
                                        }
                                    }
                                }
                                else
                                {
                                    debugInfo["error"] = "osigureniciData is empty";
                                }
                            }
                            else
                            {
                                debugInfo["kolektivnaChecked"] = Input.Kolektivna;
                                debugInfo["hasOsigureniciData"] = Request.Form.ContainsKey("osigurenici");
                            }

                            debugInfo["checkboxStates"] = new
                            {
                                Kolektivna = Input.Kolektivna,
                                KolektivnaNeodredenBrOsigurenici = Input.KolektivnaNeodredenBrOsigurenici,
                                Faktoring = Input.Faktoring,
                                GeneriranaFakturaIzlezna = Input.GeneriranaFakturaIzlezna,
                                Storno = Input.Storno
                            };

                            // After successful Polisi insert and getting polisaId
                            using (SqlCommand cmd = new SqlCommand(@"
                                INSERT INTO PolisiKlasa1 (
                                    UsernameCreated,
                                    PolisaId,
                                    BrojNaOsigurenici,
                                    ListaDejnostiId,
                                    PremijaZaEdnoLice,
                                    VkupnaPremija,
                                    SmrtOdNesrekjenSlucaj,
                                    TraenInvaliditet,
                                    TraenInvaliditet100Procenti,
                                    DnevenNadomest,
                                    Lekuvanje,
                                    TeskiBolesti,
                                    TrosociZaPogrebZaSmrtOdNezgoda,
                                    TrosociZaOstavinskaPostapka,
                                    TrosociZaOperacijaNezgoda,
                                    TrosociZaObrazovanie,
                                    ProcentNaPopustZaFakturaVoRok,
                                    IznosZaPlakjanjeVoRok,
                                    ProcentKomercijalenPopust,
                                    ProcentFinansiski,
                                    PremijaZaNaplata
                                ) 
                                VALUES (
                                    @UsernameCreated,
                                    @PolisaId,
                                    @BrojNaOsigurenici,
                                    @ListaDejnostiId,
                                    @PremijaZaEdnoLice,
                                    @VkupnaPremija,
                                    @SmrtOdNesrekjenSlucaj,
                                    @TraenInvaliditet,
                                    @TraenInvaliditet100Procenti,
                                    @DnevenNadomest,
                                    @Lekuvanje,
                                    @TeskiBolesti,
                                    @TrosociZaPogrebZaSmrtOdNezgoda,
                                    @TrosociZaOstavinskaPostapka,
                                    @TrosociZaOperacijaNezgoda,
                                    @TrosociZaObrazovanie,
                                    @ProcentNaPopustZaFakturaVoRok,
                                    @IznosZaPlakjanjeVoRok,
                                    @ProcentKomercijalenPopust,
                                    @ProcentFinansiski,
                                    @PremijaZaNaplata
                                )", connection, transaction))
                            {
                                cmd.Parameters.AddWithValue("@UsernameCreated", HttpContext.Session.GetString("Username"));
                                cmd.Parameters.AddWithValue("@PolisaId", polisaId);
                                cmd.Parameters.AddWithValue("@BrojNaOsigurenici", Input.BrojNaOsigurenici ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@ListaDejnostiId", Input.ListaDejnostiId ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@PremijaZaEdnoLice", Input.PremijaZaEdnoLice ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@VkupnaPremija", Input.VkupnaPremija ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@SmrtOdNesrekjenSlucaj", Input.SmrtOdNesrekjenSlucaj ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TraenInvaliditet", Input.TraenInvaliditet ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TraenInvaliditet100Procenti", Input.TraenInvaliditet100Procenti ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@DnevenNadomest", Input.DnevenNadomest ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@Lekuvanje", Input.Lekuvanje ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TeskiBolesti", Input.TeskiBolesti ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TrosociZaPogrebZaSmrtOdNezgoda", Input.TrosociZaPogrebZaSmrtOdNezgoda ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TrosociZaOstavinskaPostapka", Input.TrosociZaOstavinskaPostapka ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TrosociZaOperacijaNezgoda", Input.TrosociZaOperacijaNezgoda ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TrosociZaObrazovanie", Input.TrosociZaObrazovanie ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@ProcentNaPopustZaFakturaVoRok", Input.ProcentNaPopustZaFakturaVoRok ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@IznosZaPlakjanjeVoRok", Input.IznosZaPlakjanjeVoRok ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@ProcentKomercijalenPopust", Input.ProcentKomercijalenPopust ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@ProcentFinansiski", Input.ProcentFinansiski ?? (object)DBNull.Value);           
                                cmd.Parameters.AddWithValue("@PremijaZaNaplata", Input.PremijaZaNaplata ?? (object)DBNull.Value);
                                
                                await cmd.ExecuteNonQueryAsync();
                            }

                            if (Input.ProduktiIdProizvod == 11 || Input.ProduktiIdProizvod == 12)
                            {
                                using (SqlCommand cmd = new SqlCommand(@"
                                    INSERT INTO PolisiKlasa1Soobrakajna (
                                        UsernameCreated,
                                        PolisaId,
                                        SifrarnikTipNaVozilo,
                                        RegisterskaOznaka,
                                        Marka,
                                        KomercijalnaOznaka,
                                        Shasija,
                                        GodinaNaProizvodstvo,
                                        ZafatninaNaMotorotcm3,
                                        SilinaNaMotorotKW,
                                        BrojNaSedista,
                                        BojaNaVoziloto,
                                        NosivostKG,
                                        DatumNaRegistracija,
                                        BrojNaVpisot,
                                        DatumNaPrvataRegistracija,
                                        PrezimeNazivNaKorisnikot,
                                        Ime,
                                        AdresaNaPostojanoZivealiste,
                                        EMBNaKorisnikot,
                                        DatumNaPrvaRegistracijaVoRSM,
                                        DozvolataJaIzdal,
                                        OznakaNaOdobrenie,
                                        BrojNAEUPotvrdaZaSoobraznost,
                                        PrezimeNazivNaSopstvenikot,
                                        ImeSopstvenik,
                                        AdresaNaPostojanoZivealisteSediste,
                                        EMBNaFizickoLiceEMBNaPravnoLice,
                                        KategorijaIVidNaVoziloto,
                                        OblikINamenaNaKaroserijata,
                                        TipNaMotorot,
                                        VidNaGorivo,
                                        BrojNaVrtezi,
                                        IdentifikacionenBrojNaMotorot,
                                        MaksimalnaBrzinaKM,
                                        OdnosSilinaMasa,
                                        MasaNaVoziloto,
                                        NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG,
                                        NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG,
                                        NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG,
                                        BrojNaOski,
                                        RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka,
                                        NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka,
                                        Dolzhina,
                                        Visina,
                                        NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG,
                                        NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG,
                                        BrojNaMestaZaStoenje,
                                        DozvoleniPnevmaticiINaplatki,
                                        BrojNaMestazaLezenje,
                                        CO2,
                                        NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka,
                                        StacionarnaBucavost
                                    ) 
                                    VALUES (
                                        @UsernameCreated,
                                        @PolisaId,
                                        @SifrarnikTipNaVozilo,
                                        @RegisterskaOznaka,
                                        @Marka,
                                        @KomercijalnaOznaka,
                                        @Shasija,
                                        @GodinaNaProizvodstvo,
                                        @ZafatninaNaMotorotcm3,
                                        @SilinaNaMotorotKW,
                                        @BrojNaSedista,
                                        @BojaNaVoziloto,
                                        @NosivostKG,
                                        @DatumNaRegistracija,
                                        @BrojNaVpisot,
                                        @DatumNaPrvataRegistracija,
                                        @PrezimeNazivNaKorisnikot,
                                        @Ime,
                                        @AdresaNaPostojanoZivealiste,
                                        @EMBNaKorisnikot,
                                        @DatumNaPrvaRegistracijaVoRSM,
                                        @DozvolataJaIzdal,
                                        @OznakaNaOdobrenie,
                                        @BrojNAEUPotvrdaZaSoobraznost,
                                        @PrezimeNazivNaSopstvenikot,
                                        @ImeSopstvenik,
                                        @AdresaNaPostojanoZivealisteSediste,
                                        @EMBNaFizickoLiceEMBNaPravnoLice,
                                        @KategorijaIVidNaVoziloto,
                                        @OblikINamenaNaKaroserijata,
                                        @TipNaMotorot,
                                        @VidNaGorivo,
                                        @BrojNaVrtezi,
                                        @IdentifikacionenBrojNaMotorot,
                                        @MaksimalnaBrzinaKM,
                                        @OdnosSilinaMasa,
                                        @MasaNaVoziloto,
                                        @NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG,
                                        @NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG,
                                        @NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG,
                                        @BrojNaOski,
                                        @RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka,
                                        @NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka,
                                        @Dolzhina,
                                        @Visina,
                                        @NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG,
                                        @NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG,
                                        @BrojNaMestaZaStoenje,
                                        @DozvoleniPnevmaticiINaplatki,
                                        @BrojNaMestazaLezenje,
                                        @CO2,
                                        @NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka,
                                        @StacionarnaBucavost
                                    )", connection, transaction))
                                {
                                    cmd.Parameters.AddWithValue("@UsernameCreated", HttpContext.Session.GetString("Username"));
                                    cmd.Parameters.AddWithValue("@PolisaId", polisaId);
                                    cmd.Parameters.AddWithValue("@SifrarnikTipNaVozilo", Input.SifrarnikTipNaVozilo ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@RegisterskaOznaka", Input.RegisterskaOznaka ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@Marka", Input.Marka ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@KomercijalnaOznaka", Input.KomercijalnaOznaka ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@Shasija", Input.Shasija ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@GodinaNaProizvodstvo", Input.GodinaNaProizvodstvo ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@ZafatninaNaMotorotcm3", Input.ZafatninaNaMotorotcm3 ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@SilinaNaMotorotKW", Input.SilinaNaMotorotKW ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@BrojNaSedista", Input.BrojNaSedista ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@BojaNaVoziloto", Input.BojaNaVoziloto ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@NosivostKG", Input.NosivostKG ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@DatumNaRegistracija", Input.DatumNaRegistracija ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@BrojNaVpisot", Input.BrojNaVpisot ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@DatumNaPrvataRegistracija", Input.DatumNaPrvataRegistracija ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@PrezimeNazivNaKorisnikot", Input.PrezimeNazivNaKorisnikot ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@Ime", Input.Ime ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@AdresaNaPostojanoZivealiste", Input.AdresaNaPostojanoZivealiste ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@EMBNaKorisnikot", Input.EMBNaKorisnikot ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@DatumNaPrvaRegistracijaVoRSM", Input.DatumNaPrvaRegistracijaVoRSM ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@DozvolataJaIzdal", Input.DozvolataJaIzdal ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@OznakaNaOdobrenie", Input.OznakaNaOdobrenie ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@BrojNAEUPotvrdaZaSoobraznost", Input.BrojNAEUPotvrdaZaSoobraznost ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@PrezimeNazivNaSopstvenikot", Input.PrezimeNazivNaSopstvenikot ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@ImeSopstvenik", Input.ImeSopstvenik ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@AdresaNaPostojanoZivealisteSediste", Input.AdresaNaPostojanoZivealisteSediste ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@EMBNaFizickoLiceEMBNaPravnoLice", Input.EMBNaFizickoLiceEMBNaPravnoLice ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@KategorijaIVidNaVoziloto", Input.KategorijaIVidNaVoziloto ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@OblikINamenaNaKaroserijata", Input.OblikINamenaNaKaroserijata ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@TipNaMotorot", Input.TipNaMotorot ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@VidNaGorivo", Input.VidNaGorivo ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@BrojNaVrtezi", Input.BrojNaVrtezi ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@IdentifikacionenBrojNaMotorot", Input.IdentifikacionenBrojNaMotorot ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@MaksimalnaBrzinaKM", Input.MaksimalnaBrzinaKM ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@OdnosSilinaMasa", Input.OdnosSilinaMasa ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@MasaNaVoziloto", Input.MasaNaVoziloto ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG", Input.NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG", Input.NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG", Input.NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@BrojNaOski", Input.BrojNaOski ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka", Input.RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka", Input.NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@Dolzhina", Input.Dolzhina ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@Visina", Input.Visina ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG", Input.NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG", Input.NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@BrojNaMestaZaStoenje", Input.BrojNaMestaZaStoenje ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@DozvoleniPnevmaticiINaplatki", Input.DozvoleniPnevmaticiINaplatki ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@BrojNaMestazaLezenje", Input.BrojNaMestazaLezenje ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@CO2", Input.CO2 ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka", Input.NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@StacionarnaBucavost", Input.StacionarnaBucavost ?? (object)DBNull.Value);

                                    await cmd.ExecuteNonQueryAsync();
                                }
                            }

                            // Call the stored procedure to generate payment installments
                            using (SqlCommand cmd = new SqlCommand("GenerirajRatiPolisa", connection, transaction))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;

                                cmd.Parameters.AddWithValue("@PolisaId", polisaId);
                                cmd.Parameters.AddWithValue("@SifrarnikValutiId", Input.SifrarnikValutiIdValuta);
                                cmd.Parameters.AddWithValue("@DatumNaIzdavanje", Input.DatumNaIzdavanje ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@SifrarnikNacinNaPlakanjeId", Input.SifrarnikNacinNaPlakjanjeId);
                                cmd.Parameters.AddWithValue("@Premija", Input.VkupnaPremija ?? (object)DBNull.Value);

                                await cmd.ExecuteNonQueryAsync();
                            }

                            await transaction.CommitAsync();
                            TempData["SuccessMessage"] = "Полисата е успешно зачувана.";
                            return RedirectToPage("/Polisi/ListaPolisi");
                        }
                        catch (Exception ex)
                        {
                            await transaction.RollbackAsync();
                            debugInfo["error"] = ex.Message;
                            debugInfo["stackTrace"] = ex.StackTrace;
                            ModelState.AddModelError("", $"Грешка при зачувување на полисата: {ex.Message}");
                            return Page();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                debugInfo["error"] = ex.Message;
                debugInfo["stackTrace"] = ex.StackTrace;
                ModelState.AddModelError("", $"Неочекувана грешка: {ex.Message}");
                return Page();
            }
        }

        public class PolisaInputModel
        {
            [Required(ErrorMessage = "Осигурител е задолжително поле")]
            [Display(Name = "Осигурител")]
            public long KlientiIdOsiguritel { get; set; }

            [Required(ErrorMessage = "Класа на осигурување е задолжително поле")]
            [Display(Name = "Класа на осигурување")]
            public int KlasiOsiguruvanjeIdKlasa { get; set; }

            [Display(Name = "Продукт")]
            public int? ProduktiIdProizvod { get; set; }
            [Required(ErrorMessage = "Број на полиса е задолжително поле")]
            [Display(Name = "Број на полиса")]
            public string? BrojNaPolisa { get; set; }

            [Display(Name = "Колективна")]
            public bool Kolektivna { get; set; }

            [Display(Name = "Колективна со неодреден број на осигуреници")]
            public bool KolektivnaNeodredenBrOsigurenici { get; set; }

            [Display(Name = "Договорувач")]
            public long? KlientiIdDogovoruvac { get; set; }

            [Display(Name = "Осигуреник")]
            public long? KlientiIdOsigurenik { get; set; }

            [Display(Name = "Соработник")]
            public long? KlientiIdSorabotnik { get; set; }

            [Display(Name = "Факторинг")]
            public bool Faktoring { get; set; }

            [Display(Name = "Генерирана излезна фактура")]
            public bool GeneriranaFakturaIzlezna { get; set; }

            [Display(Name = "Сторно")]
            public bool Storno { get; set; }

            [Display(Name = "Причина за сторно")]
            public long? SifrarnikPricinaZaStornoId { get; set; }

            [Required(ErrorMessage = "Датум важи од е задолжително поле")]
            [Display(Name = "Важи од")]
            public DateTime? DatumVaziOd { get; set; }

            [Required(ErrorMessage = "Датум важи до е задолжително поле")]
            [Display(Name = "Важи до")]
            [DateGreaterThan("DatumVaziOd", ErrorMessage = "Датумот важи до мора да биде поголем од датумот важи од")]
            public DateTime? DatumVaziDo { get; set; }

            [Display(Name = "Датум на издавање")]
            public DateTime? DatumNaIzdavanje { get; set; }

            [Display(Name = "Тип на плаќање")]
            public long? SifrarnikTipNaPlakanjeId { get; set; }

            [Display(Name = "Банка")]
            public long? SifrarnikBankiIdBanka { get; set; }

            [Display(Name = "Тип на фактура")]
            public string TipNaFaktura { get; set; }

            [Display(Name = "Валута")]
            public long? SifrarnikValutiIdValuta { get; set; }

            [Display(Name = "Начин на плаќање")]
            public long? SifrarnikNacinNaPlakjanjeId { get; set; }

            [Display(Name = "Број на влезна фактура")]
            [DisplayFormat(NullDisplayText = "")]
            public string? BrojNaVleznaFaktura { get; set; }

            [Display(Name = "Број на излезна фактура")]
            [DisplayFormat(NullDisplayText = "")]
            public string? BrojNaIzleznaFaktura { get; set; }

            [Display(Name = "Датум на влезна фактура")]
            public DateTime? DatumNaVleznaFaktura { get; set; }

            [Display(Name = "Датум на излезна фактура")]
            public DateTime? DatumNaIzleznaFaktura { get; set; }

            [Display(Name = "Рок на плаќање излезна фактура")]
            public DateTime? RokNaPlakanjeIzleznaFaktura { get; set; }

            [Display(Name = "Рок на плаќање влезна фактура")]
            public DateTime? RokNaPlakanjeVleznaFaktura { get; set; }

            [Display(Name = "Процент франшиза")]
            [Range(0, 100)]
            public decimal? ProcentFranshiza { get; set; }

            [Display(Name = "Корегирана стапка на провизија")]
            public decimal? KoregiranaStapkaNaProvizija { get; set; }

            [Display(Name = "Број на осигуреници")]
            public int? BrojNaOsigurenici { get; set; }

            [Display(Name = "Дејност")]
            public int? ListaDejnostiId { get; set; }

            [Display(Name = "Премија за едно лице")]
            [DisplayFormat(DataFormatString = "{0:N4}")]
            public decimal? PremijaZaEdnoLice { get; set; }

            [Display(Name = "Вкупна премија")]
            [DisplayFormat(DataFormatString = "{0:N4}")]
            public decimal? VkupnaPremija { get; set; }

            [Display(Name = "Смрт од несреќен случај")]
            [DisplayFormat(DataFormatString = "{0:N4}")]
            public decimal? SmrtOdNesrekjenSlucaj { get; set; }

            [Display(Name = "Траен инвалидитет")]
            [DisplayFormat(DataFormatString = "{0:N4}")]
            public decimal? TraenInvaliditet { get; set; }

            [Display(Name = "Траен инвалидитет 100%")]
            [DisplayFormat(DataFormatString = "{0:N4}")]
            public decimal? TraenInvaliditet100Procenti { get; set; }

            [Display(Name = "Дневен надомест")]
            [DisplayFormat(DataFormatString = "{0:N4}")]
            public decimal? DnevenNadomest { get; set; }

            [Display(Name = "Лекување")]
            [DisplayFormat(DataFormatString = "{0:N4}")]
            public decimal? Lekuvanje { get; set; }

            [Display(Name = "Тешки болести")]
            [DisplayFormat(DataFormatString = "{0:N4}")]
            public decimal? TeskiBolesti { get; set; }

            [Display(Name = "Трошоци за погреб за смрт од незгода")]
            [DisplayFormat(DataFormatString = "{0:N4}")]
            public decimal? TrosociZaPogrebZaSmrtOdNezgoda { get; set; }

            [Display(Name = "Трошоци за оставинска постапка")]
            [DisplayFormat(DataFormatString = "{0:N4}")]
            public decimal? TrosociZaOstavinskaPostapka { get; set; }

            [Display(Name = "Трошоци за операција незгода")]
            [DisplayFormat(DataFormatString = "{0:N4}")]
            public decimal? TrosociZaOperacijaNezgoda { get; set; }

            [Display(Name = "Трошоци за образование")]
            [DisplayFormat(DataFormatString = "{0:N4}")]
            public decimal? TrosociZaObrazovanie { get; set; }

            [Display(Name = "Процент на попуст за фактура во рок")]
            [DisplayFormat(DataFormatString = "{0:N4}")]
            public decimal? ProcentNaPopustZaFakturaVoRok { get; set; }

            [Display(Name = "Износ за плаќање во рок")]
            [DisplayFormat(DataFormatString = "{0:N4}")]
            public decimal? IznosZaPlakjanjeVoRok { get; set; }

            [Display(Name = "Процент комерцијален попуст")]
            [DisplayFormat(DataFormatString = "{0:N4}")]
            public decimal? ProcentKomercijalenPopust { get; set; }

            [Display(Name = "Процент финансиски")]
            [DisplayFormat(DataFormatString = "{0:N4}")]
            public decimal? ProcentFinansiski { get; set; }

            [Display(Name = "Премија за наплата")]
            [DisplayFormat(DataFormatString = "{0:N4}")]
            public decimal? PremijaZaNaplata { get; set; }

            [Display(Name = "Тип на возило")]
            public long? SifrarnikTipNaVozilo { get; set; }

            [Display(Name = "Регистарска ознака")]
            [StringLength(450, ErrorMessage = "Максимална должина е 450 карактери")]
            public string? RegisterskaOznaka { get; set; }

            [Display(Name = "Марка")]
            [StringLength(450)]
            public string? Marka { get; set; }

            [Display(Name = "Комерцијална ознака")]
            [StringLength(450)]
            public string? KomercijalnaOznaka { get; set; }

            [Display(Name = "Шасија")]
            [StringLength(450)]
            public string? Shasija { get; set; }

            [Display(Name = "Година на производство")]
            public int? GodinaNaProizvodstvo { get; set; }

            [Display(Name = "Зафатнина на моторот (cm³)")]
            public int? ZafatninaNaMotorotcm3 { get; set; }

            [Display(Name = "Силина на моторот (KW)")]
            public int? SilinaNaMotorotKW { get; set; }

            [Display(Name = "Број на седишта")]
            public int? BrojNaSedista { get; set; }

            [Display(Name = "Боја на возилото")]
            [StringLength(450)]
            public string? BojaNaVoziloto { get; set; }

            [Display(Name = "Носивост (KG)")]
            public int? NosivostKG { get; set; }

            [Display(Name = "Датум на регистрација")]
            public DateTime? DatumNaRegistracija { get; set; }

            [Display(Name = "Број на впис")]
            [StringLength(450)]
            public string? BrojNaVpisot { get; set; }

            [Display(Name = "Датум на првата регистрација")]
            public DateTime? DatumNaPrvataRegistracija { get; set; }

            [Display(Name = "Презиме/Назив на корисникот")]
            [StringLength(450)]
            public string? PrezimeNazivNaKorisnikot { get; set; }

            [Display(Name = "Име")]
            [StringLength(450)]
            public string? Ime { get; set; }

            [Display(Name = "Адреса на постојано живеалиште")]
            [StringLength(450)]
            public string? AdresaNaPostojanoZivealiste { get; set; }

            [Display(Name = "ЕМБ на корисникот")]
            [StringLength(450)]
            public string? EMBNaKorisnikot { get; set; }

            [Display(Name = "Датум на прва регистрација во РСМ")]
            [StringLength(450)]
            public string? DatumNaPrvaRegistracijaVoRSM { get; set; }

            [Display(Name = "Дозволата ја издал")]
            [StringLength(450)]
            public string? DozvolataJaIzdal { get; set; }

            [Display(Name = "Ознака на одобрение")]
            [StringLength(450)]
            public string? OznakaNaOdobrenie { get; set; }

            [Display(Name = "Број на ЕУ потврда за сообразност")]
            [StringLength(450)]
            public string? BrojNAEUPotvrdaZaSoobraznost { get; set; }

            [Display(Name = "Презиме/Назив на сопственикот")]
            [StringLength(450)]
            public string? PrezimeNazivNaSopstvenikot { get; set; }

            [Display(Name = "Име на сопственик")]
            [StringLength(450)]
            public string? ImeSopstvenik { get; set; }

            [Display(Name = "Адреса на постојано живеалиште/седиште")]
            [StringLength(450)]
            public string? AdresaNaPostojanoZivealisteSediste { get; set; }

            [Display(Name = "ЕМБ на физичко лице/ЕМБ на правно лице")]
            [StringLength(450)]
            public string? EMBNaFizickoLiceEMBNaPravnoLice { get; set; }

            [Display(Name = "Категорија и вид на возилото")]
            [StringLength(450)]
            public string? KategorijaIVidNaVoziloto { get; set; }

            [Display(Name = "Облик и намена на каросеријата")]
            [StringLength(450)]
            public string? OblikINamenaNaKaroserijata { get; set; }

            [Display(Name = "Тип на моторот")]
            [StringLength(450)]
            public string? TipNaMotorot { get; set; }

            [Display(Name = "Вид на гориво")]
            [StringLength(450)]
            public string? VidNaGorivo { get; set; }

            [Display(Name = "Број на вртежи")]
            [StringLength(450)]
            public string? BrojNaVrtezi { get; set; }

            [Display(Name = "Идентификационен број на моторот")]
            [StringLength(450)]
            public string? IdentifikacionenBrojNaMotorot { get; set; }

            [Display(Name = "Максимална брзина (KM)")]
            [StringLength(450)]
            public string? MaksimalnaBrzinaKM { get; set; }

            [Display(Name = "Однос силина/маса")]
            [StringLength(450)]
            public string? OdnosSilinaMasa { get; set; }

            [Display(Name = "Маса на возилото")]
            [StringLength(450)]
            public string? MasaNaVoziloto { get; set; }

            [Display(Name = "Најголема конструктивна вкупна маса на возилото (KG)")]
            [StringLength(450)]
            public string? NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG { get; set; }

            [Display(Name = "Најголема легална вкупна маса на возилото при регистрација (KG)")]
            [StringLength(450)]
            public string? NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG { get; set; }

            [Display(Name = "Најголема легална вкупна маса на група возила при регистрација (KG)")]
            [StringLength(450)]
            public string? NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG { get; set; }

            [Display(Name = "Број на оски")]
            public int? BrojNaOski { get; set; }

            [Display(Name = "Распределба на најголемата конструктивна вкупна маса по оски (KG) и на приклучната точка")]
            [StringLength(450)]
            public string? RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka { get; set; }

            [Display(Name = "Најголемо конструктивно осно оптоварување (KG) и на приклучната точка")]
            [StringLength(450)]
            public string? NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka { get; set; }

            [Display(Name = "Должина")]
            public int? Dolzhina { get; set; }

            [Display(Name = "Висина")]
            public int? Visina { get; set; }

            [Display(Name = "Најголема конструктивна вкупна маса на кочена приколка (KG)")]
            [StringLength(450)]
            public string? NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG { get; set; }

            [Display(Name = "Најголема конструктивна вкупна маса на некочена приколка (KG)")]
            [StringLength(450)]
            public string? NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG { get; set; }

            [Display(Name = "Број на места за стоење")]
            public int? BrojNaMestaZaStoenje { get; set; }

            [Display(Name = "Дозволени пневматици и наплатки")]
            [StringLength(450)]
            public string? DozvoleniPnevmaticiINaplatki { get; set; }

            [Display(Name = "Број на места за лежење")]
            public int? BrojNaMestazaLezenje { get; set; }

            [Display(Name = "CO2")]
            [StringLength(450)]
            public string? CO2 { get; set; }

            [Display(Name = "Најголемо конструктивно оптоварување на приклучокот за приколка")]
            [StringLength(450)]
            public string? NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka { get; set; }

            [Display(Name = "Стационарна бучавост")]
            [StringLength(450)]
            public string? StacionarnaBucavost { get; set; }

            [Display(Name = "Валута за франшиза")]
            public long? SifrarnikValutiIdFranshizaValuta { get; set; }

            [Display(Name = "Франшиза износ")]            
            public decimal? FranshizaIznos { get; set; }
        }

        public class OsigurenikModel
        {
            public string Ime { get; set; }
            public string Prezime { get; set; }
            public string EMBG { get; set; }
            public string BrojNaLicnaKarta { get; set; }
            public string BrojNaPasos { get; set; }
            public string ListaOpstiniId { get; set; }
            public string Adresa { get; set; }
            public string Broj { get; set; }
            public string Telefon { get; set; }
            public string Email { get; set; }
        }
    }
} 