using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.Data;
using Microsoft.Data.SqlClient;
using System.ComponentModel.DataAnnotations;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace NextBroker.Pages.Pregledi
{
    public class ASOKvartalenIzvestajModel : PageModel
    {
        private readonly IConfiguration _configuration;
        private readonly Dictionary<string, string> _columnTranslations = new Dictionary<string, string>
        {
            { "DateCreated", "Датум на креирање" },
            { "UsernameCreated", "Креирано од" },
            { "DatumIzvestajStartDate", "Почетен датум" },
            { "DatumIzvestajEndDate", "Краен датум" },
            { "BrojNaPolisa", "Број на полиса" },
            { "KlasiOsiguruvanjeIdKlasa", "ИД на класа" },
            { "ProduktiIdProizvod", "ИД на производ" },
            { "KlientiIdOsiguritel", "ИД на осигурител" },
            { "KlientiIdSorabotnik", "ИД на соработник" },
            { "KlientiIdDogovoruvac", "ИД на договорувач" },
            { "DatumVaziOd", "Важи од" },
            { "DatumVaziDo", "Важи до" },
            { "DatumNaIzdavanje", "Датум на издавање" },
            { "NacinNaPlakjajne", "Начин на плаќање" },
            { "TipNaFaktura", "Тип на фактура" },
            { "BrojNaFakturaVlezna", "Број на влезна фактура" },
            { "BrojNaFakturaIzlezna", "Број на излезна фактура" },
            { "PremijaZaOsnovnaKlasa", "Премија за основна класа" },
            { "DopolnitelnoOsiguruvanjeKlasa1", "Дополнително осигурување класа 1" },
            { "DopolnitelnoOsiguruvanjeKlasa8", "Дополнително осигурување класа 8" },
            { "DopolnitelnoOsiguruvanjeAsistencaKlas10", "Дополнително осигурување асистенца класа 10" },
            { "VkupnaPremija", "Вкупна премија" },
            { "ProcentPopust", "Процент попуст" },
            { "PremijaZaOsnovnaKlasaPopust", "Премија за основна класа со попуст" },
            { "DopolnitelnoOsiguruvanjeKlasa1Popust", "Дополнително осигурување класа 1 со попуст" },
            { "DopolnitelnoOsiguruvanjeKlasa8Popust", "Дополнително осигурување класа 8 со попуст" },
            { "DopolnitelnoOsiguruvanjeAsistencaKlasa10Popust", "Дополнително осигурување асистенца класа 10 со попуст" },
            { "PremijaZaNaplata", "Премија за наплата(БПП)" },
            { "PremijaZaVoRok", "Премија во рок" },
            { "PlatenoVoRok", "Платено во рок" },
            { "KoeficientZaplaknjaeVoRok", "Коефициент на плаќање во рок" },
            { "PremijaZaOsnovnaKlasaVoRok", "Премија за основна класа во рок" },
            { "DopolnitelnoOsiguruvanjeKlasa1VoRok", "Дополнително осигурување класа 1 во рок" },
            { "DopolnitelnoOsiguruvanjeKlas8VoRok", "Дополнително осигурување класа 8 во рок" },
            { "DopolnitelnoOsiguruvanjeAsistencaKlas10VoRok", "Дополнително осигурување асистенца класа 10 во рок" },
            { "Storno", "Сторно" },
            { "DatumNaStorno", "Датум на сторно" },
            { "StapkaZaProvizija", "Стапка за провизија" },
            { "IznosOsnovnaPremijaZaProvizijaPresmetka-Nezhivot", "Износ основна премија за провизија пресметка - неживот" },
            { "IznosDopolnitelnoOsiguruvanjeKlasa1ZaProvizijaPresmetka-Nezhivot", "Износ дополнително осигурување класа 1 за провизија пресметка - неживот" },
            { "IznosDopolnitelnoOsiguruvanjeKlasa8ZaProvizijaPresmetka-Nezhivot", "Износ дополнително осигурување класа 8 за провизија пресметка - неживот" },
            { "IznosDopolnitelnoOsiguruvanjeKlasa10ZaProvizijaPresmetka-Nezhivot", "Износ дополнително осигурување класа 10 за провизија пресметка - неживот" },
            { "ProvizijaZaOsnovnaKlasa10", "Провизија за основна класа 10" },
            { "ProvizijaZaDopolnitelnoOsiguruvanjeKlasa1", "Провизија за дополнително осигурување класа 1" },
            { "ProvizijaZaDopolnitelnoOsiguruvanjeKlasa8", "Провизија за дополнително осигурување класа 8" },
            { "ProvizijaDopolnitelnoOsiguruvanjeAsistencaKlas10", "Провизија дополнително осигурување асистенца класа 10" },
            { "ZivotNezivot", "Живот/Неживот" }
        };

        public ASOKvartalenIzvestajModel(IConfiguration configuration)
        {
            _configuration = configuration;
            InitializeQuarterOptions();
            InitializeYearOptions();
        }

        [BindProperty(SupportsGet = true)]
        public string SelectedQuarter { get; set; }

        [BindProperty(SupportsGet = true)]
        public int SelectedYear { get; set; }

        public SelectList QuarterOptions { get; set; }
        public SelectList YearOptions { get; set; }

        public DataTable ReportData { get; set; }
        public string ReportExistsMessage { get; set; }
        [BindProperty(SupportsGet = true)]
        public bool IsConfirmed { get; set; }

        private void InitializeQuarterOptions()
        {
            var quarters = new List<SelectListItem>
            {
                new SelectListItem { Value = "1", Text = "1-ви квартал (01.01-31.03)" },
                new SelectListItem { Value = "2", Text = "2-ри квартал (01.01-30.06)" },
                new SelectListItem { Value = "3", Text = "3-ти квартал (01.01-30.09)" },
                new SelectListItem { Value = "4", Text = "4-ти квартал (01.01-31.12)" }
            };
            QuarterOptions = new SelectList(quarters, "Value", "Text");
        }

        private void InitializeYearOptions()
        {
            int currentYear = DateTime.Now.Year;
            int startYear = 2025; // They started using this system in 2025
            
            var years = new List<SelectListItem>();
            for (int year = currentYear; year >= startYear; year--)
            {
                years.Add(new SelectListItem { Value = year.ToString(), Text = year.ToString() });
            }
            YearOptions = new SelectList(years, "Value", "Text");
            
            // Set default year to current year if not already set
            if (SelectedYear == 0)
            {
                SelectedYear = currentYear;
            }
        }

        private (DateTime StartDate, DateTime EndDate) GetQuarterDateRange(string quarter, int year)
        {
            return quarter switch
            {
                "1" => (new DateTime(year, 1, 1), new DateTime(year, 3, 31)),
                "2" => (new DateTime(year, 1, 1), new DateTime(year, 6, 30)),
                "3" => (new DateTime(year, 1, 1), new DateTime(year, 9, 30)),
                "4" => (new DateTime(year, 1, 1), new DateTime(year, 12, 31)),
                _ => (new DateTime(year, 1, 1), new DateTime(year, 12, 31))
            };
        }

        public async Task<IActionResult> OnGet()
        {
            // Clean up any orphaned preview data (older than 1 hour)
            await CleanupOrphanedPreviewDataAsync();
            
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (string.IsNullOrEmpty(SelectedQuarter))
            {
                ModelState.AddModelError("SelectedQuarter", "Мора да изберете квартал.");
                return Page();
            }

            if (SelectedYear <= 0)
            {
                ModelState.AddModelError("SelectedYear", "Мора да изберете година.");
                return Page();
            }

            int currentYear = DateTime.Now.Year;
            if (SelectedYear > currentYear)
            {
                ModelState.AddModelError("SelectedYear", $"Не можете да изберете година поголема од {currentYear}.");
                return Page();
            }

            await GeneratePreviewAsync();
            return Page();
        }

        public async Task<IActionResult> OnPostConfirmAsync()
        {
            if (string.IsNullOrEmpty(SelectedQuarter))
            {
                ModelState.AddModelError("SelectedQuarter", "Мора да изберете квартал.");
                return Page();
            }

            if (SelectedYear <= 0)
            {
                ModelState.AddModelError("SelectedYear", "Мора да изберете година.");
                return Page();
            }

            int currentYear = DateTime.Now.Year;
            if (SelectedYear > currentYear)
            {
                ModelState.AddModelError("SelectedYear", $"Не можете да изберете година поголема од {currentYear}.");
                return Page();
            }

            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);
            
            // Clean up any preview data first
            await CleanupPreviewDataAsync(startDate, endDate);
            
            await GenerateReportAsync();
            IsConfirmed = true;
            return Page();
        }

        public async Task<IActionResult> OnPostCancelPreviewAsync()
        {
            if (string.IsNullOrEmpty(SelectedQuarter))
            {
                return RedirectToPage();
            }

            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);
            
            // Clean up preview data
            await CleanupPreviewDataAsync(startDate, endDate);
            
            // Reset the page
            ReportData = null;
            ReportExistsMessage = null;
            IsConfirmed = false;
            
            return Page();
        }

        public async Task<IActionResult> OnGetExportExcelAsync()
        {
            if (string.IsNullOrEmpty(SelectedQuarter))
            {
                ModelState.AddModelError("SelectedQuarter", "Мора да изберете квартал.");
                return Page();
            }

            if (SelectedYear <= 0)
            {
                ModelState.AddModelError("SelectedYear", "Мора да изберете година.");
                return Page();
            }

            int currentYear = DateTime.Now.Year;
            if (SelectedYear > currentYear)
            {
                ModelState.AddModelError("SelectedYear", $"Не можете да изберете година поголема од {currentYear}.");
                return Page();
            }

            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);

            // If ReportData is null, try to load existing data or generate new data
            if (ReportData == null || ReportData.Rows.Count == 0)
            {
                // First check if data already exists
                bool reportExists = await CheckIfReportExistsAsync(startDate, endDate);
                if (reportExists)
                {
                    // Load existing data
                    await LoadExistingReportDataAsync(startDate, endDate);
                }
                else
                {
                    // Generate new data for export (temporary, not saved to archive)
                    await GenerateTemporaryDataForExportAsync(startDate, endDate);
                }
                
                if (ReportData == null || ReportData.Rows.Count == 0)
                {
                    ReportExistsMessage = "Нема податоци за избраниот период.";
                    return Page();
                }
            }

            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("АСО Квартален Извештај");

                // Header
                worksheet.Cells["A1"].Value = "Квартален извештај";
                worksheet.Cells["A1:E1"].Merge = true;
                worksheet.Cells["A1"].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                worksheet.Cells["A1"].Style.Font.Bold = true;

                worksheet.Cells["A3"].Value = "Почетен датум:";
                worksheet.Cells["B3"].Value = startDate.ToString("dd/MM/yyyy");
                worksheet.Cells["A4"].Value = "Краен датум:";
                worksheet.Cells["B4"].Value = endDate.ToString("dd/MM/yyyy");

                int tableStartRow = 6;
                // Add headers
                for (int i = 0; i < ReportData.Columns.Count; i++)
                {
                    var cell = worksheet.Cells[tableStartRow, i + 1];
                    cell.Value = ReportData.Columns[i].ColumnName; // Column names are already translated
                    cell.Style.Font.Bold = true;
                    cell.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                    cell.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    cell.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    cell.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                }

                // Add data
                for (int row = 0; row < ReportData.Rows.Count; row++)
                {
                    // Check if this row is a storno row (contains "Да" in the Сторно column only)
                    bool isStorno = false;
                    if (ReportData.Columns.Contains("Сторно"))
                    {
                        var stornoValue = ReportData.Rows[row]["Сторно"];
                        if (stornoValue != null && stornoValue.ToString().Trim().Equals("Да", StringComparison.OrdinalIgnoreCase))
                        {
                            isStorno = true;
                        }
                    }

                    for (int col = 0; col < ReportData.Columns.Count; col++)
                    {
                        var cellValue = ReportData.Rows[row][col];
                        var cell = worksheet.Cells[row + tableStartRow + 1, col + 1];
                        if (cellValue is DateTime dateValue)
                        {
                            cell.Style.Numberformat.Format = "dd/MM/yyyy";
                            cell.Value = dateValue;
                        }
                        else if (cellValue is decimal || cellValue is double || cellValue is float)
                        {
                            cell.Style.Numberformat.Format = "#,##0.00";
                            cell.Value = cellValue;
                        }
                        else
                        {
                            cell.Value = cellValue;
                        }
                        
                        // Apply red text color for storno rows
                        if (isStorno)
                        {
                            cell.Style.Font.Color.SetColor(System.Drawing.Color.Red);
                        }
                        
                        cell.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        cell.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        cell.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        cell.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                    }
                }

                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
                var content = package.GetAsByteArray();
                var fileName = $"ASO_Kvartalen_Izvestaj_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}.xlsx";
                
                return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
        }

        private async Task<Dictionary<int, string>> GetKlasaImeMappingAsync(SqlConnection connection)
        {
            var mapping = new Dictionary<int, string>();
            string sql = "SELECT Id, KlasaIme FROM KlasiOsiguruvanje WHERE Disabled = 0";
            using (var cmd = new SqlCommand(sql, connection))
            using (var reader = await cmd.ExecuteReaderAsync())
            {
                while (await reader.ReadAsync())
                {
                    int id = reader.GetInt32(0);
                    string name = reader.GetString(1);
                    mapping[id] = name;
                }
            }
            return mapping;
        }

        private async Task<bool> CheckIfReportExistsAsync(DateTime startDate, DateTime endDate)
        {
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                
                string checkSql = @"
SELECT COUNT(*) 
FROM dbo.ASOKvartalenArhiva 
WHERE DatumIzvestajStartDate = @StartDate 
  AND DatumIzvestajEndDate = @EndDate";
                
                using (var cmd = new SqlCommand(checkSql, connection))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    int count = (int)await cmd.ExecuteScalarAsync();
                    return count > 0;
                }
            }
        }

        private async Task GeneratePreviewAsync()
        {
            ReportData = null;
            ReportExistsMessage = null;
            IsConfirmed = false;

            if (string.IsNullOrEmpty(SelectedQuarter))
            {
                return;
            }

            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);
            
            // Check if report already exists for this quarter
            bool reportExists = await CheckIfReportExistsAsync(startDate, endDate);
            if (reportExists)
            {
                ReportExistsMessage = $"Извештајот за {GetQuarterDisplayName(SelectedQuarter, SelectedYear)} веќе е генериран. Можете да го прегледате или извезете.";
                
                // Load existing data for preview
                await LoadExistingReportDataAsync(startDate, endDate);
                return;
            }
            
            string startDateStr = startDate.ToString("yyyyMMdd");
            string endDateStr = endDate.ToString("yyyyMMdd");
            
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                
                // Call stored procedure to populate archive table
                using (var cmd = new SqlCommand("ASOKvartalen", connection))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@StartDate", startDateStr);
                    cmd.Parameters.AddWithValue("@EndDate", endDateStr);
                    await cmd.ExecuteNonQueryAsync();
                }
                
                // Select from archive for preview
                string selectSql = @"
SELECT arh.*, k.KlasaIme, p.Ime AS ProduktIme,
       CASE WHEN o.Ime IS NOT NULL OR o.Prezime IS NOT NULL
            THEN CONCAT(ISNULL(o.Ime, ''), ' ', ISNULL(o.Prezime, ''))
            ELSE o.Naziv END AS OsiguritelIme,
       CASE WHEN s.Ime IS NOT NULL OR s.Prezime IS NOT NULL
            THEN CONCAT(ISNULL(s.Ime, ''), ' ', ISNULL(s.Prezime, ''))
            ELSE s.Naziv END AS SorabotnikIme,
       CASE WHEN d.Ime IS NOT NULL OR d.Prezime IS NOT NULL
            THEN CONCAT(ISNULL(d.Ime, ''), ' ', ISNULL(d.Prezime, ''))
            ELSE d.Naziv END AS DogovoruvacIme
FROM dbo.ASOKvartalenArhiva arh
LEFT JOIN dbo.KlasiOsiguruvanje k ON arh.KlasiOsiguruvanjeIdKlasa = k.Id
LEFT JOIN dbo.Produkti p ON arh.ProduktiIdProizvod = p.Id
LEFT JOIN dbo.Klienti o ON arh.KlientiIdOsiguritel = o.Id
LEFT JOIN dbo.Klienti s ON arh.KlientiIdSorabotnik = s.Id
LEFT JOIN dbo.Klienti d ON arh.KlientiIdDogovoruvac = d.Id
WHERE arh.DatumIzvestajStartDate >= @StartDate AND arh.DatumIzvestajEndDate <= @EndDate";
                
                using (var cmd = new SqlCommand(selectSql, connection))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    using (var adapter = new SqlDataAdapter(cmd))
                    {
                        var dt = new DataTable();
                        adapter.Fill(dt);
                        
                        ProcessDataTable(dt);
                        ReportData = dt;
                    }
                }
                
                // Mark preview data for cleanup (don't delete immediately)
                // We'll clean this up when user confirms or cancels
                string markForCleanupSql = @"
UPDATE dbo.ASOKvartalenArhiva 
SET UsernameCreated = @UsernameCreated + '_PREVIEW_' + CAST(GETDATE() AS VARCHAR(50))
WHERE DatumIzvestajStartDate >= @StartDate AND DatumIzvestajEndDate <= @EndDate";
                
                using (var cmd = new SqlCommand(markForCleanupSql, connection))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    cmd.Parameters.AddWithValue("@UsernameCreated", HttpContext.Session.GetString("Username") ?? "System");
                    await cmd.ExecuteNonQueryAsync();
                }
            }
        }

        private async Task LoadExistingReportDataAsync(DateTime startDate, DateTime endDate)
        {
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                
                string selectSql = @"
SELECT arh.*, k.KlasaIme, p.Ime AS ProduktIme,
       CASE WHEN o.Ime IS NOT NULL OR o.Prezime IS NOT NULL
            THEN CONCAT(ISNULL(o.Ime, ''), ' ', ISNULL(o.Prezime, ''))
            ELSE o.Naziv END AS OsiguritelIme,
       CASE WHEN s.Ime IS NOT NULL OR s.Prezime IS NOT NULL
            THEN CONCAT(ISNULL(s.Ime, ''), ' ', ISNULL(s.Prezime, ''))
            ELSE s.Naziv END AS SorabotnikIme,
       CASE WHEN d.Ime IS NOT NULL OR d.Prezime IS NOT NULL
            THEN CONCAT(ISNULL(d.Ime, ''), ' ', ISNULL(d.Prezime, ''))
            ELSE d.Naziv END AS DogovoruvacIme
FROM dbo.ASOKvartalenArhiva arh
LEFT JOIN dbo.KlasiOsiguruvanje k ON arh.KlasiOsiguruvanjeIdKlasa = k.Id
LEFT JOIN dbo.Produkti p ON arh.ProduktiIdProizvod = p.Id
LEFT JOIN dbo.Klienti o ON arh.KlientiIdOsiguritel = o.Id
LEFT JOIN dbo.Klienti s ON arh.KlientiIdSorabotnik = s.Id
LEFT JOIN dbo.Klienti d ON arh.KlientiIdDogovoruvac = d.Id
WHERE arh.DatumIzvestajStartDate = @StartDate AND arh.DatumIzvestajEndDate = @EndDate";
                
                using (var cmd = new SqlCommand(selectSql, connection))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    using (var adapter = new SqlDataAdapter(cmd))
                    {
                        var dt = new DataTable();
                        adapter.Fill(dt);
                        
                        ProcessDataTable(dt);
                        ReportData = dt;
                        IsConfirmed = true; // Mark as confirmed since it already exists
                    }
                }
            }
        }

        private string GetQuarterDisplayName(string quarter, int year)
        {
            return quarter switch
            {
                "1" => $"1-ви квартал {year}",
                "2" => $"2-ри квартал {year}",
                "3" => $"3-ти квартал {year}",
                "4" => $"4-ти квартал {year}",
                _ => $"Квартал {quarter} {year}"
            };
        }

        private async Task GenerateReportAsync()
        {
            ReportData = null;
            ReportExistsMessage = null;

            if (string.IsNullOrEmpty(SelectedQuarter))
            {
                return;
            }

            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);
            
            // Check if report already exists for this quarter
            bool reportExists = await CheckIfReportExistsAsync(startDate, endDate);
            if (reportExists)
            {
                ReportExistsMessage = $"Извештајот за {GetQuarterDisplayName(SelectedQuarter, SelectedYear)} веќе е генериран. Можете да го прегледате или извезете.";
                
                // Load existing data
                await LoadExistingReportDataAsync(startDate, endDate);
                return;
            }
            
            string startDateStr = startDate.ToString("yyyyMMdd");
            string endDateStr = endDate.ToString("yyyyMMdd");
            
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                
                // Call stored procedure to save data to archive table
                using (var cmd = new SqlCommand("ASOKvartalen", connection))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@StartDate", startDateStr);
                    cmd.Parameters.AddWithValue("@EndDate", endDateStr);
                    await cmd.ExecuteNonQueryAsync();
                }
                
                // Update UsernameCreated to current session user
                string updateUsernameSql = @"
UPDATE dbo.ASOKvartalenArhiva 
SET UsernameCreated = @UsernameCreated
WHERE DatumIzvestajStartDate = @StartDate AND DatumIzvestajEndDate = @EndDate";
                
                using (var cmd = new SqlCommand(updateUsernameSql, connection))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    cmd.Parameters.AddWithValue("@UsernameCreated", HttpContext.Session.GetString("Username") ?? "System");
                    await cmd.ExecuteNonQueryAsync();
                }
                
                // Select from archive after saving
                string selectSql = @"
SELECT arh.*, k.KlasaIme, p.Ime AS ProduktIme,
       CASE WHEN o.Ime IS NOT NULL OR o.Prezime IS NOT NULL
            THEN CONCAT(ISNULL(o.Ime, ''), ' ', ISNULL(o.Prezime, ''))
            ELSE o.Naziv END AS OsiguritelIme,
       CASE WHEN s.Ime IS NOT NULL OR s.Prezime IS NOT NULL
            THEN CONCAT(ISNULL(s.Ime, ''), ' ', ISNULL(s.Prezime, ''))
            ELSE s.Naziv END AS SorabotnikIme,
       CASE WHEN d.Ime IS NOT NULL OR d.Prezime IS NOT NULL
            THEN CONCAT(ISNULL(d.Ime, ''), ' ', ISNULL(d.Prezime, ''))
            ELSE d.Naziv END AS DogovoruvacIme
FROM dbo.ASOKvartalenArhiva arh
LEFT JOIN dbo.KlasiOsiguruvanje k ON arh.KlasiOsiguruvanjeIdKlasa = k.Id
LEFT JOIN dbo.Produkti p ON arh.ProduktiIdProizvod = p.Id
LEFT JOIN dbo.Klienti o ON arh.KlientiIdOsiguritel = o.Id
LEFT JOIN dbo.Klienti s ON arh.KlientiIdSorabotnik = s.Id
LEFT JOIN dbo.Klienti d ON arh.KlientiIdDogovoruvac = d.Id
WHERE arh.DatumIzvestajStartDate = @StartDate AND arh.DatumIzvestajEndDate = @EndDate";
                using (var cmd = new SqlCommand(selectSql, connection))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    using (var adapter = new SqlDataAdapter(cmd))
                    {
                        var dt = new DataTable();
                        adapter.Fill(dt);
                        
                        ProcessDataTable(dt);
                        ReportData = dt;
                    }
                }
            }
        }

        private void ProcessDataTable(DataTable dt)
        {
            // Translate column names to Macedonian
            foreach (DataColumn column in dt.Columns)
            {
                if (_columnTranslations.ContainsKey(column.ColumnName))
                {
                    column.ColumnName = _columnTranslations[column.ColumnName];
                }
                // Remove 'ИД на' prefix and capitalize the rest
                if (column.ColumnName.StartsWith("ИД на "))
                {
                    var rest = column.ColumnName.Substring(6).Trim();
                    if (!string.IsNullOrEmpty(rest))
                    {
                        column.ColumnName = char.ToUpper(rest[0]) + rest.Substring(1).ToLower();
                    }
                }
            }
            // Remove Klasa ID column if present
            if (dt.Columns.Contains("ИД на класа"))
            {
                dt.Columns.Remove("ИД на класа");
            }
            // Remove existing 'Класа' column if present to avoid duplicate name error
            if (dt.Columns.Contains("Класа"))
            {
                dt.Columns.Remove("Класа");
            }
            // Rename KlasaIme to 'Класа'
            if (dt.Columns.Contains("KlasaIme"))
            {
                dt.Columns["KlasaIme"].ColumnName = "Класа";
            }
            // Remove Produkt ID column if present
            if (dt.Columns.Contains("ИД на производ"))
            {
                dt.Columns.Remove("ИД на производ");
            }
            // Remove existing 'Производ' column if present to avoid duplicate name error
            if (dt.Columns.Contains("Производ"))
            {
                dt.Columns.Remove("Производ");
            }
            // Rename ProduktIme to 'Производ'
            if (dt.Columns.Contains("ProduktIme"))
            {
                dt.Columns["ProduktIme"].ColumnName = "Производ";
            }
            // Move 'Класа' column right after 'Број на полиса'
            if (dt.Columns.Contains("Класа") && dt.Columns.Contains("Број на полиса"))
            {
                var klasaCol = dt.Columns["Класа"];
                int targetIndex = dt.Columns["Број на полиса"].Ordinal + 1;
                klasaCol.SetOrdinal(targetIndex);
            }
            // Move 'Производ' column right after 'Класа'
            if (dt.Columns.Contains("Производ") && dt.Columns.Contains("Класа"))
            {
                var proizvodCol = dt.Columns["Производ"];
                int targetIndex = dt.Columns["Класа"].Ordinal + 1;
                proizvodCol.SetOrdinal(targetIndex);
            }
            // Remove Osiguritel ID column if present
            if (dt.Columns.Contains("ИД на осигурител"))
            {
                dt.Columns.Remove("ИД на осигурител");
            }
            // Remove existing 'Осигурител' column if present to avoid duplicate name error
            if (dt.Columns.Contains("Осигурител"))
            {
                dt.Columns.Remove("Осигурител");
            }
            // Rename OsiguritelIme to 'Осигурител'
            if (dt.Columns.Contains("OsiguritelIme"))
            {
                dt.Columns["OsiguritelIme"].ColumnName = "Осигурител";
            }
            // Move 'Осигурител' column right after 'Производ'
            if (dt.Columns.Contains("Осигурител") && dt.Columns.Contains("Производ"))
            {
                var osiguritelCol = dt.Columns["Осигурител"];
                int targetIndex = dt.Columns["Производ"].Ordinal + 1;
                osiguritelCol.SetOrdinal(targetIndex);
            }
            // Remove Sorabotnik and Dogovoruvac ID columns if present
            if (dt.Columns.Contains("ИД на соработник"))
            {
                dt.Columns.Remove("ИД на соработник");
            }
            if (dt.Columns.Contains("ИД на договорувач"))
            {
                dt.Columns.Remove("ИД на договорувач");
            }
            // Remove existing 'Соработник' and 'Договорувач' columns if present to avoid duplicate name error
            if (dt.Columns.Contains("Соработник"))
            {
                dt.Columns.Remove("Соработник");
            }
            if (dt.Columns.Contains("Договорувач"))
            {
                dt.Columns.Remove("Договорувач");
            }
            // Rename SorabotnikIme and DogovoruvacIme to 'Соработник' and 'Договорувач'
            if (dt.Columns.Contains("SorabotnikIme"))
            {
                dt.Columns["SorabotnikIme"].ColumnName = "Соработник";
            }
            if (dt.Columns.Contains("DogovoruvacIme"))
            {
                dt.Columns["DogovoruvacIme"].ColumnName = "Договорувач";
            }
            // Move 'Соработник' and 'Договорувач' columns right after 'Осигурител'
            if (dt.Columns.Contains("Соработник") && dt.Columns.Contains("Осигурител"))
            {
                var sorabotnikCol = dt.Columns["Соработник"];
                int targetIndex = dt.Columns["Осигурител"].Ordinal + 1;
                sorabotnikCol.SetOrdinal(targetIndex);
            }
            if (dt.Columns.Contains("Договорувач") && dt.Columns.Contains("Соработник"))
            {
                var dogovoruvacCol = dt.Columns["Договорувач"];
                int targetIndex = dt.Columns["Соработник"].Ordinal + 1;
                dogovoruvacCol.SetOrdinal(targetIndex);
            }
            // Remove unwanted columns (after all renaming/moves)
            string[] columnsToRemove = { "Id", "DateCreated", "Креирано од", "Креирано Од", "UsernameCreated", "DateModified", "UsernameModified", "Датум на креирање" };
            foreach (var colName in columnsToRemove)
            {
                if (dt.Columns.Contains(colName))
                    dt.Columns.Remove(colName);
            }
            // Debug: Print column order
            System.Diagnostics.Debug.WriteLine("Column order just before ReportData assignment:");
            for (int i = 0; i < dt.Columns.Count; i++)
            {
                System.Diagnostics.Debug.WriteLine($"{i}: {dt.Columns[i].ColumnName}");
            }
        }

        private async Task GenerateTemporaryDataForExportAsync(DateTime startDate, DateTime endDate)
        {
            string startDateStr = startDate.ToString("yyyyMMdd");
            string endDateStr = endDate.ToString("yyyyMMdd");
            
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                
                // Call stored procedure to populate archive table temporarily
                using (var cmd = new SqlCommand("ASOKvartalen", connection))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@StartDate", startDateStr);
                    cmd.Parameters.AddWithValue("@EndDate", endDateStr);
                    await cmd.ExecuteNonQueryAsync();
                }
                
                // Select from archive for export
                string selectSql = @"
SELECT arh.*, k.KlasaIme, p.Ime AS ProduktIme,
       CASE WHEN o.Ime IS NOT NULL OR o.Prezime IS NOT NULL
            THEN CONCAT(ISNULL(o.Ime, ''), ' ', ISNULL(o.Prezime, ''))
            ELSE o.Naziv END AS OsiguritelIme,
       CASE WHEN s.Ime IS NOT NULL OR s.Prezime IS NOT NULL
            THEN CONCAT(ISNULL(s.Ime, ''), ' ', ISNULL(s.Prezime, ''))
            ELSE s.Naziv END AS SorabotnikIme,
       CASE WHEN d.Ime IS NOT NULL OR d.Prezime IS NOT NULL
            THEN CONCAT(ISNULL(d.Ime, ''), ' ', ISNULL(d.Prezime, ''))
            ELSE d.Naziv END AS DogovoruvacIme
FROM dbo.ASOKvartalenArhiva arh
LEFT JOIN dbo.KlasiOsiguruvanje k ON arh.KlasiOsiguruvanjeIdKlasa = k.Id
LEFT JOIN dbo.Produkti p ON arh.ProduktiIdProizvod = p.Id
LEFT JOIN dbo.Klienti o ON arh.KlientiIdOsiguritel = o.Id
LEFT JOIN dbo.Klienti s ON arh.KlientiIdSorabotnik = s.Id
LEFT JOIN dbo.Klienti d ON arh.KlientiIdDogovoruvac = d.Id
WHERE arh.DatumIzvestajStartDate >= @StartDate AND arh.DatumIzvestajEndDate <= @EndDate";
                
                using (var cmd = new SqlCommand(selectSql, connection))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    using (var adapter = new SqlDataAdapter(cmd))
                    {
                        var dt = new DataTable();
                        adapter.Fill(dt);
                        
                        ProcessDataTable(dt);
                        ReportData = dt;
                    }
                }
                
                // Clean up temporary data from archive after export
                // Delete all temporary data for this date range
                string deleteSql = @"
DELETE FROM dbo.ASOKvartalenArhiva 
WHERE DatumIzvestajStartDate >= @StartDate AND DatumIzvestajEndDate <= @EndDate";
                
                using (var cmd = new SqlCommand(deleteSql, connection))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    await cmd.ExecuteNonQueryAsync();
                }
            }
        }

        private async Task CleanupPreviewDataAsync(DateTime startDate, DateTime endDate)
        {
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                
                // Clean up preview data - DELETE instead of UPDATE since UsernameCreated doesn't allow NULL
                string cleanupSql = @"
DELETE FROM dbo.ASOKvartalenArhiva 
WHERE DatumIzvestajStartDate >= @StartDate AND DatumIzvestajEndDate <= @EndDate
AND (UsernameCreated LIKE '%_PREVIEW_%' OR UsernameCreated LIKE '%_SAME_QUARTER_STORNO%')";
                
                using (var cmd = new SqlCommand(cleanupSql, connection))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    await cmd.ExecuteNonQueryAsync();
                }
            }
        }

        private async Task CleanupOrphanedPreviewDataAsync()
        {
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                
                // Clean up orphaned preview data (older than 1 hour)
                string cleanupSql = @"
DELETE FROM dbo.ASOKvartalenArhiva 
WHERE UsernameCreated LIKE '%_PREVIEW_%'";
                
                using (var cmd = new SqlCommand(cleanupSql, connection))
                {
                    await cmd.ExecuteNonQueryAsync();
                }
                
                // Also clean up any orphaned same-quarter storno entries
                string cleanupStornoSql = @"
DELETE FROM dbo.ASOKvartalenArhiva 
WHERE UsernameCreated LIKE '%_SAME_QUARTER_STORNO%'
AND DateCreated < DATEADD(HOUR, -1, GETDATE())";
                
                using (var cmd = new SqlCommand(cleanupStornoSql, connection))
                {
                    await cmd.ExecuteNonQueryAsync();
                }
            }
        }
    }

    public class QuarterModel
    {
        public int Value { get; set; }
        public string Name { get; set; }
    }
}
