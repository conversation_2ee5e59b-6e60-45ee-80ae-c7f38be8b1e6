using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace NextBroker.Pages.Polisi
{
    public class ZadolzuvanjeRazdolzuvanjeDodajModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public ZadolzuvanjeRazdolzuvanjeDodajModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        [BindProperty]
        public ZadolzuvanjeRazdolzuvanjeInput Input { get; set; } = new();

        public IEnumerable<SelectListItem> Osiguriteli { get; set; }
        public IEnumerable<SelectListItem> KlasiOsiguruvanje { get; set; }
        public IEnumerable<SelectListItem> Produkti { get; set; }
        public IEnumerable<SelectListItem> Zadolzeni { get; set; }

        public class ZadolzuvanjeRazdolzuvanjeInput
        {
            public long? KlientiIdOsiguritel { get; set; }
            public int? KlasiOsiguruvanjeIdKlasa { get; set; }
            public int? ProduktiIdProdukt { get; set; }
            public string BrojNaPolisa { get; set; }
            public string RangeDo { get; set; }
            public long? BrojNaPonuda { get; set; }
            public long? RangePonudaDo { get; set; }
            public long? KlientiIdZadolzen { get; set; }
            public DateTime? DatumNaZadolzuvanje { get; set; }
        }

        public async Task<IActionResult> OnGetAsync()
        {
            if (!await HasPageAccess("ZadolzuvanjeRazdolzuvanjePolisi"))
            {
                return RedirectToAccessDenied();
            }

            await LoadOsiguriteli();
            await LoadKlasiOsiguruvanje();
            await LoadZadolzeni();

            return Page();
        }

        private async Task LoadOsiguriteli()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Naziv 
                    FROM Klienti 
                    WHERE Osiguritel = 1 
                    ORDER BY Naziv", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Naziv"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Osiguriteli = items;
                }
            }
        }

        private async Task LoadKlasiOsiguruvanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, KlasaIme 
                    FROM KlasiOsiguruvanje 
                    WHERE Id IN (1,2,3,8,9,10,18,19)
                    ORDER BY KlasaIme", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["KlasaIme"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    KlasiOsiguruvanje = items;
                }
            }
        }

        private async Task LoadZadolzeni()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, 
                           CASE 
                               WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                               ELSE CONCAT(Ime, ' ', Prezime)
                           END as DisplayName
                    FROM Klienti 
                        WHERE 
                        (KlientVraboten = 1 AND DogovorVaziDo > GETDATE() or (KlientVraboten = 1 and DogovorOpredelenoNeopredeleno = 'Неопределено'))
                        OR 
                        ((KlientSorabotnik = 1 OR BrokerskoDrustvo = 1) 
                        AND DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti > GETDATE()
                        AND DatumNaOdzemenaLicenca IS NULL)    
                    ORDER BY DisplayName", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayName"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Zadolzeni = items;
                }
            }
        }

        public async Task<JsonResult> OnGetProdukti(int klasaId)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            var produkti = new List<object>();

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Ime, Kategorija
                    FROM Produkti
                    WHERE KlasaOsiguruvanjeId = @KlasaId
                    ORDER BY Ime", connection))
                {
                    cmd.Parameters.AddWithValue("@KlasaId", klasaId);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        produkti.Add(new
                        {
                            id = reader["Id"].ToString(),
                            text = reader["Ime"].ToString()
                        });
                    }
                }
            }

            return new JsonResult(produkti);
        }

        private List<string> GeneratePolicyRange(string start, string end)
        {
            var result = new List<string>();
            
            // If either start or end is empty, return just the non-empty value
            if (string.IsNullOrEmpty(start) || string.IsNullOrEmpty(end))
            {
                if (!string.IsNullOrEmpty(start)) result.Add(start);
                if (!string.IsNullOrEmpty(end)) result.Add(end);
                return result;
            }

            // Try to extract numeric part and prefix
            string startPrefix = start.TrimEnd("0123456789".ToCharArray());
            string endPrefix = end.TrimEnd("0123456789".ToCharArray());

            // If prefixes don't match, just return start and end
            if (startPrefix != endPrefix)
            {
                result.Add(start);
                if (start != end) result.Add(end);
                return result;
            }

            string startNumber = start.Substring(startPrefix.Length);
            string endNumber = end.Substring(endPrefix.Length);

            // Try parse numbers
            if (long.TryParse(startNumber, out long startNum) && long.TryParse(endNumber, out long endNum))
            {
                // Ensure proper order
                if (startNum > endNum)
                {
                    (startNum, endNum) = (endNum, startNum);
                }

                // Generate range
                int padding = Math.Max(startNumber.Length, endNumber.Length);
                for (long i = startNum; i <= endNum; i++)
                {
                    result.Add($"{startPrefix}{i.ToString().PadLeft(padding, '0')}");
                }
            }
            else
            {
                // If parsing fails, just add start and end
                result.Add(start);
                if (start != end) result.Add(end);
            }

            return result;
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!await HasPageAccess("ZadolzuvanjeRazdolzuvanjePolisi"))
            {
                return RedirectToAccessDenied();
            }

            if (!ModelState.IsValid)
            {
                await LoadOsiguriteli();
                await LoadKlasiOsiguruvanje();
                await LoadZadolzeni();
                return Page();
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                
                // Generate policy range
                var polisiToInsert = GeneratePolicyRange(Input.BrojNaPolisa, Input.RangeDo);
                
                long? startPonuda = Input.BrojNaPonuda;
                long endPonuda = Input.RangePonudaDo ?? startPonuda ?? 0;
                
                if (startPonuda.HasValue && startPonuda > endPonuda)
                {
                    var temp = startPonuda.Value;
                    startPonuda = endPonuda;
                    endPonuda = temp;
                }

                // Check for existing numbers before proceeding
                var existingPolisi = new List<string>();
                var existingPonudi = new List<long>();

                // Check BrojNaPolisa range only if we have values to insert
                if (polisiToInsert.Any())
                {
                    using (SqlCommand cmdCheckPolisi = new SqlCommand(@"
                        SELECT BrojNaPolisa 
                        FROM PolisiZadolzuvanjeRazdolzuvanje 
                        WHERE BrojNaPolisa IN ({0})", connection))
                    {
                        // Create parameters for each policy number
                        for (int i = 0; i < polisiToInsert.Count; i++)
                        {
                            cmdCheckPolisi.Parameters.AddWithValue($"@p{i}", polisiToInsert[i]);
                        }

                        // Replace placeholder with actual parameter names
                        cmdCheckPolisi.CommandText = string.Format(
                            cmdCheckPolisi.CommandText,
                            string.Join(",", polisiToInsert.Select((_, i) => $"@p{i}"))
                        );

                        using SqlDataReader reader = await cmdCheckPolisi.ExecuteReaderAsync();
                        while (await reader.ReadAsync())
                        {
                            existingPolisi.Add(reader.GetString(0));
                        }
                    }
                }

                // Check BrojNaPonuda range only if we have a value
                if (startPonuda.HasValue)
                {
                    using (SqlCommand cmdCheckPonudi = new SqlCommand(@"
                        SELECT BrojNaPonuda 
                        FROM PolisiZadolzuvanjeRazdolzuvanje 
                        WHERE BrojNaPonuda BETWEEN @StartPonuda AND @EndPonuda", connection))
                    {
                        cmdCheckPonudi.Parameters.AddWithValue("@StartPonuda", startPonuda.Value);
                        cmdCheckPonudi.Parameters.AddWithValue("@EndPonuda", endPonuda);

                        using SqlDataReader reader = await cmdCheckPonudi.ExecuteReaderAsync();
                        while (await reader.ReadAsync())
                        {
                            existingPonudi.Add(reader.GetInt64(0));
                        }
                    }
                }

                // If we found existing numbers, return with error message
                if (existingPolisi.Any() || existingPonudi.Any())
                {
                    string errorMessage = "";
                    if (existingPolisi.Any())
                    {
                        errorMessage += $"Следните броеви на полиси веќе постојат: {string.Join(", ", existingPolisi)}. ";
                    }
                    if (existingPonudi.Any())
                    {
                        errorMessage += $"Следните броеви на понуди веќe постојат: {string.Join(", ", existingPonudi)}";
                    }

                    ModelState.AddModelError(string.Empty, errorMessage);
                    await LoadOsiguriteli();
                    await LoadKlasiOsiguruvanje();
                    await LoadZadolzeni();
                    return Page();
                }

                // Calculate increment for ponuda numbers
                double? ponudaIncrement = null;
                if (startPonuda.HasValue && startPonuda != endPonuda && polisiToInsert.Count > 1)
                {
                    ponudaIncrement = (double)(endPonuda - startPonuda.Value) / (polisiToInsert.Count - 1);
                }

                // If no existing numbers found, proceed with insert
                double? currentPonuda = startPonuda;
                foreach (var currentNum in polisiToInsert)
                {
                    using (SqlCommand cmd = new SqlCommand(@"
                        INSERT INTO PolisiZadolzuvanjeRazdolzuvanje (
                            UsernameCreated,
                            KlientiIdOsiguritel,
                            KlasiOsiguruvanjeIdKlasa,
                            ProduktiIdProdukt,
                            BrojNaPolisa,
                            BrojNaPonuda,
                            KlientiIdZadolzen,
                            DatumNaZadolzuvanje
                        ) VALUES (
                            @UsernameCreated,
                            @KlientiIdOsiguritel,
                            @KlasiOsiguruvanjeIdKlasa,
                            @ProduktiIdProdukt,
                            @BrojNaPolisa,
                            @BrojNaPonuda,
                            @KlientiIdZadolzen,
                            @DatumNaZadolzuvanje
                        )", connection))
                    {
                        cmd.Parameters.AddWithValue("@UsernameCreated", HttpContext.Session.GetString("Username"));
                        cmd.Parameters.AddWithValue("@KlientiIdOsiguritel", Input.KlientiIdOsiguritel ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@KlasiOsiguruvanjeIdKlasa", Input.KlasiOsiguruvanjeIdKlasa ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ProduktiIdProdukt", Input.ProduktiIdProdukt ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaPolisa", !string.IsNullOrEmpty(currentNum) ? (object)currentNum : DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaPonuda", currentPonuda.HasValue ? (object)(long)Math.Round(currentPonuda.Value) : DBNull.Value);
                        cmd.Parameters.AddWithValue("@KlientiIdZadolzen", Input.KlientiIdZadolzen ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumNaZadolzuvanje", Input.DatumNaZadolzuvanje ?? (object)DBNull.Value);

                        await cmd.ExecuteNonQueryAsync();
                        
                        if (ponudaIncrement.HasValue)
                        {
                            currentPonuda += ponudaIncrement;
                        }
                    }
                }
            }

            return RedirectToPage("ZadolzuvanjeRazdolzuvanjePolisi");
        }
    }
} 