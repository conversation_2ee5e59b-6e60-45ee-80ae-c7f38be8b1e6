﻿@page
@model RazorPortal.Pages.LoginModel
@{
    ViewData["Title"] = "Најава";
}

<link rel="stylesheet" href="~/css/login.css" asp-append-version="true" />

<div class="login-container">
    <h2>@ViewData["Title"]</h2>

    <form method="post">
        <div class="form-group">
            <label asp-for="Username">Корисничко име</label>
            <input asp-for="Username" class="form-control" />
            <span asp-validation-for="Username" class="text-danger"></span>
        </div>
        <div class="form-group">
            <label asp-for="Password">Лозинка</label>
            <input asp-for="Password" type="password" class="form-control" />
            <span asp-validation-for="Password" class="text-danger"></span>
        </div>

        <button type="submit" class="btn btn-primary">Најава</button>
        
        @if (!string.IsNullOrEmpty(Model.Message))
        {
            <p class="message">@Model.Message</p>
        }

        <div class="links">
            <p><a asp-page="/ResetPassword/ResetRequest">Заборавена лозинка?</a></p>
            <p><a asp-page="/Registration">Нов корисник?</a></p>
        </div>
    </form>
</div>
