namespace NextBroker.Helpers
{
    public static class NumberToMacedonianWordsConverter
    {
        private static readonly string[] edinici = { "", "еден", "два", "три", "четири", "пет", "шест", "седум", "осум", "девет" };
        private static readonly string[] desetki = { "", "десет", "дваесет", "триесет", "четириесет", "педесет", "шеесет", "седумдесет", "осумдесет", "деведесет" };
        private static readonly string[] teens = { "десет", "единаесет", "дванаесет", "тринаесет", "четиринаесет", "петнаесет", "шеснаесет", "седумнаесет", "осумнаесет", "деветнаесет" };

        public static string Convert(decimal number)
        {
            if (number == 0) return "нула денари";

            // Get the integer part directly from the decimal
            long wholePart = (long)Math.Floor(number);
            // Get the decimal part by subtracting the whole part and multiplying by 100
            int decimalPart = (int)Math.Round((number - wholePart) * 100);

            string result = ConvertWholePart(wholePart);

            if (decimalPart > 0)
            {
                result += " и " + decimalPart.ToString("D2") + "/100";
            }

            return result + " денари";
        }

        private static string ConvertWholePart(long number)
        {
            if (number == 0) return "";

            if (number < 10)
                return edinici[number];

            if (number < 20)
                return teens[number - 10];

            if (number < 100)
            {
                var ed = number % 10;
                var des = number / 10;
                return desetki[des] + (ed > 0 ? " и " + edinici[ed] : "");
            }

            if (number < 1000)
            {
                var stotici = number / 100;
                var ostatok = number % 100;
                var stoticiText = stotici == 1 ? "сто" :
                                stotici == 2 ? "двесте" :
                                stotici == 3 ? "триста" :
                                stotici == 5 ? "петстотини" :
                                stotici == 6 ? "шестотини" :
                                stotici == 7 ? "седумстотини" :
                                stotici == 8 ? "осумстотини" :
                                stotici == 9 ? "деветстотини" :
                                edinici[stotici] + "стотини";
                
                return stoticiText + (ostatok > 0 ? " " + ConvertWholePart(ostatok) : "");
            }

            if (number < 1000000)
            {
                var iljadi = number / 1000;
                var ostatok = number % 1000;
                var iljadiText = iljadi == 1 ? "илјада" :
                               iljadi < 10 ? edinici[iljadi] + " илјади" :
                               ConvertWholePart(iljadi) + " илјади";
                
                return iljadiText + (ostatok > 0 ? " " + ConvertWholePart(ostatok) : "");
            }

            if (number < 1000000000)
            {
                var milioni = number / 1000000;
                var ostatok = number % 1000000;
                var milioniText = milioni == 1 ? "еден милион" :
                                ConvertWholePart(milioni) + " милиони";
                
                return milioniText + (ostatok > 0 ? " " + ConvertWholePart(ostatok) : "");
            }

            return number.ToString();
        }
    }
} 