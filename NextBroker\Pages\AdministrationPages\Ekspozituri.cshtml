@page
@model NextBroker.Pages.AdministrationPages.EkspozituriModel
@{
    ViewData["Title"] = "Експозитури";
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container">
    <div class="mb-3">
        <form method="post" asp-page-handler="ToggleEdit" class="d-flex gap-2">
            <button type="submit" class="btn btn-primary" onclick="return confirmCancel()">
                @(Model.IsEditing ? "Откажи" : "Промени ги податоците")
            </button>
            @if (Model.IsEditing)
            {
                <button type="button" class="btn btn-success" id="saveChanges">Зачувај промени</button>
            }
            <button type="button" class="btn btn-primary" onclick="toggleAddForm()">Додади експозитура</button>
        </form>
    </div>

    <div id="addForm" style="display: none;" class="mb-3">
        <div class="card">
            <div class="card-body">
                <form method="post" asp-page-handler="AddEkspozitura">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">Име</label>
                            <input type="text" class="form-control" name="ime" required />
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Адреса</label>
                            <input type="text" class="form-control" name="adresa" required />
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Телефон</label>
                            <input type="text" class="form-control" name="telefon" required />
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="submit" class="btn btn-success">Додади</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="mb-3">
        <input type="text" id="searchBox" class="form-control" placeholder="Пребарувај..." />
    </div>

    <div class="table-responsive">
        <table class="table table-striped" id="ekspozituriTable">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Датум на креирање</th>
                    <th>Име</th>
                    <th>Адреса</th>
                    <th>Телефон</th>
                    @if (Model.IsEditing)
                    {
                        <th>Акции</th>
                    }
                </tr>
            </thead>
            <tbody>
                @foreach (var ekspozitura in Model.EkspozituriList)
                {
                    <tr data-id="@ekspozitura.Id">
                        <td>@ekspozitura.Id</td>
                        <td>@(ekspozitura.DateCreated == DateTime.MinValue ? "" : ekspozitura.DateCreated.ToString("dd.MM.yyyy HH:mm:ss"))</td>
                        <td>
                            @if (Model.IsEditing)
                            {
                                <input type="text" class="form-control ime-edit" value="@ekspozitura.Ime" />
                            }
                            else
                            {
                                @ekspozitura.Ime
                            }
                        </td>
                        <td>
                            @if (Model.IsEditing)
                            {
                                <input type="text" class="form-control adresa-edit" value="@ekspozitura.Adresa" />
                            }
                            else
                            {
                                @ekspozitura.Adresa
                            }
                        </td>
                        <td>
                            @if (Model.IsEditing)
                            {
                                <input type="text" class="form-control telefon-edit" value="@ekspozitura.Telefon" />
                            }
                            else
                            {
                                @ekspozitura.Telefon
                            }
                        </td>
                        @if (Model.IsEditing)
                        {
                            <td>
                                <button type="button" class="btn btn-danger btn-sm delete-ekspozitura" onclick="deleteEkspozitura(@ekspozitura.Id)">Избриши</button>
                            </td>
                        }
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            var table = $('#ekspozituriTable').DataTable({
                "dom": "t",
                "language": {
                    "emptyTable": "Нема пронајдени записи",
                    "zeroRecords": "Нема пронајдени записи"
                },
                "ordering": false,
                "paging": false,
                "pageLength": -1
            });

            $('#searchBox').on('keyup', function() {
                table.search(this.value).draw();
            });
        });

        function toggleAddForm() {
            const form = document.getElementById('addForm');
            if (form.style.display === 'none' || form.style.display === '') {
                form.style.display = 'block';
            } else {
                form.style.display = 'none';
            }
        }

        function confirmCancel() {
            if (@Json.Serialize(Model.IsEditing)) {
                return confirm("Дали сте сигурни дека сакате да го откажете уредувањето? Сите незачувани промени ќе бидат изгубени.");
            }
            return true;
        }

        function deleteEkspozitura(id) {
            if (confirm('Дали сте сигурни дека сакате да ја избришете оваа експозитура?')) {
                fetch('?handler=DeleteEkspozitura', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    }
                })
                .catch(error => console.error('Error:', error));
            }
        }

        @if (Model.IsEditing)
        {
            <text>
            document.getElementById('saveChanges').addEventListener('click', function() {
                const rows = document.querySelectorAll('#ekspozituriTable tbody tr');
                const updates = [];

                rows.forEach(row => {
                    const id = row.getAttribute('data-id');
                    const ime = row.querySelector('.ime-edit').value;
                    const adresa = row.querySelector('.adresa-edit').value;
                    const telefon = row.querySelector('.telefon-edit').value;

                    updates.push({
                        id: parseInt(id),
                        ime: ime,
                        adresa: adresa,
                        telefon: telefon
                    });
                });

                fetch('?handler=SaveChanges', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify(updates)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = window.location.pathname;
                    }
                })
                .catch(error => console.error('Error:', error));
            });
            </text>
        }
    </script>
} 