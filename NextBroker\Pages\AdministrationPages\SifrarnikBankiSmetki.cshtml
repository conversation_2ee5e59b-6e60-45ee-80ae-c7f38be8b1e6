@page
@model NextBroker.Pages.AdministrationPages.SifrarnikBankiSmetkiModel
@{
    ViewData["Title"] = "Банки и сметки";
}

<div class="container-fluid">
    <h4 class="mb-3">@ViewData["Title"]</h4>

    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title" id="formTitle">Додај нова сметка</h5>
                    <form method="post">
                        <input type="hidden" asp-for="Input.Id" id="bankAccountId" />
                        
                        <div class="mb-3">
                            <label asp-for="Input.SifrarnikBankiId" class="form-label"></label>
                            <select asp-for="Input.SifrarnikBankiId" asp-items="Model.Banki" class="form-select">
                                <option value="">-- Изберете банка --</option>
                            </select>
                            <span asp-validation-for="Input.SifrarnikBankiId" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Input.BrojNaSmetka" class="form-label"></label>
                            <input asp-for="Input.BrojNaSmetka" class="form-control" />
                            <span asp-validation-for="Input.BrojNaSmetka" class="text-danger"></span>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">Зачувај</button>
                            <button type="button" class="btn btn-secondary" onclick="resetForm()">Откажи</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <table id="bankAccountsTable" class="table table-striped">
                        <thead>
                            <tr>
                                <th>Банка</th>
                                <th>Број на сметка</th>
                                <th>Акции</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var account in Model.BankAccounts)
                            {
                                <tr>
                                    <td>@account.BankName</td>
                                    <td>@account.BrojNaSmetka</td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-primary" onclick="editBankAccount(@account.Id)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <form method="post" asp-page-handler="Delete" asp-route-id="@account.Id" style="display: inline;">
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Дали сте сигурни дека сакате да ја избришете оваа сметка?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#bankAccountsTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Macedonian.json"
                }
            });
        });

        function editBankAccount(id) {
            fetch(`?handler=Edit&id=${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data) {
                        $('#bankAccountId').val(data.id);
                        $('#Input_SifrarnikBankiId').val(data.sifrarnikBankiId);
                        $('#Input_BrojNaSmetka').val(data.brojNaSmetka);
                        $('#formTitle').text('Измени сметка');
                    }
                });
        }

        function resetForm() {
            $('#bankAccountId').val('');
            $('#Input_SifrarnikBankiId').val('');
            $('#Input_BrojNaSmetka').val('');
            $('#formTitle').text('Додај нова сметка');
        }
    </script>
}
