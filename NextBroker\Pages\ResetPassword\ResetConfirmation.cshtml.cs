﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.Data.SqlClient;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Linq;
using System.ComponentModel.DataAnnotations;
using Microsoft.Data.SqlClient;

public class ResetConfirmationModel : PageModel
{
    private readonly IConfiguration _configuration;

    public ResetConfirmationModel(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    [BindProperty]
    [Required(ErrorMessage = "Задолжително поле!")]
    public string EMB { get; set; }

    [BindProperty]
    [Required(ErrorMessage = "Задолжително поле!")]
    [RegularExpression(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\da-zA-Z]).{8,}$", 
        ErrorMessage = "Лозинката мора да содржи најмалку 8 карактери, една голема буква, една бројка и еден специјален карактер.")]
    public string NewPassword { get; set; }

    [BindProperty]
    [Required(ErrorMessage = "Задолжително поле!")]
    [Compare("NewPassword", ErrorMessage = "Лозинките не се совпаѓаат.")]
    public string ConfirmPassword { get; set; }

    [BindProperty]
    [Required(ErrorMessage = "Задолжително поле!")]
    public string ResetRequest { get; set; }

    public string ErrorMessage { get; set; }
    public string SuccessMessage { get; set; }

    public async Task<IActionResult> OnPostAsync()
    {
        if (!ModelState.IsValid)
        {
            return Page();
        }

        var connectionString = _configuration.GetConnectionString("DefaultConnection");

        try
        {
            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // Check if the reset request code matches the one in the database
                string storedResetRequest = null;
                using (var selectCommand = new SqlCommand("SELECT resetrequest FROM users WHERE emb = @emb", connection))
                {
                    selectCommand.Parameters.AddWithValue("@emb", EMB);

                    var result = await selectCommand.ExecuteScalarAsync();
                    if (result == null)
                    {
                        ErrorMessage = "Корисникот со дадениот матичен број не постои.";
                        return Page();
                    }

                    storedResetRequest = result.ToString();
                }

                if (storedResetRequest != ResetRequest)
                {
                    ErrorMessage = "Кодот за ресетирање на лозинката не се совпаѓа.";
                    return Page();
                }

                // Generate random salt
                string salt = GenerateSalt(10);

                // Hash the new password
                byte[] key;
                byte[] iv;
                string passwordHash = HashPasswordAES(NewPassword, salt, out key, out iv);

                // Convert key and IV to Base64 for storage
                string keyBase64 = Convert.ToBase64String(key);
                string ivBase64 = Convert.ToBase64String(iv);

                // Update the user's password in the database
                using (var updateCommand = new SqlCommand(@"
                    UPDATE users 
                    SET passwordhash = @passwordHash, passwordsalt = @passwordSalt, passwordkey = @passwordKey, passwordiv = @passwordIV, resetrequest = NULL 
                    WHERE emb = @emb", connection))
                {
                    updateCommand.Parameters.AddWithValue("@passwordHash", passwordHash);
                    updateCommand.Parameters.AddWithValue("@passwordSalt", salt);
                    updateCommand.Parameters.AddWithValue("@passwordKey", keyBase64);
                    updateCommand.Parameters.AddWithValue("@passwordIV", ivBase64);
                    updateCommand.Parameters.AddWithValue("@emb", EMB);

                    await updateCommand.ExecuteNonQueryAsync();
                }
            }

            SuccessMessage = "Вашата лозинка е успешно променета.";
            return Page();
        }
        catch (Exception ex)
        {
            ErrorMessage = $"Настана грешка: {ex.Message}";
            return Page();
        }
    }

    private string GenerateSalt(int length)
    {
        const string validChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        var random = new Random();
        return new string(Enumerable.Repeat(validChars, length)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }

    private string HashPasswordAES(string password, string salt, out byte[] key, out byte[] iv)
    {
        using (var aes = Aes.Create())
        {
            aes.KeySize = 256;
            aes.BlockSize = 128;
            aes.GenerateKey();
            aes.GenerateIV();

            key = Encoding.UTF8.GetBytes(salt);
            Array.Resize(ref key, aes.Key.Length);

            iv = aes.IV;
            aes.Key = key;
            aes.Mode = CipherMode.CBC;

            using (var encryptor = aes.CreateEncryptor(aes.Key, aes.IV))
            {
                var passwordBytes = Encoding.UTF8.GetBytes(password);
                var encryptedPassword = encryptor.TransformFinalBlock(passwordBytes, 0, passwordBytes.Length);
                return Convert.ToBase64String(encryptedPassword);
            }
        }
    }
}
