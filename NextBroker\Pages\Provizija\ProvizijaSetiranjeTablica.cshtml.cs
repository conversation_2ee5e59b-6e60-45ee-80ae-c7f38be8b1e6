using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Data;
using System;

namespace NextBroker.Pages.Provizija
{
    public class ProvizijaSetiranjeTablicaModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        
        [BindProperty]
        public long? KlientiId { get; set; }
        
        public DataTable ResultTable { get; set; }
        
        public bool IsDataLoaded { get; set; }

        public ProvizijaSetiranjeTablicaModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
            ResultTable = new DataTable();
            IsDataLoaded = false;
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("ProvizijaSetiranjeTablica"))
            {
                return RedirectToAccessDenied();
            }

            return Page();
        }
        
        public async Task<IActionResult> OnPostAsync()
        {
            if (!await HasPageAccess("ProvizijaSetiranjeTablica"))
            {
                return RedirectToAccessDenied();
            }
            
            if (KlientiId.HasValue)
            {
                await LoadDataAsync(KlientiId.Value);
                IsDataLoaded = true;
            }
            
            return Page();
        }
        
        private async Task LoadDataAsync(long klientiId)
        {
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                
                using (var command = new SqlCommand("VratiListaVneseniPravilaZaProvizijaPoKlient", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@KlientiId", klientiId);
                    
                    using (var adapter = new SqlDataAdapter(command))
                    {
                        ResultTable = new DataTable();
                        adapter.Fill(ResultTable);
                    }
                }
            }
        }
        
        public async Task<IActionResult> OnGetSearchKlientiAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE MB LIKE @Search + '%'
                       OR EDB LIKE @Search + '%'
                       OR EMBG LIKE @Search + '%'
                       OR Naziv LIKE '%' + @Search + '%'
                       OR Ime LIKE '%' + @Search + '%'
                       OR Prezime LIKE '%' + @Search + '%'
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }
    }
}
