# Use the base image for ASP.NET
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Copy the font file into the image with the correct case
COPY wwwroot/fonts/Arial.ttf /app/fonts/Arial.ttf

# Install Microsoft core fonts and Tesseract libraries
RUN apt-get update && \
    apt-get install -y ttf-mscorefonts-installer fontconfig tesseract-ocr libtesseract5 liblept5 tesseract-ocr-eng wget && \
    fc-cache -f -v && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Create the keys directory and set permissions
RUN mkdir /app/keys && chmod -R 777 /app/keys

# Set up tessdata and download Macedonian language pack
RUN mkdir -p /app/wwwroot/tessdata && \
    wget -O /app/wwwroot/tessdata/mkd.traineddata https://github.com/tesseract-ocr/tessdata/raw/main/mkd.traineddata && \
    cp /usr/share/tesseract-ocr/4.00/tessdata/eng.traineddata /app/wwwroot/tessdata/ && \
    cp /usr/share/tesseract-ocr/4.00/tessdata/osd.traineddata /app/wwwroot/tessdata/ && \
    chmod -R 777 /app/wwwroot/tessdata

# Set environment variable for tessdata
ENV TESSDATA_PREFIX=/app/wwwroot/tessdata

# Build stage
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["RazorPortal/NextBroker.csproj", "RazorPortal/"]
RUN dotnet restore "./RazorPortal/NextBroker.csproj"
COPY . .
WORKDIR "/src/RazorPortal"
RUN dotnet build "./NextBroker.csproj" -c $BUILD_CONFIGURATION -o /app/build

# Publish stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./NextBroker.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# Final stage
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish . 
COPY ssl_certificate/certificate.pfx /https/certificate.pfx 
ENTRYPOINT ["dotnet", "NextBroker.dll"]
