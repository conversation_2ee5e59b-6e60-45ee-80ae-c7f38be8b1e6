@page
@model RazorPortal.Pages.ResetConfirmationEnModel
@{
    ViewData["Title"] = "Reset Password";
}

<link rel="stylesheet" href="~/css/login.css" asp-append-version="true" />

<div class="login-container">
    <h2>@ViewData["Title"]</h2>

    @if (Model.SuccessMessage == null)
    {
        <form method="post" id="resetForm">
            <div class="form-group">
                <label for="emb">ID Number</label>
                <input type="text" 
                       id="emb" 
                       name="EMB" 
                       class="form-control" 
                       pattern="^\d+$" 
                       title="Enter a valid ID number."
                       required />
            </div>

            <div class="form-group">
                <label for="newPassword">New Password</label>
                <input type="password" 
                       id="newPassword" 
                       name="NewPassword" 
                       class="form-control" 
                       pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\da-zA-Z]).{8,}$" 
                       title="Password must contain at least 8 characters, one uppercase letter, one number, and one special character." 
                       required />
            </div>

            <div class="form-group">
                <label for="confirmPassword">Confirm Password</label>
                <input type="password" 
                       id="confirmPassword" 
                       name="ConfirmPassword" 
                       class="form-control" 
                       required />
            </div>

            <div class="form-group">
                <label for="resetRequest">Confirmation Code</label>
                <input type="text" 
                       id="resetRequest" 
                       name="ResetRequest" 
                       class="form-control" 
                       placeholder="Enter the code from your email"
                       required />
            </div>

            <button type="submit" class="btn btn-primary">Change Password</button>

            @if (Model.ErrorMessage != null)
            {
                <div class="alert alert-danger mt-3">@Model.ErrorMessage</div>
            }
        </form>
    }
    else
    {
        <div class="alert alert-success mt-3">@Model.SuccessMessage</div>
        
        <button onclick="window.location.href='@Url.Page("/LoginEn")'" class="btn btn-primary">
            Continue to Login
        </button>
    }
</div> 