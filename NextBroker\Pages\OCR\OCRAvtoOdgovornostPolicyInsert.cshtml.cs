using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using Microsoft.Data.SqlClient;

namespace NextBroker.Pages.OCR
{
    public class OCRAvtoOdgovornostPolicyInsertModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public OCRAvtoOdgovornostPolicyInsertModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        // All the policy data properties with BindProperty for form binding
        [BindProperty]
        public string? BrojNaPolisa { get; set; }
        
        [BindProperty]
        public string? Registracija { get; set; }
        
        [BindProperty]
        public string? Prodavac { get; set; }
        
        [BindProperty]
        public string? Dogovoruvac { get; set; }
        
        [BindProperty]
        public string? DogovoruvacEMBG { get; set; }
        
        [BindProperty]
        public string? DogovoruvacAdresa { get; set; }
        
        [BindProperty]
        public string? DogovoruvacMesto { get; set; }

        [BindProperty]
        public int? DogovoruvacMestoId { get; set; }

        // Additional field for database mapping
        [BindProperty]
        public string? OpstinaDogovoruvac { get; set; }
        
        [BindProperty]
        public string? Osigurenik { get; set; }
        
        [BindProperty]
        public string? OsigurenikEMBG { get; set; }
        
        [BindProperty]
        public string? OsigurenikMesto { get; set; }

        [BindProperty]
        public int? OsigurenikMestoId { get; set; }

        // Additional field for database mapping  
        [BindProperty]
        public string? AdresaOsigurenik { get; set; }
        
        [BindProperty]
        public string? VidVozilo { get; set; }

        [BindProperty]
        public long? VidVoziloId { get; set; }
        
        [BindProperty]
        public string? ModelVozilo { get; set; }
        
        [BindProperty]
        public string? MarkaVozilo { get; set; }
        
        [BindProperty]
        public string? Shasija { get; set; }
        
        [BindProperty]
        public string? GodinaProizvodstvo { get; set; }
        
        [BindProperty]
        public string? MoknostVoKW { get; set; }
        
        [BindProperty]
        public string? ZafatninaVoCm3 { get; set; }
        
        [BindProperty]
        public string? NosivostVoKG { get; set; }
        
        [BindProperty]
        public string? RegMesta { get; set; }
        
        [BindProperty]
        public string? OsnovnaPremijaAO { get; set; }
        
        [BindProperty]
        public string? Bonus { get; set; }
        
        [BindProperty]
        public string? KrsenjeStaklo { get; set; }
        
        [BindProperty]
        public string? OsiguruvanjePatnici { get; set; }
        
        [BindProperty]
        public string? DopolnitelnoOsiguruvanje { get; set; }
        
        [BindProperty]
        public string? Asistencija { get; set; }
        
        [BindProperty]
        public string? MestoIzdavanje { get; set; }

        [BindProperty]
        public int? MestoIzdavanjeId { get; set; }

        // Dropdown list for municipalities
        public List<OpstiniDropdownItem> OpstiniList { get; set; } = new List<OpstiniDropdownItem>();

        // Dropdown list for vehicle types
        public List<VidVoziloDropdownItem> VidVoziloList { get; set; } = new List<VidVoziloDropdownItem>();

        // Dropdown list for clients (Osiguritel)
        public List<KlientiDropdownItem> KlientiList { get; set; } = new List<KlientiDropdownItem>();

        // Dropdown list for payment methods
        public List<NacinNaPlakanjeDropdownItem> NacinNaPlakanjeList { get; set; } = new List<NacinNaPlakanjeDropdownItem>();

        public class OpstiniDropdownItem
        {
            public int Id { get; set; }
            public string Opstina { get; set; } = string.Empty;
        }

        public class VidVoziloDropdownItem
        {
            public long Id { get; set; }
            public string TipNaVozilo { get; set; } = string.Empty;
        }

        public class KlientiDropdownItem
        {
            public long Id { get; set; }
            public string Naziv { get; set; } = string.Empty;
        }

        public class NacinNaPlakanjeDropdownItem
        {
            public long Id { get; set; }
            public string NacinNaPlakanje { get; set; } = string.Empty;
        }
        
        [BindProperty]
        public string? DatumNaIzdavanje { get; set; }
        
        [BindProperty]
        public string? PolisaVaziOd { get; set; }
        
        [BindProperty]
        public string? PolisaVaziDo { get; set; }

        [BindProperty]
        public string? TipNaFaktura { get; set; }

        [BindProperty]
        public long? KlientiIdOsiguritel { get; set; }

        // Additional fields for database mapping
        [BindProperty]
        public decimal? Doplatok { get; set; }

        [BindProperty]
        public decimal? Popust { get; set; }

        [BindProperty]
        public decimal? VkupnoOsnovnaPremijaAO { get; set; }

        [BindProperty]
        public decimal? VkupnaPremija { get; set; }

        // New field for payment method
        [BindProperty]
        public long? SifrarnikNacinNaPlakanjeId { get; set; }

        // Status messages
        public string? ErrorMessage { get; set; }
        public string? SuccessMessage { get; set; }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("OCRPolisaAO"))
            {
                return RedirectToAccessDenied();
            }

            // Get data from query parameters (passed from OCR page)
            BrojNaPolisa = Request.Query["brojNaPolisa"];
            Registracija = Request.Query["registracija"];
            Prodavac = Request.Query["prodavac"];
            Dogovoruvac = Request.Query["dogovoruvac"];
            DogovoruvacEMBG = Request.Query["dogovoruvacEMBG"];
            DogovoruvacAdresa = Request.Query["dogovoruvacAdresa"];
            DogovoruvacMesto = Request.Query["dogovoruvacMesto"];
            Osigurenik = Request.Query["osigurenik"];
            OsigurenikEMBG = Request.Query["osigurenikEMBG"];
            AdresaOsigurenik = Request.Query["osigurenikAdresa"];
            OsigurenikMesto = Request.Query["osigurenikMesto"];
            VidVozilo = Request.Query["vidVozilo"];
            ModelVozilo = Request.Query["modelVozilo"];
            MarkaVozilo = Request.Query["markaVozilo"];
            Shasija = Request.Query["shasija"];
            GodinaProizvodstvo = Request.Query["godinaProizvodstvo"];
            MoknostVoKW = Request.Query["moknostVoKW"];
            ZafatninaVoCm3 = Request.Query["zafatninaVoCm3"];
            NosivostVoKG = Request.Query["nosivostVoKG"];
            RegMesta = Request.Query["regMesta"];
            OsnovnaPremijaAO = Request.Query["osnovnaPremijaAO"];
            Bonus = Request.Query["bonus"];
            KrsenjeStaklo = Request.Query["krsenjeStaklo"];
            OsiguruvanjePatnici = Request.Query["osiguruvanjePatnici"];
            DopolnitelnoOsiguruvanje = Request.Query["dopolnitelnoOsiguruvanje"];
            Asistencija = Request.Query["asistencija"];
            MestoIzdavanje = Request.Query["mestoIzdavanje"];
            
            // Convert date formats from dd.mm.yyyy to yyyy-mm-dd for HTML date inputs
            DatumNaIzdavanje = ConvertDateFormat(Request.Query["datumNaIzdavanje"]);
            PolisaVaziOd = ConvertDateFormat(Request.Query["polisaVaziOd"]);
            PolisaVaziDo = ConvertDateFormat(Request.Query["polisaVaziDo"]);

            // Load municipalities dropdown and match existing value
            await LoadOpstiniDropdown();

            // Load vehicle types dropdown and match existing value
            await LoadVidVoziloDropdown();

            // Load clients dropdown
            await LoadKlientiDropdown();

            // Load payment methods dropdown
            await LoadNacinNaPlakanjeDropdown();

            return Page();
        }

        private string? ConvertDateFormat(string? dateString)
        {
            if (string.IsNullOrEmpty(dateString))
                return null;

            // Try to parse dd.mm.yyyy format
            if (DateTime.TryParseExact(dateString, "dd.MM.yyyy", null, System.Globalization.DateTimeStyles.None, out DateTime parsedDate))
            {
                // Return in yyyy-mm-dd format for HTML date input
                return parsedDate.ToString("yyyy-MM-dd");
            }
            
            // If parsing fails, return original string
            return dateString;
        }

        private OpstiniDropdownItem? FindMatchingOpstina(string searchText)
        {
            if (string.IsNullOrEmpty(searchText))
                return null;

            // First try exact match
            var matchedOpstina = OpstiniList.FirstOrDefault(o => 
                o.Opstina.Equals(searchText, StringComparison.OrdinalIgnoreCase));
            
            // If no exact match, try partial match (contains)
            if (matchedOpstina == null)
            {
                matchedOpstina = OpstiniList.FirstOrDefault(o => 
                    o.Opstina.Contains(searchText, StringComparison.OrdinalIgnoreCase));
            }
            
            // If still no match, try reverse contains (passed value contains database value)
            if (matchedOpstina == null)
            {
                matchedOpstina = OpstiniList.FirstOrDefault(o => 
                    searchText.Contains(o.Opstina, StringComparison.OrdinalIgnoreCase));
            }

            return matchedOpstina;
        }

        private async Task LoadOpstiniDropdown()
        {
            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    string selectSql = "SELECT Id, Opstina FROM ListaOpstini ORDER BY Opstina";
                    using (SqlCommand cmd = new SqlCommand(selectSql, connection))
                    {
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                OpstiniList.Add(new OpstiniDropdownItem
                                {
                                    Id = reader.GetInt32(0), // Id column (first column)
                                    Opstina = reader.GetString(1) // Opstina column (second column)
                                });
                            }
                        }
                    }
                }

                // Match the passed MestoIzdavanje text to find the correct Id
                if (!string.IsNullOrEmpty(MestoIzdavanje))
                {
                    var matchedOpstina = FindMatchingOpstina(MestoIzdavanje);
                    if (matchedOpstina != null)
                    {
                        MestoIzdavanjeId = matchedOpstina.Id;
                    }
                }

                // Match the passed DogovoruvacMesto text to find the correct Id
                if (!string.IsNullOrEmpty(DogovoruvacMesto))
                {
                    var matchedOpstina = FindMatchingOpstina(DogovoruvacMesto);
                    if (matchedOpstina != null)
                    {
                        DogovoruvacMestoId = matchedOpstina.Id;
                    }
                }

                // Match the passed OsigurenikMesto text to find the correct Id
                if (!string.IsNullOrEmpty(OsigurenikMesto))
                {
                    var matchedOpstina = FindMatchingOpstina(OsigurenikMesto);
                    if (matchedOpstina != null)
                    {
                        OsigurenikMestoId = matchedOpstina.Id;
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but don't break the page
                System.Diagnostics.Debug.WriteLine($"Error loading opstini dropdown: {ex.Message}");
            }
        }

        private VidVoziloDropdownItem? FindMatchingVidVozilo(string searchText)
        {
            if (string.IsNullOrEmpty(searchText))
                return null;

            // Get first 5 letters of search text
            var searchFirst5 = searchText.Length >= 5 ? searchText.Substring(0, 5) : searchText;

            // Find matching vehicle type based on first 5 letters
            var matchedVidVozilo = VidVoziloList.FirstOrDefault(v => 
                !string.IsNullOrEmpty(v.TipNaVozilo) && 
                v.TipNaVozilo.Length >= 5 && 
                v.TipNaVozilo.Substring(0, 5).Equals(searchFirst5, StringComparison.OrdinalIgnoreCase));

            // If no match with first 5 letters, try exact match
            if (matchedVidVozilo == null)
            {
                matchedVidVozilo = VidVoziloList.FirstOrDefault(v => 
                    v.TipNaVozilo.Equals(searchText, StringComparison.OrdinalIgnoreCase));
            }

            return matchedVidVozilo;
        }

        private async Task LoadVidVoziloDropdown()
        {
            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    string selectSql = "SELECT Id, TipNaVozilo FROM SifrarnikTipNaVozilo ORDER BY TipNaVozilo";
                    using (SqlCommand cmd = new SqlCommand(selectSql, connection))
                    {
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                VidVoziloList.Add(new VidVoziloDropdownItem
                                {
                                    Id = reader.GetInt64(0), // Id column (first column)
                                    TipNaVozilo = reader.IsDBNull(1) ? "" : reader.GetString(1) // TipNaVozilo column (second column)
                                });
                            }
                        }
                    }
                }

                // Match the passed VidVozilo text to find the correct Id
                if (!string.IsNullOrEmpty(VidVozilo))
                {
                    var matchedVidVozilo = FindMatchingVidVozilo(VidVozilo);
                    if (matchedVidVozilo != null)
                    {
                        VidVoziloId = matchedVidVozilo.Id;
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but don't break the page
                System.Diagnostics.Debug.WriteLine($"Error loading vid vozilo dropdown: {ex.Message}");
            }
        }

        private async Task LoadKlientiDropdown()
        {
            try
            {
                // Clear existing items
                KlientiList.Clear();
                
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    string selectSql = "SELECT Id, Naziv FROM Klienti WHERE Osiguritel = 1 ORDER BY Naziv";
                    using (SqlCommand cmd = new SqlCommand(selectSql, connection))
                    {
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                KlientiList.Add(new KlientiDropdownItem
                                {
                                    Id = reader.GetInt64(0), // Id column (first column) - bigint
                                    Naziv = reader.IsDBNull(1) ? "" : reader.GetString(1) // Naziv column (second column)
                                });
                            }
                        }
                    }
                }
                
                // Debug information
                System.Diagnostics.Debug.WriteLine($"Loaded {KlientiList.Count} clients from Klienti table");
            }
            catch (Exception ex)
            {
                // Log error but don't break the page
                System.Diagnostics.Debug.WriteLine($"Error loading klienti dropdown: {ex.Message}");
                ErrorMessage = $"Грешка при вчитување на листата на осигурители: {ex.Message}";
            }
        }

        private async Task LoadNacinNaPlakanjeDropdown()
        {
            try
            {
                // Clear existing items
                NacinNaPlakanjeList.Clear();
                
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    string selectSql = "SELECT Id, NacinNaPlakanje FROM SifrarnikNacinNaPlakanje ORDER BY NacinNaPlakanje";
                    using (SqlCommand cmd = new SqlCommand(selectSql, connection))
                    {
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                NacinNaPlakanjeList.Add(new NacinNaPlakanjeDropdownItem
                                {
                                    Id = reader.GetInt64(0), // Id column (first column)
                                    NacinNaPlakanje = reader.IsDBNull(1) ? "" : reader.GetString(1) // NacinNaPlakanje column (second column)
                                });
                            }
                        }
                    }
                }

                // Set default selected option to ID 1 if not already set
                if (SifrarnikNacinNaPlakanjeId == null && NacinNaPlakanjeList.Any(n => n.Id == 1))
                {
                    SifrarnikNacinNaPlakanjeId = 1;
                }
                
                // Debug information
                System.Diagnostics.Debug.WriteLine($"Loaded {NacinNaPlakanjeList.Count} payment methods from SifrarnikNacinNaPlakanje table");
            }
            catch (Exception ex)
            {
                // Log error but don't break the page
                System.Diagnostics.Debug.WriteLine($"Error loading nacin na plakanje dropdown: {ex.Message}");
                ErrorMessage = $"Грешка при вчитување на листата на начини на плаќање: {ex.Message}";
            }
        }

        public async Task<IActionResult> OnPost()
        {
            if (!await HasPageAccess("OCRPolisaAO"))
            {
                return RedirectToAccessDenied();
            }

            // Check for duplicate policy number before inserting
            if (!string.IsNullOrEmpty(BrojNaPolisa))
            {
                try
                {
                    string connectionString = _configuration.GetConnectionString("DefaultConnection");
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        await connection.OpenAsync();
                        
                        string checkSql = @"
                            SELECT COUNT(*) 
                            FROM Polisi 
                            WHERE BrojNaPolisa = @BrojNaPolisa 
                            AND (Storno IS NULL OR Storno = 0)";
                        
                        using (SqlCommand cmd = new SqlCommand(checkSql, connection))
                        {
                            cmd.Parameters.AddWithValue("@BrojNaPolisa", BrojNaPolisa);
                            
                            int count = (int)await cmd.ExecuteScalarAsync();
                            if (count > 0)
                            {
                                ErrorMessage = "Бројот на полиса веќе постои во системот, сторнирајте ја постоечката полиса доколку саката да ја внесете полисата поново.";
                                
                                // Reload dropdown data and return to page
                                await LoadOpstiniDropdown();
                                await LoadVidVoziloDropdown();
                                await LoadKlientiDropdown();
                                await LoadNacinNaPlakanjeDropdown();
                                
                                return Page();
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    ErrorMessage = $"Грешка при проверка на дуплират: {ex.Message}";
                    
                    // Reload dropdown data and return to page
                    await LoadOpstiniDropdown();
                    await LoadVidVoziloDropdown();
                    await LoadKlientiDropdown();
                    await LoadNacinNaPlakanjeDropdown();
                    
                    return Page();
                }
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    string insertSql = @"
                        INSERT INTO [dbo].[OCRAvtoOdgovornost] (
                            [UsernameCreated],
                            [BrojNaPolisa],
                            [Dogovoruvac],
                            [MBDogovoruvac],
                            [OpstinaDogovoruvac],
                            [AdresaDogovoruvac],
                            [Osigurenik],
                            [MBOsigurenik],
                            [MestoOsigurenik],
                            [AdresaOsigurenik],
                            [Registracija],
                            [PolisaVaziOd],
                            [PolisaVaziDo],
                            [Marka],
                            [Model],
                            [Shasija],
                            [GodinaProizvodstvo],
                            [MoknostKW],
                            [ZafatninaCM3],
                            [NosivostKG],
                            [RegistriraniMesta],
                            [OsnovnaPremijaAo],
                            [Bonus],
                            [Doplatok],
                            [Popust],
                            [VkupnoOsnovnaPremijaAO],
                            [Stakla],
                            [Patnici],
                            [DopolnitelnoOsiguruvanje],
                            [VkupnaPremija],
                            [DatumNaIzdavanje],
                            [VidVozilo],
                            [Prodavac],
                            [MestoIzdavanje],
                            [TipNaFaktura],
                            [KlientiIdOsiguritel],
                            [SifrarnikNacinNaPlakanjeId]
                        ) VALUES (
                            @UsernameCreated,
                            @BrojNaPolisa,
                            @Dogovoruvac,
                            @MBDogovoruvac,
                            @OpstinaDogovoruvac,
                            @AdresaDogovoruvac,
                            @Osigurenik,
                            @MBOsigurenik,
                            @MestoOsigurenik,
                            @AdresaOsigurenik,
                            @Registracija,
                            @PolisaVaziOd,
                            @PolisaVaziDo,
                            @Marka,
                            @Model,
                            @Shasija,
                            @GodinaProizvodstvo,
                            @MoknostKW,
                            @ZafatninaCM3,
                            @NosivostKG,
                            @RegistriraniMesta,
                            @OsnovnaPremijaAo,
                            @Bonus,
                            @Doplatok,
                            @Popust,
                            @VkupnoOsnovnaPremijaAO,
                            @Stakla,
                            @Patnici,
                            @DopolnitelnoOsiguruvanje,
                            @VkupnaPremija,
                            @DatumNaIzdavanje,
                            @VidVozilo,
                            @Prodavac,
                            @MestoIzdavanje,
                            @TipNaFaktura,
                            @KlientiIdOsiguritel,
                            @SifrarnikNacinNaPlakanjeId
                        )";

                    using (SqlCommand cmd = new SqlCommand(insertSql, connection))
                    {
                        cmd.Parameters.AddWithValue("@UsernameCreated", HttpContext.Session.GetString("Username") ?? "System");
                        cmd.Parameters.AddWithValue("@BrojNaPolisa", (object)BrojNaPolisa ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Dogovoruvac", (object)Dogovoruvac ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@MBDogovoruvac", (object)DogovoruvacEMBG ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@OpstinaDogovoruvac", (object)DogovoruvacMestoId ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@AdresaDogovoruvac", (object)DogovoruvacAdresa ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Osigurenik", (object)Osigurenik ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@MBOsigurenik", (object)OsigurenikEMBG ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@MestoOsigurenik", (object)OsigurenikMestoId ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@AdresaOsigurenik", (object)AdresaOsigurenik ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Registracija", (object)Registracija ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@PolisaVaziOd", (object)PolisaVaziOd ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@PolisaVaziDo", (object)PolisaVaziDo ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Marka", (object)MarkaVozilo ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Model", (object)ModelVozilo ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Shasija", (object)Shasija ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@GodinaProizvodstvo", (object)GodinaProizvodstvo ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@MoknostKW", (object)MoknostVoKW ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ZafatninaCM3", (object)ZafatninaVoCm3 ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@NosivostKG", (object)NosivostVoKG ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@RegistriraniMesta", (object)RegMesta ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@OsnovnaPremijaAo", (object)OsnovnaPremijaAO ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Bonus", (object)Bonus ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Doplatok", (object)Doplatok ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Popust", (object)Popust ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@VkupnoOsnovnaPremijaAO", (object)VkupnoOsnovnaPremijaAO ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Stakla", (object)KrsenjeStaklo ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Patnici", (object)OsiguruvanjePatnici ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@DopolnitelnoOsiguruvanje", (object)DopolnitelnoOsiguruvanje ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@VkupnaPremija", (object)VkupnaPremija ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumNaIzdavanje", (object)DatumNaIzdavanje ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@VidVozilo", (object)VidVoziloId ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Prodavac", (object)Prodavac ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@MestoIzdavanje", (object)MestoIzdavanjeId ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@TipNaFaktura", (object)TipNaFaktura ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@KlientiIdOsiguritel", (object)KlientiIdOsiguritel ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@SifrarnikNacinNaPlakanjeId", (object)SifrarnikNacinNaPlakanjeId ?? DBNull.Value);
                        await cmd.ExecuteNonQueryAsync();
                    }
                }

                SuccessMessage = "Податоците се успешно внесени во базата на податоци.";
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Грешка при внесување на податоците: {ex.Message}";
            }
            
            // Reload dropdown data in case of errors
            await LoadOpstiniDropdown();
            await LoadVidVoziloDropdown();
            await LoadKlientiDropdown();
            await LoadNacinNaPlakanjeDropdown();
            
            return Page();
        }
    }
}
