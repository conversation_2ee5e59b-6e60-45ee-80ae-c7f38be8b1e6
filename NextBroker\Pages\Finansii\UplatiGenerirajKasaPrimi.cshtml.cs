using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using Microsoft.Data.SqlClient;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Mvc.ViewEngines;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using System.IO;
using System.Threading.Tasks;
using iText.Html2pdf;
using iText.Kernel.Pdf;
using iText.Layout;
using iText.Layout.Element;
using iText.Layout.Properties;
using NextBroker.Helpers;

namespace NextBroker.Pages.Finansii
{
    public class UplatiGenerirajKasaPrimiModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        private readonly ICompositeViewEngine _viewEngine;
        private readonly ITempDataProvider _tempDataProvider;
        public bool HasAdminAccess { get; private set; }

        [BindProperty(SupportsGet = true)]
        public long Id { get; set; }

        public UplataView Uplata { get; set; }
        public string Izdal { get; set; }
        public string Mesto { get; set; }


        public class UplataView
        {
            public long? KasaPrimi { get; set; }
            public decimal Iznos { get; set; }
            public string? PolisaBroj { get; set; }
            public string UplakjacInfo { get; set; }
            public string CelNaUplata { get; set; }
        }

        public UplatiGenerirajKasaPrimiModel(
            IConfiguration configuration,
            ICompositeViewEngine viewEngine,
            ITempDataProvider tempDataProvider)
            : base(configuration)
        {
            _configuration = configuration;
            _viewEngine = viewEngine;
            _tempDataProvider = tempDataProvider;
        }

        public async Task<IActionResult> OnGetAsync()
        {
            if (!await HasPageAccess("Uplati"))
            {
                return RedirectToAccessDenied();
            }

            if (Id <= 0)
            {
                return RedirectToPage("/Finansii/Uplati");
            }

            HasAdminAccess = await HasPageAccess("UplatiAdmin");

            var username = HttpContext.Session.GetString("Username");
            if (!string.IsNullOrEmpty(username))
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    // Get Izdal info
                    using (SqlCommand cmd = new SqlCommand("SELECT dbo.VratiImePrezimePoUsername(@Username)", connection))
                    {
                        cmd.Parameters.AddWithValue("@Username", username);
                        var result = await cmd.ExecuteScalarAsync();
                        Izdal = result?.ToString() ?? string.Empty;
                    }

                    // Get Mesto info
                    using (SqlCommand cmd = new SqlCommand("SELECT dbo.VratiImeEkspozituraPoUsername(@Username)", connection))
                    {
                        cmd.Parameters.AddWithValue("@Username", username);
                        var result = await cmd.ExecuteScalarAsync();
                        Mesto = result?.ToString() ?? string.Empty;
                    }
                }
            }


            string connectionString3 = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString3))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT 
                        u.KasaPrimi,
                        u.Iznos,
                        ISNULL(u.PolisaBroj, '') as PolisaBroj,
                        CASE 
                            WHEN k.KlientFizickoPravnoLice = 'P' THEN k.Naziv
                            ELSE CONCAT(k.Id, ' ', ISNULL(k.Ime, ''), ' ', ISNULL(k.Prezime, ''))
                        END as UplakjacInfo
                    FROM Uplati u
                    LEFT JOIN Klienti k ON u.KlientIdUplakjac = k.Id
                    WHERE u.Id = @Id", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", Id);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    if (await reader.ReadAsync())
                    {
                        Uplata = new UplataView
                        {
                            KasaPrimi = reader["KasaPrimi"] != DBNull.Value ? (long?)reader.GetInt64(reader.GetOrdinal("KasaPrimi")) : null,
                            Iznos = reader.GetDecimal(reader.GetOrdinal("Iznos")),
                            PolisaBroj = reader["PolisaBroj"] != DBNull.Value ? (string?)reader.GetString(reader.GetOrdinal("PolisaBroj")) : null,
                            UplakjacInfo = reader["UplakjacInfo"].ToString()
                        };
                    }
                    else
                    {
                        return RedirectToPage("/Finansii/Uplati");
                    }
                }
            }

            return Page();
        }

        public async Task<IActionResult> OnGetGeneratePdfAsync(long id, string soBukvi, string celNaUplata, string opstina)
        {
            if (!await HasPageAccess("Uplati"))
            {
                return RedirectToAccessDenied();
            }

            if (id <= 0)
            {
                return RedirectToPage("/Finansii/Uplati");
            }

            // Load the data first
            await OnGetAsync();

            // Sanitize the input for HTML
            celNaUplata = System.Web.HttpUtility.HtmlEncode(celNaUplata);
            soBukvi = System.Web.HttpUtility.HtmlEncode(soBukvi);
            opstina = System.Web.HttpUtility.HtmlEncode(opstina);

            // Create HTML template
            var html = $@"
                <html>
                <head>
                    <style>
                        body {{ 
                            font-family: Arial, sans-serif;
                            margin: 0;
                            font-size: 11px;
                            height: 29.7cm;  // Full A4 height
                            width: 21cm;
                        }}
                        .container {{ 
                            width: 15cm; 
                            margin: 0 auto; 
                            padding: 0.5cm 1cm;
                            border: 2px solid #004080;
                            height: 11cm;  // Reduced height
                            display: flex;
                            flex-direction: column;
                            justify-content: flex-start;  // Align content to top
                        }}
                        .container:first-child {{
                            margin-bottom: 0.5cm;  // Space between copies
                        }}
                        .header {{ 
                            text-align: center; 
                            margin-bottom: 0.3cm;
                        }}
                        .header h4 {{
                            margin: 0 0 0.2cm 0;
                            font-size: 16px;
                            font-weight: bold;
                        }}
                        .header p {{
                            margin: 0 0 0.1cm 0;
                        }}
                        .header h5 {{
                            margin: 0;
                        }}
                        .content {{
                            flex: 1;
                            display: flex;
                            flex-direction: column;
                            justify-content: flex-start;  // Align content to top
                            gap: 0.2cm;
                        }}
                        .field {{ 
                            margin-bottom: 0.2cm;
                            line-height: 1.2;
                        }}
                        .underline {{ 
                            border-bottom: 1px solid #000; 
                            padding: 0 5px;
                            display: inline-block;
                            min-width: 60%;
                        }}
                        .signatures {{ 
                            margin-top: 0.5cm;  // Fixed margin instead of auto
                            padding-top: 0.3cm;
                        }}
                        .signature-line {{ 
                            border-top: 1px solid #000; 
                            margin-top: 0.3cm;
                            width: 90%;
                            margin-left: auto;
                            margin-right: auto;
                        }}
                        table {{
                            width: 100%;
                            border-spacing: 0.3cm 0;
                        }}
                        td {{
                            width: 33%; 
                            text-align: center;
                            font-size: 11px;
                            vertical-align: top;
                        }}
                        td p {{
                            margin: 0;
                        }}
                        .uplatio {{
                            text-align: right;
                            margin-top: 0.2cm;
                            margin-right: 0.5cm;
                        }}
                        .uplatio p {{
                            margin: 0 0 0.1cm 0;
                            margin-right: 2cm;
                        }}
                        .uplatio .signature-line {{
                            width: 200px;
                            margin-right: 0;
                            margin-top: 0.2cm;
                        }}
                    </style>
                </head>
                <body>
                    <div class='container'>
                        <div class='header'>
                            <h4>Осигурително Брокерско Друштво ИНКО АД Скопје</h4>
                            <p>Касата да</p>
                            <h5>ПРИМИ бр. {Uplata.KasaPrimi?.ToString() ?? "______"}</h5>
                        </div>

                        <div class='content'>
                            <div>
                                <div class='field'>
                                    <span>Од: </span>
                                    <span class='underline'>{Uplata.UplakjacInfo}</span>
                                </div>

                                <div class='field'>
                                    <span>сумата од: </span>
                                    <span class='underline'>{Uplata.Iznos.ToString("N2")} денари</span>
                                </div>

                                <div class='field'>
                                    <span>со букви: </span>
                                    <span class='underline'>{soBukvi}</span>
                                </div>

                                <div class='field'>
                                    <span>Цел на уплата: </span>
                                    <span class='underline'>{celNaUplata}</span>
                                </div>

                                <div class='field'>
                                    <span>Издал: </span>
                                    <span class='underline'>{Izdal}</span>
                                </div>

                                <div class='field'>
                                    <span>Место: </span>
                                    <span class='underline'>{Mesto}</span>
                                </div>

                                <div class='field'>
                                    <span>{opstina}, {DateTime.Now.ToString("dd.MM.yyyy")}</span>
                                </div>
                            </div>

                            <div class='signatures'>
                                <table>
                                    <tr>
                                        <td>
                                            <p>Генерален директор</p>
                                            <div class='signature-line'></div>
                                        </td>
                                        <td>
                                            <p>Директор на сектор</p>
                                            <div class='signature-line'></div>
                                        </td>
                                        <td>
                                            <p>Благајник</p>
                                            <div class='signature-line'></div>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <div class='uplatio'>
                                <p>Уплатил</p>
                                <div class='signature-line'></div>
                            </div>
                        </div>
                    </div>
                    <div class='container'>
                        <div class='header'>
                            <h4>Осигурително Брокерско Друштво ИНКО АД Скопје</h4>
                            <p>Касата да</p>
                            <h5>ПРИМИ бр. {Uplata.KasaPrimi?.ToString() ?? "______"}</h5>
                        </div>

                        <div class='content'>
                            <div>
                                <div class='field'>
                                    <span>Од: </span>
                                    <span class='underline'>{Uplata.UplakjacInfo}</span>
                                </div>

                                <div class='field'>
                                    <span>сумата од: </span>
                                    <span class='underline'>{Uplata.Iznos.ToString("N2")} денари</span>
                                </div>

                                <div class='field'>
                                    <span>со букви: </span>
                                    <span class='underline'>{soBukvi}</span>
                                </div>

                                <div class='field'>
                                    <span>Цел на уплата: </span>
                                    <span class='underline'>{celNaUplata}</span>
                                </div>

                                <div class='field'>
                                    <span>Издал: </span>
                                    <span class='underline'>{Izdal}</span>
                                </div>

                                <div class='field'>
                                    <span>Место: </span>
                                    <span class='underline'>{Mesto}</span>
                                </div>

                                <div class='field'>
                                    <span>{opstina}, {DateTime.Now.ToString("dd.MM.yyyy")}</span>
                                </div>
                            </div>

                            <div class='signatures'>
                                <table>
                                    <tr>
                                        <td>
                                            <p>Генерален директор</p>
                                            <div class='signature-line'></div>
                                        </td>
                                        <td>
                                            <p>Директор на сектор</p>
                                            <div class='signature-line'></div>
                                        </td>
                                        <td>
                                            <p>Благајник</p>
                                            <div class='signature-line'></div>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <div class='uplatio'>
                                <p>Уплатил</p>
                                <div class='signature-line'></div>
                            </div>
                        </div>
                    </div>
                </body>
                </html>";

            // Convert HTML to PDF
            using (var memoryStream = new MemoryStream())
            {
                var converterProperties = new ConverterProperties();
                
                // Convert HTML to PDF using the document
                HtmlConverter.ConvertToPdf(html, memoryStream, converterProperties);
                return File(memoryStream.ToArray(), "application/pdf", $"KasaPrimi_{id}.pdf");
            }
        }
    }
} 