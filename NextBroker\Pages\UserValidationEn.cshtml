@page
@model RazorPortal.Pages.UserValidationEnModel
@{
    ViewData["Title"] = "User Validation";
}

<link rel="stylesheet" href="~/css/login.css" asp-append-version="true" />

<div class="login-container">
    <h2>@ViewData["Title"]</h2>

    <form method="post">
        <div id="validationForm" @(Model.IsConfirmed ? "style='display:none;'" : "")>
            <div class="form-group">
                <label for="validationCode">Enter validation code</label>
                <input type="text" 
                       id="validationCode" 
                       name="validationCode" 
                       class="form-control" 
                       required 
                       placeholder="Enter the code from your email" />
                
                @if (!string.IsNullOrEmpty(Model.Message))
                {
                    <span class="text-danger">@Model.Message</span>
                }
            </div>

            <button type="submit" class="btn btn-primary">Validate</button>
        </div>

        @if (Model.IsConfirmed)
        {
            <div class="alert alert-success text-center">
                <p style="color: #4CAF50; font-size: 24px; font-weight: bold; margin: 20px 0;">
                    ✅ User Validated Successfully!
                </p>
                <p>Redirecting in <span id="countdown" style="font-weight: bold; color: #4CAF50;">5</span> seconds...</p>
            </div>

            <script>
                let countdown = 5;
                const countdownElement = document.getElementById("countdown");

                const timer = setInterval(() => {
                    countdown--;
                    countdownElement.textContent = countdown;

                    if (countdown <= 0) {
                        clearInterval(timer);
                        window.location.href = '@Url.Page("/LoginEn")';
                    }
                }, 1000);
            </script>
        }
    </form>
</div> 