using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Data.SqlClient;

namespace NextBroker.Pages
{
    public class UserPortalHomeEnModel : PageModel
    {
        private readonly IConfiguration _configuration;

        public UserPortalHomeEnModel(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public string FirstName { get; set; }
        public string LastName { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            var username = HttpContext.Session.GetString("Username");

            if (string.IsNullOrEmpty(username))
            {
                return RedirectToPage("/Login");
            }

            await LoadUserDetailsAsync(username);
            return Page();
        }

        private async Task LoadUserDetailsAsync(string username)
        {
            var connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                var query = "SELECT FirstName, LastName FROM Users WHERE Username = @Username";

                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Username", username);
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            FirstName = reader.IsDBNull(0) ? string.Empty : reader.GetString(0);
                            LastName = reader.IsDBNull(1) ? string.Empty : reader.GetString(1);
                        }
                    }
                }
            }
        }
    }
} 