@page
@model NextBroker.Pages.Finansii.IzvodiModel
@{
    ViewData["Title"] = "Изводи";
    var formClass = !Model.HasAdminAccess ? "form-disabled" : "";
    var antiforgery = Html.AntiForgeryToken();
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container-fluid mt-4 px-4">
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Филтри</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3 align-items-end" id="filterForm" name="filterForm">
                <div class="col-md-4">
                    <label class="form-label">Датум од</label>
                    <input type="date" class="form-control" name="DatumOd" value="@(Model.DatumOd?.ToString("yyyy-MM-dd"))" />
                </div>
                <div class="col-md-4">
                    <label class="form-label">Датум до</label>
                    <input type="date" class="form-control" name="DatumDo" value="@(Model.DatumDo?.ToString("yyyy-MM-dd"))" />
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i> Филтрирај
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearDates()">
                        <i class="fas fa-times me-1"></i> Исчисти
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container-fluid mt-4 px-4">
    <!-- Success Message -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div id="successMessage" class="alert alert-success alert-dismissible fade show" role="alert">
            <strong>Успешно!</strong> @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (Model.HasAdminAccess)
    {
        <div class="card mb-4">
            <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#addNewIzvod" aria-expanded="false" aria-controls="addNewIzvod">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Додај нов извод</h5>
                    <i class="bi bi-chevron-down"></i>
                </div>
            </div>
            <div class="collapse" id="addNewIzvod">
                <div class="card-body">
                    <!-- Error Message -->
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div id="errorMessage" class="alert alert-danger alert-dismissible fade show mb-3" role="alert">
                            <strong>Грешка!</strong> @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }
                    <form method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label asp-for="Input.SifrarnikBankiId" class="form-label">Банка</label>
                                <select asp-for="Input.SifrarnikBankiId" 
                                        asp-items="Model.Banki" 
                                        class="form-select"
                                        id="bankDropdown"
                                        name="Input.SifrarnikBankiId">
                                    <option value="">-- Избери банка --</option>
                                </select>
                                <span asp-validation-for="Input.SifrarnikBankiId" class="text-danger"></span>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label asp-for="Input.BrojNaSmetka" class="form-label">Број на сметка</label>
                                <select asp-for="Input.BrojNaSmetka" 
                                        class="form-select"
                                        id="accountDropdown"
                                        name="Input.BrojNaSmetka">
                                    <option value="">-- Избери сметка --</option>
                                </select>
                                <span asp-validation-for="Input.BrojNaSmetka" class="text-danger"></span>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label asp-for="Input.BrojNaIzvod" class="form-label"></label>
                                <input asp-for="Input.BrojNaIzvod" class="form-control" />
                                <span asp-validation-for="Input.BrojNaIzvod" class="text-danger"></span>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label asp-for="Input.DatumNaIzvod" class="form-label"></label>
                                <input asp-for="Input.DatumNaIzvod" 
                                       class="form-control" 
                                       type="date" 
                                       value="@DateTime.Today.ToString("yyyy-MM-dd")" />
                                <span asp-validation-for="Input.DatumNaIzvod" class="text-danger"></span>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label asp-for="Input.Priliv" class="form-label">Износ прилив</label>
                                <input asp-for="Input.Priliv" class="form-control" type="number" step="0.0001" />
                                <span asp-validation-for="Input.Priliv" class="text-danger"></span>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label asp-for="Input.Odliv" class="form-label">Износ одлив</label>
                                <input asp-for="Input.Odliv" class="form-control" type="number" step="0.0001" />
                                <span asp-validation-for="Input.Odliv" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i> Додај извод
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    }

    @if (!Model.HasAdminAccess)
    {
        <style>
            .form-disabled input:not([type="hidden"]),
            .form-disabled select,
            .form-disabled textarea {
                pointer-events: none;
                background-color: #e9ecef;
                opacity: 1;
            }
           
            .btn-light-primary {
                background-color: rgba(13, 110, 253, 0.2) !important;
                border-color: rgba(13, 110, 253, 0.2) !important;
                color: #0d6efd !important;
            }
           
            .btn-light-primary:hover {
                background-color: rgba(13, 110, 253, 0.4) !important;
                border-color: rgba(13, 110, 253, 0.4) !important;
                color: #0d6efd !important;
            }
           
            .actions-column {
                width: 100px !important;
                min-width: 100px !important;
                white-space: nowrap;
            }
           
            .actions-column .btn {
                padding: 0.25rem 0.5rem;
                margin-right: 0.25rem;
            }
           
            .actions-column .btn:last-child {
                margin-right: 0;
            }
        </style>
    }

    @if (!string.IsNullOrEmpty(TempData["DebugInfo"]?.ToString()))
    {
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Debug Information</h5>
            </div>
            <div class="card-body">
                <pre class="mb-0" style="white-space: pre-wrap;">@TempData["DebugInfo"]</pre>
            </div>
        </div>
    }

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table id="izvodiTable" class="table table-striped">
                    <thead>
                        <tr>
                            <th>ИД</th>
                            <th>Датум на креирање</th>
                            <th>Креирано од</th>
                            <th>Последна промена</th>
                            <th>Променето од</th>
                            <th>Банка</th>
                            <th>Број на извод</th>
                            <th>Број на сметка</th>
                            <th>Датум на извод</th>
                            <th>Прилив</th>
                            <th>Раскнижен прилив</th>
                            <th>Одлив</th>
                            <th>Раскнижен одлив</th>
                            <th>Акции</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var izvod in Model.Izvodi)
                        {
                            <tr>
                                <td>@izvod.Id</td>
                                <td>@(izvod.DateCreated?.ToString("dd.MM.yyyy HH:mm:ss"))</td>
                                <td>@izvod.UsernameCreated</td>
                                <td>@(izvod.DateModified?.ToString("dd.MM.yyyy HH:mm:ss"))</td>
                                <td>@izvod.UsernameModified</td>
                                <td>@izvod.BankaNaziv</td>
                                <td>@izvod.BrojNaIzvod</td>
                                <td>@izvod.BrojNaSmetka</td>
                                <td>@izvod.DatumNaIzvod.ToString("dd.MM.yyyy")</td>
                                <td class="text-end">@izvod.Priliv.ToString("N2")</td>
                                <td class="text-center">
                                    @if (izvod.Rasknizen == true)
                                    {
                                        <i class="fas fa-check text-success"></i>
                                    }
                                </td>
                                <td class="text-end">@izvod.Odliv.ToString("N2")</td>
                                <td class="text-center">
                                    @if (izvod.RasknizenOdliv == true)
                                    {
                                        <i class="fas fa-check text-success"></i>
                                    }
                                </td>
                                <td class="actions-column">
                                    <a href="/Finansii/ViewEditIzvod/@izvod.Id" class="btn btn-sm btn-light-primary">
                                        <i class="fas fa-pencil-alt"></i>
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        $(document).ready(function () {
            $('#izvodiTable').DataTable({
                "order": [[7, "desc"]], // Sort by DatumNaIzvod by default
                "language": {
                    "sEmptyTable":     "Нема податоци во табелата",
                    "sInfo":           "Прикажани _START_ до _END_ од _TOTAL_ записи",
                    "sInfoEmpty":      "Прикажани 0 до 0 од 0 записи",
                    "sInfoFiltered":   "(филтрирано од вкупно _MAX_ записи)",
                    "sInfoPostFix":    "",
                    "sInfoThousands":  ",",
                    "sLengthMenu":     "Прикажи _MENU_ записи",
                    "sLoadingRecords": "Вчитување...",
                    "sProcessing":     "Обработка...",
                    "sSearch":         "Пребарај:",
                    "sZeroRecords":    "Не се пронајдени записи",
                    "oPaginate": {
                        "sFirst":    "Прва",
                        "sLast":     "Последна",
                        "sNext":     "Следна",
                        "sPrevious": "Претходна"
                    },
                    "oAria": {
                        "sSortAscending":  ": активирај за растечко сортирање на колона",
                        "sSortDescending": ": активирај за опаѓачко сортирање на колона"
                    }
                }
            });

            // Add event handler for bank dropdown change
            $('#bankDropdown').change(function() {
                var selectedBankId = $(this).val();
                var accountDropdown = $('#accountDropdown');
                
                // Clear and disable account dropdown if no bank selected
                if (!selectedBankId) {
                    accountDropdown.empty().append('<option value="">-- Избери сметка --</option>').prop('disabled', true);
                    return;
                }

                // Enable account dropdown and load data
                accountDropdown.prop('disabled', true); // Temporarily disable while loading
                $.getJSON(`?handler=BankAccounts&bankId=${selectedBankId}`, function(data) {
                    accountDropdown.empty().append('<option value="">-- Избери сметка --</option>');
                    if (data && data.length > 0) {
                        data.forEach(function(item) {
                            accountDropdown.append($('<option></option>')
                                .val(item.value)
                                .text(item.text));
                        });
                        accountDropdown.prop('disabled', false);
                    } else {
                        accountDropdown.append('<option value="">-- Нема достапни сметки --</option>');
                        accountDropdown.prop('disabled', true);
                    }
                }).fail(function(jqXHR, textStatus, errorThrown) {
                    console.error('Error fetching bank accounts:', textStatus, errorThrown);
                    accountDropdown.empty().append('<option value="">-- Грешка при вчитување --</option>');
                    accountDropdown.prop('disabled', true);
                });
            });
            

            // Initialize account dropdown as disabled
            $('#accountDropdown').prop('disabled', true);

            // Add validation for the account dropdown
            $('form').on('submit', function(e) {
                var bankId = $('#bankDropdown').val();
                var accountNumber = $('#accountDropdown').val();
                
                if (bankId && !accountNumber) {
                    e.preventDefault();
                    alert('Ве молиме изберете број на сметка');
                    return false;
                }
            });

            // Form submission handling
            $('form').on('submit', async function (e) {
                e.preventDefault();
                
                // Check required fields
                let hasErrors = false;
                const errorMessages = [];
                
                // Check Banka
                const selectedBankId = $('#bankDropdown').val();
                if (!selectedBankId) {
                    $('#bankDropdown').addClass('is-invalid');
                    errorMessages.push('Изберете банка');
                    hasErrors = true;
                } else {
                    $('#bankDropdown').removeClass('is-invalid');
                }
                
                // Check Broj na izvod
                const brojNaIzvod = $('#Input_BrojNaIzvod').val();
                if (!brojNaIzvod) {
                    $('#Input_BrojNaIzvod').addClass('is-invalid');
                    errorMessages.push('Внесете број на извод');
                    hasErrors = true;
                } else {
                    $('#Input_BrojNaIzvod').removeClass('is-invalid');
                }
                
                // Check Datum na izvod
                if (!$('#Input_DatumNaIzvod').val()) {
                    $('#Input_DatumNaIzvod').addClass('is-invalid');
                    errorMessages.push('Внесете датум на извод');
                    hasErrors = true;
                } else {
                    $('#Input_DatumNaIzvod').removeClass('is-invalid');
                }
                
                // Remove Priliv validation since we now allow 0
                $('#Input_Priliv').removeClass('is-invalid');

                // Check BrojNaSmetka if bank is selected
                if (selectedBankId && !$('#accountDropdown').val()) {
                    $('#accountDropdown').addClass('is-invalid');
                    errorMessages.push('Изберете број на сметка');
                    hasErrors = true;
                } else {
                    $('#accountDropdown').removeClass('is-invalid');
                }
                
                // If there are validation errors, show them and return
                if (hasErrors) {
                    // Show error message
                    $('#errorMessage').remove(); // Remove any existing error message
                    const errorHtml = `
                        <div id="errorMessage" class="alert alert-danger alert-dismissible fade show mb-3" role="alert">
                            <strong>Грешка!</strong> Ве молиме пополнете ги сите задолжителни полиња.
                            <ul class="mb-0 mt-2">
                                ${errorMessages.map(msg => `<li>${msg}</li>`).join('')}
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>`;
                    $(this).prepend(errorHtml);
                    return;
                }
                
                try {
                    const response = await fetch(`?handler=CheckBrojNaIzvod&brojNaIzvod=${encodeURIComponent(brojNaIzvod)}`);
                    const data = await response.json();
                    
                    if (data.exists) {
                        // Show error message
                        $('#errorMessage').remove(); // Remove any existing error message
                        const errorHtml = `
                            <div id="errorMessage" class="alert alert-danger alert-dismissible fade show mb-3" role="alert">
                                <strong>Грешка!</strong> Извод со овој број веќе постои.
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>`;
                        $(this).prepend(errorHtml);
                        
                        // Add validation error to the field
                        $('#Input_BrojNaIzvod').addClass('is-invalid');
                        const errorSpan = $('#Input_BrojNaIzvod').siblings('.text-danger');
                        errorSpan.text('Извод со овој број веќе постои.');
                    } else {
                        // If no duplicate, submit the form
                        this.submit();
                    }
                } catch (error) {
                    console.error('Error checking BrojNaIzvod:', error);
                    this.submit(); // Submit anyway if check fails
                }
            });

            // Clear validation errors when any input changes
            $('input, select').on('input change', function() {
                $(this).removeClass('is-invalid');
                $(this).siblings('.text-danger').text('');
                if ($('.is-invalid').length === 0) {
                    $('#errorMessage').remove();
                }
            });

            // Auto-hide messages after 3 seconds
            setTimeout(function () {
                $("#successMessage, #errorMessage").fadeOut('slow');
            }, 3000);

            // Initialize form collapse with icon rotation
            document.querySelector('[data-bs-toggle="collapse"]').addEventListener('click', function() {
                const icon = this.querySelector('.bi');
                icon.style.transform = icon.style.transform === 'rotate(180deg)' ? '' : 'rotate(180deg)';
            });

            // Set today's date as default for DatumNaIzvod
            if (!$('#Input_DatumNaIzvod').val()) {
                $('#Input_DatumNaIzvod').val(new Date().toISOString().split('T')[0]);
            }

            // Add this after your existing document.ready
            $.ajaxSetup({
                beforeSend: function (xhr) {
                    xhr.setRequestHeader("RequestVerificationToken", 
                        $('input:hidden[name="__RequestVerificationToken"]').val());
                }
            });

            // Prevent the filter form from triggering validation on the add form
            $('#filterForm').on('submit', function(e) {
                e.preventDefault();
                this.submit();
            });
        });

        function clearDates() {
            document.querySelector('input[name="DatumOd"]').value = '';
            document.querySelector('input[name="DatumDo"]').value = '';
            document.getElementById('filterForm').submit();
        }
    </script>
} 