@page "{id:long}"
@model NextBroker.Pages.Klienti.EditFizickoLiceModel
@{
    ViewData["Title"] = "Измени физичко лице";
}

<div class="container mt-4">
    <h1 class="text-center">@ViewData["Title"]</h1>

    @if (!string.IsNullOrEmpty(TempData["SuccessMessage"]?.ToString()))
    {
        <div class="alert alert-success" id="successMessage">
            @TempData["SuccessMessage"]
        </div>
    }

    @if (!ModelState.IsValid)
    {
        <div class="alert alert-danger">
            <ul>
                @foreach (var error in ModelState.Values.SelectMany(v => v.Errors))
                {
                    <li>@error.ErrorMessage</li>
                }
            </ul>
        </div>
    }

    <form method="post">
        <input type="hidden" name="id" value="@Model.Id" />

        <!-- Basic Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Основни информации</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Име</label>
                        <input asp-for="Input.Ime" class="form-control" />
                        <span class="text-danger" id="ime-error"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Презиме</label>
                        <input asp-for="Input.Prezime" class="form-control" />
                        <span class="text-danger" id="prezime-error"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">ЕМБГ</label>
                        <input asp-for="Input.EMBG" class="form-control" maxlength="13" readonly />
                        <span class="text-danger" id="embg-error"></span>           
                        <span asp-validation-for="Input.EMBG" class="text-danger"></span>             
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="form-check">
                            <input type="radio" name="fizickoLiceType" class="form-check-input" id="Input_KlientVraboten" onchange="handleFizickoLiceTypeChange(this)" @(Model.Input.KlientVraboten ? "checked" : "") />
                            <label class="form-check-label" for="Input_KlientVraboten">
                                Вработен
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-check">
                            <input type="radio" name="fizickoLiceType" class="form-check-input" id="Input_KlientSorabotnik" onchange="handleFizickoLiceTypeChange(this)" @(Model.Input.KlientSorabotnik ? "checked" : "") />
                            <label class="form-check-label" for="Input_KlientSorabotnik">
                                Соработник
                            </label>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFizickoLiceSelection()">Исчисти избор</button>
                    </div>
                </div>
                <!-- Hidden inputs to store actual values -->
                <input type="hidden" asp-for="Input.KlientVraboten" id="Hidden_KlientVraboten" />
                <input type="hidden" asp-for="Input.KlientSorabotnik" id="Hidden_KlientSorabotnik" />
            </div>
        </div>

        <!-- Address Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Адресни податоци</h5>
            </div>
            <div class="card-body">
                <h6 class="mb-3">Адреса од документ за идентификација</h6>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Општина</label>
                        <select asp-for="Input.ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija" 
                                asp-items="Model.Opstini" 
                                class="form-select">
                            <option value="">-- Избери општина --</option>
                        </select>
                        <span class="text-danger" id="opstina-id-error"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Улица</label>
                        <input asp-for="Input.UlicaOdDokumentZaIdentifikacija" class="form-control" />
                        <span class="text-danger" id="ulica-id-error"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Број</label>
                        <input asp-for="Input.BrojOdDokumentZaIdentifikacija" class="form-control" />
                        <span class="text-danger" id="broj-id-error"></span>
                    </div>
                </div>

                <h6 class="mb-3 mt-4">Адреса за комуникација</h6>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Општина</label>
                        <select asp-for="Input.ListaOpstiniIdOpstinaZaKomunikacija" 
                                asp-items="Model.Opstini" 
                                class="form-select">
                            <option value="">-- Избери општина --</option>
                        </select>
                        <span class="text-danger" id="opstina-kom-error"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Улица</label>
                        <input asp-for="Input.UlicaZaKomunikacija" class="form-control" />
                        <span class="text-danger" id="ulica-kom-error"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Број</label>
                        <input asp-for="Input.BrojZaKomunikacija" class="form-control" />
                        <span class="text-danger" id="broj-kom-error"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Забелешка</label>
                        <input asp-for="Input.Zabeleska" class="form-control" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Document Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Документ за идентификација</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Број на пасош/лична карта</label>
                        <input asp-for="Input.BrojPasosLicnaKarta" class="form-control" />
                        <span class="text-danger" id="dokument-broj-error"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Важи од</label>
                        <input asp-for="Input.DatumVaziOdPasosLicnaKarta" type="date" class="form-control" />
                        <span class="text-danger" id="vazi-od-error"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Важи до</label>
                        <input asp-for="Input.DatumVaziDoPasosLicnaKarta" type="date" class="form-control" />
                        <span class="text-danger" id="vazi-do-error"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Контакт информации</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Email</label>
                        <input asp-for="Input.Email" type="email" class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Телефон</label>
                        <input asp-for="Input.Tel" class="form-control" />
                        <span class="text-danger" id="telefon-error"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Веб страна</label>
                        <input asp-for="Input.Webstrana" class="form-control" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Consent Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Согласности</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="form-check">
                            <input asp-for="Input.SoglasnostZaDirektenMarketing" class="form-check-input" type="checkbox" />
                            <label class="form-check-label">Согласност за директен маркетинг</label>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-check">
                            <input asp-for="Input.SoglasnostZaEmailKomunikacija" class="form-check-input" type="checkbox" />
                            <label class="form-check-label">Согласност за email комуникација</label>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-check">
                            <input asp-for="Input.SoglasnostZaTelKomunikacija" class="form-check-input" type="checkbox" />
                            <label class="form-check-label">Согласност за телефонска комуникација</label>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Датум на повлечена согласност за директен маркетинг</label>
                        <input asp-for="Input.DatumNaPovlecenaSoglasnostZaDirektenMarketing" type="date" class="form-control" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Public Function Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Носител на јавна функција</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="form-check">
                            <input asp-for="Input.NositelNaJF" class="form-check-input" type="checkbox" />
                            <label class="form-check-label">Носител на јавна функција</label>
                        </div>
                    </div>
                    <div class="col-md-8 mb-3">
                        <label class="form-label">Основ за носител на јавна функција</label>
                        <input asp-for="Input.OsnovZaNositelNaJF" class="form-control" />
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="form-check">
                            <input asp-for="Input.ZasilenaAnaliza" class="form-check-input" type="checkbox" />
                            <label class="form-check-label">Засилена анализа</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Risk Level Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Ниво на ризик</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Ниво на ризик</label>
                        <select asp-for="Input.NivoaNaRizikIdNivoNaRizik" 
                                asp-items="Model.NivoaNaRizik" 
                                class="form-select">
                            <option value="">-- Избери ниво на ризик --</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contract Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Договор</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Датум на овластување</label>
                        <input asp-for="Input.DatumNaOvlastuvanje" type="date" class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Датум на договор</label>
                        <input asp-for="Input.DaumNaDogovor" type="date" class="form-control" />
                        <span class="text-danger" id="dogovor-od-error"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Договор важи до</label>
                        <input asp-for="Input.DogovorVaziDo" type="date" class="form-control" />
                        <span class="text-danger" id="dogovor-do-error"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Број на договор</label>
                        <input asp-for="Input.BrojNaDogovor" class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Договор определено/неопределено</label>
                        <select asp-for="Input.DogovorOpredelenoNeopredeleno" class="form-select">
                            <option value="">-- Избери тип --</option>
                            <option value="Определено">Определено</option>
                            <option value="Неопределено">Неопределено</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bank Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Банкарски информации</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Платежна сметка</label>
                        <input asp-for="Input.PlateznaSmetka" class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Банка</label>
                        <select asp-for="Input.SifrarnikBankiIdBanka" 
                                asp-items="Model.Banki" 
                                class="form-select">
                            <option value="">-- Избери банка --</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Рок на плаќање за фактури кон клиентот (денови)</label>
                        <input asp-for="Input.RokNaPlakanjeDenovi" type="number" class="form-control" step="1" min="0" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Work Position Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Работна позиција</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Работна позиција</label>
                        <select asp-for="Input.SifrarnikRabotniPoziciiIdRabotnaPozicija" 
                                asp-items="Model.RabotniPozicii" 
                                class="form-select">
                            <option value="">-- Избери работна позиција --</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Организациона единица</label>
                        <select asp-for="Input.SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica" 
                                asp-items="Model.OrganizacioniEdinici" 
                                class="form-select">
                            <option value="">-- Избери организациона единица --</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Експозитура</label>
                        <select asp-for="Input.EkspozituriIdEkspozitura" 
                                asp-items="Model.EkspozituriIdEkspozitura" 
                                class="form-select">
                            <option value="">-- Избери експозитура --</option>
                        </select>
                    </div>
                
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Надреден</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" data-target="nadreden" style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="nadredenSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..."
                                   value="@Model.Input.NadredenImePrezime" />
                            <input type="hidden" asp-for="Input.SifrarnikRabotniPoziciiIdNadreden" id="SifrarnikRabotniPoziciiIdNadreden" />
                        </div>
                        <div id="nadredenSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Надреден важи од</label>
                        <input asp-for="Input.NadredenVaziOd" type="date" class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Надреден до</label>
                        <input asp-for="Input.NadredenDo" type="date" class="form-control" />
                    </div>
                </div>
            </div>
        </div>

        <!-- License Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Лиценца</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="form-check">
                            <input asp-for="Input.ZadolzitelnoLicenca" class="form-check-input" type="checkbox" />
                            <label class="form-check-label">Задолжително лиценца</label>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Број на решение од АСО за лиценца</label>
                        <input asp-for="Input.BrojNaResenieOdASOZaLicenca" class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Датум на решение од АСО за лиценца</label>
                        <input asp-for="Input.DatumNaResenieOdASOZaLicenca" type="date" class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Датум на одземена лиценца</label>
                        <input asp-for="Input.DatumNaOdzemenaLicenca" type="date" class="form-control" />
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Број на дозвола за вршење осигурително брокерски работи</label>
                        <input asp-for="Input.BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti" class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Датум на дозвола за вршење осигурително брокерски работи</label>
                        <input asp-for="Input.DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti" type="date" class="form-control" />
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mb-4">
            <button type="submit" class="btn btn-primary">Зачувај измени</button>
            <a href="/Klienti/ListaKlienti" class="btn btn-secondary">Назад</a>
        </div>
    </form>
</div>

<!-- File Upload Section -->
<div class="container mt-4">
    @if (!string.IsNullOrEmpty(TempData["ErrorMessage"]?.ToString()))
    {
        <div class="alert alert-danger">
            @TempData["ErrorMessage"]
        </div>
    }

    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Прикачени документи</h5>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data" asp-page-handler="UploadFile">
                @Html.AntiForgeryToken()
                <input type="hidden" name="klientId" value="@Model.Id" />
                <div class="row mb-3">
                    <div class="col-md-8">
                        <input type="file" class="form-control" name="files" multiple required />
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary">Прикачи документи</button>
                    </div>
                </div>
            </form>

            @if (Model.Files != null && Model.Files.Any())
            {
                <div class="table-responsive mt-3">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Име на документ</th>
                                <th>Датум на прикачување</th>
                                <th>Прикачил</th>
                                <th>Акции</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var file in Model.Files)
                            {
                                <tr>
                                    <td>@file.FileName</td>
                                    <td>@file.DateCreated.ToString("dd.MM.yyyy HH:mm")</td>
                                    <td>@file.UsernameCreated</td>
                                    <td>
                                        <div class="btn-group">
                                            <a asp-page-handler="DownloadFile" asp-route-fileId="@file.Id" 
                                               class="btn btn-sm btn-primary">
                                                <i class="fas fa-download"></i> Преземи
                                            </a>
                                            <form method="post" asp-page-handler="DeleteFile" class="d-inline" 
                                                  onsubmit="return confirm('Дали сте сигурни дека сакате да го избришете овој документ?');">
                                                <input type="hidden" name="fileId" value="@file.Id" />
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i> Избриши
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Phone number validation
        function validatePhoneNumber(phoneNumber) {
            var phonePattern = /^\+?([0-9][\s-.]?){6,}[0-9]$/;
            return phonePattern.test(phoneNumber.replace(/[\s.-]/g, ''));
        }

        // EMBG validation
        function validateEMBG() {
            var embgField = $('#Input_EMBG');
            var embgError = $('#embg-error');
            var embgValue = embgField.val();
            
            if (!embgValue) {
                embgError.text('ЕМБГ е задолжително поле');
                return false;
            } else if (!/^\d{13}$/.test(embgValue)) {
                embgError.text('ЕМБГ мора да содржи точно 13 цифри');
                return false;
            } else {
                embgError.text('');
                return true;
            }
        }

        // Validate all required fields
        function validateFizickoLiceFields() {
            var isValid = true;

            // Validate EMBG
            if (!validateEMBG()) {
                isValid = false;
            }

            // Validate Ime
            var imeField = $('#Input_Ime');
            var imeError = $('#ime-error');
            if (!imeField.val().trim()) {
                imeError.text('Име е задолжително поле');
                isValid = false;
            } else {
                imeError.text('');
            }

            // Validate Prezime
            var prezimeField = $('#Input_Prezime');
            var prezimeError = $('#prezime-error');
            if (!prezimeField.val().trim()) {
                prezimeError.text('Презиме е задолжително поле');
                isValid = false;
            } else {
                prezimeError.text('');
            }

            // Validate Address from ID
            var opstinaIdField = $('#Input_ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija');
            var opstinaIdError = $('#opstina-id-error');
            if (!opstinaIdField.val()) {
                opstinaIdError.text('Општина е задолжително поле');
                isValid = false;
            } else {
                opstinaIdError.text('');
            }

            var ulicaIdField = $('#Input_UlicaOdDokumentZaIdentifikacija');
            var ulicaIdError = $('#ulica-id-error');
            if (!ulicaIdField.val().trim()) {
                ulicaIdError.text('Улица е задолжително поле');
                isValid = false;
            } else {
                ulicaIdError.text('');
            }

            var brojIdField = $('#Input_BrojOdDokumentZaIdentifikacija');
            var brojIdError = $('#broj-id-error');
            if (!brojIdField.val().trim()) {
                brojIdError.text('Број е задолжително поле');
                isValid = false;
            } else {
                brojIdError.text('');
            }

            // Validate Communication Address
            var opstinaKomField = $('#Input_ListaOpstiniIdOpstinaZaKomunikacija');
            var opstinaKomError = $('#opstina-kom-error');
            if (!opstinaKomField.val()) {
                opstinaKomError.text('Општина е задолжително поле');
                isValid = false;
            } else {
                opstinaKomError.text('');
            }

            var ulicaKomField = $('#Input_UlicaZaKomunikacija');
            var ulicaKomError = $('#ulica-kom-error');
            if (!ulicaKomField.val().trim()) {
                ulicaKomError.text('Улица е задолжително поле');
                isValid = false;
            } else {
                ulicaKomError.text('');
            }

            var brojKomField = $('#Input_BrojZaKomunikacija');
            var brojKomError = $('#broj-kom-error');
            if (!brojKomField.val().trim()) {
                brojKomError.text('Број е задолжително поле');
                isValid = false;
            } else {
                brojKomError.text('');
            }

            // Document Info fields are now optional - validation removed

            // Validate Phone
            var telField = $('#Input_Tel');
            var telError = $('#telefon-error');
            var phoneValue = telField.val().trim();
            
            if (!phoneValue) {
                telError.text('Телефон е задолжително поле');
                isValid = false;
            } else if (!validatePhoneNumber(phoneValue)) {
                telError.text('Внесете валиден телефонски број');
                isValid = false;
            } else {
                telError.text('');
            }
            
            return isValid;
        }

        // Add validation on EMBG input change
        $('#Input_EMBG').on('input', function() {
            validateEMBG();
        });

        // Add validation on input change for all fields
        $('#Input_Ime, #Input_Prezime, ' +
        '#Input_ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija, #Input_UlicaOdDokumentZaIdentifikacija, ' +
        '#Input_BrojOdDokumentZaIdentifikacija, #Input_ListaOpstiniIdOpstinaZaKomunikacija, ' +
        '#Input_UlicaZaKomunikacija, #Input_BrojZaKomunikacija, #Input_Tel').on('input change', function() {
            validateFizickoLiceFields();
        });

        // Add validation for document date fields
        function validateDocumentDates() {
            var vaziOdField = $('#Input_DatumVaziOdPasosLicnaKarta');
            var vaziDoField = $('#Input_DatumVaziDoPasosLicnaKarta');
            var vaziOdError = $('#vazi-od-error');
            var vaziDoError = $('#vazi-do-error');
            
            // Clear previous error messages
            vaziOdError.text('');
            vaziDoError.text('');
            
            var vaziOdDate = vaziOdField.val() ? new Date(vaziOdField.val()) : null;
            var vaziDoDate = vaziDoField.val() ? new Date(vaziDoField.val()) : null;
            
            // If vaziDo is set, vaziOd must be set
            if (vaziDoDate && !vaziOdDate) {
                vaziOdError.text('Мора да внесете датум "Важи од" пред да внесете "Важи до"');
                return false;
            }
            
            // If both dates are set and vaziDo is before vaziOd,
            // automatically set vaziDo to match vaziOd
            if (vaziOdDate && vaziDoDate && vaziDoDate < vaziOdDate) {
                vaziDoField.val(vaziOdField.val());
            }
            
            return true;
        }

        // Add validation for contract date fields
        function validateContractDates() {
            var dogovorOdField = $('#Input_DaumNaDogovor');
            var dogovorDoField = $('#Input_DogovorVaziDo');
            var dogovorOdError = $('#dogovor-od-error');
            var dogovorDoError = $('#dogovor-do-error');
            
            // Clear previous error messages
            dogovorOdError.text('');
            dogovorDoError.text('');
            
            var dogovorOdDate = dogovorOdField.val() ? new Date(dogovorOdField.val()) : null;
            var dogovorDoDate = dogovorDoField.val() ? new Date(dogovorDoField.val()) : null;
            
            // If DogovorVaziDo is set, DaumNaDogovor must be set
            if (dogovorDoDate && !dogovorOdDate) {
                dogovorOdError.text('Мора да внесете датум на договор пред да внесете датум на важење');
                return false;
            }
            
            // If both dates are set and DogovorVaziDo is before DaumNaDogovor,
            // automatically set DogovorVaziDo to match DaumNaDogovor
            if (dogovorOdDate && dogovorDoDate && dogovorDoDate < dogovorOdDate) {
                dogovorDoField.val(dogovorOdField.val());
            }
            
            return true;
        }

        // Add event listeners for document date fields
        $('#Input_DatumVaziOdPasosLicnaKarta, #Input_DatumVaziDoPasosLicnaKarta').on('change', function() {
            validateDocumentDates();
        });

        // Add event listeners for contract date fields
        $('#Input_DaumNaDogovor, #Input_DogovorVaziDo').on('change', function() {
            validateContractDates();
        });

        // Update form submission validation to include both date validations
        $('form').on('submit', function(e) {
            var isValid = validateFizickoLiceFields();

            if (!validateDocumentDates() || !validateContractDates()) {
                isValid = false;
            }

            if (!isValid) {
                e.preventDefault();
            }
        });

        // Remove success message after 10 seconds
        document.addEventListener('DOMContentLoaded', function () {
            var successMessage = document.getElementById('successMessage');
            if (successMessage) {
                setTimeout(function () {
                    successMessage.style.transition = 'opacity 1s';
                    successMessage.style.opacity = '0';
                    setTimeout(function () {
                        successMessage.remove();
                    }, 1000);
                }, 10000);
            }
        });

        function handleFizickoLiceTypeChange(radio) {
            // Reset all hidden inputs to false
            document.getElementById('Hidden_KlientVraboten').value = 'false';
            document.getElementById('Hidden_KlientSorabotnik').value = 'false';

            // Set the selected one to true
            if (radio.id === 'Input_KlientVraboten') {
                document.getElementById('Hidden_KlientVraboten').value = 'true';
            } else if (radio.id === 'Input_KlientSorabotnik') {
                document.getElementById('Hidden_KlientSorabotnik').value = 'true';
            }
        }

        function clearFizickoLiceSelection() {
            // Uncheck all radio buttons
            document.querySelectorAll('input[name="fizickoLiceType"]').forEach(radio => {
                radio.checked = false;
            });

            // Reset hidden inputs
            document.getElementById('Hidden_KlientVraboten').value = 'false';
            document.getElementById('Hidden_KlientSorabotnik').value = 'false';
        }

        $(document).ready(function() {
            // Add the search functionality for nadreden field
            let searchTimeout;
            const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();

            $('#nadredenSearch').on('input', function() {
                clearTimeout(searchTimeout);
                const searchTerm = $(this).val();
                const resultsDiv = $('#nadredenSearchResults');

                if (searchTerm.length < 1) {
                    resultsDiv.hide();
                    return;
                }

                searchTimeout = setTimeout(function() {
                    $.ajax({
                        url: '?handler=SearchNadreden',
                        type: 'GET',
                        data: { mb: searchTerm },
                        headers: {
                            "RequestVerificationToken": antiForgeryToken
                        },
                        success: function(data) {
                            if (data && data.length > 0) {
                                let html = '<div class="list-group">';
                                data.forEach(item => {
                                    let displayText = '';
                                    if (item.tip === 'P') {
                                        displayText = `${item.naziv}`;
                                        let identifiers = [];
                                        if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                        if (item.edb) identifiers.push(`ЕДБ: ${item.edb}`);
                                        if (identifiers.length > 0) {
                                            displayText += ` (${identifiers.join(', ')})`;
                                        }
                                    } else {
                                        displayText = `${item.ime} ${item.prezime}`;
                                        let identifiers = [];
                                        if (item.embg) identifiers.push(`ЕМБГ: ${item.embg}`);
                                        if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                        if (identifiers.length > 0) {
                                            displayText += ` (${identifiers.join(', ')})`;
                                        }
                                    }
                                    html += `<a href="#" class="list-group-item list-group-item-action" data-id="${item.id}">
                                              ${displayText}
                                           </a>`;
                                });
                                html += '</div>';
                                resultsDiv.html(html).show();
                            } else {
                                resultsDiv.html('<div class="list-group"><div class="list-group-item">Нема пронајдени резултати</div></div>').show();
                            }
                        },
                        error: function() {
                            resultsDiv.html('<div class="list-group"><div class="list-group-item text-danger">Грешка при пребарување</div></div>').show();
                        }
                    });
                }, 300);
            });

            // Handle selection
            $(document).on('click', '#nadredenSearchResults .list-group-item', function(e) {
                e.preventDefault();
                const id = $(this).data('id');
                const displayText = $(this).text();
                $('#nadredenSearch').val(displayText.trim());
                $('#SifrarnikRabotniPoziciiIdNadreden').val(id);
                $('#nadredenSearchResults').hide();
            });

            // Hide results when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('#nadredenSearch, #nadredenSearchResults').length) {
                    $('#nadredenSearchResults').hide();
                }
            });

            // Clear field functionality
            $('.clear-field[data-target="nadreden"]').click(function() {
                $('#nadredenSearch').val('');
                $('#SifrarnikRabotniPoziciiIdNadreden').val('');
                $('#nadredenSearchResults').hide();
            });
        });
    </script>
} 