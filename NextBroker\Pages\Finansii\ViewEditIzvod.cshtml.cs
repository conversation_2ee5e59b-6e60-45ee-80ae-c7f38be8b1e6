using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.Data.SqlClient;
using Microsoft.Data.SqlClient;

namespace NextBroker.Pages.Finansii
{
    public class ViewEditIzvodModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public bool HasAdminAccess { get; private set; }

        [BindProperty]
        public IzvodPremija Input { get; set; } = new();
        public List<StavkaPremija> Stavki { get; set; } = new();
        public List<StavkaPremija> StornoStavki { get; set; } = new();
        public List<StavkaPremija> StavkiOdliv { get; set; } = new();
        public List<StavkaPremija> StornoStavkiOdliv { get; set; } = new();

        public class IzvodPremija
        {
            public long Id { get; set; }
            public DateTime? DateCreated { get; set; }
            public string UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string UsernameModified { get; set; }
            public int SifrarnikBankiId { get; set; }
            public string BankaNaziv { get; set; }
            public string BrojNaIzvod { get; set; }
            public string BrojNaSmetka { get; set; }
            public DateTime DatumNaIzvod { get; set; }
            public decimal Priliv { get; set; }
            public bool? Rasknizen { get; set; }
            public decimal Odliv { get; set; }
            public bool? RasknizenOdliv { get; set; }
        }

        public class StavkaPremija
        {
            public long Id { get; set; }
            public DateTime? DateCreated { get; set; }
            public string UsernameCreated { get; set; }
            public string ReferencaUplakjac { get; set; }
            public string PovikBroj { get; set; }
            public string CelNaDoznaka { get; set; }
            public decimal Iznos { get; set; }
            public string PolisaBroj { get; set; }
            public bool? Neraspredelena { get; set; }
            public bool? PovratNaSredstva { get; set; }
            public bool? EvidentiranjeUplata { get; set; }
            public bool? Storno { get; set; }
            public string BrojNaKasoviZvestaj { get; set; }
            public string BrojNaFaktura { get; set; }
            public DateTime? DatumStorno { get; set; }
            public int? StornoZaMesec { get; set; }
            public int? StornoZaGodina { get; set; }
        }

        public ViewEditIzvodModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public async Task<IActionResult> OnGetAsync(long? id)
        {
            if (!await HasPageAccess("Izvodi"))
            {
                return RedirectToAccessDenied();
            }

            HasAdminAccess = await HasPageAccess("IzvodiAdmin");

            if (id == null)
            {
                return NotFound();
            }

            await LoadIzvod(id.Value);
            await LoadStavki(id.Value);

            if (Input == null)
            {
                return NotFound();
            }

            return Page();
        }

        private async Task LoadIzvod(long id)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT i.*, b.Banka as BankaNaziv
                    FROM IzvodPremija i
                    LEFT JOIN SifrarnikBanki b ON i.SifrarnikBankiId = b.Id
                    WHERE i.Id = @Id", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", id);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    if (await reader.ReadAsync())
                    {
                        Input = new IzvodPremija
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            DateModified = reader["DateModified"] as DateTime?,
                            UsernameModified = reader["UsernameModified"] as string,
                            SifrarnikBankiId = reader.GetInt32(reader.GetOrdinal("SifrarnikBankiId")),
                            BankaNaziv = reader["BankaNaziv"] as string,
                            BrojNaIzvod = reader["BrojNaIzvod"] as string,
                            BrojNaSmetka = reader["BrojNaSmetka"] as string,
                            DatumNaIzvod = reader.GetDateTime(reader.GetOrdinal("DatumNaIzvod")),
                            Priliv = reader.IsDBNull(reader.GetOrdinal("Priliv")) ? 0 : reader.GetDecimal(reader.GetOrdinal("Priliv")),
                            Rasknizen = reader["Rasknizen"] as bool?,
                            Odliv = reader.IsDBNull(reader.GetOrdinal("Odliv")) ? 0 : reader.GetDecimal(reader.GetOrdinal("Odliv")),
                            RasknizenOdliv = reader["RasknizenOdliv"] as bool?
                        };
                    }
                }
            }
        }

        private async Task LoadStavki(long izvodId)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                // First query for non-storno items
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT sp.Id, sp.DateCreated, sp.UsernameCreated, 
                           sp.ReferencaUplakjac, sp.PovikBroj, sp.CelNaDoznaka, 
                           sp.Iznos, sp.PolisaBroj, sp.PovratNaSredstva, sp.EvidentiranjeUplata,
                           sp.Neraspredelena, ki.BrojNaKasovIzvestaj, sp.BrojNaFaktura
                    FROM StavkaPremija sp
                    LEFT JOIN KasovIzvestaj ki on sp.Id = ki.StavkaPremijaId
                    WHERE IzvodPremijaId = @IzvodId 
                      AND (sp.Storno IS NULL OR sp.Storno = 0) and sp.Odliv = 0
                    ORDER BY sp.DateCreated", connection))
                {
                    cmd.Parameters.AddWithValue("@IzvodId", izvodId);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        Stavki.Add(new StavkaPremija
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            ReferencaUplakjac = reader["ReferencaUplakjac"] as string,
                            PovikBroj = reader["PovikBroj"] as string,
                            CelNaDoznaka = reader["CelNaDoznaka"] as string,
                            Iznos = reader.GetDecimal(reader.GetOrdinal("Iznos")),
                            PolisaBroj = reader["PolisaBroj"] as string,
                            PovratNaSredstva = reader["PovratNaSredstva"] as bool?,
                            EvidentiranjeUplata = reader["EvidentiranjeUplata"] as bool?,
                            Neraspredelena = reader["Neraspredelena"] as bool?,
                            BrojNaKasoviZvestaj = reader["BrojNaKasovIzvestaj"] as string,
                            BrojNaFaktura = reader["BrojNaFaktura"] as string
                        });
                    }
                }

                // Second query for storno items - also add the LEFT JOIN
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT sp.Id, sp.DateCreated, sp.UsernameCreated, 
                           sp.ReferencaUplakjac, sp.PovikBroj, sp.CelNaDoznaka, 
                           sp.Iznos, sp.PolisaBroj, sp.PovratNaSredstva, sp.EvidentiranjeUplata,
                           sp.Storno, sp.DatumStorno, sp.StornoZaMesec, sp.StornoZaGodina,
                           sp.Neraspredelena, ki.BrojNaKasovIzvestaj, sp.BrojNaFaktura   
                    FROM StavkaPremija sp
                    LEFT JOIN KasovIzvestaj ki on sp.Id = ki.StavkaPremijaId
                    WHERE sp.IzvodPremijaId = @IzvodId 
                      AND sp.Storno = 1 and sp.Odliv = 0
                    ORDER BY sp.DateCreated", connection))
                {
                    cmd.Parameters.AddWithValue("@IzvodId", izvodId);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        StornoStavki.Add(new StavkaPremija
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            ReferencaUplakjac = reader["ReferencaUplakjac"] as string,
                            PovikBroj = reader["PovikBroj"] as string,
                            CelNaDoznaka = reader["CelNaDoznaka"] as string,
                            Iznos = reader.GetDecimal(reader.GetOrdinal("Iznos")),
                            PolisaBroj = reader["PolisaBroj"] as string,
                            Neraspredelena = reader["Neraspredelena"] as bool?,
                            PovratNaSredstva = reader["PovratNaSredstva"] as bool?,
                            EvidentiranjeUplata = reader["EvidentiranjeUplata"] as bool?,
                            Storno = reader["Storno"] as bool?,
                            DatumStorno = reader["DatumStorno"] as DateTime?,
                            StornoZaMesec = reader.IsDBNull(reader.GetOrdinal("StornoZaMesec")) ? null : reader.GetInt32(reader.GetOrdinal("StornoZaMesec")),
                            StornoZaGodina = reader.IsDBNull(reader.GetOrdinal("StornoZaGodina")) ? null : reader.GetInt32(reader.GetOrdinal("StornoZaGodina")),
                            BrojNaKasoviZvestaj = reader["BrojNaKasovIzvestaj"] as string,
                            BrojNaFaktura = reader["BrojNaFaktura"] as string
                        });
                    }
                }

                // Add query for odliv items
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT sp.Id, sp.DateCreated, sp.UsernameCreated, 
                           sp.ReferencaUplakjac, sp.PovikBroj, sp.CelNaDoznaka, 
                           sp.Iznos, sp.PolisaBroj, sp.PovratNaSredstva, sp.EvidentiranjeUplata,
                           sp.Neraspredelena, ki.BrojNaKasovIzvestaj, sp.BrojNaFaktura
                    FROM StavkaPremija sp
                    LEFT JOIN KasovIzvestaj ki on sp.Id = ki.StavkaPremijaId
                    WHERE sp.IzvodPremijaId = @IzvodId 
                      AND (sp.Storno IS NULL OR sp.Storno = 0) 
                      AND sp.Odliv = 1
                    ORDER BY sp.DateCreated", connection))
                {
                    cmd.Parameters.AddWithValue("@IzvodId", izvodId);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        StavkiOdliv.Add(new StavkaPremija
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            ReferencaUplakjac = reader["ReferencaUplakjac"] as string,
                            PovikBroj = reader["PovikBroj"] as string,
                            CelNaDoznaka = reader["CelNaDoznaka"] as string,
                            Iznos = reader.GetDecimal(reader.GetOrdinal("Iznos")),
                            PolisaBroj = reader["PolisaBroj"] as string,
                            PovratNaSredstva = reader["PovratNaSredstva"] as bool?,
                            EvidentiranjeUplata = reader["EvidentiranjeUplata"] as bool?,
                            Neraspredelena = reader["Neraspredelena"] as bool?,
                            BrojNaKasoviZvestaj = reader["BrojNaKasovIzvestaj"] as string,
                            BrojNaFaktura = reader["BrojNaFaktura"] as string
                        });
                    }
                }

                // Add query for storno odliv items
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT sp.Id, sp.DateCreated, sp.UsernameCreated, 
                           sp.ReferencaUplakjac, sp.PovikBroj, sp.CelNaDoznaka, 
                           sp.Iznos, sp.PolisaBroj, sp.PovratNaSredstva, sp.EvidentiranjeUplata,
                           sp.Storno, sp.DatumStorno, sp.StornoZaMesec, sp.StornoZaGodina,
                           sp.Neraspredelena, ki.BrojNaKasovIzvestaj, sp.BrojNaFaktura   
                    FROM StavkaPremija sp
                    LEFT JOIN KasovIzvestaj ki on sp.Id = ki.StavkaPremijaId
                    WHERE sp.IzvodPremijaId = @IzvodId 
                      AND sp.Storno = 1 
                      AND sp.Odliv = 1
                    ORDER BY sp.DateCreated", connection))
                {
                    cmd.Parameters.AddWithValue("@IzvodId", izvodId);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        StornoStavkiOdliv.Add(new StavkaPremija
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            ReferencaUplakjac = reader["ReferencaUplakjac"] as string,
                            PovikBroj = reader["PovikBroj"] as string,
                            CelNaDoznaka = reader["CelNaDoznaka"] as string,
                            Iznos = reader.GetDecimal(reader.GetOrdinal("Iznos")),
                            PolisaBroj = reader["PolisaBroj"] as string,
                            Neraspredelena = reader["Neraspredelena"] as bool?,
                            PovratNaSredstva = reader["PovratNaSredstva"] as bool?,
                            EvidentiranjeUplata = reader["EvidentiranjeUplata"] as bool?,
                            Storno = reader["Storno"] as bool?,
                            DatumStorno = reader["DatumStorno"] as DateTime?,
                            StornoZaMesec = reader.IsDBNull(reader.GetOrdinal("StornoZaMesec")) ? null : reader.GetInt32(reader.GetOrdinal("StornoZaMesec")),
                            StornoZaGodina = reader.IsDBNull(reader.GetOrdinal("StornoZaGodina")) ? null : reader.GetInt32(reader.GetOrdinal("StornoZaGodina")),
                            BrojNaKasoviZvestaj = reader["BrojNaKasovIzvestaj"] as string,
                            BrojNaFaktura = reader["BrojNaFaktura"] as string
                        });
                    }
                }
            }
        }

        public async Task<JsonResult> OnPostStornoStavkaAsync([FromBody] StornoStavkaModel model)
        {
            if (!await HasPageAccess("IzvodiAdmin"))
            {
                return new JsonResult(new { success = false, message = "Немате пристап" });
            }

            try
            {
                string username = HttpContext.Session.GetString("Username");
                if (string.IsNullOrEmpty(username))
                {
                    return new JsonResult(new { success = false, message = "Не е најавен корисник" });
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        UPDATE StavkaPremija 
                        SET Storno = 1,
                            DatumStorno = @DatumStorno,
                            StornoZaMesec = @StornoZaMesec,
                            StornoZaGodina = @StornoZaGodina,
                            DateModified = GETDATE(),
                            UsernameModified = @UsernameModified
                        WHERE Id = @Id", connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", model.StavkaId);
                        cmd.Parameters.AddWithValue("@DatumStorno", DateTime.Parse(model.DatumStorno));
                        cmd.Parameters.AddWithValue("@StornoZaMesec", model.StornoZaMesec);
                        cmd.Parameters.AddWithValue("@StornoZaGodina", model.StornoZaGodina);
                        cmd.Parameters.AddWithValue("@UsernameModified", username);

                        int rowsAffected = await cmd.ExecuteNonQueryAsync();
                        if (rowsAffected == 0)
                        {
                            return new JsonResult(new { success = false, message = "Ставката не е најдена или не е променета" });
                        }
                        return new JsonResult(new { success = true });
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { 
                    success = false, 
                    message = ex.Message,
                });
            }
        }

        public async Task<JsonResult> OnPostAddStavkaAsync([FromBody] AddStavkaModel model)
        {
            if (!await HasPageAccess("IzvodiAdmin"))
            {
                return new JsonResult(new { success = false, message = "Немате пристап" });
            }

            try
            {
                string username = HttpContext.Session.GetString("Username");
                if (string.IsNullOrEmpty(username))
                {
                    return new JsonResult(new { success = false, message = "Не е најавен корисник" });
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        INSERT INTO StavkaPremija (
                            IzvodPremijaId,
                            ReferencaUplakjac,
                            PovikBroj,
                            CelNaDoznaka,
                            Iznos,
                            PolisaBroj,
                            BrojNaFaktura,
                            Neraspredelena,
                            PovratNaSredstva,
                            EvidentiranjeUplata,
                            UsernameCreated,
                            Odliv
                        ) VALUES (
                            @IzvodPremijaId,
                            @ReferencaUplakjac,
                            @PovikBroj,
                            @CelNaDoznaka,
                            @Iznos,
                            @PolisaBroj,
                            @BrojNaFaktura,
                            @Neraspredelena,
                            @PovratNaSredstva,
                            @EvidentiranjeUplata,
                            @UsernameCreated,
                            @Odliv
                        )", connection))
                    {
                        cmd.Parameters.AddWithValue("@IzvodPremijaId", Input.Id);
                        cmd.Parameters.AddWithValue("@ReferencaUplakjac", (object)model.ReferencaUplakjac ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@PovikBroj", (object)model.PovikBroj ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@CelNaDoznaka", (object)model.CelNaDoznaka ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Iznos", model.Iznos);
                        cmd.Parameters.AddWithValue("@PolisaBroj", (object)model.PolisaBroj ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaFaktura", (object)model.BrojNaFaktura ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Neraspredelena", model.Neraspredelena);
                        cmd.Parameters.AddWithValue("@PovratNaSredstva", model.PovratNaSredstva);
                        cmd.Parameters.AddWithValue("@EvidentiranjeUplata", model.EvidentiranjeUplata);
                        cmd.Parameters.AddWithValue("@UsernameCreated", username);
                        cmd.Parameters.AddWithValue("@Odliv", model.IsOdliv);

                        await cmd.ExecuteNonQueryAsync();
                        return new JsonResult(new { success = true });
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        public async Task<JsonResult> OnGetDolznaPremijaAsync(string brojNaPolisa)
        {
            try 
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand("SELECT dbo.VratiPolisaDolznaPremija(@BrojNaPolisa)", connection))
                    {
                        cmd.Parameters.AddWithValue("@BrojNaPolisa", brojNaPolisa);
                        var result = await cmd.ExecuteScalarAsync();
                        return new JsonResult(new { 
                            success = true, 
                            dolznaPremija = result ?? 0 
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { 
                    success = false, 
                    message = ex.Message 
                });
            }
        }

        public async Task<JsonResult> OnGetNerasknizenPrilivAsync(long? id)
        {
            try 
            {
                if (!id.HasValue)
                {
                    return new JsonResult(new { success = false, message = "Missing ID parameter" });
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    // First get the Priliv value for debugging
                    using (SqlCommand cmdPriliv = new SqlCommand("SELECT Priliv FROM IzvodPremija WHERE Id = @Id", connection))
                    {
                        cmdPriliv.Parameters.AddWithValue("@Id", id.Value);
                        var priliv = await cmdPriliv.ExecuteScalarAsync();
                        
                        // Now get the function result
                        using (SqlCommand cmd = new SqlCommand("SELECT dbo.VratiIzvodNerasknizenPriliv(@IzvodPremijaId)", connection))
                        {
                            cmd.Parameters.AddWithValue("@IzvodPremijaId", id.Value);
                            var result = await cmd.ExecuteScalarAsync();
                            
                            return new JsonResult(new { 
                                success = true, 
                                nerasknizenPriliv = result ?? 0,
                                debug = new {
                                    izvodId = id.Value,
                                    priliv = priliv ?? 0
                                }
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { 
                    success = false, 
                    message = ex.Message 
                });
            }
        }

        public async Task<JsonResult> OnGetProverkaPolisaAsync(string brojNaPolisa)
        {
            try 
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand("SELECT dbo.ProverkaZaPostoenjeNaPolisa(@BrojNaPolisa)", connection))
                    {
                        cmd.Parameters.AddWithValue("@BrojNaPolisa", brojNaPolisa);
                        var result = await cmd.ExecuteScalarAsync();
                        return new JsonResult(new { 
                            success = true, 
                            exists = result ?? 0 
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { 
                    success = false, 
                    message = ex.Message 
                });
            }
        }

        public async Task<JsonResult> OnGetSearchPolisiAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm) || searchTerm.Length < 3)
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 5 
                        BrojNaPolisa,
                        ISNULL(BrojNaFakturaIzlezna, '') as BrojNaFakturaIzlezna
                    FROM Polisi 
                    WHERE BrojNaPolisa LIKE '%' + @Search + '%'
                       OR BrojNaFakturaIzlezna LIKE '%' + @Search + '%'
                    ORDER BY BrojNaPolisa", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", searchTerm);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            brojNaPolisa = reader["BrojNaPolisa"],
                            brojNaFakturaIzlezna = reader["BrojNaFakturaIzlezna"]
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<JsonResult> OnGetNerasknizenOdlivAsync(long? id)
        {
            try 
            {
                if (!id.HasValue)
                {
                    return new JsonResult(new { success = false, message = "Missing ID parameter" });
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    // First get the Odliv value
                    using (SqlCommand cmdOdliv = new SqlCommand("SELECT Odliv FROM IzvodPremija WHERE Id = @Id", connection))
                    {
                        cmdOdliv.Parameters.AddWithValue("@Id", id.Value);
                        var odliv = await cmdOdliv.ExecuteScalarAsync();
                        
                        // Now get the sum of all non-storno stavki odliv
                        using (SqlCommand cmdSum = new SqlCommand(@"
                            SELECT COALESCE(SUM(Iznos), 0)
                            FROM StavkaPremija
                            WHERE IzvodPremijaId = @IzvodPremijaId
                            AND Odliv = 1
                            AND (Storno IS NULL OR Storno = 0)", connection))
                        {
                            cmdSum.Parameters.AddWithValue("@IzvodPremijaId", id.Value);
                            var usedAmount = await cmdSum.ExecuteScalarAsync();
                            
                            decimal totalOdliv = odliv != DBNull.Value ? Convert.ToDecimal(odliv) : 0;
                            decimal usedOdliv = usedAmount != DBNull.Value ? Convert.ToDecimal(usedAmount) : 0;
                            decimal remainingOdliv = totalOdliv - usedOdliv;
                            
                            return new JsonResult(new { 
                                success = true, 
                                nerasknizenOdliv = remainingOdliv,
                                debug = new {
                                    izvodId = id.Value,
                                    odliv = totalOdliv,
                                    usedOdliv = usedOdliv
                                }
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { 
                    success = false, 
                    message = ex.Message 
                });
            }
        }

        public async Task<JsonResult> OnGetSearchFakturaAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm) || searchTerm.Length < 2)
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT 
                        pnns.BrojNaFaktura,
                        klnt.Naziv,
                        pnns.DatumNaVleznaFaktura
                    FROM PrenosNaNaplataSpecifikacii pnns
                    LEFT JOIN Klienti klnt ON pnns.KlientiIdOsiguritel = klnt.Id
                    WHERE pnns.BrojNaFaktura LIKE '%' + @Search + '%'
                    ORDER BY pnns.BrojNaFaktura", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", searchTerm);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            brojNaFaktura = reader["BrojNaFaktura"].ToString(),
                            naziv = reader["Naziv"].ToString(),
                            datumNaFaktura = reader.IsDBNull(reader.GetOrdinal("DatumNaVleznaFaktura")) ? 
                                null : ((DateTime)reader["DatumNaVleznaFaktura"]).ToString("dd.MM.yyyy")
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public class StornoStavkaModel
        {
            public long StavkaId { get; set; }
            public string DatumStorno { get; set; }
            public int StornoZaMesec { get; set; }
            public int StornoZaGodina { get; set; }
        }

        public class AddStavkaModel
        {
            public string ReferencaUplakjac { get; set; }
            public string PovikBroj { get; set; }
            public string CelNaDoznaka { get; set; }
            public decimal Iznos { get; set; }
            public string PolisaBroj { get; set; }
            public string BrojNaFaktura { get; set; }
            public bool Neraspredelena { get; set; }
            public bool PovratNaSredstva { get; set; }
            public bool EvidentiranjeUplata { get; set; }
            public bool IsOdliv { get; set; }
        }    
    }
}   