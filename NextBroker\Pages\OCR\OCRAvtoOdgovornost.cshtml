@page
@model NextBroker.Pages.OCR.OCRAvtoOdgovornostModel
@{
    ViewData["Title"] = "OCR Авто Одговорност";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🚀 OCR Авто Одговорност - Мулти-енџин анализа</h3>
                    <p class="card-text mb-0">
                        <small class="text-muted">Тестира 3 различни OCR енџини и избира најдобар резултат за мешани документи</small>
                    </p>
                    <p class="card-text mb-0 mt-1">
                        <small class="text-info">📄 Поддржува слики и PDF документи</small>
                    </p>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" asp-page-handler="ProcessOCR">
                        <div class="row">
                            <!-- Image Upload Section -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="imageFile" class="form-label">Избери датотека за OCR:</label>
                                    <input type="file" class="form-control" id="imageFile" name="ImageFile" 
                                           accept="image/*,.pdf" required onchange="previewFile(this)">
                                    <small class="form-text text-muted">
                                        Поддржани формати: JPG, PNG, BMP, TIFF, GIF, PDF
                                    </small>
                                </div>
                                
                                <!-- File Preview -->
                                <div class="form-group mt-3">
                                    <label class="form-label">Преглед на датотека:</label>
                                    <div id="filePreview" style="border: 2px dashed #ddd; padding: 20px; text-align: center; min-height: 200px;">
                                        <span class="text-muted">Датотеката ќе се прикаже тука...</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Settings and Results Section -->
                            <div class="col-md-6">
                                <!-- OCR Settings -->
                                <div class="form-group">
                                    <label for="language" class="form-label">Режим на препознавање:</label>
                                    <select class="form-control" id="language" name="Language">
                                        <option value="auto" selected>🚀 Мулти-енџин анализа (Препорачано)</option>
                                        <option value="eng">🔤 English (Само англиски)</option>
                                        <option value="mkd">🔠 Macedonian (Само македонски)</option>
                                        <option value="eng+mkd">🌐 English + Macedonian (Двојазично)</option>
                                    </select>
                                    <small class="form-text text-muted">
                                        Мулти-енџин анализа: ги тестира сите 3 OCR енџини и го избира најточниот резултат.
                                    </small>
                                </div>

                                <div class="form-group">
                                    <label for="ocrMode" class="form-label">Режим на препознавање:</label>
                                    <select class="form-control" id="ocrMode" name="OcrMode">
                                        <option value="3">Автоматски (Default)</option>
                                        <option value="6">Единечен блок од текст</option>
                                        <option value="7">Една линија од текст</option>
                                        <option value="8">Една збор</option>
                                        <option value="13">Сурова линија (без речник)</option>
                                    </select>
                                </div>

                                <!-- Coordinate Extraction Option -->
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="useCoordinateExtraction" name="UseCoordinateExtraction" value="true">
                                        <label class="form-check-label" for="useCoordinateExtraction">
                                            <strong>🎯 Прецизна екстракција (координати)</strong>
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">
                                        Користи измерени координати за екстракција од специфични области на документот.
                                        <br><strong>Препорачано за стандардни полиси со конзистентен распоред!</strong>
                                    </small>
                                </div>

                                <!-- Process Button -->
                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary btn-lg" id="processBtn">
                                        <i class="fas fa-eye"></i> Препознај текст
                                    </button>
                                </div>

                                <!-- Structured Data Results -->
                                @if (!string.IsNullOrEmpty(Model.ExtractedText))
                                {
                                    <div class="form-group mt-4">
                                        <label class="form-label">📋 Извлечени податоци од полисата:</label>
                                        <div class="alert alert-primary">
                                            <h5><i class="fas fa-file-contract"></i> Структурирани податоци</h5>
                                            @if (Model.UseCoordinateExtraction)
                                            {
                                                <div class="mb-2">
                                                    <span class="badge bg-success">🎯 Прецизна екстракција</span>
                                                    <small class="text-muted ms-2">Користени се измерени координати</small>
                                                </div>
                                            }
                                            @if (Model.HasStructuredData)
                                            {
                                                <div class="mb-2">
                                                    <span class="badge bg-info">✅ Пронајдени податоци</span>
                                                </div>
                                            }
                                            else
                                            {
                                                <div class="mb-2">
                                                    <span class="badge bg-warning">⚠️ Нема пронајдено податоци</span>
                                                    <small class="text-muted ms-2">Прикажани се сите полиња за дебагирање</small>
                                                </div>
                                            }
                                            <div class="row">
                                                <div class="col-12">
                                                    <table class="table table-borderless mb-0">
                                                        <tbody>
                                                            <tr>
                                                                <td class="fw-bold text-primary">🔢 Број на полиса:</td>
                                                                <td class="text-dark fw-bold">@(Model.BrojNaPolisa ?? "N/A")</td>
                                                            </tr>
                                                            <tr>
                                                                <td class="fw-bold text-primary">👤 Име на договорувач:</td>
                                                                <td class="text-dark fw-bold">@(Model.ImeNaDgovoruvac ?? "N/A")</td>
                                                            </tr>
                                                            <tr>
                                                                <td class="fw-bold text-primary">🆔 ЕМБГ/МЕ:</td>
                                                                <td class="text-dark fw-bold">@(Model.EMBG_ME ?? "N/A")</td>
                                                            </tr>
                                                            <tr>
                                                                <td class="fw-bold text-primary">🚗 Регистрација:</td>
                                                                <td class="text-dark fw-bold">@(Model.Registracija ?? "N/A")</td>
                                                            </tr>
                                                            <tr>
                                                                <td class="fw-bold text-primary">🏙️ Место:</td>
                                                                <td class="text-dark fw-bold">@(Model.Mesto ?? "N/A")</td>
                                                            </tr>
                                                            <tr>
                                                                <td class="fw-bold text-primary">🛡️ Осигуреник:</td>
                                                                <td class="text-dark fw-bold">@(Model.Osigurenik ?? "N/A")</td>
                                                            </tr>
                                                            <tr>
                                                                <td class="fw-bold text-primary">🏠 Адреса:</td>
                                                                <td class="text-dark fw-bold">@(Model.Adresa ?? "N/A")</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                            <div class="mt-3">
                                                <button type="button" class="btn btn-primary btn-sm" onclick="copyStructuredData()">
                                                    <i class="fas fa-copy"></i> Копирај структурирани податоци
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                }

                                <!-- Coordinate Extraction Details -->
                                @if (Model.UseCoordinateExtraction && !string.IsNullOrEmpty(Model.CoordinateExtractionResults))
                                {
                                    <div class="form-group mt-4">
                                        <label class="form-label">🎯 Детали од прецизната екстракција:</label>
                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-crosshairs"></i> Резултати од координатна екстракција</h6>
                                            <p class="small mb-2">Овие резултати се добиени со екстракција од специфични области на документот користејќи измерени координати.</p>
                                        </div>
                                        <div class="card">
                                            <div class="card-header bg-light">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span><i class="fas fa-map-marker-alt"></i> Координатни резултати</span>
                                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="copyCoordinateResults()">
                                                        <i class="fas fa-copy"></i> Копирај резултати
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <pre id="coordinateResults" class="mb-0" style="font-family: 'Courier New', monospace; font-size: 12px; white-space: pre-wrap;">@Model.CoordinateExtractionResults</pre>
                                            </div>
                                        </div>
                                    </div>
                                }

                                <!-- Debug Information -->
                                @if (!string.IsNullOrEmpty(Model.DebugInfo))
                                {
                                    <div class="form-group mt-4">
                                        <label class="form-label">🐛 Дебаг информации:</label>
                                        <div class="alert alert-warning">
                                            <h6><i class="fas fa-bug"></i> Детални информации за екстракција</h6>
                                            <p class="small mb-2">Овие информации покажуваат што се случува во позадина при екстракцијата на податоците.</p>
                                        </div>
                                        <div class="card">
                                            <div class="card-header bg-warning bg-opacity-25">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span><i class="fas fa-code"></i> Дебаг лог</span>
                                                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="copyDebugInfo()">
                                                        <i class="fas fa-copy"></i> Копирај дебаг информации
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <pre id="debugInfo" class="mb-0" style="font-family: 'Courier New', monospace; font-size: 11px; white-space: pre-wrap; max-height: 400px; overflow-y: auto;">@Model.DebugInfo</pre>
                                            </div>
                                        </div>
                                    </div>
                                }

                                <!-- Raw OCR Text for Analysis -->
                                @if (!string.IsNullOrEmpty(Model.ExtractedText))
                                {
                                    <div class="form-group mt-4">
                                        <label class="form-label">🔍 Сиров OCR текст (за анализа):</label>
                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-search"></i> Помогни за подобрување на екстракцијата</h6>
                                            <p class="small mb-2">Овој текст е точно она што OCR-от го прочита од документот. Користи го за да ми помогнеш да ги подобрам правилата за екстракција.</p>
                                        </div>
                                        <div class="card">
                                            <div class="card-header bg-light">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span><i class="fas fa-file-alt"></i> Сиров текст од OCR</span>
                                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="copyRawText()">
                                                        <i class="fas fa-copy"></i> Копирај сиров текст
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <textarea id="rawOcrText" class="form-control" rows="15" readonly style="font-family: 'Courier New', monospace; font-size: 12px; white-space: pre-wrap;">@Model.ExtractedText</textarea>
                                            </div>
                                        </div>
                                    </div>
                                }

                                <!-- Full OCR Results -->
                                @if (!string.IsNullOrEmpty(Model.ExtractedText))
                                {
                                    <div class="form-group mt-4">
                                        <label class="form-label">📄 Целосен препознат текст:</label>
                                        <div class="alert alert-success">
                                            <strong>✅ Успешно препознавање!</strong>
                                            @if (!string.IsNullOrEmpty(Model.DetectedLanguage))
                                            {
                                                <br><small>🎯 Резултат: @Model.DetectedLanguage</small>
                                            }
                                            @if (Model.ProcessedWords > 0)
                                            {
                                                <br><small>📊 Статистика: @Model.ProcessedWords вкупно зборови</small>
                                                <br><small>🔤 Англиски: @Model.EnglishWords | 🔠 Македонски: @Model.MacedonianWords</small>
                                            }
                                        </div>
                                        <div class="accordion" id="fullTextAccordion">
                                            <div class="accordion-item">
                                                <h2 class="accordion-header" id="fullTextHeading">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#fullTextCollapse" aria-expanded="false" aria-controls="fullTextCollapse">
                                                        <i class="fas fa-eye me-2"></i> Прикажи целосен текст
                                                    </button>
                                                </h2>
                                                <div id="fullTextCollapse" class="accordion-collapse collapse" aria-labelledby="fullTextHeading" data-bs-parent="#fullTextAccordion">
                                                    <div class="accordion-body">
                                                        <textarea class="form-control" rows="10" readonly>@Model.ExtractedText</textarea>
                                                        <button type="button" class="btn btn-secondary mt-2" onclick="copyToClipboard()">
                                                            <i class="fas fa-copy"></i> Копирај целосен текст
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }

                                @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                                {
                                    <div class="form-group mt-4">
                                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                            <div class="d-flex">
                                                <div class="flex-shrink-0">
                                                    <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <h5 class="alert-heading mb-2">
                                                        <i class="fas fa-times-circle"></i> OCR Грешка
                                                        @if (!string.IsNullOrEmpty(Model.ErrorType))
                                                        {
                                                            <span class="badge bg-danger ms-2">@Model.ErrorType</span>
                                                        }
                                                    </h5>
                                                    <p class="mb-2">
                                                        <strong>@Model.ErrorMessage</strong>
                                                    </p>
                                                    
                                                    @if (!string.IsNullOrEmpty(Model.ErrorDetails))
                                                    {
                                                        <div class="mt-3">
                                                            <button class="btn btn-outline-danger btn-sm" type="button" data-bs-toggle="collapse" data-bs-target="#errorDetails" aria-expanded="false" aria-controls="errorDetails">
                                                                <i class="fas fa-info-circle"></i> Прикажи детали
                                                            </button>
                                                        </div>
                                                        <div class="collapse mt-2" id="errorDetails">
                                                            <div class="card card-body bg-light border-danger">
                                                                <small class="text-muted">
                                                                    <strong>Технички детали:</strong><br>
                                                                    @Model.ErrorDetails
                                                                </small>
                                                            </div>
                                                        </div>
                                                    }
                                                    
                                                    <hr class="my-3">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <h6><i class="fas fa-lightbulb text-warning"></i> Можни решенија:</h6>
                                                            <ul class="small mb-0">
                                                                <li>Обидете се со различна слика</li>
                                                                <li>Проверете дали сликата е јасна и читлива</li>
                                                                <li>Користете слика со повисока резолуција</li>
                                                                <li>Обидете се со различен OCR режим</li>
                                                            </ul>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <h6><i class="fas fa-tools text-info"></i> Системски проверки:</h6>
                                                            <ul class="small mb-0">
                                                                <li>Docker контејнер статус</li>
                                                                <li>Tesseract OCR библиотеки</li>
                                                                <li>Јазични датотеки (tessdata)</li>
                                                                <li>Достапна меморија</li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for file preview and functionality -->
<script>
    function previewFile(input) {
        const preview = document.getElementById('filePreview');
        const file = input.files[0];
        
        if (file) {
            const fileName = file.name;
            const fileSize = (file.size / 1024 / 1024).toFixed(2); // Convert to MB
            const fileExtension = fileName.split('.').pop().toLowerCase();
            
            if (fileExtension === 'pdf') {
                // Show PDF info instead of preview
                preview.innerHTML = `
                    <div class="d-flex align-items-center justify-content-center">
                        <div class="text-center">
                            <i class="fas fa-file-pdf fa-5x text-danger mb-3"></i>
                            <h5 class="text-dark">${fileName}</h5>
                            <p class="text-muted">Големина: ${fileSize} MB</p>
                            <small class="text-info">PDF документот ќе биде конвертиран во слики за OCR обработка</small>
                        </div>
                    </div>
                `;
            } else {
                // Show image preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.innerHTML = `
                        <div>
                            <img src="${e.target.result}" style="max-width: 100%; max-height: 300px; border-radius: 5px;">
                            <div class="mt-2">
                                <small class="text-muted">${fileName} (${fileSize} MB)</small>
                            </div>
                        </div>
                    `;
                }
                reader.readAsDataURL(file);
            }
            
            // Enable process button
            document.getElementById('processBtn').disabled = false;
        } else {
            preview.innerHTML = '<span class="text-muted">Датотеката ќе се прикаже тука...</span>';
            document.getElementById('processBtn').disabled = true;
        }
    }

    function copyRawText() {
        const rawTextArea = document.getElementById('rawOcrText');
        rawTextArea.select();
        rawTextArea.setSelectionRange(0, 99999); // For mobile devices
        
        try {
            document.execCommand('copy');
            
            // Show success feedback
            const btn = event.target.closest('button');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check"></i> Копирано!';
            btn.classList.remove('btn-outline-primary');
            btn.classList.add('btn-success');
            
            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.classList.remove('btn-success');
                btn.classList.add('btn-outline-primary');
            }, 2000);
        } catch (err) {
            console.error('Failed to copy text: ', err);
        }
    }

    function copyToClipboard() {
        const textarea = document.querySelector('textarea[readonly]');
        textarea.select();
        document.execCommand('copy');
        
        // Show feedback
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i> Копирано!';
        setTimeout(() => {
            btn.innerHTML = originalText;
        }, 2000);
    }

    function copyStructuredData() {
        // Extract structured data from the table
        let structuredText = "=== СТРУКТУРИРАНИ ПОДАТОЦИ ===\n";
        
        const table = document.querySelector('.alert-primary table');
        if (table) {
            const rows = table.querySelectorAll('tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length === 2) {
                    const label = cells[0].textContent.trim();
                    const value = cells[1].textContent.trim();
                    structuredText += `${label} ${value}\n`;
                }
            });
        }
        
        // Copy to clipboard
        navigator.clipboard.writeText(structuredText).then(() => {
            // Show feedback
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check"></i> Копирано!';
            setTimeout(() => {
                btn.innerHTML = originalText;
            }, 2000);
        }).catch(() => {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = structuredText;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            
            // Show feedback
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check"></i> Копирано!';
            setTimeout(() => {
                btn.innerHTML = originalText;
            }, 2000);
        });
    }

    function copyCoordinateResults() {
        const coordinateResultsElement = document.getElementById('coordinateResults');
        if (coordinateResultsElement) {
            const text = coordinateResultsElement.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                // Show feedback
                const btn = event.target.closest('button');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i> Копирано!';
                btn.classList.remove('btn-outline-info');
                btn.classList.add('btn-success');
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-outline-info');
                }, 2000);
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                
                // Show feedback
                const btn = event.target.closest('button');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i> Копирано!';
                btn.classList.remove('btn-outline-info');
                btn.classList.add('btn-success');
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-outline-info');
                }, 2000);
            });
        }
    }
    
    function copyDebugInfo() {
        const debugInfoElement = document.getElementById('debugInfo');
        if (debugInfoElement) {
            const text = debugInfoElement.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                // Show feedback
                const btn = event.target.closest('button');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i> Копирано!';
                btn.classList.remove('btn-outline-warning');
                btn.classList.add('btn-success');
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-outline-warning');
                }, 2000);
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                
                // Show feedback
                const btn = event.target.closest('button');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i> Копирано!';
                btn.classList.remove('btn-outline-warning');
                btn.classList.add('btn-success');
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-outline-warning');
                }, 2000);
            });
        }
    }

    // Disable process button initially
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('processBtn').disabled = true;
    });
</script>

<style>
    .card {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    #imagePreview img {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .btn-primary:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
    
    /* Enhanced error styling */
    .alert-danger {
        border-left: 5px solid #dc3545;
        background: linear-gradient(135deg, #fff5f5 0%, #fef2f2 100%);
    }
    
    .alert-danger .alert-heading {
        color: #721c24;
        font-weight: 600;
    }
    
    .alert-danger .fa-exclamation-triangle {
        animation: pulse 2s infinite;
    }
    
    @@keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    .collapse .card {
        border: 1px solid #f5c6cb;
        background-color: #f8d7da;
    }
    
    .badge.bg-danger {
        background-color: #dc3545 !important;
        font-size: 0.7em;
    }
    
    .alert-danger ul li {
        margin-bottom: 0.25rem;
    }
    
    .alert-danger .text-warning {
        color: #856404 !important;
    }
    
    .alert-danger .text-info {
        color: #0c5460 !important;
    }
</style>
