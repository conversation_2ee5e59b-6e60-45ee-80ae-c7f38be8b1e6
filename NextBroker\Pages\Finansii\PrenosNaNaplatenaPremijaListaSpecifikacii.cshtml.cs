using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using Microsoft.Data.SqlClient;
using System.Collections.Generic;
using OfficeOpenXml;
using System.IO;
using OfficeOpenXml.Style;

namespace NextBroker.Pages.Finansii
{
    public class PrenosNaNaplatenaPremijaListaSpecifikaciiModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public PrenosNaNaplatenaPremijaListaSpecifikaciiModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        [BindProperty(SupportsGet = true)]
        public string BrojNaFakturaFilter { get; set; }

        [BindProperty(SupportsGet = true)]
        public DateTime? DatumNaFakturaFilter { get; set; }

        [BindProperty(SupportsGet = true)]
        public DateTime? RokNaPlakanjeFilter { get; set; }

        public List<SpecifikacijaViewModel> Specifikacii { get; set; } = new();

        public class SpecifikacijaViewModel
        {
            public long Id { get; set; }
            public DateTime DateCreated { get; set; }
            public string UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string UsernameModified { get; set; }
            public string Naziv { get; set; }
            public string BrojNaFaktura { get; set; }
            public DateTime? DatumNaVleznaFaktura { get; set; }
            public DateTime? RokNaPlakjanjeFakturaVlezna { get; set; }
            public decimal? IznosNaFaktura { get; set; }
            public decimal? IznosNaFakturaVoRok { get; set; }
            public decimal? PlatenoOdDogovoruvac { get; set; }
            public decimal? Dolzi { get; set; }
            public string Banka { get; set; }
            public string BrojNaIzvod { get; set; }
            public long? StavkaVoIzvod { get; set; }
            public string TipNaFaktura { get; set; }
            public decimal? PlatenIznosPoFaktura { get; set; }
            public decimal? DolgPoFaktura { get; set; }
            public DateTime? DatumNaPlakanjeFaktura { get; set; }
            public string StatusNaPlakanje { get; set; }
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("PrenosNaNaplatenaPremijaListaSpecifikacii"))
            {
                return RedirectToAccessDenied();
            }

            await LoadSpecifikacii();
            return Page();
        }

        private async Task LoadSpecifikacii()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                var query = @"
                    select
                        pnns.Id,
                        pnns.DateCreated,
                        pnns.UsernameCreated,
                        pnns.DateModified,
                        pnns.UsernameModified,
                        klnt.Naziv,
                        pnns.BrojNaFaktura,
                        pnns.DatumNaVleznaFaktura,
                        pnns.RokNaPlakjanjeFakturaVlezna,
                        pnns.IznosNaFaktura + dbo.VratiOZZbirIznosOZSpecifikacijaPoPolisi(pnns.BrojNaFaktura) + dbo.VraziOZIznosVleznaFaktura(pnns.BrojNaFaktura) as [IznosNaFaktura],
                        pnns.IznosNaFakturaVoRok + dbo.VratiOZZbirIznosOZSpecifikacijaPoPolisi(pnns.BrojNaFaktura) + dbo.VraziOZIznosVleznaFaktura(pnns.BrojNaFaktura) as [IznosNaFakturaVoRok],
                        dbo.VratiPlatenoOdDogovoruvacPoFakturaPenosNaNaplatenaPremija (pnns.BrojNaFaktura, pnns.KlientiIdOsiguritel) as PlatenoOdDogovoruvac,
                        (pnns.IznosNaFaktura + dbo.VratiOZZbirIznosOZSpecifikacijaPoPolisi(pnns.BrojNaFaktura) + dbo.VraziOZIznosVleznaFaktura(pnns.BrojNaFaktura)) - dbo.VratiPlatenoOdDogovoruvacPoFakturaPenosNaNaplatenaPremija (
                            pnns.BrojNaFaktura, 
                            pnns.KlientiIdOsiguritel
                        ) AS Dolzi,
                        dbo.VratiImeNaBankaPoStavkaOdIzvod(pnns.StavkaPremijaId) as Banka,
                        dbo.VratiBrojNaIzvodPoStavka(pnns.StavkaPremijaId) as BrojNaIzvod,
                        pnns.StavkaPremijaId as StavkaVoIzvod,
                        dbo.VratiTipNaFakturaPoBrojNaFaktura(pnns.BrojNaFaktura) as TipNaFaktura,
                        case
                        when dbo.VratiTipNaFakturaPoBrojNaFaktura(pnns.BrojNaFaktura) = 'Влезна фактура кон брокер'
                        then dbo.VratiZbirNaIznosStavkiPovrzaniSoSpecifikacijaPrenosNaNaplata(pnns.ID) 
                        when dbo.VratiTipNaFakturaPoBrojNaFaktura(pnns.BrojNaFaktura) = 'Влезна фактура кон клиент'
                        then dbo.VratiPlatenoOdDogovoruvacPoFakturaPenosNaNaplatenaPremija (pnns.BrojNaFaktura, pnns.KlientiIdOsiguritel) 
                        end as PlatenIznosPoFaktura,
                        (pnns.IznosNaFaktura + dbo.VratiOZZbirIznosOZSpecifikacijaPoPolisi(pnns.BrojNaFaktura) + dbo.VraziOZIznosVleznaFaktura(pnns.BrojNaFaktura)) - dbo.VratiZbirNaIznosStavkiPovrzaniSoSpecifikacijaPrenosNaNaplata(pnns.ID) as DolgPoFaktura,
                        dbo.VratiDatumNaIzvodPoStavka(pnns.StavkaPremijaId) as DatumNaPlakanjeFaktura,                        
                        CASE 
                        WHEN (((pnns.IznosNaFaktura + dbo.VratiOZZbirIznosOZSpecifikacijaPoPolisi(pnns.BrojNaFaktura) + dbo.VraziOZIznosVleznaFaktura(pnns.BrojNaFaktura)) - dbo.VratiZbirNaIznosStavkiPovrzaniSoSpecifikacijaPrenosNaNaplata(pnns.ID)) = 0) OR (((pnns.IznosNaFakturaVoRok + dbo.VratiOZZbirIznosOZSpecifikacijaPoPolisi(pnns.BrojNaFaktura) + dbo.VraziOZIznosVleznaFaktura(pnns.BrojNaFaktura)) - dbo.VratiZbirNaIznosStavkiPovrzaniSoSpecifikacijaPrenosNaNaplata(pnns.ID)) = 0 and dbo.VratiDatumNaIzvodPoStavka(dbo.VratiPrenosNaNaplataListaSpecifikaciiPoslednaPovrzanaStavka(pnns.Id)) <= pnns.RokNaPlakjanjeFakturaVlezna) and dbo.VratiTipNaFakturaPoBrojNaFaktura(pnns.BrojNaFaktura) = 'Влезна фактура кон брокер' THEN 'Платена'
                        WHEN ((((pnns.IznosNaFaktura + dbo.VratiOZZbirIznosOZSpecifikacijaPoPolisi(pnns.BrojNaFaktura) + dbo.VraziOZIznosVleznaFaktura(pnns.BrojNaFaktura)) - dbo.VratiPlatenoOdDogovoruvacPoFakturaPenosNaNaplatenaPremija (pnns.BrojNaFaktura, pnns.KlientiIdOsiguritel)) <= 0) and dbo.VratiTipNaFakturaPoBrojNaFaktura(pnns.BrojNaFaktura) = 'Влезна фактура кон клиент') OR (((pnns.IznosNaFakturaVoRok + dbo.VratiOZZbirIznosOZSpecifikacijaPoPolisi(pnns.BrojNaFaktura) + dbo.VraziOZIznosVleznaFaktura(pnns.BrojNaFaktura)) - dbo.VratiPlatenoOdDogovoruvacPoFakturaPenosNaNaplatenaPremija (pnns.BrojNaFaktura, pnns.KlientiIdOsiguritel)) <= 0 and dbo.VratiDatumPoslednaUplataKajOsiguritelPoSpecifikacija(pnns.BrojNaFaktura) <= pnns.RokNaPlakjanjeFakturaVlezna) and dbo.VratiTipNaFakturaPoBrojNaFaktura(pnns.BrojNaFaktura) = 'Влезна фактура кон клиент' THEN 'Платена'
                        ELSE 'Фактура со долг'
                        END AS StatusNaPlakanje
                    from PrenosNaNaplataSpecifikacii pnns
                    left join Klienti klnt on pnns.KlientiIdOsiguritel = klnt.Id
                    left join StavkaPremija stavprm on pnns.StavkaPremijaId = stavprm.Id
                    WHERE 1=1 ";

                var parameters = new List<SqlParameter>();

                if (!string.IsNullOrEmpty(BrojNaFakturaFilter))
                {
                    query += " AND pnns.BrojNaFaktura LIKE @BrojNaFaktura";
                    parameters.Add(new SqlParameter("@BrojNaFaktura", $"%{BrojNaFakturaFilter}%"));
                }

                if (DatumNaFakturaFilter.HasValue)
                {
                    query += " AND CAST(pnns.DatumNaVleznaFaktura AS DATE) = @DatumNaFaktura";
                    parameters.Add(new SqlParameter("@DatumNaFaktura", DatumNaFakturaFilter.Value.Date));
                }

                if (RokNaPlakanjeFilter.HasValue)
                {
                    query += " AND CAST(pnns.RokNaPlakjanjeFakturaVlezna AS DATE) = @RokNaPlakanje";
                    parameters.Add(new SqlParameter("@RokNaPlakanje", RokNaPlakanjeFilter.Value.Date));
                }

                query += " ORDER BY pnns.DateCreated DESC";

                using (SqlCommand cmd = new SqlCommand(query, connection))
                {
                    cmd.Parameters.AddRange(parameters.ToArray());
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        Specifikacii.Add(new SpecifikacijaViewModel
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            DateCreated = reader.GetDateTime(reader.GetOrdinal("DateCreated")),
                            UsernameCreated = reader.GetString(reader.GetOrdinal("UsernameCreated")),
                            DateModified = reader.IsDBNull(reader.GetOrdinal("DateModified")) ? null : reader.GetDateTime(reader.GetOrdinal("DateModified")),
                            UsernameModified = reader.IsDBNull(reader.GetOrdinal("UsernameModified")) ? null : reader.GetString(reader.GetOrdinal("UsernameModified")),
                            Naziv = reader.IsDBNull(reader.GetOrdinal("Naziv")) ? null : reader.GetString(reader.GetOrdinal("Naziv")),
                            BrojNaFaktura = reader.IsDBNull(reader.GetOrdinal("BrojNaFaktura")) ? null : reader.GetString(reader.GetOrdinal("BrojNaFaktura")),
                            DatumNaVleznaFaktura = reader.IsDBNull(reader.GetOrdinal("DatumNaVleznaFaktura")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumNaVleznaFaktura")),
                            RokNaPlakjanjeFakturaVlezna = reader.IsDBNull(reader.GetOrdinal("RokNaPlakjanjeFakturaVlezna")) ? null : reader.GetDateTime(reader.GetOrdinal("RokNaPlakjanjeFakturaVlezna")),
                            IznosNaFaktura = reader.IsDBNull(reader.GetOrdinal("IznosNaFaktura")) ? null : reader.GetDecimal(reader.GetOrdinal("IznosNaFaktura")),
                            IznosNaFakturaVoRok = reader.IsDBNull(reader.GetOrdinal("IznosNaFakturaVoRok")) ? null : reader.GetDecimal(reader.GetOrdinal("IznosNaFakturaVoRok")),
                            PlatenoOdDogovoruvac = reader.IsDBNull(reader.GetOrdinal("PlatenoOdDogovoruvac")) ? null : reader.GetDecimal(reader.GetOrdinal("PlatenoOdDogovoruvac")),
                            Dolzi = reader.IsDBNull(reader.GetOrdinal("Dolzi")) ? null : reader.GetDecimal(reader.GetOrdinal("Dolzi")),
                            Banka = reader.IsDBNull(reader.GetOrdinal("Banka")) ? null : reader.GetString(reader.GetOrdinal("Banka")),
                            BrojNaIzvod = reader.IsDBNull(reader.GetOrdinal("BrojNaIzvod")) ? null : reader.GetString(reader.GetOrdinal("BrojNaIzvod")),
                            StavkaVoIzvod = reader.IsDBNull(reader.GetOrdinal("StavkaVoIzvod")) ? null : reader.GetInt64(reader.GetOrdinal("StavkaVoIzvod")),
                            TipNaFaktura = reader.IsDBNull(reader.GetOrdinal("TipNaFaktura")) ? null : reader.GetString(reader.GetOrdinal("TipNaFaktura")),
                            PlatenIznosPoFaktura = reader.IsDBNull(reader.GetOrdinal("PlatenIznosPoFaktura")) ? null : reader.GetDecimal(reader.GetOrdinal("PlatenIznosPoFaktura")),
                            DolgPoFaktura = reader.IsDBNull(reader.GetOrdinal("DolgPoFaktura")) ? null : reader.GetDecimal(reader.GetOrdinal("DolgPoFaktura")),
                            DatumNaPlakanjeFaktura = reader.IsDBNull(reader.GetOrdinal("DatumNaPlakanjeFaktura")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumNaPlakanjeFaktura")),
                            StatusNaPlakanje = reader.IsDBNull(reader.GetOrdinal("StatusNaPlakanje")) ? null : reader.GetString(reader.GetOrdinal("StatusNaPlakanje"))
                        });
                    }
                }
            }
        }

        public async Task<IActionResult> OnPostExportToExcel(string BrojNaFakturaFilter, string DatumNaFakturaFilter, string RokNaPlakanjeFilter)
        {
            if (!await HasPageAccess("PrenosNaNaplatenaPremijaListaSpecifikacii"))
            {
                return RedirectToAccessDenied();
            }

            // Set the filter values
            this.BrojNaFakturaFilter = BrojNaFakturaFilter;
            this.DatumNaFakturaFilter = !string.IsNullOrEmpty(DatumNaFakturaFilter) ? DateTime.Parse(DatumNaFakturaFilter) : null;
            this.RokNaPlakanjeFilter = !string.IsNullOrEmpty(RokNaPlakanjeFilter) ? DateTime.Parse(RokNaPlakanjeFilter) : null;

            await LoadSpecifikacii();

            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Спецификации");

            // Add headers
            worksheet.Cells[1, 1].Value = "ID";
            worksheet.Cells[1, 2].Value = "Датум на креирање";
            worksheet.Cells[1, 3].Value = "Креирал";
            worksheet.Cells[1, 4].Value = "Датум на промена";
            worksheet.Cells[1, 5].Value = "Променил";
            worksheet.Cells[1, 6].Value = "Назив";
            worksheet.Cells[1, 7].Value = "Број на фактура";
            worksheet.Cells[1, 8].Value = "Датум на влезна фактура";
            worksheet.Cells[1, 9].Value = "Рок на плаќање";
            worksheet.Cells[1, 10].Value = "Износ на фактура";
            worksheet.Cells[1, 11].Value = "Износ на фактура во рок";
            worksheet.Cells[1, 12].Value = "Платено од договорувач";
            worksheet.Cells[1, 13].Value = "Должи договорувач";
            worksheet.Cells[1, 14].Value = "Банка";
            worksheet.Cells[1, 15].Value = "Број на извод";
            worksheet.Cells[1, 16].Value = "Ставка во извод";
            worksheet.Cells[1, 17].Value = "Тип на фактура";
            worksheet.Cells[1, 18].Value = "Платено по фактура";
            worksheet.Cells[1, 19].Value = "Долг по фактура";
            worksheet.Cells[1, 20].Value = "Датум на плаќање";
            worksheet.Cells[1, 21].Value = "Статус на плаќање";

            // Style the header
            var headerRange = worksheet.Cells[1, 1, 1, 21];
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
            headerRange.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            headerRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

            // Add data
            int row = 2;
            foreach (var item in Specifikacii)
            {
                worksheet.Cells[row, 1].Value = item.Id;
                worksheet.Cells[row, 2].Value = item.DateCreated;
                worksheet.Cells[row, 3].Value = item.UsernameCreated;
                worksheet.Cells[row, 4].Value = item.DateModified;
                worksheet.Cells[row, 5].Value = item.UsernameModified;
                worksheet.Cells[row, 6].Value = item.Naziv;
                worksheet.Cells[row, 7].Value = item.BrojNaFaktura;
                worksheet.Cells[row, 8].Value = item.DatumNaVleznaFaktura;
                worksheet.Cells[row, 9].Value = item.RokNaPlakjanjeFakturaVlezna;
                worksheet.Cells[row, 10].Value = item.IznosNaFaktura;
                worksheet.Cells[row, 11].Value = item.IznosNaFakturaVoRok;
                worksheet.Cells[row, 12].Value = item.PlatenoOdDogovoruvac;
                worksheet.Cells[row, 13].Value = item.Dolzi;
                worksheet.Cells[row, 14].Value = item.Banka;
                worksheet.Cells[row, 15].Value = item.BrojNaIzvod;
                worksheet.Cells[row, 16].Value = item.StavkaVoIzvod;
                worksheet.Cells[row, 17].Value = item.TipNaFaktura;
                worksheet.Cells[row, 18].Value = item.PlatenIznosPoFaktura;
                worksheet.Cells[row, 19].Value = item.DolgPoFaktura;
                worksheet.Cells[row, 20].Value = item.DatumNaPlakanjeFaktura;
                worksheet.Cells[row, 21].Value = item.StatusNaPlakanje;

                row++;
            }

            // Format date columns
            worksheet.Cells[2, 2, row - 1, 2].Style.Numberformat.Format = "dd.MM.yyyy";
            worksheet.Cells[2, 4, row - 1, 4].Style.Numberformat.Format = "dd.MM.yyyy";
            worksheet.Cells[2, 8, row - 1, 8].Style.Numberformat.Format = "dd.MM.yyyy";
            worksheet.Cells[2, 9, row - 1, 9].Style.Numberformat.Format = "dd.MM.yyyy";
            worksheet.Cells[2, 20, row - 1, 20].Style.Numberformat.Format = "dd.MM.yyyy";

            // Format decimal columns
            string decimalFormat = "#,##0.00";
            worksheet.Cells[2, 10, row - 1, 13].Style.Numberformat.Format = decimalFormat;
            worksheet.Cells[2, 18, row - 1, 19].Style.Numberformat.Format = decimalFormat;

            // Auto-fit columns
            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

            // Generate the file
            var content = package.GetAsByteArray();
            string fileName = $"Спецификации_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx";

            return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
        }
    }
}
