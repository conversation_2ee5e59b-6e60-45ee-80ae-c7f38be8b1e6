@page
@model NextBroker.Pages.Pregledi.IzvestajZaNepostoeckiSetiranjaProvizijaModel
@{
    ViewData["Title"] = "Извештај за непостоечки сетирања провизија";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">@ViewData["Title"]</h3>
                </div>
                <div class="card-body">
                    <p class="text-muted">
                        Овој извештај прикажува полиси кои немаат сетирање за брокерска провизија.
                    </p>

                    @if (Model.IsDataLoaded && Model.Polisi.Any())
                    {
                        <div class="mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="searchBox" class="form-label">Пребарај:</label>
                                    <input type="text" id="searchBox" class="form-control" placeholder="Внесете текст за пребарување...">
                                </div>
                                <div class="col-md-6 d-flex align-items-end">
                                    <span class="badge bg-info">Вкупно записи: @Model.Polisi.Count</span>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table id="polisiTable" class="table table-striped table-bordered table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Број на полиса</th>
                                        <th>Осигурител</th>
                                        <th>Класа</th>
                                        <th>Продукт</th>
                                        <th>Статус на полиса</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var polisa in Model.Polisi)
                                    {
                                        <tr>
                                            <td>@polisa.BrojNaPolisa</td>
                                            <td>@polisa.Osiguritel</td>
                                            <td>@polisa.Klasa</td>
                                            <td>@polisa.Produkt</td>
                                            <td>
                                                @if (polisa.StatusPolisa == "Активна")
                                                {
                                                    <span class="badge bg-success">@polisa.StatusPolisa</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">@polisa.StatusPolisa</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else if (Model.IsDataLoaded && !Model.Polisi.Any())
                    {
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle"></i>
                            Не се пронајдени полиси без сетирање за брокерска провизија.
                        </div>
                    }
                    else
                    {
                        <div class="d-flex justify-content-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Се вчитува...</span>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <!-- DataTables Buttons CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.2.3/css/buttons.dataTables.min.css">
}

@section Scripts {
    <!-- DataTables Buttons JS -->
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.2.3/js/dataTables.buttons.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.2.3/js/buttons.html5.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.2.3/js/buttons.print.min.js"></script>

    <script>
        $(document).ready(function() {
            @if (Model.IsDataLoaded && Model.Polisi.Any())
            {
                <text>
                // Initialize DataTable
                var table = $('#polisiTable').DataTable({
                    dom: 'Bfrtip',
                    buttons: [
                        {
                            extend: 'excel',
                            text: '<i class="fas fa-file-excel"></i> Excel',
                            className: 'btn btn-success btn-sm me-1',
                            exportOptions: {
                                columns: ':visible'
                            },
                            title: 'Извештај за непостоечки сетирања провизија'
                        },
                        {
                            extend: 'pdf',
                            text: '<i class="fas fa-file-pdf"></i> PDF',
                            className: 'btn btn-danger btn-sm me-1',
                            exportOptions: {
                                columns: ':visible'
                            },
                            title: 'Извештај за непостоечки сетирања провизија'
                        },
                        {
                            extend: 'print',
                            text: '<i class="fas fa-print"></i> Печати',
                            className: 'btn btn-info btn-sm me-1',
                            exportOptions: {
                                columns: ':visible'
                            },
                            title: 'Извештај за непостоечки сетирања провизија'
                        }
                    ],
                    pageLength: 25,
                    order: [[0, 'asc']],
                    language: {
                        "lengthMenu": "Прикажи _MENU_ записи",
                        "zeroRecords": "Не се пронајдени записи",
                        "info": "Прикажани _START_ до _END_ од _TOTAL_ записи",
                        "infoEmpty": "Прикажани 0 до 0 од 0 записи",
                        "infoFiltered": "(филтрирано од вкупно _MAX_ записи)",
                        "search": "Пребарај:",
                        "paginate": {
                            "first": "Прва",
                            "last": "Последна",
                            "next": "Следна",
                            "previous": "Претходна"
                        },
                        "emptyTable": "Нема податоци во табелата"
                    },
                    lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Сите"]],
                    columnDefs: [
                        { orderable: true, targets: "_all" }
                    ]
                });

                // Handle custom search box
                $('#searchBox').on('keyup', function() {
                    table.search(this.value).draw();
                });
                </text>
            }
        });
    </script>
}