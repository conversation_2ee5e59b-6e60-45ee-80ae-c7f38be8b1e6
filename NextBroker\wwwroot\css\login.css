body {
    background-color: #f8f9fa;
    font-family: '<PERSON> Sans', '<PERSON> Sans MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS', sans-serif;
    font-size: 18px; /* Increased base font size */
}

.login-container {
    max-width: 500px; /* Increased from 400px */
    margin: 60px auto; /* Increased top and bottom margin */
    padding: 40px; /* Increased padding */
    background-color: #ffffff;
    border-radius: 20px; /* Increased border radius */
    box-shadow: 0 6px 18px rgba(0, 51, 102, 0.15); /* Increased shadow */
    transition: box-shadow 0.3s ease;
}

.login-container:hover {
    box-shadow: 0 8px 24px rgba(0, 51, 102, 0.2);
}

h2 {
    color: #003366;
    font-size: 2.5rem; /* Increased from 2rem */
    text-align: center;
    margin-bottom: 40px; /* Increased margin */
    position: relative;
    padding-bottom: 20px; /* Increased padding */
    font-family: '<PERSON> Sans', '<PERSON> MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS', sans-serif !important;
}

h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 60%; /* Increased width */
    height: 3px; /* Increased height */
    background-color: rgba(76, 175, 80, 0.7);
    transform: translateX(-50%);
    transition: width 0.3s ease, left 0.3s ease;
}

.login-container:hover h2::after {
    width: 70%;
    left: 50%;
}

.form-group {
    margin-bottom: 30px; /* Increased margin */
}

label {
    color: #003366;
    font-weight: bold;
    font-size: 1.2rem; /* Increased font size */
    margin-bottom: 10px; /* Added margin */
    display: block; /* Ensure label is on its own line */
}

.form-control {
    border: 2px solid rgba(0, 51, 102, 0.2); /* Increased border width */
    border-radius: 12px; /* Increased border radius */
    padding: 15px; /* Increased padding */
    width: 100%;
    transition: border-color 0.3s ease;
    font-size: 1.1rem; /* Increased font size */
}

.form-control:focus {
    border-color: #003366;
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 51, 102, 0.1); /* Increased shadow */
}

.text-danger {
    color: #dc3545;
    font-size: 1rem; /* Increased font size */
    margin-top: 8px; /* Increased margin */
}

.btn-primary {
    background-color: #003366;
    border: none !important;
    color: white !important;
    padding: 15px 25px;
    border-radius: 30px !important;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    font-size: 1.2rem;
    margin-top: 10px;
}

.btn-primary:hover {
    background-color: #004080 !important;
    transform: scale(1.05);
    color: white !important;
    border-radius: 30px !important;
}

.btn-primary:active,
.btn-primary:focus,
.btn-primary.active,
.btn-primary:active:focus {
    background-color: #4CAF50 !important;
    border-radius: 30px !important;
    outline: none;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.3) !important;
    border: none !important;
    color: white !important;
}

/* Add these additional overrides for Bootstrap */
.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled).active,
.show > .btn-primary.dropdown-toggle {
    background-color: #4CAF50 !important;
    border-radius: 30px !important;
    border-color: transparent !important;
    color: white !important;
}

.btn-primary.focus,
.btn-primary:focus {
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.3) !important;
}

a {
    color: #003366;
    text-decoration: none;
    transition: color 0.3s;
    font-size: 1.1rem; /* Increased font size */
}

a:hover {
    color: #004080;
    text-decoration: underline;
}

.message {
    text-align: center;
    margin-top: 20px; /* Increased margin */
    font-weight: bold;
    font-size: 1.1rem; /* Increased font size */
}

.links {
    margin-top: 30px; /* Increased margin */
    text-align: center;
}

.links p {
    margin: 15px 0; /* Increased margin */
}
