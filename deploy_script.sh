#!/bin/bash


# Variables
LOCAL_DIR="/home/<USER>/Desktop/NextByte Project/NextByte_RazorPortal_Official/"
REMOTE_USER="root"
REMOTE_PASS="123qwe123qwe"
REMOTE_IP="xxx.xxx.xxx.xxx"
REMOTE_DIR="/dotnet_deploy/strizlo_ubuntu_dev/"
DOCKER_IMAGE="razorportal_secure:latest"
CONTAINER_NAME="razorportal_v0.7"

# Function to upload files using scp
upload_files() {
    echo "Uploading files to remote server..."
    sshpass -p "$REMOTE_PASS" scp -r "$LOCAL_DIR"/* "$REMOTE_USER"@"$REMOTE_IP":"$REMOTE_DIR"
    if [ $? -ne 0 ]; then
        echo "Error uploading files."
        exit 1
    fi
    echo "Files uploaded successfully."
}

# Function to run Docker commands
run_docker_commands() {
    echo "Running Docker commands..."
    sshpass -p "$REMOTE_PASS" ssh "$REMOTE_USER"@"$REMOTE_IP" << EOF
        cd "$REMOTE_DIR" || exit
        docker stop $CONTAINER_NAME || true
        docker rm $CONTAINER_NAME || true
        docker build -t $DOCKER_IMAGE .
        docker run -d \
          --network razorportalnet \
          -p 5080:80 \
          -p 5081:443 \
          --name $CONTAINER_NAME \
          -e DOCKER_ENV=true \
          $DOCKER_IMAGE
EOF
    if [ $? -ne 0 ]; then
        echo "Error running Docker commands."
        exit 1
    fi
    echo "Docker commands executed successfully."
}

# Main script execution
upload_files
run_docker_commands
