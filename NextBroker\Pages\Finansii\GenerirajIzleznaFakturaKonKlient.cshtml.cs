using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using Microsoft.Data.SqlClient;
using System.Data;
using Renci.SshNet;
using System.Text;
using System.IO;
using System.Linq;

namespace NextBroker.Pages.Finansii
{
    public class GenerirajIzleznaFakturaKonKlientModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public bool HasAdminAccess { get; private set; }
        public long? PolisaId { get; set; }
        public DataTable FakturaData { get; set; }
        public DataTable KarticaData { get; set; }
        public DataTable PreviousInvoicesData { get; set; }
        public decimal? NextRataIznos { get; set; }
        public string RataSoZborovi { get; set; }
        public decimal? ProcentNaPopustZaFakturaVoRok { get; set; }
        public string Izdal { get; set; }
        public string Mesto { get; set; }
        public decimal? IznosZaPlakjanjeVoRok { get; set; }
        public string IznosZaPlakjanjeVoRokSoZborovi { get; set; }
        public decimal? IznosOZPoPolisa { get; set; }
        public string AdresaDogovoruvac { get; set; }
        public string AdresaOsigurenik { get; set; }
        public string RegistracijaVozilo { get; set; }

        public GenerirajIzleznaFakturaKonKlientModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
            FakturaData = new DataTable();
            FakturaData.Columns.Add("VkupnaPremijaSoZborovi", typeof(string));
            FakturaData.Columns.Add("PremijaZaNaplataSoZborovi", typeof(string));
            KarticaData = new DataTable();
            PreviousInvoicesData = new DataTable();
        }

        // Method to upload a file to SFTP server
        private async Task<(string filePath, string fileName)> UploadPdfToSftp(byte[] pdfData, string originalFileName)
        {
            System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Starting SFTP upload for file: {originalFileName}");
            
            try
            {
                var sftpConfig = _configuration.GetSection("SftpConfig");
                var host = sftpConfig["Host"];
                var port = int.Parse(sftpConfig["Port"]);
                var username = sftpConfig["Username"];
                var password = sftpConfig["Password"];
                
                System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Connection details - Host: {host}, Port: {port}, Username: {username}");
                
                string fileName = $"{DateTime.Now:yyyyMMddHHmmss}_{originalFileName}";
                string remotePath = "/upload/izleznifakturi";
                string remoteFilePath = $"{remotePath}/{fileName}";
                
                System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Remote file path: {remoteFilePath}");
                
                using (var client = new SftpClient(host, port, username, password))
                {
                    System.Diagnostics.Debug.WriteLine("[SFTP_DEBUG] Connecting to SFTP server...");
                    client.Connect();
                    System.Diagnostics.Debug.WriteLine("[SFTP_DEBUG] Connected successfully to SFTP server");
                    
                    // Create directory if it doesn't exist
                    System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Checking if directory exists: {remotePath}");
                    if (!client.Exists(remotePath))
                    {
                        System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Directory doesn't exist, creating: {remotePath}");
                        client.CreateDirectory(remotePath);
                        System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Directory created successfully");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Directory already exists");
                    }
                    
                    // Upload the file
                    System.Diagnostics.Debug.WriteLine("[SFTP_DEBUG] Starting file upload...");
                    using (var ms = new MemoryStream(pdfData))
                    {
                        client.UploadFile(ms, remoteFilePath);
                        System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] File uploaded successfully to {remoteFilePath}");
                    }
                    
                    client.Disconnect();
                    System.Diagnostics.Debug.WriteLine("[SFTP_DEBUG] Disconnected from SFTP server");
                }
                
                System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] SFTP upload completed successfully");
                return (remoteFilePath, fileName);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] ERROR: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[SFTP_DEBUG] Inner exception: {ex.InnerException.Message}");
                }
                throw; // Rethrow to be handled by the caller
            }
        }
        
        // Method to update the database with file path and name
        private async Task UpdateInvoiceWithFileInfo(SqlConnection connection, long polisaId, string brojNaFaktura, string filePath, string fileName)
        {
            using (var updateCommand = new SqlCommand(@"
                UPDATE PolisiIzlezniFakturiKonKlient 
                SET FilePath = @FilePath, FileName = @FileName
                WHERE PolisaId = @PolisaId AND BrojNaFaktura = @BrojNaFaktura", connection))
            {
                updateCommand.Parameters.AddWithValue("@FilePath", filePath);
                updateCommand.Parameters.AddWithValue("@FileName", fileName);
                updateCommand.Parameters.AddWithValue("@PolisaId", polisaId);
                updateCommand.Parameters.AddWithValue("@BrojNaFaktura", brojNaFaktura);
                
                await updateCommand.ExecuteNonQueryAsync();
            }
        }

        // API Endpoint to upload PDF to SFTP
        [HttpPost]
        [RequestSizeLimit(50 * 1024 * 1024)] // 50 MB limit
        [RequestFormLimits(MultipartBodyLengthLimit = 50 * 1024 * 1024)]
        public async Task<IActionResult> OnPostUploadPdfToSftpAsync(long polisaId, string brojNaFaktura, string fileName, string pdfBase64)
        {
            System.Diagnostics.Debug.WriteLine($"[PDF_UPLOAD] Starting OnPostUploadPdfToSftpAsync - PolisaId: {polisaId}, BrojNaFaktura: {brojNaFaktura}, Filename: {fileName}");
            System.Diagnostics.Debug.WriteLine($"[PDF_UPLOAD] Request content type: {Request.ContentType}");
            
            try
            {
                // Validate required parameters
                if (polisaId <= 0)
                {
                    System.Diagnostics.Debug.WriteLine("[PDF_UPLOAD] ERROR: Invalid polisaId");
                    return new JsonResult(new { success = false, message = "Invalid policy ID" });
                }
                
                if (string.IsNullOrEmpty(brojNaFaktura))
                {
                    System.Diagnostics.Debug.WriteLine("[PDF_UPLOAD] ERROR: Missing brojNaFaktura");
                    return new JsonResult(new { success = false, message = "Invoice number is required" });
                }
                
                // Check if PDF data is empty
                if (string.IsNullOrEmpty(pdfBase64))
                {
                    System.Diagnostics.Debug.WriteLine("[PDF_UPLOAD] ERROR: Base64 data is empty or null");
                    return new JsonResult(new { success = false, message = "PDF data is empty or invalid" });
                }
                
                // Log data length for debugging
                System.Diagnostics.Debug.WriteLine($"[PDF_UPLOAD] Received base64 data length: {pdfBase64.Length} characters");
                
                // Verify the data has a reasonable length
                if (pdfBase64.Length < 100)
                {
                    System.Diagnostics.Debug.WriteLine("[PDF_UPLOAD] ERROR: Base64 data is too short (< 100 chars)");
                    return new JsonResult(new { success = false, message = "PDF data is too short to be valid" });
                }
                
                // Log a small sample of the data for debugging
                System.Diagnostics.Debug.WriteLine($"[PDF_UPLOAD] Sample of base64 data: {pdfBase64.Substring(0, Math.Min(100, pdfBase64.Length))}...");

                // Create a standardized filename
                string serverFileName = $"faktura_{brojNaFaktura.Replace("/", "_")}_{DateTime.Now:yyyyMMddHHmmss}.pdf";
                System.Diagnostics.Debug.WriteLine($"[PDF_UPLOAD] Using server filename: {serverFileName}");
                
                // Decode the base64 string to byte array
                byte[] pdfBytes;
                try
                {
                    // Strip data URL prefix if present (should already be done in client-side)
                    if (pdfBase64.StartsWith("data:"))
                    {
                        int commaIndex = pdfBase64.IndexOf(",");
                        if (commaIndex > 0)
                        {
                            pdfBase64 = pdfBase64.Substring(commaIndex + 1);
                            System.Diagnostics.Debug.WriteLine("[PDF_UPLOAD] Removed data URL prefix from base64 data");
                        }
                    }
                    
                    // Remove any whitespace that might have been introduced
                    pdfBase64 = pdfBase64.Trim();
                    
                    // Try to decode the base64 string
                    try
                    {
                        pdfBytes = Convert.FromBase64String(pdfBase64);
                        System.Diagnostics.Debug.WriteLine($"[PDF_UPLOAD] Decoded base64 data to {pdfBytes.Length} bytes");
                        
                        // Verify we have a PDF (check for PDF header)
                        if (pdfBytes.Length > 4 && 
                            pdfBytes[0] == 0x25 && // %
                            pdfBytes[1] == 0x50 && // P
                            pdfBytes[2] == 0x44 && // D
                            pdfBytes[3] == 0x46)   // F
                        {
                            System.Diagnostics.Debug.WriteLine("[PDF_UPLOAD] Data appears to be a valid PDF (contains PDF header)");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("[PDF_UPLOAD] WARNING: Data does not start with PDF header but proceeding anyway");
                        }
                    }
                    catch (FormatException fe)
                    {
                        System.Diagnostics.Debug.WriteLine($"[PDF_UPLOAD] Base64 format error: {fe.Message}");
                        
                        // Try to fix common base64 padding issues
                        while (pdfBase64.Length % 4 != 0)
                        {
                            pdfBase64 += "=";
                        }
                        
                        System.Diagnostics.Debug.WriteLine("[PDF_UPLOAD] Trying to decode with fixed padding");
                        pdfBytes = Convert.FromBase64String(pdfBase64);
                        System.Diagnostics.Debug.WriteLine($"[PDF_UPLOAD] Successfully decoded after fixing padding, size: {pdfBytes.Length} bytes");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[PDF_UPLOAD] ERROR decoding base64: {ex.Message}");
                    System.Diagnostics.Debug.WriteLine($"[PDF_UPLOAD] Exception type: {ex.GetType().Name}");
                    
                    // Still update database with file info but indicate failure
                    string intendedPath = $"/upload/izleznifakturi/faktura_{brojNaFaktura.Replace("/", "_")}_{DateTime.Now:yyyyMMddHHmmss}.pdf";
                    string intendedFileName = $"faktura_{brojNaFaktura.Replace("/", "_")}_{DateTime.Now:yyyyMMddHHmmss}.pdf";
                    
                    try
                    {
                        using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                        {
                            await connection.OpenAsync();
                            await UpdateInvoiceWithFileInfo(connection, polisaId, brojNaFaktura, intendedPath, intendedFileName);
                            System.Diagnostics.Debug.WriteLine("[PDF_UPLOAD] Database updated with intended file info despite decode failure");
                        }
                    }
                    catch (Exception dbEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"[PDF_UPLOAD] ERROR updating database: {dbEx.Message}");
                    }
                    
                    return new JsonResult(new { success = false, message = $"Failed to decode PDF data: {ex.Message}" });
                }
                
                // Validate PDF size
                if (pdfBytes.Length < 1000)
                {
                    System.Diagnostics.Debug.WriteLine("[PDF_UPLOAD] ERROR: Decoded PDF is too small to be valid");
                    return new JsonResult(new { success = false, message = "Decoded PDF is too small to be valid" });
                }
                
                // Upload to SFTP
                try
                {
                    var (filePath, uploadedFileName) = await UploadPdfToSftp(pdfBytes, serverFileName);
                    System.Diagnostics.Debug.WriteLine($"[PDF_UPLOAD] SFTP upload succeeded: {filePath}");
                    
                    // Update database with file info
                    using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                    {
                        System.Diagnostics.Debug.WriteLine("[PDF_UPLOAD] Opening database connection to update file info");
                        await connection.OpenAsync();
                        await UpdateInvoiceWithFileInfo(connection, polisaId, brojNaFaktura, filePath, uploadedFileName);
                        System.Diagnostics.Debug.WriteLine("[PDF_UPLOAD] Database updated successfully with file path and name");
                    }
                    
                    return new JsonResult(new { 
                        success = true, 
                        message = "PDF successfully uploaded to SFTP server",
                        filePath = filePath,
                        fileName = uploadedFileName
                    });
                }
                catch (Exception sftpEx)
                {
                    System.Diagnostics.Debug.WriteLine($"[PDF_UPLOAD] SFTP upload error: {sftpEx.Message}");
                    
                    // Still update database with intended file path info
                    string intendedPath = $"/upload/izleznifakturi/faktura_{brojNaFaktura.Replace("/", "_")}_{DateTime.Now:yyyyMMddHHmmss}.pdf";
                    string intendedFileName = $"faktura_{brojNaFaktura.Replace("/", "_")}_{DateTime.Now:yyyyMMddHHmmss}.pdf";
                    
                    try
                    {
                        using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                        {
                            await connection.OpenAsync();
                            await UpdateInvoiceWithFileInfo(connection, polisaId, brojNaFaktura, intendedPath, intendedFileName);
                            System.Diagnostics.Debug.WriteLine("[PDF_UPLOAD] Database updated with intended file info despite SFTP failure");
                        }
                    }
                    catch (Exception dbEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"[PDF_UPLOAD] ERROR updating database: {dbEx.Message}");
                    }
                    
                    return new JsonResult(new { success = false, message = $"Failed to upload PDF to SFTP: {sftpEx.Message}" });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PDF_UPLOAD] Unhandled ERROR: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[PDF_UPLOAD] Stack trace: {ex.StackTrace}");
                
                // Try to at least update the database with the intended file name
                try
                {
                    // Still update database with intended file path info
                    string intendedPath = $"/upload/izleznifakturi/faktura_{brojNaFaktura.Replace("/", "_")}_{DateTime.Now:yyyyMMddHHmmss}.pdf";
                    string intendedFileName = $"faktura_{brojNaFaktura.Replace("/", "_")}_{DateTime.Now:yyyyMMddHHmmss}.pdf";
                    using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                    {
                        System.Diagnostics.Debug.WriteLine("[PDF_UPLOAD] Opening database connection to update with intended file info");
                        await connection.OpenAsync();
                        await UpdateInvoiceWithFileInfo(connection, polisaId, brojNaFaktura, intendedPath, intendedFileName);
                        System.Diagnostics.Debug.WriteLine("[PDF_UPLOAD] Database updated with intended file path despite upload failure");
                    }
                }
                catch (Exception dbEx)
                {
                    System.Diagnostics.Debug.WriteLine($"[PDF_UPLOAD] ERROR updating database after failed upload: {dbEx.Message}");
                }
                
                return new JsonResult(new { success = false, message = $"Failed to upload PDF: {ex.Message}" });
            }
        }
        
        public async Task<IActionResult> OnGetAsync(long? id)
        {
            if (!await HasPageAccess("GenerirajIzleznaFakturaKonKlient"))
            {
                return RedirectToAccessDenied();
            }           

            PolisaId = id;

            // Get username from session and load Izdal and Mesto info
            var username = HttpContext.Session.GetString("Username");
            if (!string.IsNullOrEmpty(username))
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    
                    // Get Izdal info
                    using (var cmd = new SqlCommand("SELECT dbo.VratiImePrezimePoUsername(@Username)", connection))
                    {
                        cmd.Parameters.AddWithValue("@Username", username);
                        var result = await cmd.ExecuteScalarAsync();
                        Izdal = result?.ToString() ?? string.Empty;
                    }

                    // Get Mesto info
                    using (var cmd = new SqlCommand("SELECT dbo.VratiImeEkspozituraPoUsername(@Username)", connection))
                    {
                        cmd.Parameters.AddWithValue("@Username", username);
                        var result = await cmd.ExecuteScalarAsync();
                        Mesto = result?.ToString() ?? string.Empty;
                    }
                }
            }

            if (PolisaId.HasValue)
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    
                    // Call VratiProcentNaPopustZaFakturaVoRok stored procedure
                    using (var command = new SqlCommand("VratiProcentNaPopustZaFakturaVoRok", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@PolisaId", PolisaId.Value);
                        
                        var result = await command.ExecuteScalarAsync();
                        if (result != DBNull.Value)
                        {
                            ProcentNaPopustZaFakturaVoRok = Convert.ToDecimal(result);
                        }
                    }

                    // Get IznosOZPoPolisa value first
                    using (var command = new SqlCommand("SELECT dbo.VraziOZIznosPolisa(@PolisaId)", connection))
                    {
                        command.Parameters.AddWithValue("@PolisaId", PolisaId.Value);
                        var result = await command.ExecuteScalarAsync();
                        if (result != DBNull.Value)
                        {
                            IznosOZPoPolisa = Convert.ToDecimal(result);
                        }
                    }

                    // Call VratiPolisaIznosZaPlakjanjeVoRok stored procedure
                    using (var command = new SqlCommand("VratiPolisaIznosZaPlakjanjeVoRok", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@PolisaId", PolisaId.Value);
                        
                        var result = await command.ExecuteScalarAsync();
                        if (result != DBNull.Value)
                        {
                            IznosZaPlakjanjeVoRok = Convert.ToDecimal(result);
                            
                            // Calculate total amount including IznosOZPoPolisa for text representation
                            decimal totalAmount = IznosZaPlakjanjeVoRok.Value + (IznosOZPoPolisa ?? 0);
                            
                            // Get text representation using fn_BrojVoZborovi
                            using (var cmdText = new SqlCommand("SELECT dbo.fn_BrojVoZborovi(@Number)", connection))
                            {
                                cmdText.Parameters.AddWithValue("@Number", Convert.ToInt32(totalAmount));
                                IznosZaPlakjanjeVoRokSoZborovi = (await cmdText.ExecuteScalarAsync())?.ToString();
                            }
                        }
                    }
                    
                    // Call GenerirajIzleznaFakturaKonKlient
                    using (var command = new SqlCommand("GenerirajIzleznaFakturaKonKlient", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@PolisaId", PolisaId.Value);

                        using (var adapter = new SqlDataAdapter(command))
                        {
                            await Task.Run(() => adapter.Fill(FakturaData));
                        }
                    }

                    // After getting the data, get the text representation for the numbers
                    if (FakturaData.Rows.Count > 0)
                    {
                        var row = FakturaData.Rows[0];
                        if (row["VkupnaPremija"] != DBNull.Value)
                        {
                            using (var cmdText = new SqlCommand("SELECT dbo.fn_BrojVoZborovi(@Number)", connection))
                            {
                                cmdText.Parameters.AddWithValue("@Number", Convert.ToInt32(Convert.ToDecimal(row["VkupnaPremija"])));
                                row["VkupnaPremijaSoZborovi"] = await cmdText.ExecuteScalarAsync();
                            }
                        }
                        if (row["PremijaZaNaplata"] != DBNull.Value)
                        {
                            using (var cmdText = new SqlCommand("SELECT dbo.fn_BrojVoZborovi(@Number)", connection))
                            {
                                cmdText.Parameters.AddWithValue("@Number", Convert.ToInt32(Convert.ToDecimal(row["PremijaZaNaplata"])));
                                row["PremijaZaNaplataSoZborovi"] = await cmdText.ExecuteScalarAsync();
                            }
                        }
                    }
                    
                    // Load previous invoices for this policy
                    using (var command = new SqlCommand(@"
                        SELECT 
                        Id,
                        DateCreated,
                        UsernameCreated,
                        BrojNaFaktura,
                        DatumNaFaktura,
                        RokNaPlakanjeFakturaIzlezna,
                        VkupnaPremija,
                        dbo.VraziOZIznosIzleznaFakturaPremija(BrojNaFaktura) as IznosOzdobrenjeZadolzuvane,
                        (PremijaZaNaplata + dbo.VraziOZIznosIzleznaFakturaPremija(BrojNaFaktura)) as PremijaZaNaplata,
                        RataIznos,
                        FilePath,
                        FileName
                        FROM 
                        PolisiIzlezniFakturiKonKlient 
                        WHERE PolisaId = @PolisaId
                        ORDER BY DateCreated DESC", connection))
                    {
                        command.Parameters.AddWithValue("@PolisaId", PolisaId.Value);
                        using (var adapter = new SqlDataAdapter(command))
                        {
                            await Task.Run(() => adapter.Fill(PreviousInvoicesData));
                        }
                    }

                    // Call VratiPolisaKartica
                    using (var command = new SqlCommand("VratiPolisaKartica", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@PolisaId", PolisaId.Value);

                        using (var adapter = new SqlDataAdapter(command))
                        {
                            await Task.Run(() => adapter.Fill(KarticaData));
                        }
                    }
                    
                    // Find the next rata (installment) based on today's date
                    DateTime today = DateTime.Today;
                    DataRow nextRata = null;
                    
                    foreach (DataRow row in KarticaData.Rows)
                    {
                        if (row["DatumNaDospevanje"] != DBNull.Value)
                        {
                            DateTime datumDospevanja = Convert.ToDateTime(row["DatumNaDospevanje"]);
                            if (datumDospevanja >= today)
                            {
                                // This is the next upcoming rata
                                nextRata = row;
                                break;
                            }
                        }
                    }
                    
                    // If we found a next rata, get the amount and convert to words
                    if (nextRata != null && nextRata["IznosRata"] != DBNull.Value)
                    {
                        NextRataIznos = Convert.ToDecimal(nextRata["IznosRata"]);
                        
                        // Get the text representation using fn_BrojVoZborovi
                        using (var cmdText = new SqlCommand("SELECT dbo.fn_BrojVoZborovi(@Number)", connection))
                        {
                            cmdText.Parameters.AddWithValue("@Number", Convert.ToInt32(NextRataIznos));
                            RataSoZborovi = (await cmdText.ExecuteScalarAsync())?.ToString();
                        }
                    }
                    
                    // Get addresses for Dogovoruvac and Osigurenik
                    using (var cmdAdresaDogovoruvac = new SqlCommand("SELECT dbo.VratiAdresaDogovoruvacPoPolisaId(@PolisaId)", connection))
                    {
                        cmdAdresaDogovoruvac.Parameters.AddWithValue("@PolisaId", PolisaId.Value);
                        var result = await cmdAdresaDogovoruvac.ExecuteScalarAsync();
                        AdresaDogovoruvac = result?.ToString() ?? string.Empty;
                    }
                    
                    using (var cmdAdresaOsigurenik = new SqlCommand("SELECT dbo.VratiAdresaOsigurenikPoPolisaId(@PolisaId)", connection))
                    {
                        cmdAdresaOsigurenik.Parameters.AddWithValue("@PolisaId", PolisaId.Value);
                        var result = await cmdAdresaOsigurenik.ExecuteScalarAsync();
                        AdresaOsigurenik = result?.ToString() ?? string.Empty;
                    }
                    
                    // Get vehicle registration information
                    using (var cmdRegistracijaVozilo = new SqlCommand("SELECT dbo.VratiRegistracijaVoziloPoPolisaId(@PolisaId)", connection))
                    {
                        cmdRegistracijaVozilo.Parameters.AddWithValue("@PolisaId", PolisaId.Value);
                        var result = await cmdRegistracijaVozilo.ExecuteScalarAsync();
                        var registracija = result?.ToString()?.Trim() ?? string.Empty;
                        RegistracijaVozilo = registracija.Length >= 3 ? registracija : string.Empty;
                    }
                }
            }

            return Page();
        }
        
        public string GetIznosRataVoZborovi(decimal iznos)
        {
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();
                using (var command = new SqlCommand("SELECT dbo.fn_BrojVoZborovi(@Number)", connection))
                {
                    command.Parameters.AddWithValue("@Number", Convert.ToInt32(iznos));
                    var result = command.ExecuteScalar();
                    return result?.ToString() ?? string.Empty;
                }
            }
        }

        [HttpPost]
        public async Task<IActionResult> OnPostSaveInvoiceAsync(long polisaId)
        {
            if (!await HasPageAccess("GenerirajIzleznaFakturaKonKlient"))
            {
                return new JsonResult(new { success = false, message = "Не е дозволен пристап до оваа страница." });
            }

            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    
                    // First, get the invoice data
                    var fakturaData = new DataTable();
                    using (var command = new SqlCommand("GenerirajIzleznaFakturaKonKlient", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@PolisaId", polisaId);

                        using (var adapter = new SqlDataAdapter(command))
                        {
                            await Task.Run(() => adapter.Fill(fakturaData));
                        }
                    }

                    if (fakturaData.Rows.Count == 0)
                    {
                        return new JsonResult(new { success = false, message = "Не се најдени податоци за фактурата." });
                    }

                    var row = fakturaData.Rows[0];
                    string brojNaFaktura = row["BrojNaFaktura"].ToString();
                    
                    // Get the kartica data to find the next rata
                    var karticaData = new DataTable();
                    using (var command = new SqlCommand("VratiPolisaKartica", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@PolisaId", polisaId);

                        using (var adapter = new SqlDataAdapter(command))
                        {
                            await Task.Run(() => adapter.Fill(karticaData));
                        }
                    }
                    
                    // Find the next rata (installment) based on today's date
                    DateTime today = DateTime.Today;
                    DataRow nextRata = null;
                    decimal? rataIznos = null;
                    
                    foreach (DataRow karticaRow in karticaData.Rows)
                    {
                        if (karticaRow["DatumNaDospevanje"] != DBNull.Value)
                        {
                            DateTime datumDospevanja = Convert.ToDateTime(karticaRow["DatumNaDospevanje"]);
                            if (datumDospevanja >= today)
                            {
                                // This is the next upcoming rata
                                nextRata = karticaRow;
                                break;
                            }
                        }
                    }
                    
                    // Get the RataIznos from the next rata
                    if (nextRata != null && nextRata["IznosRata"] != DBNull.Value)
                    {
                        rataIznos = Convert.ToDecimal(nextRata["IznosRata"]);
                    }
                    
                    // Get IznosOZPoPolisa value for this policy
                    decimal iznosOZPoPolisa = 0;
                    using (var ozCommand = new SqlCommand("SELECT dbo.VraziOZIznosPolisa(@PolisaId)", connection))
                    {
                        ozCommand.Parameters.AddWithValue("@PolisaId", polisaId);
                        var ozResult = await ozCommand.ExecuteScalarAsync();
                        if (ozResult != DBNull.Value)
                        {
                            iznosOZPoPolisa = Convert.ToDecimal(ozResult);
                        }
                    }

                    // Check if invoice with same number already exists, if so, append suffix
                    string uniqueBrojNaFaktura = brojNaFaktura;
                    int suffix = 0;
                    
                    using (var checkCommand = new SqlCommand("SELECT COUNT(*) FROM PolisiIzlezniFakturiKonKlient WHERE BrojNaFaktura = @BrojNaFaktura", connection))
                    {
                        checkCommand.Parameters.AddWithValue("@BrojNaFaktura", brojNaFaktura);
                        int existingCount = Convert.ToInt32(await checkCommand.ExecuteScalarAsync());
                        
                        if (existingCount > 0)
                        {
                            // Find highest suffix for this invoice number
                            using (var suffixCommand = new SqlCommand(
                                "SELECT COUNT(*) FROM PolisiIzlezniFakturiKonKlient WHERE BrojNaFaktura LIKE @BrojNaFakturaPattern", connection))
                            {
                                suffixCommand.Parameters.AddWithValue("@BrojNaFakturaPattern", brojNaFaktura + "-%");
                                int suffixCount = Convert.ToInt32(await suffixCommand.ExecuteScalarAsync());
                                suffix = suffixCount + 1;
                                uniqueBrojNaFaktura = $"{brojNaFaktura}-{suffix}";
                            }
                        }
                    }

                    // Calculate RokNaPlakanjeFakturaIzlezna
                    DateTime datumNaFaktura = DateTime.Now;
                    DateTime rokNaPlakanje = datumNaFaktura;
                    
                    if (row["RokNaPlakanjeDenovi"] != DBNull.Value)
                    {
                        int rokDenovi = Convert.ToInt32(row["RokNaPlakanjeDenovi"]);
                        rokNaPlakanje = datumNaFaktura.AddDays(rokDenovi);
                    }
                    
                    // Insert into PolisiIzlezniFakturiKonKlient with all the columns from the procedure
                    using (var insertCommand = new SqlCommand(@"
                        INSERT INTO PolisiIzlezniFakturiKonKlient (
                            PolisaId, BrojNaFaktura, DatumNaFaktura, RokNaPlakanjeFakturaIzlezna,
                            VkupnaPremija, PremijaZaNaplata, UsernameCreated, DateCreated,
                            BrojNaPolisa, DogovoruvacIme, DogovoruvacPrezime, DogovoruvacNaziv,
                            OsigurenikIme, OsigurenikPrezime, OsigurenikNaziv, Osiguritel, Produkt,
                            RataIznos, FilePath, FileName
                        ) VALUES (
                            @PolisaId, @BrojNaFaktura, @DatumNaFaktura, @RokNaPlakanjeFakturaIzlezna,
                            @VkupnaPremija, @PremijaZaNaplata, @UsernameCreated, @DateCreated,
                            @BrojNaPolisa, @DogovoruvacIme, @DogovoruvacPrezime, @DogovoruvacNaziv,
                            @OsigurenikIme, @OsigurenikPrezime, @OsigurenikNaziv, @Osiguritel, @Produkt,
                            @RataIznos, @FilePath, @FileName
                        )", connection))
                    {
                        insertCommand.Parameters.AddWithValue("@PolisaId", polisaId);
                        insertCommand.Parameters.AddWithValue("@BrojNaFaktura", uniqueBrojNaFaktura);
                        insertCommand.Parameters.AddWithValue("@DatumNaFaktura", datumNaFaktura);
                        insertCommand.Parameters.AddWithValue("@RokNaPlakanjeFakturaIzlezna", rokNaPlakanje);
                        
                        // Add VkupnaPremija parameter (including IznosOZPoPolisa)
                        if (row["VkupnaPremija"] != DBNull.Value)
                            insertCommand.Parameters.AddWithValue("@VkupnaPremija", Convert.ToDecimal(row["VkupnaPremija"]) + iznosOZPoPolisa);
                        else
                            insertCommand.Parameters.AddWithValue("@VkupnaPremija", iznosOZPoPolisa);
                            
                        // Add PremijaZaNaplata parameter (including IznosOZPoPolisa)
                        if (row["PremijaZaNaplata"] != DBNull.Value)
                            insertCommand.Parameters.AddWithValue("@PremijaZaNaplata", Convert.ToDecimal(row["PremijaZaNaplata"]) + iznosOZPoPolisa);
                        else
                            insertCommand.Parameters.AddWithValue("@PremijaZaNaplata", iznosOZPoPolisa);
                        
                        // Add user tracking parameters    
                        insertCommand.Parameters.AddWithValue("@UsernameCreated", HttpContext.Session.GetString("Username") ?? "System");
                        insertCommand.Parameters.AddWithValue("@DateCreated", DateTime.Now);
                        
                        // Add BrojNaPolisa parameter
                        if (row["BrojNaPolisa"] != DBNull.Value)
                            insertCommand.Parameters.AddWithValue("@BrojNaPolisa", row["BrojNaPolisa"].ToString());
                        else
                            insertCommand.Parameters.AddWithValue("@BrojNaPolisa", DBNull.Value);
                        
                        // Add DogovoruvacIme parameter
                        if (row["DogovoruvacIme"] != DBNull.Value)
                            insertCommand.Parameters.AddWithValue("@DogovoruvacIme", row["DogovoruvacIme"].ToString());
                        else
                            insertCommand.Parameters.AddWithValue("@DogovoruvacIme", DBNull.Value);
                            
                        // Add DogovoruvacPrezime parameter
                        if (row["DogovoruvacPrezime"] != DBNull.Value)
                            insertCommand.Parameters.AddWithValue("@DogovoruvacPrezime", row["DogovoruvacPrezime"].ToString());
                        else
                            insertCommand.Parameters.AddWithValue("@DogovoruvacPrezime", DBNull.Value);
                            
                        // Add DogovoruvacNaziv parameter
                        if (row["DogovoruvacNaziv"] != DBNull.Value)
                            insertCommand.Parameters.AddWithValue("@DogovoruvacNaziv", row["DogovoruvacNaziv"].ToString());
                        else
                            insertCommand.Parameters.AddWithValue("@DogovoruvacNaziv", DBNull.Value);
                            
                        // Add OsigurenikIme parameter
                        if (row["OsigurenikIme"] != DBNull.Value)
                            insertCommand.Parameters.AddWithValue("@OsigurenikIme", row["OsigurenikIme"].ToString());
                        else
                            insertCommand.Parameters.AddWithValue("@OsigurenikIme", DBNull.Value);
                            
                        // Add OsigurenikPrezime parameter
                        if (row["OsigurenikPrezime"] != DBNull.Value)
                            insertCommand.Parameters.AddWithValue("@OsigurenikPrezime", row["OsigurenikPrezime"].ToString());
                        else
                            insertCommand.Parameters.AddWithValue("@OsigurenikPrezime", DBNull.Value);
                            
                        // Add OsigurenikNaziv parameter
                        if (row["OsigurenikNaziv"] != DBNull.Value)
                            insertCommand.Parameters.AddWithValue("@OsigurenikNaziv", row["OsigurenikNaziv"].ToString());
                        else
                            insertCommand.Parameters.AddWithValue("@OsigurenikNaziv", DBNull.Value);
                            
                        // Add Osiguritel parameter
                        if (row["Osiguritel"] != DBNull.Value)
                            insertCommand.Parameters.AddWithValue("@Osiguritel", row["Osiguritel"].ToString());
                        else
                            insertCommand.Parameters.AddWithValue("@Osiguritel", DBNull.Value);
                            
                        // Add Produkt parameter
                        if (row["Produkt"] != DBNull.Value)
                            insertCommand.Parameters.AddWithValue("@Produkt", row["Produkt"].ToString());
                        else
                            insertCommand.Parameters.AddWithValue("@Produkt", DBNull.Value);
                        
                        // Add RataIznos parameter
                        if (rataIznos.HasValue)
                            insertCommand.Parameters.AddWithValue("@RataIznos", rataIznos.Value);
                        else
                            insertCommand.Parameters.AddWithValue("@RataIznos", DBNull.Value);
                        
                        // Add FilePath and FileName parameters as NULL initially
                        insertCommand.Parameters.AddWithValue("@FilePath", DBNull.Value);
                        insertCommand.Parameters.AddWithValue("@FileName", DBNull.Value);
                        
                        await insertCommand.ExecuteNonQueryAsync();
                    }
                    
                    // Update Polisi table to mark that an invoice has been generated
                    using (var updatePolisiCommand = new SqlCommand(@"
                        Update Polisi set GeneriranaFakturaIzlezna = 1 where Id = @PolisaId", connection))
                    {
                        updatePolisiCommand.Parameters.AddWithValue("@PolisaId", polisaId);
                        await updatePolisiCommand.ExecuteNonQueryAsync();
                    }

                    // Update Polisi table to set the invoice number
                    using (var updateFakturaCommand = new SqlCommand(@"
                        Update Polisi set 
                            BrojNaFakturaIzlezna = @BrojNaFaktura,
                            DatumNaIzleznaFaktura = @DatumNaFaktura,
                            RokNaPlakjanjeFakturaIzlezna = @RokNaPlakanje
                        where Id = @PolisaId", connection))
                    {
                        updateFakturaCommand.Parameters.AddWithValue("@PolisaId", polisaId);
                        updateFakturaCommand.Parameters.AddWithValue("@BrojNaFaktura", uniqueBrojNaFaktura);
                        updateFakturaCommand.Parameters.AddWithValue("@DatumNaFaktura", DateTime.Now);
                        updateFakturaCommand.Parameters.AddWithValue("@RokNaPlakanje", rokNaPlakanje);
                        await updateFakturaCommand.ExecuteNonQueryAsync();
                    }
                    
                    return new JsonResult(new { success = true, message = "Фактурата е успешно сочувана.", brojNaFaktura = uniqueBrojNaFaktura });
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = $"Грешка при зачувување на фактурата: {ex.Message}" });
            }
        }

        // Handler to download invoice from SFTP server
        [HttpPost]
        public async Task<IActionResult> OnPostDownloadInvoiceAsync(long invoiceId, string filePath, string fileName)
        {
            if (!await HasPageAccess("GenerirajIzleznaFakturaKonKlient"))
            {
                return RedirectToAccessDenied();
            }

            System.Diagnostics.Debug.WriteLine($"[DOWNLOAD] Starting download for invoice ID: {invoiceId}, Path: {filePath}, FileName: {fileName}");
            
            try
            {
                // Get SFTP configuration
                var sftpConfig = _configuration.GetSection("SftpConfig");
                var host = sftpConfig["Host"];
                var port = int.Parse(sftpConfig["Port"]);
                var username = sftpConfig["Username"];
                var password = sftpConfig["Password"];
                
                System.Diagnostics.Debug.WriteLine($"[DOWNLOAD] SFTP Connection details - Host: {host}, Port: {port}, Username: {username}");
                
                byte[] fileData;
                
                // Connect to SFTP server and download the file
                using (var client = new SftpClient(host, port, username, password))
                {
                    System.Diagnostics.Debug.WriteLine("[DOWNLOAD] Connecting to SFTP server...");
                    client.Connect();
                    System.Diagnostics.Debug.WriteLine("[DOWNLOAD] Connected successfully to SFTP server");
                    
                    // Check if file exists
                    if (!client.Exists(filePath))
                    {
                        System.Diagnostics.Debug.WriteLine($"[DOWNLOAD] File not found: {filePath}");
                        
                        // If file not found, try looking in the directory
                        string directory = Path.GetDirectoryName(filePath).Replace("\\", "/");
                        string fileNamePattern = Path.GetFileName(filePath);
                        
                        System.Diagnostics.Debug.WriteLine($"[DOWNLOAD] Looking for files in directory: {directory} with pattern: {fileNamePattern}");
                        
                        // Try to find any files that match the pattern
                        var files = client.ListDirectory(directory)
                            .Where(f => !f.IsDirectory && f.Name.Contains(fileNamePattern))
                            .ToList();
                            
                        if (files.Any())
                        {
                            // Use the first file found
                            var foundFile = files.First();
                            filePath = foundFile.FullName;
                            System.Diagnostics.Debug.WriteLine($"[DOWNLOAD] Found alternative file: {filePath}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"[DOWNLOAD] No matching files found in directory: {directory}");
                            return NotFound($"The requested invoice file could not be found on the SFTP server. Path: {filePath}");
                        }
                    }
                    
                    // Download file to memory
                    using (var memoryStream = new MemoryStream())
                    {
                        System.Diagnostics.Debug.WriteLine($"[DOWNLOAD] Downloading file: {filePath}");
                        client.DownloadFile(filePath, memoryStream);
                        fileData = memoryStream.ToArray();
                        System.Diagnostics.Debug.WriteLine($"[DOWNLOAD] Downloaded {fileData.Length} bytes");
                    }
                    
                    // Disconnect
                    client.Disconnect();
                    System.Diagnostics.Debug.WriteLine("[DOWNLOAD] Disconnected from SFTP server");
                }
                
                // Return file
                string safeFileName = fileName;
                if (string.IsNullOrEmpty(safeFileName) || !safeFileName.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
                {
                    safeFileName = $"faktura_{invoiceId}.pdf";
                }
                
                System.Diagnostics.Debug.WriteLine($"[DOWNLOAD] Sending file to client: {safeFileName}");
                return File(fileData, "application/pdf", safeFileName);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DOWNLOAD] ERROR: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[DOWNLOAD] Stack trace: {ex.StackTrace}");
                
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[DOWNLOAD] Inner exception: {ex.InnerException.Message}");
                }
                
                return StatusCode(500, $"Error downloading invoice: {ex.Message}");
            }
        }
    }
}
