@page
@model NextBroker.Pages.Pregledi.ASOKvartalenOBD2Model

@{
    ViewData["Title"] = "АСО Квартален ОБД2";
}

<div class="container-fluid">
    <h2 class="mb-4">АСО Квартален ОБД2</h2>
    <form method="post" class="row g-3 mb-4">
        <div class="col-auto">
            <label asp-for="SelectedYear" class="form-label">Година</label>
            <select asp-for="SelectedYear" asp-items="Model.YearOptions" class="form-select"></select>
        </div>
        <div class="col-auto">
            <label asp-for="SelectedQuarter" class="form-label">Квартал</label>
            <select asp-for="SelectedQuarter" asp-items="Model.QuarterOptions" class="form-select"></select>
        </div>
        <div class="col-auto align-self-end">
            <button type="submit" formaction="?handler=LoadData" class="btn btn-primary">Преглед</button>
        </div>
    </form>

    @if (!string.IsNullOrEmpty(Model.NoDataMessage) && !Model.ShowTable)
    {
        <div class="alert alert-warning mt-3">
            <i class="fas fa-exclamation-triangle me-2"></i> @Model.NoDataMessage
        </div>
    }

    @if (Model.ShowTable)
    {
        <div class="row mb-3">
            @if (!Model.IsReadOnly)
            {
                @if (Model.IsPreview)
                {
                    <div class="col-auto">
                        <form method="post" asp-page-handler="Confirm" id="confirmForm" style="display:inline;">
                            <input type="hidden" name="SelectedYear" value="@Model.SelectedYear" />
                            <input type="hidden" name="SelectedQuarter" value="@Model.SelectedQuarter" />
                            @for (int i = 0; i < Model.ActivityRows.Count; i++)
                            {
                                <input type="hidden" name="ActivityRows[@i].Description" value="@Model.ActivityRows[i].Description" />
                                <input type="hidden" name="ActivityRows[@i].BrojDogovori" value="@Model.ActivityRows[i].BrojDogovori" />
                                <input type="hidden" name="ActivityRows[@i].RealiziranaProvizija" value="@Model.ActivityRows[i].RealiziranaProvizija" />
                            }
                            <button type="submit" class="btn btn-success me-2">Потврди</button>
                        </form>
                    </div>
                    <div class="col-auto">
                        <form method="post" asp-page-handler="CancelPreview" id="cancelPreviewForm" style="display:inline;">
                            <input type="hidden" name="SelectedYear" value="@Model.SelectedYear" />
                            <input type="hidden" name="SelectedQuarter" value="@Model.SelectedQuarter" />
                            <button type="submit" class="btn btn-secondary me-2">Откажи</button>
                        </form>
                    </div>
                }
            }
            <div class="col-auto">
                <form method="post" asp-page-handler="ExportExcel" id="exportExcelForm" style="display:inline;">
                    <input type="hidden" name="SelectedYear" value="@Model.SelectedYear" />
                    <input type="hidden" name="SelectedQuarter" value="@Model.SelectedQuarter" />
                    @for (int i = 0; i < Model.ActivityRows.Count; i++)
                    {
                        <input type="hidden" name="ActivityRows[@i].Description" value="@Model.ActivityRows[i].Description" />
                        <input type="hidden" name="ActivityRows[@i].BrojDogovori" value="@Model.ActivityRows[i].BrojDogovori" />
                        <input type="hidden" name="ActivityRows[@i].RealiziranaProvizija" value="@Model.ActivityRows[i].RealiziranaProvizija" />
                    }
                    <button type="submit" class="btn btn-outline-primary me-2">Export to Excel</button>
                </form>
            </div>
            <div class="col-auto">
                <form method="post" asp-page-handler="ExportPdf" id="exportPdfForm" style="display:inline;">
                    <input type="hidden" name="SelectedYear" value="@Model.SelectedYear" />
                    <input type="hidden" name="SelectedQuarter" value="@Model.SelectedQuarter" />
                    @for (int i = 0; i < Model.ActivityRows.Count; i++)
                    {
                        <input type="hidden" name="ActivityRows[@i].Description" value="@Model.ActivityRows[i].Description" />
                        <input type="hidden" name="ActivityRows[@i].BrojDogovori" value="@Model.ActivityRows[i].BrojDogovori" />
                        <input type="hidden" name="ActivityRows[@i].RealiziranaProvizija" value="@Model.ActivityRows[i].RealiziranaProvizija" />
                    }
                    <button type="submit" class="btn btn-outline-danger">Export to PDF</button>
                </form>
            </div>
        </div>
        <div class="mb-3">
            @if (Model.IsReadOnly)
            {
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> Записот за овој квартал и година е веќе потврден. Може само да го експортирате.
                </div>
            }
            @if (!ViewData.ModelState.IsValid && ViewData.ModelState.ErrorCount > 0)
            {
                <div class="alert alert-danger">
                    <ul class="mb-0">
                    @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                    {
                        <li>@error.ErrorMessage</li>
                    }
                    </ul>
                </div>
            }
            @if (Model.IsPreview)
            {
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> Ова е преглед на податоците. Кликнете "Потврди" за да ги зачувате во базата на податоци или "Откажи" за да се вратите на уредување.
                </div>
            }
            else if (Model.IsConfirmed)
            {
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i> Податоците се зачувани во базата на податоци.
                </div>
            }
        </div>
        <div class="table-responsive">
            <table class="table table-bordered table-striped align-middle" id="obd2Table">
                <thead class="table-light">
                    <tr>
                        <th>Опис</th>
                        <th>Број на договори</th>
                        <th>Реализирана провизија</th>
                    </tr>
                </thead>
                <tbody>
                    @for (int i = 0; i < Model.ActivityRows.Count; i++)
                    {
                        <tr>
                            <td>
                                @Model.ActivityRows[i].Description
                                <input type="hidden" name="ActivityRows[@i].Description" value="@Model.ActivityRows[i].Description" />
                            </td>
                            <td>
                                <input type="number" class="form-control" name="ActivityRows[@i].BrojDogovori" value="@Model.ActivityRows[i].BrojDogovori" @(Model.IsConfirmed || Model.IsReadOnly ? "readonly" : "") />
                            </td>
                            <td>
                                <input type="number" step="0.01" class="form-control" name="ActivityRows[@i].RealiziranaProvizija" value="@Model.ActivityRows[i].RealiziranaProvizija" @(Model.IsConfirmed || Model.IsReadOnly ? "readonly" : "") />
                            </td>
                        </tr>
                    }
                </tbody>
                <tfoot>
                    <tr>
                        <td><strong>ВКУПНО</strong></td>
                        <td><strong id="vkupno-broj" style="color: #000; font-weight: bold;">@Model.ActivityRows.Sum(x => x.BrojDogovori ?? 0)</strong></td>
                        <td><strong id="vkupno-realizirana" style="color: #000; font-weight: bold;">@{
                            var totalRealizirana = Model.ActivityRows.Sum(x => x.RealiziranaProvizija ?? 0);
                            @(totalRealizirana % 1 == 0 ? totalRealizirana.ToString("F0") : totalRealizirana.ToString("F2"))
                        }</strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>
        <script>
        document.addEventListener('DOMContentLoaded', function () {
            console.log('OBD2: Page loaded');
            
            // Function to recalculate totals - simple approach like OBD1
            function recalcTotals() {
                console.log('OBD2: recalcTotals called');
                
                let brojDogovori = 0;
                let realizirana = 0;
                
                // Calculate totals from visible table inputs
                document.querySelectorAll('tbody input[name$=".BrojDogovori"]').forEach((input, index) => {
                    const value = parseInt(input.value) || 0;
                    brojDogovori += value;
                    console.log(`OBD2: Row ${index} BrojDogovori: ${value} (running total: ${brojDogovori})`);
                });
                
                document.querySelectorAll('tbody input[name$=".RealiziranaProvizija"]').forEach((input, index) => {
                    const value = parseFloat(input.value) || 0;
                    realizirana += value;
                    console.log(`OBD2: Row ${index} RealiziranaProvizija: ${value} (running total: ${realizirana})`);
                });
                
                // Update the totals display - clean numbers without .00
                document.getElementById('vkupno-broj').textContent = brojDogovori;
                document.getElementById('vkupno-realizirana').textContent = realizirana % 1 === 0 ? realizirana.toFixed(0) : realizirana.toFixed(2);
                
                console.log(`OBD2: Updated totals - BrojDogovori: ${brojDogovori}, Realizirana: ${realizirana.toFixed(2)}`);
            }

            // Sync hidden fields for all forms before submit
            function syncHiddenFields(formId) {
                const table = document.getElementById('obd2Table');
                if (!table) return;
                for (let i = 0; i < 6; i++) {
                    const brojInput = table.querySelector('input[name="ActivityRows[' + i + '].BrojDogovori"]');
                    const provizijaInput = table.querySelector('input[name="ActivityRows[' + i + '].RealiziranaProvizija"]');
                    const descInput = table.querySelector('input[name="ActivityRows[' + i + '].Description"]');
                    const brojHidden = document.querySelector(`#${formId} input[name='ActivityRows[${i}].BrojDogovori']`);
                    const provizijaHidden = document.querySelector(`#${formId} input[name='ActivityRows[${i}].RealiziranaProvizija']`);
                    const descHidden = document.querySelector(`#${formId} input[name='ActivityRows[${i}].Description']`);
                    if (brojHidden && brojInput) brojHidden.value = brojInput.value || 0;
                    if (provizijaHidden && provizijaInput) provizijaHidden.value = provizijaInput.value || 0;
                    if (descHidden && descInput) descHidden.value = descInput.value || '';
                }
            }

            // Attach event listeners to all editable inputs for totals recalculation - simple approach like OBD1
            document.querySelectorAll('input[type="number"]').forEach((input, inputIndex) => {
                input.addEventListener('input', function() {
                    const currentValue = this.value;
                    const rowIndex = Math.floor(inputIndex / 2); // 2 inputs per row in OBD2
                    const fieldType = this.name.split('.').pop();
                    
                    console.log(`OBD2: User modified input: Row ${rowIndex}, Field ${fieldType} = ${currentValue}`);
                    
                    // Recalculate totals immediately - simple approach like OBD1
                    recalcTotals();
                });
            });

            const confirmForm = document.getElementById('confirmForm');
            if (confirmForm) {
                confirmForm.addEventListener('submit', function (e) {
                    syncHiddenFields('confirmForm');
                });
            }
            const cancelPreviewForm = document.getElementById('cancelPreviewForm');
            if (cancelPreviewForm) {
                cancelPreviewForm.addEventListener('submit', function (e) {
                    syncHiddenFields('cancelPreviewForm');
                });
            }
            const exportExcelForm = document.getElementById('exportExcelForm');
            if (exportExcelForm) {
                exportExcelForm.addEventListener('submit', function (e) {
                    syncHiddenFields('exportExcelForm');
                });
            }
            const exportPdfForm = document.getElementById('exportPdfForm');
            if (exportPdfForm) {
                exportPdfForm.addEventListener('submit', function (e) {
                    syncHiddenFields('exportPdfForm');
                });
            }

            // Calculate initial totals
            console.log('OBD2: Calculating initial totals...');
            recalcTotals();
        });
        </script>
    }
</div>
