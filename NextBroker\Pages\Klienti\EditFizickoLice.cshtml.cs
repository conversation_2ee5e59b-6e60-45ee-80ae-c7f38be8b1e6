using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using Renci.SshNet;
using System.Text;

namespace NextBroker.Pages.Klienti
{
    public class EditFizickoLiceModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public long Id { get; set; }
        public List<KlientiFile> Files { get; set; }

        public EditFizickoLiceModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        [BindProperty]
        public InputModel Input { get; set; }

        public SelectList Opstini { get; set; }
        public SelectList Banki { get; set; }
        public SelectList NivoaNaRizik { get; set; }
        public SelectList RabotniPozicii { get; set; }
        public SelectList OrganizacioniEdinici { get; set; }
        public SelectList EkspozituriIdEkspozitura { get; set; }

        public class KlientiFile
        {
            public long Id { get; set; }
            public string FileName { get; set; }
            public string FilePath { get; set; }
            public DateTime DateCreated { get; set; }
            public string UsernameCreated { get; set; }
        }

        public class InputModel
        {
            public string? Ime { get; set; }
            public string? Prezime { get; set; }
            public string? EMBG { get; set; }
            public int? ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija { get; set; }
            public string? UlicaOdDokumentZaIdentifikacija { get; set; }
            public string? BrojOdDokumentZaIdentifikacija { get; set; }
            public int? ListaOpstiniIdOpstinaZaKomunikacija { get; set; }
            public string? UlicaZaKomunikacija { get; set; }
            public string? BrojZaKomunikacija { get; set; }
            public string? BrojPasosLicnaKarta { get; set; }
            public DateTime? DatumVaziOdPasosLicnaKarta { get; set; }
            public DateTime? DatumVaziDoPasosLicnaKarta { get; set; }
            public string? Email { get; set; }
            public string? Tel { get; set; }
            public string? Webstrana { get; set; }
            public bool SoglasnostZaDirektenMarketing { get; set; }
            public bool SoglasnostZaEmailKomunikacija { get; set; }
            public bool SoglasnostZaTelKomunikacija { get; set; }
            public DateTime? DatumNaPovlecenaSoglasnostZaDirektenMarketing { get; set; }
            public bool NositelNaJF { get; set; }
            public string? OsnovZaNositelNaJF { get; set; }
            public bool ZasilenaAnaliza { get; set; }
            public int? NivoaNaRizikIdNivoNaRizik { get; set; }
            public DateTime? DaumNaDogovor { get; set; }
            public DateTime? DogovorVaziDo { get; set; }
            public string? BrojNaDogovor { get; set; }
            public string? DogovorOpredelenoNeopredeleno { get; set; }
            public string? PlateznaSmetka { get; set; }
            public int? SifrarnikBankiIdBanka { get; set; }
            public long? SifrarnikRabotniPoziciiIdRabotnaPozicija { get; set; }
            public long? SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica { get; set; }
            public long? SifrarnikRabotniPoziciiIdNadreden { get; set; }
            public DateTime? NadredenDo { get; set; }
            public bool ZadolzitelnoLicenca { get; set; }
            public string? BrojNaResenieOdASOZaLicenca { get; set; }
            public DateTime? DatumNaResenieOdASOZaLicenca { get; set; }
            public DateTime? DatumNaOdzemenaLicenca { get; set; }
            public string? BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti { get; set; }
            public DateTime? DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti { get; set; }
            public bool KlientVraboten { get; set; }
            public bool KlientSorabotnik { get; set; }
            public DateTime? DatumNaOvlastuvanje { get; set; }
            public int? RokNaPlakanjeDenovi { get; set; }
            public string? NadredenImePrezime { get; set; }
            public DateTime? NadredenVaziOd { get; set; }
            public string? Zabeleska { get; set; }
            public int? EkspozituriIdEkspozitura { get; set; }
        }

        public async Task<IActionResult> OnGetAsync(long id)
        {
            if (!await HasPageAccess("DodajKlient"))
            {
                return RedirectToAccessDenied();
            }

            Id = id;

            // Load dropdown data
            await LoadDropdownData();

            // Load client data
            await LoadClientData(id);

            // Load files
            await LoadFiles(id);

            return Page();
        }

        private async Task LoadDropdownData()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // Load Opstini for both address types
                using (SqlCommand cmd = new SqlCommand("SELECT Id, Opstina FROM ListaOpstini", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["Opstina"].ToString(), reader["Id"].ToString()));
                    }
                    Opstini = new SelectList(items, "Value", "Text");
                }

                // Load NivoaNaRizik
                using (SqlCommand cmd = new SqlCommand("SELECT Id, OpisNivoRizik FROM NivoaNaRizik", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["OpisNivoRizik"].ToString(), reader["Id"].ToString()));
                    }
                    NivoaNaRizik = new SelectList(items, "Value", "Text");
                }

                // Load Banki
                using (SqlCommand cmd = new SqlCommand("SELECT Id, Banka FROM SifrarnikBanki", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["Banka"].ToString(), reader["Id"].ToString()));
                    }
                    Banki = new SelectList(items, "Value", "Text");
                }

                // Load RabotniPozicii
                using (SqlCommand cmd = new SqlCommand("SELECT Id, RabotnaPozicija FROM SifrarnikRabotniPozicii", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["RabotnaPozicija"].ToString(), reader["Id"].ToString()));
                    }
                    RabotniPozicii = new SelectList(items, "Value", "Text");
                }

                // Load OrganizacioniEdinici
                using (SqlCommand cmd = new SqlCommand("SELECT Id, OrganizacionaEdinica FROM SifrarnikOrganizacioniEdinici", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["OrganizacionaEdinica"].ToString(), reader["Id"].ToString()));
                    }
                    OrganizacioniEdinici = new SelectList(items, "Value", "Text");
                }

                // Load Ekspozituri
                using (SqlCommand cmd = new SqlCommand("SELECT Id, Ime FROM Ekspozituri", connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["Ime"].ToString(), reader["Id"].ToString()));
                    }
                    EkspozituriIdEkspozitura = new SelectList(items, "Value", "Text");
                }
                
            }
        }

        private async Task LoadClientData(long id)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                string sql = @"
                    SELECT k.*, 
                           CASE 
                               WHEN n.KlientFizickoPravnoLice = 'P' THEN n.Naziv
                               ELSE CONCAT(ISNULL(n.Ime, ''), ' ', ISNULL(n.Prezime, ''))
                           END as NadredenImePrezime
                    FROM Klienti k
                    LEFT JOIN Klienti n ON k.SifrarnikRabotniPoziciiIdNadreden = n.Id
                    WHERE k.Id = @Id";

                using (SqlCommand cmd = new SqlCommand(sql, connection))
                {
                    cmd.Parameters.AddWithValue("@Id", id);
                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            Input = new InputModel
                            {
                                Ime = reader["Ime"].ToString(),
                                Prezime = reader["Prezime"].ToString(),
                                EMBG = reader["EMBG"].ToString(),
                                ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija = reader.IsDBNull(reader.GetOrdinal("ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija")),
                                UlicaOdDokumentZaIdentifikacija = reader["UlicaOdDokumentZaIdentifikacija"].ToString(),
                                BrojOdDokumentZaIdentifikacija = reader["BrojOdDokumentZaIdentifikacija"].ToString(),
                                ListaOpstiniIdOpstinaZaKomunikacija = reader.IsDBNull(reader.GetOrdinal("ListaOpstiniIdOpstinaZaKomunikacija")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("ListaOpstiniIdOpstinaZaKomunikacija")),
                                UlicaZaKomunikacija = reader["UlicaZaKomunikacija"].ToString(),
                                BrojZaKomunikacija = reader["BrojZaKomunikacija"].ToString(),
                                BrojPasosLicnaKarta = reader["BrojPasosLicnaKarta"].ToString(),
                                DatumVaziOdPasosLicnaKarta = reader.IsDBNull(reader.GetOrdinal("DatumVaziOdPasosLicnaKarta")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DatumVaziOdPasosLicnaKarta")),
                                DatumVaziDoPasosLicnaKarta = reader.IsDBNull(reader.GetOrdinal("DatumVaziDoPasosLicnaKarta")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DatumVaziDoPasosLicnaKarta")),
                                Email = reader["Email"].ToString(),
                                Tel = reader["Tel"].ToString(),
                                Webstrana = reader["Webstrana"].ToString(),
                                SoglasnostZaDirektenMarketing = !reader.IsDBNull(reader.GetOrdinal("SoglasnostZaDirektenMarketing")) && reader.GetBoolean(reader.GetOrdinal("SoglasnostZaDirektenMarketing")),
                                SoglasnostZaEmailKomunikacija = !reader.IsDBNull(reader.GetOrdinal("SoglasnostZaEmailKomunikacija")) && reader.GetBoolean(reader.GetOrdinal("SoglasnostZaEmailKomunikacija")),
                                SoglasnostZaTelKomunikacija = !reader.IsDBNull(reader.GetOrdinal("SoglasnostZaTelKomunikacija")) && reader.GetBoolean(reader.GetOrdinal("SoglasnostZaTelKomunikacija")),
                                DatumNaPovlecenaSoglasnostZaDirektenMarketing = reader.IsDBNull(reader.GetOrdinal("DatumNaPovlecenaSoglasnostZaDirektenMarketing")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DatumNaPovlecenaSoglasnostZaDirektenMarketing")),
                                NositelNaJF = !reader.IsDBNull(reader.GetOrdinal("NositelNaJF")) && reader.GetBoolean(reader.GetOrdinal("NositelNaJF")),
                                OsnovZaNositelNaJF = reader["OsnovZaNositelNaJF"].ToString(),
                                ZasilenaAnaliza = !reader.IsDBNull(reader.GetOrdinal("ZasilenaAnaliza")) && reader.GetBoolean(reader.GetOrdinal("ZasilenaAnaliza")),
                                NivoaNaRizikIdNivoNaRizik = reader.IsDBNull(reader.GetOrdinal("NivoaNaRizikIdNivoNaRizik")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("NivoaNaRizikIdNivoNaRizik")),
                                DaumNaDogovor = reader.IsDBNull(reader.GetOrdinal("DaumNaDogovor")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DaumNaDogovor")),
                                DogovorVaziDo = reader.IsDBNull(reader.GetOrdinal("DogovorVaziDo")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DogovorVaziDo")),
                                BrojNaDogovor = reader["BrojNaDogovor"].ToString(),
                                DogovorOpredelenoNeopredeleno = reader["DogovorOpredelenoNeopredeleno"].ToString(),
                                PlateznaSmetka = reader["PlateznaSmetka"].ToString(),
                                SifrarnikBankiIdBanka = reader.IsDBNull(reader.GetOrdinal("SifrarnikBankiIdBanka")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("SifrarnikBankiIdBanka")),
                                SifrarnikRabotniPoziciiIdRabotnaPozicija = reader.IsDBNull(reader.GetOrdinal("SifrarnikRabotniPoziciiIdRabotnaPozicija")) ? null : (long?)reader.GetInt64(reader.GetOrdinal("SifrarnikRabotniPoziciiIdRabotnaPozicija")),
                                SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica = reader.IsDBNull(reader.GetOrdinal("SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica")) ? null : (long?)reader.GetInt64(reader.GetOrdinal("SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica")),
                                SifrarnikRabotniPoziciiIdNadreden = reader.IsDBNull(reader.GetOrdinal("SifrarnikRabotniPoziciiIdNadreden")) ? null : (long?)reader.GetInt64(reader.GetOrdinal("SifrarnikRabotniPoziciiIdNadreden")),
                                NadredenDo = reader.IsDBNull(reader.GetOrdinal("NadredenDo")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("NadredenDo")),
                                ZadolzitelnoLicenca = !reader.IsDBNull(reader.GetOrdinal("ZadolzitelnoLicenca")) && reader.GetBoolean(reader.GetOrdinal("ZadolzitelnoLicenca")),
                                BrojNaResenieOdASOZaLicenca = reader["BrojNaResenieOdASOZaLicenca"].ToString(),
                                DatumNaResenieOdASOZaLicenca = reader.IsDBNull(reader.GetOrdinal("DatumNaResenieOdASOZaLicenca")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DatumNaResenieOdASOZaLicenca")),
                                DatumNaOdzemenaLicenca = reader.IsDBNull(reader.GetOrdinal("DatumNaOdzemenaLicenca")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DatumNaOdzemenaLicenca")),
                                BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti = reader["BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti"].ToString(),
                                DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti = reader.IsDBNull(reader.GetOrdinal("DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti")),
                                KlientVraboten = !reader.IsDBNull(reader.GetOrdinal("KlientVraboten")) && reader.GetBoolean(reader.GetOrdinal("KlientVraboten")),
                                KlientSorabotnik = !reader.IsDBNull(reader.GetOrdinal("KlientSorabotnik")) && reader.GetBoolean(reader.GetOrdinal("KlientSorabotnik")),
                                DatumNaOvlastuvanje = reader.IsDBNull(reader.GetOrdinal("DatumNaOvlastuvanje")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("DatumNaOvlastuvanje")),
                                RokNaPlakanjeDenovi = reader.IsDBNull(reader.GetOrdinal("RokNaPlakanjeDenovi")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("RokNaPlakanjeDenovi")),
                                NadredenImePrezime = reader["NadredenImePrezime"].ToString(),
                                NadredenVaziOd = reader.IsDBNull(reader.GetOrdinal("NadredenVaziOd")) ? null : (DateTime?)reader.GetDateTime(reader.GetOrdinal("NadredenVaziOd")),
                                Zabeleska = reader["Zabeleska"].ToString(),
                                EkspozituriIdEkspozitura = reader.IsDBNull(reader.GetOrdinal("EkspozituriIdEkspozitura")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("EkspozituriIdEkspozitura"))
                            };
                        }
                    }
                }
            }
        }

        public async Task<IActionResult> OnPostAsync(long id)
        {
            Id = id;  // Set the Id property
            if (!await HasPageAccess("DodajKlient"))
            {
                return RedirectToAccessDenied();
            }

            if (!ModelState.IsValid)
            {
                await LoadDropdownData();
                return Page();
            }

            try
            {
                // Get username from session
                var currentUsername = HttpContext.Session.GetString("Username");
                if (string.IsNullOrEmpty(currentUsername))
                {
                    ModelState.AddModelError("", "Session expired. Please log in again.");
                    return RedirectToPage("/Login");
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // First verify if the client exists
                    string checkSql = "SELECT COUNT(*) FROM Klienti WHERE Id = @Id";
                    using (SqlCommand checkCmd = new SqlCommand(checkSql, connection))
                    {
                        checkCmd.Parameters.AddWithValue("@Id", Id);
                        int count = (int)await checkCmd.ExecuteScalarAsync();
                        System.Diagnostics.Debug.WriteLine($"Found {count} clients with ID {Id}");
                        
                        if (count == 0)
                        {
                            ModelState.AddModelError("", "Client not found in database.");
                            await LoadDropdownData();
                            return Page();
                        }
                    }

                    string sql = @"
                        UPDATE Klienti 
                        SET 
                            Ime = @Ime,
                            Prezime = @Prezime,
                            EMBG = @EMBG,
                            ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija = @ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija,
                            UlicaOdDokumentZaIdentifikacija = @UlicaOdDokumentZaIdentifikacija,
                            BrojOdDokumentZaIdentifikacija = @BrojOdDokumentZaIdentifikacija,
                            ListaOpstiniIdOpstinaZaKomunikacija = @ListaOpstiniIdOpstinaZaKomunikacija,
                            UlicaZaKomunikacija = @UlicaZaKomunikacija,
                            BrojZaKomunikacija = @BrojZaKomunikacija,
                            BrojPasosLicnaKarta = @BrojPasosLicnaKarta,
                            DatumVaziOdPasosLicnaKarta = @DatumVaziOdPasosLicnaKarta,
                            DatumVaziDoPasosLicnaKarta = @DatumVaziDoPasosLicnaKarta,
                            Email = @Email,
                            Tel = @Tel,
                            Webstrana = @Webstrana,
                            SoglasnostZaDirektenMarketing = @SoglasnostZaDirektenMarketing,
                            SoglasnostZaEmailKomunikacija = @SoglasnostZaEmailKomunikacija,
                            SoglasnostZaTelKomunikacija = @SoglasnostZaTelKomunikacija,
                            DatumNaPovlecenaSoglasnostZaDirektenMarketing = @DatumNaPovlecenaSoglasnostZaDirektenMarketing,
                            NositelNaJF = @NositelNaJF,
                            OsnovZaNositelNaJF = @OsnovZaNositelNaJF,
                            ZasilenaAnaliza = @ZasilenaAnaliza,
                            NivoaNaRizikIdNivoNaRizik = @NivoaNaRizikIdNivoNaRizik,
                            DaumNaDogovor = @DaumNaDogovor,
                            DogovorVaziDo = @DogovorVaziDo,
                            BrojNaDogovor = @BrojNaDogovor,
                            DogovorOpredelenoNeopredeleno = @DogovorOpredelenoNeopredeleno,
                            PlateznaSmetka = @PlateznaSmetka,
                            SifrarnikBankiIdBanka = @SifrarnikBankiIdBanka,
                            SifrarnikRabotniPoziciiIdRabotnaPozicija = @SifrarnikRabotniPoziciiIdRabotnaPozicija,
                            SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica = @SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica,
                            SifrarnikRabotniPoziciiIdNadreden = @SifrarnikRabotniPoziciiIdNadreden,
                            NadredenVaziOd = @NadredenVaziOd,
                            NadredenDo = @NadredenDo,
                            ZadolzitelnoLicenca = @ZadolzitelnoLicenca,
                            BrojNaResenieOdASOZaLicenca = @BrojNaResenieOdASOZaLicenca,
                            DatumNaResenieOdASOZaLicenca = @DatumNaResenieOdASOZaLicenca,
                            DatumNaOdzemenaLicenca = @DatumNaOdzemenaLicenca,
                            BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti = @BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti,
                            DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti = @DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti,
                            KlientVraboten = @KlientVraboten,
                            KlientSorabotnik = @KlientSorabotnik,
                            DatumNaOvlastuvanje = @DatumNaOvlastuvanje,
                            RokNaPlakanjeDenovi = @RokNaPlakanjeDenovi,
                            Zabeleska = @Zabeleska,
                            EkspozituriIdEkspozitura = @EkspozituriIdEkspozitura,
                            DateModified = GETDATE(),
                            UsernameModified = @UsernameModified
                        WHERE Id = @Id";

                    using (SqlCommand cmd = new SqlCommand(sql, connection))
                    {
                        // Debug parameter values
                        System.Diagnostics.Debug.WriteLine($"Update parameters:");
                        System.Diagnostics.Debug.WriteLine($"Id: {Id}");
                        System.Diagnostics.Debug.WriteLine($"Ime: {Input.Ime}");
                        System.Diagnostics.Debug.WriteLine($"Prezime: {Input.Prezime}");

                        cmd.Parameters.AddWithValue("@Id", Id);
                        cmd.Parameters.AddWithValue("@UsernameModified", currentUsername);
                        cmd.Parameters.AddWithValue("@Ime", Input.Ime ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Prezime", Input.Prezime ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@EMBG", Input.EMBG ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija", Input.ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@UlicaOdDokumentZaIdentifikacija", Input.UlicaOdDokumentZaIdentifikacija ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojOdDokumentZaIdentifikacija", Input.BrojOdDokumentZaIdentifikacija ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ListaOpstiniIdOpstinaZaKomunikacija", Input.ListaOpstiniIdOpstinaZaKomunikacija ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@UlicaZaKomunikacija", Input.UlicaZaKomunikacija ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojZaKomunikacija", Input.BrojZaKomunikacija ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojPasosLicnaKarta", Input.BrojPasosLicnaKarta ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumVaziOdPasosLicnaKarta", Input.DatumVaziOdPasosLicnaKarta ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumVaziDoPasosLicnaKarta", Input.DatumVaziDoPasosLicnaKarta ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Email", Input.Email ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Tel", Input.Tel ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Webstrana", Input.Webstrana ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SoglasnostZaDirektenMarketing", Input.SoglasnostZaDirektenMarketing);
                        cmd.Parameters.AddWithValue("@SoglasnostZaEmailKomunikacija", Input.SoglasnostZaEmailKomunikacija);
                        cmd.Parameters.AddWithValue("@SoglasnostZaTelKomunikacija", Input.SoglasnostZaTelKomunikacija);
                        cmd.Parameters.AddWithValue("@DatumNaPovlecenaSoglasnostZaDirektenMarketing", Input.DatumNaPovlecenaSoglasnostZaDirektenMarketing ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@NositelNaJF", Input.NositelNaJF);
                        cmd.Parameters.AddWithValue("@OsnovZaNositelNaJF", Input.OsnovZaNositelNaJF ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ZasilenaAnaliza", Input.ZasilenaAnaliza);
                        cmd.Parameters.AddWithValue("@NivoaNaRizikIdNivoNaRizik", Input.NivoaNaRizikIdNivoNaRizik ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DaumNaDogovor", Input.DaumNaDogovor ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DogovorVaziDo", Input.DogovorVaziDo ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaDogovor", Input.BrojNaDogovor ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DogovorOpredelenoNeopredeleno", Input.DogovorOpredelenoNeopredeleno ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@PlateznaSmetka", Input.PlateznaSmetka ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SifrarnikBankiIdBanka", Input.SifrarnikBankiIdBanka ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SifrarnikRabotniPoziciiIdRabotnaPozicija", Input.SifrarnikRabotniPoziciiIdRabotnaPozicija ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica", Input.SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SifrarnikRabotniPoziciiIdNadreden", Input.SifrarnikRabotniPoziciiIdNadreden ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@NadredenVaziOd", Input.NadredenVaziOd ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@NadredenDo", Input.NadredenDo ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ZadolzitelnoLicenca", Input.ZadolzitelnoLicenca);
                        cmd.Parameters.AddWithValue("@BrojNaResenieOdASOZaLicenca", Input.BrojNaResenieOdASOZaLicenca ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumNaResenieOdASOZaLicenca", Input.DatumNaResenieOdASOZaLicenca ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumNaOdzemenaLicenca", Input.DatumNaOdzemenaLicenca ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti", Input.BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti", Input.DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@KlientVraboten", Input.KlientVraboten);
                        cmd.Parameters.AddWithValue("@KlientSorabotnik", Input.KlientSorabotnik);
                        cmd.Parameters.AddWithValue("@DatumNaOvlastuvanje", Input.DatumNaOvlastuvanje ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@RokNaPlakanjeDenovi", Input.RokNaPlakanjeDenovi ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Zabeleska", Input.Zabeleska ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@EkspozituriIdEkspozitura", Input.EkspozituriIdEkspozitura ?? (object)DBNull.Value);
                        int rowsAffected = await cmd.ExecuteNonQueryAsync();
                        System.Diagnostics.Debug.WriteLine($"Rows affected by update: {rowsAffected}");

                        if (rowsAffected == 0)
                        {
                            System.Diagnostics.Debug.WriteLine("Update failed - no rows affected");
                            ModelState.AddModelError("", "No records were updated. Please check if the client still exists.");
                            await LoadDropdownData();
                            return Page();
                        }
                    }
                }

                TempData["SuccessMessage"] = "Клиентот е успешно ажуриран.";
                return RedirectToPage("/Klienti/ListaKlienti");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", $"Error updating client: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in OnPostAsync: {ex}");
                await LoadDropdownData();
                return Page();
            }
        }

        private async Task LoadFiles(long clientId)
        {
            Files = new List<KlientiFile>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                string sql = @"SELECT Id, FileName, FilePath, DateCreated, UsernameCreated 
                             FROM KlientiFileSystem 
                             WHERE KlientId = @KlientId 
                             ORDER BY DateCreated DESC";

                using (SqlCommand command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@KlientId", clientId);
                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            Files.Add(new KlientiFile
                            {
                                Id = reader.GetInt64(0),
                                FileName = reader.GetString(1),
                                FilePath = reader.GetString(2),
                                DateCreated = reader.GetDateTime(3),
                                UsernameCreated = reader.GetString(4)
                            });
                        }
                    }
                }
            }
        }

        public async Task<IActionResult> OnPostUploadFileAsync(List<IFormFile> files, long klientId)
        {
           

            if (files == null || !files.Any())
            {
                TempData["ErrorMessage"] = "Не се избрани документи за прикачување.";
                return RedirectToPage(new { id = klientId });
            }

            var username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                TempData["ErrorMessage"] = "Корисничката сесија е истечена.";
                return RedirectToPage(new { id = klientId });
            }

            var sftpConfig = _configuration.GetSection("SftpConfig").Get<Dictionary<string, string>>();
            var uploadDir = "/upload/Klienti";

            using (var client = new SftpClient(sftpConfig["Host"], int.Parse(sftpConfig["Port"]), sftpConfig["Username"], sftpConfig["Password"]))
            {
                try
                {
                    client.Connect();
                    if (!client.Exists("/upload"))
                    {
                        client.CreateDirectory("/upload");
                    }

                    if (!client.Exists(uploadDir))
                    {
                        client.CreateDirectory(uploadDir);
                    }

                    foreach (var file in files)
                    {
                        var fileName = Path.GetFileName(file.FileName);
                        var fileExt = Path.GetExtension(fileName);
                        var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
                        var randomChars = GenerateRandomString(10);
                        var uniqueFileName = $"Klient_{klientId}_{timestamp}_{randomChars}{fileExt}";
                        var filePath = $"{uploadDir}/{uniqueFileName}";

                        using (var ms = new MemoryStream())
                        {
                            await file.CopyToAsync(ms);
                            ms.Position = 0;
                            client.UploadFile(ms, filePath);

                            string connectionString = _configuration.GetConnectionString("DefaultConnection");
                            using (SqlConnection connection = new SqlConnection(connectionString))
                            {
                                await connection.OpenAsync();
                                string sql = @"INSERT INTO KlientiFileSystem (KlientId, FileName, FilePath, DateCreated, UsernameCreated) 
                                             VALUES (@KlientId, @FileName, @FilePath, @DateCreated, @UsernameCreated)";

                                using (SqlCommand command = new SqlCommand(sql, connection))
                                {
                                    command.Parameters.AddWithValue("@KlientId", klientId);
                                    command.Parameters.AddWithValue("@FileName", fileName);
                                    command.Parameters.AddWithValue("@FilePath", filePath);
                                    command.Parameters.AddWithValue("@DateCreated", DateTime.Now);
                                    command.Parameters.AddWithValue("@UsernameCreated", username);
                                    await command.ExecuteNonQueryAsync();
                                }
                            }
                        }
                    }

                    TempData["SuccessMessage"] = "Документите се успешно прикачени.";
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = $"Грешка при прикачување на документите: {ex.Message}";
                }
                finally
                {
                    if (client.IsConnected)
                    {
                        client.Disconnect();
                    }
                }
            }

            return RedirectToPage(new { id = klientId });
        }

        public async Task<IActionResult> OnGetDownloadFileAsync(long fileId)
        {
           

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            string fileName = string.Empty;
            string filePath = string.Empty;

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                string sql = "SELECT FileName, FilePath FROM KlientiFileSystem WHERE Id = @FileId";

                using (SqlCommand command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@FileId", fileId);
                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            fileName = reader.GetString(0);
                            filePath = reader.GetString(1);
                        }
                        else
                        {
                            return NotFound("Документот не е пронајден.");
                        }
                    }
                }
            }

            var sftpConfig = _configuration.GetSection("SftpConfig").Get<Dictionary<string, string>>();

            using (var client = new SftpClient(sftpConfig["Host"], int.Parse(sftpConfig["Port"]), sftpConfig["Username"], sftpConfig["Password"]))
            {
                try
                {
                    client.Connect();

                    if (!client.Exists(filePath))
                    {
                        return NotFound("Документот не е пронајден на серверот.");
                    }

                    using (var ms = new MemoryStream())
                    {
                        client.DownloadFile(filePath, ms);
                        ms.Position = 0;
                        return File(ms.ToArray(), "application/octet-stream", fileName);
                    }
                }
                catch (Exception ex)
                {
                    return StatusCode(500, $"Грешка при преземање на документот: {ex.Message}");
                }
            }
        }

        public async Task<IActionResult> OnPostDeleteFileAsync(long fileId)
        {
            
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            string filePath = string.Empty;
            long klientId = 0;

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                string sql = "SELECT FilePath, KlientId FROM KlientiFileSystem WHERE Id = @FileId";

                using (SqlCommand command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@FileId", fileId);
                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            filePath = reader.GetString(0);
                            klientId = reader.GetInt64(1);
                        }
                        else
                        {
                            TempData["ErrorMessage"] = "Документот не е пронајден.";
                            return RedirectToPage(new { id = klientId });
                        }
                    }
                }

                var sftpConfig = _configuration.GetSection("SftpConfig").Get<Dictionary<string, string>>();

                using (var client = new SftpClient(sftpConfig["Host"], int.Parse(sftpConfig["Port"]), sftpConfig["Username"], sftpConfig["Password"]))
                {
                    try
                    {
                        client.Connect();

                        if (client.Exists(filePath))
                        {
                            client.DeleteFile(filePath);
                        }

                        sql = "DELETE FROM KlientiFileSystem WHERE Id = @FileId";
                        using (SqlCommand command = new SqlCommand(sql, connection))
                        {
                            command.Parameters.AddWithValue("@FileId", fileId);
                            await command.ExecuteNonQueryAsync();
                        }

                        TempData["SuccessMessage"] = "Документот е успешно избришан.";
                    }
                    catch (Exception ex)
                    {
                        TempData["ErrorMessage"] = $"Грешка при бришење на документот: {ex.Message}";
                    }
                }
            }

            return RedirectToPage(new { id = klientId });
        }

        private string GenerateRandomString(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        public async Task<IActionResult> OnGetSearchNadredenAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE KlientVraboten = 1
                       AND (MB LIKE @Search + '%'
                       OR EDB LIKE @Search + '%'
                       OR EMBG LIKE @Search + '%'
                       OR Naziv LIKE '%' + @Search + '%'
                       OR Ime LIKE '%' + @Search + '%'
                       OR Prezime LIKE '%' + @Search + '%')
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }
    }
} 