using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.SignalR;
using NextBroker.Hubs;
using NextBroker.Services;

namespace NextBroker.Pages.Notifications
{
    public class UserNotificationsModel : PageModel
    {
        private readonly NotificationService _notificationService;
        private readonly IHubContext<NotificationHub> _hubContext;

        public UserNotificationsModel(
            NotificationService notificationService,
            IHubContext<NotificationHub> hubContext)
        {
            _notificationService = notificationService;
            _hubContext = hubContext;
        }

        public IActionResult OnGet()
        {
            return Page();
        }

        public async Task<IActionResult> OnGetNotifications()
        {
            var username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                return new JsonResult(new List<NotificationModel>());
            }
            var notifications = await _notificationService.GetLatestNotifications(username);
            return new JsonResult(notifications);
        }

        public async Task<IActionResult> OnPostMarkAsRead(long id)
        {
            var username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                return new JsonResult(new { success = false });
            }
            
            await _notificationService.MarkAsRead(id, username);
            await _hubContext.Clients.All.SendAsync("ReceiveNotifications");
            return RedirectToPage();
        }

        public async Task<IActionResult> OnGetUnreadCount()
        {
            var username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                return new JsonResult(0);
            }
            var count = await _notificationService.GetUnreadCount(username);
            return new JsonResult(count);
        }
    }
} 