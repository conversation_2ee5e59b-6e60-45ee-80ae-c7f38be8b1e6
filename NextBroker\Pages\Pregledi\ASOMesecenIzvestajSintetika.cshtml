@page
@model NextBroker.Pages.Pregledi.ASOMesecenIzvestajSintetikaModel
@{
    ViewData["Title"] = "АСО Месечен Извештај";
}

<div class="container-fluid">
    <h2>@ViewData["Title"]</h2>

    <form method="post" class="mb-4">
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="SelectedMonth">Месец</label>
                    <select asp-for="SelectedMonth" class="form-control" asp-items="@(new SelectList(Model.Months, "Value", "Name"))">
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="Year">Година</label>
                    <input asp-for="Year" class="form-control" type="number" />
                </div>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">Генерирај извештај</button>
                @if (Model.ReportData != null && Model.ReportData.Rows.Count > 0)
                {
                    <button type="submit" asp-page-handler="ExportExcel" class="btn btn-success">
                        <i class="fas fa-file-excel me-1"></i> Export Excel
                    </button>
                }
            </div>
        </div>
    </form>

    @if (Model.ReportData != null && Model.ReportData.Rows.Count > 0)
    {
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        @foreach (System.Data.DataColumn column in Model.ReportData.Columns)
                        {
                            <th>@column.ColumnName</th>
                        }
                    </tr>
                </thead>
                <tbody>
                    @foreach (System.Data.DataRow row in Model.ReportData.Rows)
                    {
                        <tr>
                            @foreach (var item in row.ItemArray)
                            {
                                <td>
                                    @if (item is DateTime date)
                                    {
                                        @date.ToString("dd/MM/yyyy")
                                    }
                                    else if (item is decimal decimalValue)
                                    {
                                        @decimalValue.ToString("N2")
                                    }
                                    else if (item is double doubleValue)
                                    {
                                        @doubleValue.ToString("N2")
                                    }
                                    else if (item is float floatValue)
                                    {
                                        @floatValue.ToString("N2")
                                    }
                                    else
                                    {
                                        @item
                                    }
                                </td>
                            }
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
    else if (Model.ReportData != null)
    {
        <div class="alert alert-info">
            Нема податоци за избраниот период.
        </div>
    }
</div>
