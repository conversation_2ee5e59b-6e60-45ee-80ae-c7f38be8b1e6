using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Data.SqlClient;
using RazorPortal.Services;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace RazorPortal.Pages
{
    public class UserPortalHomeModel : PageModel
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<UserPortalHomeModel> _logger;

        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string Username { get; set; }
        public string Emb { get; set; }
        public string EkspozituraName { get; set; }
        public List<string> AccessiblePages { get; set; }

        public UserPortalHomeModel(IConfiguration configuration, ILogger<UserPortalHomeModel> logger)
        {
            _configuration = configuration;
            _logger = logger;
            AccessiblePages = new List<string>();
        }

        public void OnGet()
        {
            var username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                Response.Redirect("/Login");
                return;
            }

            try
            {
                using (SqlConnection connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    connection.Open();

                    // First, get the EkspozituraId using the stored function
                    string functionSql = "SELECT dbo.VratiIdEkspozituraPoUsername(@Username)";
                    using (SqlCommand functionCommand = new SqlCommand(functionSql, connection))
                    {
                        functionCommand.Parameters.AddWithValue("@Username", username);
                        var functionResult = functionCommand.ExecuteScalar();
                        if (functionResult != null && functionResult != DBNull.Value)
                        {
                            HttpContext.Session.SetInt32("EkspozituraId", (int)functionResult);
                        }
                    }

                    string sql = @"SELECT firstname, lastname, email, phone, username, emb 
                                 FROM Users 
                                 WHERE username = @Username";

                    using (SqlCommand command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@Username", username);
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                FirstName = reader["firstname"].ToString();
                                LastName = reader["lastname"].ToString();
                                Email = reader["email"].ToString();
                                Phone = reader["phone"].ToString();
                                Username = reader["username"].ToString();
                                Emb = reader["emb"].ToString();
                            }
                        }
                    }

                    // Get Ekspozitura name
                    var storedEkspozituraId = HttpContext.Session.GetInt32("EkspozituraId");
                    if (storedEkspozituraId.HasValue)
                    {
                        string ekspSql = "SELECT Ime FROM Ekspozituri WHERE Id = @Id";
                        using (SqlCommand ekspCommand = new SqlCommand(ekspSql, connection))
                        {
                            ekspCommand.Parameters.AddWithValue("@Id", storedEkspozituraId.Value);
                            var ekspName = ekspCommand.ExecuteScalar();
                            if (ekspName != null && ekspName != DBNull.Value)
                            {
                                EkspozituraName = ekspName.ToString();
                            }
                        }
                    }

                    // Load accessible pages (assuming you have a method for this)
                    AccessiblePages = Services.SecurePageModel.GetUserAccessiblePages(username, _configuration).Result;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error loading user information: {ex.Message}");
            }
        }
    }
}
