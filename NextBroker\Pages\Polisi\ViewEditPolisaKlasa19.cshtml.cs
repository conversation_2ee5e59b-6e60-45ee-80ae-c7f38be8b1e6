using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Renci.SshNet;
using System.IO;
using System.Text;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Data;
using System.Runtime.Serialization;
using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace NextBroker.Pages.Polisi
{
    public class ViewEditPolisaKlasa19Model : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        private readonly IHttpContextAccessor _httpContextAccessor;
        public bool HasAdminAccess { get; private set; }
        public bool HasStornoAccess { get; private set; }

        public ViewEditPolisaKlasa19Model(IConfiguration configuration, IHttpContextAccessor httpContextAccessor)
            : base(configuration)
        {
            _configuration = configuration;
            _httpContextAccessor = httpContextAccessor;
        }

        [BindProperty]
        public PolisaViewModel Input { get; set; } = new();

        [BindProperty]
        public PolisiKlasa19ViewModel InputZivot { get; set; } = new();

        public IEnumerable<SelectListItem> Osiguriteli { get; set; }
        public IEnumerable<SelectListItem> KlasiOsiguruvanje { get; set; }
        public IEnumerable<SelectListItem> Produkti { get; set; }
        public IEnumerable<SelectListItem> Valuti { get; set; }
        public IEnumerable<SelectListItem> NaciniNaPlakjanje { get; set; }
        public IEnumerable<SelectListItem> TipoviNaPlakanje { get; set; }
        public IEnumerable<SelectListItem> Banki { get; set; }
        public IEnumerable<SelectListItem> ListaDejnosti { get; set; }
        public IEnumerable<SelectListItem> TipoviNaFaktura { get; set; }

        public List<FileInfo> Files { get; set; }

        public System.Data.DataTable KarticaData { get; set; }

        public class ZadolzuvanjeInfo
        {
            public string Zadolzen { get; set; }
            public DateTime? DatumNaZadolzuvanje { get; set; }
            public string OsnovZaRazdolzuvanje { get; set; }
            public bool PotvrdenoRazdolzuvanjeKajBroker { get; set; }
            public DateTime? DatumNaRazdolzuvanjeKajBroker { get; set; }
            public bool PotvrdenoRazdolzuvanjeKajOsiguritel { get; set; }
            public DateTime? DatumNaRazdolzuvanjeKajOsiguritel { get; set; }
        }

        public ZadolzuvanjeInfo ZadolzuvanjeData { get; set; }

        // OZ Information properties
        public decimal OZIznosIzleznaFakturaPremija { get; set; }
        public decimal OZIznosPolisa { get; set; }

        public class PolisaViewModel
        {
            public long Id { get; set; }
            public DateTime? DateCreated { get; set; }
            public string? UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string? UsernameModified { get; set; }
            public long? KlientiIdOsiguritel { get; set; }
            public int? KlasiOsiguruvanjeIdKlasa { get; set; }
            public int? ProduktiIdProizvod { get; set; }
            public string? BrojNaPolisa { get; set; }
            public long? BrojNaPonuda { get; set; }
            public long? KlientiIdDogovoruvac { get; set; }
            public long? KlientiIdOsigurenik { get; set; }
            public bool Kolektivna { get; set; }
            public bool KolektivnaNeodredenBrOsigurenici { get; set; }
            public string? NeodredenBrOsigureniciZabeleska { get; set; }
            public DateTime? DatumVaziOd { get; set; }
            public DateTime? DatumVaziDo { get; set; }
            public DateTime? DatumNaIzdavanje { get; set; }
            public int? VremetraenjeNaPolisa { get; set; }
            public int? PeriodNaUplata { get; set; }
            public long SifrarnikValutiIdValuta { get; set; }
            public long? SifrarnikValutiIdFranshizaValuta { get; set; }
            public long? KlientiIdSorabotnik { get; set; }
            public bool Faktoring { get; set; }
            public decimal? ProcentFranshiza { get; set; }
            public decimal? ProcentFinansiski { get; set; }
            public decimal? KoregiranaStapkaNaProvizija { get; set; }
            public long? SifrarnikNacinNaPlakjanjeId { get; set; }
            public string? TipNaFaktura { get; set; }
            public string? BrojNaFakturaVlezna { get; set; }
            public DateTime? DatumNaFakturaVlezna { get; set; }
            public DateTime? RokNaPlakjanjeFakturaVlezna { get; set; }
            public long? SifrarnikTipNaPlakanjeId { get; set; }
            public long? SifrarnikBankiIdBanka { get; set; }
            public bool GeneriranaFakturaIzlezna { get; set; }
            public string? BrojNaFakturaIzlezna { get; set; }
            public DateTime? DatumNaIzleznaFaktura { get; set; }
            public DateTime? RokNaPlakjanjeFakturaIzlezna { get; set; }
            public bool Storno { get; set; }
            public string? PricinaZaStorno { get; set; }
            public string? Zabeleska { get; set; }
            public decimal? FranshizaIznos { get; set; }
        }

        public class PolisiKlasa19ViewModel
        {
            public long Id { get; set; }
            public DateTime? DateCreated { get; set; }
            public string? UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string? UsernameModified { get; set; }
            public long PolisaId { get; set; }
            public int? ListaDejnostiId { get; set; }
            public int? BrojNaOsigurenici { get; set; }
            public decimal? PremijaZaEdnoLice { get; set; }
            public decimal? OsigurenaSumaZaDozhivuvanje { get; set; }
            public decimal? OsigurenaSumaZaSmrtOdBolest { get; set; }
            public decimal? OsigurenaSumaZaSmrtOdNezgoda { get; set; }
            public decimal? PrivremenaOsiguritelnaZastita { get; set; }
            public decimal? PremijaGodishnaEdnokratna { get; set; }
            public decimal? OsigurenaSumaZa100PercentTraenInvaliditet { get; set; }
            public decimal? OsigurenaSumaZaTraenInvaliditet { get; set; }
            public decimal? DnevenNadomest { get; set; }
            public decimal? PremijaNezgodaGodishna { get; set; }
            public decimal? OsigurenaSumaZaTeskoBolniSostojbi { get; set; }
            public decimal? PremijaTeshkoBolniSostojbiGodishna { get; set; }
            public decimal? OsigurenaSumaZaOperacii { get; set; }
            public decimal? PremijaZaOperaciiGodishna { get; set; }
            public decimal? OsigurenaSumazaTrajnaNesposobnost { get; set; }
            public decimal? PremijaZaTrajnaNesposobnostGodishna { get; set; }
            public decimal? OsigurenaSumaZaHirushkiIntervencii { get; set; }
            public decimal? PremijaZaHirushkiIntervenciiGodishna { get; set; }
            public decimal? VkupnaPremijaGodishna { get; set; }
            public decimal? VkupnaPremijaEdnokratna { get; set; }
            public decimal? DoplatociZaPodgodishnoPlakjanje { get; set; }
            public decimal? ProcentNaPopustZaFakturaVoRok { get; set; }
            public decimal? IznosZaPlakjanjeVoRok { get; set; }
            public decimal? ProcentKomercijalenPopust { get; set; }
            public decimal? ProcentFinansiski { get; set; }
            public decimal? PremijaZaNaplata { get; set; }
            public decimal? VkupnaPremija { get; set; }
        }

        public class FileInfo
        {
            public long Id { get; set; }
            public string FileName { get; set; }
            public DateTime DateCreated { get; set; }
            public string UsernameCreated { get; set; }
        }

        public class OsigurenikKolektivno
        {
            public long Id { get; set; }
            public string Ime { get; set; }
            public string Prezime { get; set; }
            public string EMBG { get; set; }
            public string BrojNaLicnaKarta { get; set; }
            public string BrojNaPasos { get; set; }
            public string ListaOpstiniId { get; set; }
            public string OpstinaNaziv { get; set; }
            public string Adresa { get; set; }
            public string Broj { get; set; }
            public string Telefon { get; set; }
            public string Email { get; set; }
        }

        public List<OsigurenikKolektivno> OsigureniciKolektivno { get; set; }

        private async Task LoadOsiguriteli()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Naziv 
                    FROM Klienti 
                    WHERE Osiguritel = 1 
                    ORDER BY Naziv", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Naziv"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Osiguriteli = items;
                }
            }
        }

          public int BrojNaRatiPolisa { get; set; }

        private async Task LoadBrojNaRatiPolisa(long polisaId)
        {
            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    var command = new SqlCommand("SELECT dbo.VratiPolisaVkupenBrojNaRati(@PolisaId)", connection);
                    command.Parameters.AddWithValue("@PolisaId", polisaId);
                    var result = await command.ExecuteScalarAsync();
                    BrojNaRatiPolisa = result != DBNull.Value ? Convert.ToInt32(result) : 0;
                }
            }
            catch (Exception)
            {
                BrojNaRatiPolisa = 0;
            }
        }


        private async Task LoadKlasiOsiguruvanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, CONCAT(KlasaBroj, ' - ', KlasaIme) as DisplayName 
                    FROM KlasiOsiguruvanje 
                    WHERE KlasaBroj = 19 
                    AND (Disabled = 0 OR Disabled IS NULL)", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayName"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    KlasiOsiguruvanje = items;
                }
            } 
        }

        private async Task LoadProdukti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Ime 
                    FROM Produkti 
                    WHERE KlasaOsiguruvanjeId = 19", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Ime"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Produkti = items;
                }
            }
        }

        private async Task LoadValuti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Valuta 
                    FROM SifrarnikValuti 
                    ORDER BY Valuta", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Valuta"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Valuti = items;
                }
            }
        }

        private async Task LoadNaciniNaPlakjanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, NacinNaPlakanje 
                    FROM SifrarnikNacinNaPlakanje 
                    ORDER BY NacinNaPlakanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["NacinNaPlakanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    NaciniNaPlakjanje = items;
                }
            }
        }

        private async Task LoadTipoviNaPlakanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, TipNaPlakanje 
                    FROM SifrarnikTipNaPlakanje 
                    ORDER BY TipNaPlakanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["TipNaPlakanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    TipoviNaPlakanje = items;
                }
            }
        }

        private async Task LoadBanki()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Banka 
                    FROM SifrarnikBanki 
                    ORDER BY Banka", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Banka"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Banki = items;
                }
            }
        }

        private async Task LoadListaDejnosti()
        {
            using SqlConnection connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection"));
            await connection.OpenAsync();

            using SqlCommand cmd = new SqlCommand(@"
                SELECT Id, NazivDejnost
                FROM ListaDejnosti
                ORDER BY NazivDejnost", connection);

            using SqlDataReader reader = await cmd.ExecuteReaderAsync();
            var items = new List<SelectListItem>();

            while (await reader.ReadAsync())
            {
                items.Add(new SelectListItem
                {
                    Value = reader.GetInt32(0).ToString(),
                    Text = reader.GetString(1)
                });
            }

            ListaDejnosti = items;
        }

        private async Task LoadFiles()
        {
            Files = new List<FileInfo>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand command = new SqlCommand(
                    "SELECT Id, FileName, DateCreated, UsernameCreated " +
                    "FROM PolisiFileSystem " +
                    "WHERE PolisaId = @PolisaId " +
                    "ORDER BY DateCreated DESC", connection))
                {
                    command.Parameters.AddWithValue("@PolisaId", Input.Id);

                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            Files.Add(new FileInfo
                            {
                                Id = reader.GetInt64(0),
                                FileName = reader.GetString(1),
                                DateCreated = reader.GetDateTime(2),
                                UsernameCreated = reader.GetString(3)
                            });
                        }
                    }
                }
            }
        }

        private async Task LoadOsigureniciKolektivno()
        {
            OsigureniciKolektivno = new List<OsigurenikKolektivno>();
            
            using (SqlConnection connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();

                string query = @"
                    SELECT 
                        pok.*,
                        lo.Opstina as OpstinaNaziv
                    FROM PolisiOsigureniciKolektivno pok
                    LEFT JOIN ListaOpstini lo ON lo.Id = TRY_CAST(pok.ListaOpstiniId AS INT)
                    WHERE pok.PolisaId = @PolisaId
                    ORDER BY pok.Id";

                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@PolisaId", Input.Id);

                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            OsigureniciKolektivno.Add(new OsigurenikKolektivno
                            {
                                Id = reader.GetInt64(reader.GetOrdinal("Id")),
                                Ime = reader.IsDBNull(reader.GetOrdinal("Ime")) ? null : reader.GetString(reader.GetOrdinal("Ime")),
                                Prezime = reader.IsDBNull(reader.GetOrdinal("Prezime")) ? null : reader.GetString(reader.GetOrdinal("Prezime")),
                                EMBG = reader.IsDBNull(reader.GetOrdinal("EMBG")) ? null : reader.GetString(reader.GetOrdinal("EMBG")),
                                BrojNaLicnaKarta = reader.IsDBNull(reader.GetOrdinal("BrojNaLicnaKarta")) ? null : reader.GetString(reader.GetOrdinal("BrojNaLicnaKarta")),
                                BrojNaPasos = reader.IsDBNull(reader.GetOrdinal("BrojNaPasos")) ? null : reader.GetString(reader.GetOrdinal("BrojNaPasos")),
                                ListaOpstiniId = reader.IsDBNull(reader.GetOrdinal("ListaOpstiniId")) ? null : reader.GetString(reader.GetOrdinal("ListaOpstiniId")),
                                OpstinaNaziv = reader.IsDBNull(reader.GetOrdinal("OpstinaNaziv")) ? null : reader.GetString(reader.GetOrdinal("OpstinaNaziv")),
                                Adresa = reader.IsDBNull(reader.GetOrdinal("Adresa")) ? null : reader.GetString(reader.GetOrdinal("Adresa")),
                                Broj = reader.IsDBNull(reader.GetOrdinal("Broj")) ? null : reader.GetString(reader.GetOrdinal("Broj")),
                                Telefon = reader.IsDBNull(reader.GetOrdinal("Telefon")) ? null : reader.GetString(reader.GetOrdinal("Telefon")),
                                Email = reader.IsDBNull(reader.GetOrdinal("Email")) ? null : reader.GetString(reader.GetOrdinal("Email"))
                            });
                        }
                    }
                }
            }
        }

        private async Task LoadKarticaData(long polisaId)
        {
            using (SqlConnection conn = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await conn.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT 
                        [ID],
                        [Рата број],
                        [Датум на доспевање],
                        [Износ на рата],
                        [Датум на уплата],
                        [Уплатен износ],
                        [Затворена рата],
                        [Сторно]
                    FROM PolisaKartica 
                    WHERE PolisaID = @PolisaID
                    ORDER BY [Рата број]", conn))
                {
                    cmd.Parameters.AddWithValue("@PolisaID", polisaId);
                    using (SqlDataAdapter adapter = new SqlDataAdapter(cmd))
                    {
                        KarticaData = new System.Data.DataTable();
                        adapter.Fill(KarticaData);
                    }
                }
            }
        }

        private async Task LoadZadolzuvanjeData()
        {
            if (string.IsNullOrEmpty(Input.BrojNaPolisa)) return;
            
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT
                        CAST(
                            COALESCE(kl.Ime, '') + ' ' + 
                            COALESCE(kl.Prezime, '') + ' ' + 
                            COALESCE(kl.Naziv, '') 
                        AS VARCHAR(MAX)) AS Zadolzen,
                        pzr.DatumNaZadolzuvanje,
                        sozr.OsnovZaRazdolzuvanje,
                        pzr.PotvrdenoRazdolzuvanjeKajBroker,
                        pzr.DatumNaRazdolzuvanjeKajBroker,
                        pzr.PotvrdenoRazdolzuvanjeKajOsiguritel,
                        pzr.DatumNaRazdolzuvanjeKajOsiguritel
                    FROM PolisiZadolzuvanjeRazdolzuvanje pzr
                    LEFT JOIN Klienti kl on pzr.KlientiIdZadolzen = kl.Id
                    LEFT JOIN SifrarnikOsnovZaRazdolzuvanje sozr on pzr.SifrarnikOsnovZaRazdolzuvanjeId = sozr.Id
                    LEFT JOIN Polisi p on pzr.BrojNaPolisa = p.BrojNaPolisa
                    WHERE pzr.BrojNaPolisa = @brojnapolisa
                    AND (p.Storno is null or p.Storno != 1)", connection))
                {
                    cmd.Parameters.AddWithValue("@brojnapolisa", Input.BrojNaPolisa);
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            ZadolzuvanjeData = new ZadolzuvanjeInfo
                            {
                                Zadolzen = reader.IsDBNull(0) ? null : reader.GetString(0),
                                DatumNaZadolzuvanje = reader.IsDBNull(1) ? null : reader.GetDateTime(1),
                                OsnovZaRazdolzuvanje = reader.IsDBNull(2) ? null : reader.GetString(2),
                                PotvrdenoRazdolzuvanjeKajBroker = !reader.IsDBNull(3) && reader.GetBoolean(3),
                                DatumNaRazdolzuvanjeKajBroker = reader.IsDBNull(4) ? null : reader.GetDateTime(4),
                                PotvrdenoRazdolzuvanjeKajOsiguritel = !reader.IsDBNull(5) && reader.GetBoolean(5),
                                DatumNaRazdolzuvanjeKajOsiguritel = reader.IsDBNull(6) ? null : reader.GetDateTime(6)
                            };
                        }
                    }
                }
            }
        }

        private async Task LoadOZInformation()
        {
            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();

                    // First query: Get BrojNaFakturaIzlezna from polisi table
                    var getBrojNaFakturaQuery = "select top 1 BrojNaFakturaIzlezna from polisi where id = @polisaId order by DateCreated Desc";
                    using (var command = new SqlCommand(getBrojNaFakturaQuery, connection))
                    {
                        command.Parameters.AddWithValue("@polisaId", Input.Id);
                        var result = await command.ExecuteScalarAsync();
                        var brojNaFaktura = result?.ToString();

                        if (!string.IsNullOrEmpty(brojNaFaktura))
                        {
                            // Second query: Get OZ amount for faktura
                            var ozFakturaQuery = "SELECT dbo.VraziOZIznosIzleznaFakturaPremija('" + brojNaFaktura + "')";
                            using (var ozFakturaCommand = new SqlCommand(ozFakturaQuery, connection))
                            {
                                var ozResult = await ozFakturaCommand.ExecuteScalarAsync();
                                if (ozResult != null && ozResult != DBNull.Value)
                                {
                                    OZIznosIzleznaFakturaPremija = Convert.ToDecimal(ozResult);
                                }
                            }
                        }

                        // Third query: Get OZ amount for polisa
                        var ozPolisaQuery = "SELECT dbo.VraziOZIznosPolisa(@polisaId)";
                        using (var ozPolisaCommand = new SqlCommand(ozPolisaQuery, connection))
                        {
                            ozPolisaCommand.Parameters.AddWithValue("@polisaId", Input.Id);
                            var ozPolisaResult = await ozPolisaCommand.ExecuteScalarAsync();
                            if (ozPolisaResult != null && ozPolisaResult != DBNull.Value)
                            {
                                OZIznosPolisa = Convert.ToDecimal(ozPolisaResult);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading OZ information: {ex.Message}");
                OZIznosIzleznaFakturaPremija = 0;
                OZIznosPolisa = 0;
            }
        }

        private void LoadTipoviNaFaktura()
        {
            TipoviNaFaktura = Enum.GetValues(typeof(TipNaFakturaenumKlasa19))
                .Cast<TipNaFakturaenumKlasa19>()
                .Select(e => new SelectListItem
                {
                    Text = e.GetType()
                        .GetMember(e.ToString())
                        .First()
                        .GetCustomAttribute<DisplayAttribute>()
                        ?.Name ?? e.ToString(),
                    Value = e.GetType()
                        .GetMember(e.ToString())
                        .First()
                        .GetCustomAttribute<EnumMemberAttribute>()
                        ?.Value ?? e.ToString()
                })
                .ToList();
        }

        public async Task<IActionResult> OnGetAsync(long id)
        {
            if (!await HasPageAccess("ViewEditPolisaKlasa19"))
            {
                return RedirectToAccessDenied();
            }

            // Check admin access
            HasAdminAccess = await HasPageAccess("ViewEditPolisaKlasa19Admin");

            // Check storno access
            HasStornoAccess = await HasPageAccess("StorniranjePolisi");

            await LoadOsiguriteli();
            await LoadKlasiOsiguruvanje();
            await LoadProdukti();
            await LoadValuti();
            await LoadNaciniNaPlakjanje();
            await LoadTipoviNaPlakanje();
            await LoadBanki();
            await LoadListaDejnosti();
            await LoadBrojNaRatiPolisa(id);
            LoadTipoviNaFaktura();

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // Existing Polisi query
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT p.Id, p.DateCreated, p.UsernameCreated, p.DateModified, p.UsernameModified, 
                           p.KlientiIdOsiguritel, p.KlasiOsiguruvanjeIdKlasa, p.ProduktiIdProizvod,
                           p.BrojNaPolisa, p.BrojNaPonuda, p.KlientiIdDogovoruvac, p.KlientiIdOsigurenik,
                           p.Kolektivna, p.KolektivnaNeodredenBrOsigurenici, p.NeodredenBrOsigureniciZabeleska,
                           p.DatumVaziOd, p.DatumVaziDo, p.DatumNaIzdavanje, p.VremetraenjeNaPolisa, p.PeriodNaUplata,
                           p.SifrarnikValutiIdValuta, p.KlientiIdSorabotnik, p.Faktoring, p.SifrarnikValutiIdFranshizaValuta,
                           p.ProcentFranshiza, p.ProcentFinansiski, p.KoregiranaStapkaNaProvizija,
                           p.SifrarnikNacinNaPlakjanjeId,
                           p.TipNaFaktura, p.BrojNaFakturaVlezna,
                           p.DatumNaFakturaVlezna, p.RokNaPlakjanjeFakturaVlezna,
                           p.SifrarnikTipNaPlakanjeId,
                           p.SifrarnikBankiIdBanka,
                           p.GeneriranaFakturaIzlezna, p.BrojNaFakturaIzlezna,
                           p.DatumNaIzleznaFaktura, p.RokNaPlakjanjeFakturaIzlezna,
                           p.Storno, p.PricinaZaStorno, p.Zabeleska, p.FranshizaIznos,
                           CASE 
                               WHEN k.KlientFizickoPravnoLice = 'P' THEN k.Naziv
                               ELSE CONCAT(ISNULL(k.Ime, ''), ' ', ISNULL(k.Prezime, ''))
                           END as DogovoruvacNaziv,
                           CASE 
                               WHEN k2.KlientFizickoPravnoLice = 'P' THEN k2.Naziv
                               ELSE CONCAT(ISNULL(k2.Ime, ''), ' ', ISNULL(k2.Prezime, ''))
                           END as OsigurenikNaziv,
                           CASE 
                               WHEN k3.KlientFizickoPravnoLice = 'P' THEN k3.Naziv
                               ELSE CONCAT(ISNULL(k3.Ime, ''), ' ', ISNULL(k3.Prezime, ''))
                           END as SorabotnikNaziv
                    FROM Polisi p
                    LEFT JOIN Klienti k ON p.KlientiIdDogovoruvac = k.Id
                    LEFT JOIN Klienti k2 ON p.KlientiIdOsigurenik = k2.Id
                    LEFT JOIN Klienti k3 ON p.KlientiIdSorabotnik = k3.Id
                    WHERE p.Id = @Id", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", id);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    if (await reader.ReadAsync())
                    {
                        Input.Id = reader.GetInt64(0);
                        Input.DateCreated = reader.IsDBNull(1) ? null : reader.GetDateTime(1);
                        Input.UsernameCreated = reader.GetString(2);
                        Input.DateModified = reader.IsDBNull(3) ? null : reader.GetDateTime(3);
                        Input.UsernameModified = reader.IsDBNull(4) ? null : reader.GetString(4);
                        Input.KlientiIdOsiguritel = reader.IsDBNull(5) ? null : reader.GetInt64(5);
                        Input.KlasiOsiguruvanjeIdKlasa = reader.IsDBNull(6) ? null : reader.GetInt32(6);
                        Input.ProduktiIdProizvod = reader.IsDBNull(7) ? null : reader.GetInt32(7);
                        Input.BrojNaPolisa = reader.IsDBNull(8) ? null : reader.GetString(8);
                        Input.BrojNaPonuda = reader.IsDBNull(9) ? null : reader.GetInt64(9);
                        Input.KlientiIdDogovoruvac = reader.IsDBNull(10) ? null : reader.GetInt64(10);
                        Input.KlientiIdOsigurenik = reader.IsDBNull(11) ? null : reader.GetInt64(11);
                        Input.Kolektivna = !reader.IsDBNull(12) && reader.GetBoolean(12);
                        Input.KolektivnaNeodredenBrOsigurenici = !reader.IsDBNull(13) && reader.GetBoolean(13);
                        Input.NeodredenBrOsigureniciZabeleska = reader.IsDBNull(14) ? null : reader.GetString(14);
                        Input.DatumVaziOd = reader.IsDBNull(15) ? null : reader.GetDateTime(15);
                        Input.DatumVaziDo = reader.IsDBNull(16) ? null : reader.GetDateTime(16);
                        Input.DatumNaIzdavanje = reader.IsDBNull(17) ? null : reader.GetDateTime(17);
                        Input.VremetraenjeNaPolisa = reader.IsDBNull(18) ? null : reader.GetInt32(18);
                        Input.PeriodNaUplata = reader.IsDBNull(19) ? null : reader.GetInt32(19);
                        Input.SifrarnikValutiIdValuta = reader.IsDBNull(20) ? 0 : reader.GetInt64(20);
                        Input.KlientiIdSorabotnik = reader.IsDBNull(21) ? null : reader.GetInt64(21);
                        Input.Faktoring = !reader.IsDBNull(22) && reader.GetBoolean(22);
                        Input.SifrarnikValutiIdFranshizaValuta = reader.IsDBNull(23) ? null : reader.GetInt64(23);
                        Input.ProcentFranshiza = reader.IsDBNull(24) ? null : reader.GetDecimal(24);
                        Input.ProcentFinansiski = reader.IsDBNull(25) ? null : reader.GetDecimal(25);
                        Input.KoregiranaStapkaNaProvizija = reader.IsDBNull(26) ? null : reader.GetDecimal(26);
                        Input.SifrarnikNacinNaPlakjanjeId = reader.IsDBNull(27) ? null : reader.GetInt64(27);
                        Input.TipNaFaktura = reader.IsDBNull(28) ? null : reader.GetString(28);
                        Input.BrojNaFakturaVlezna = reader.IsDBNull(29) ? null : reader.GetString(29);
                        Input.DatumNaFakturaVlezna = reader.IsDBNull(30) ? null : reader.GetDateTime(30);
                        Input.RokNaPlakjanjeFakturaVlezna = reader.IsDBNull(31) ? null : reader.GetDateTime(31);
                        Input.SifrarnikTipNaPlakanjeId = reader.IsDBNull(32) ? null : reader.GetInt64(32);
                        Input.SifrarnikBankiIdBanka = reader.IsDBNull(33) ? null : reader.GetInt64(33);
                        Input.GeneriranaFakturaIzlezna = !reader.IsDBNull(34) && reader.GetBoolean(34);
                        Input.BrojNaFakturaIzlezna = reader.IsDBNull(35) ? null : reader.GetString(35);
                        Input.DatumNaIzleznaFaktura = reader.IsDBNull(36) ? null : reader.GetDateTime(36);
                        Input.RokNaPlakjanjeFakturaIzlezna = reader.IsDBNull(37) ? null : reader.GetDateTime(37);
                        Input.Storno = !reader.IsDBNull(38) && reader.GetBoolean(38);
                        Input.PricinaZaStorno = reader.IsDBNull(39) ? null : reader.GetString(39);
                        Input.Zabeleska = reader.IsDBNull(40) ? null : reader.GetString(40);
                        Input.FranshizaIznos = reader.IsDBNull(41) ? null : reader.GetDecimal(41);
                        ViewData["DogovoruvacNaziv"] = reader.IsDBNull(42) ? "" : reader.GetString(42);
                        ViewData["OsigurenikNaziv"] = reader.IsDBNull(43) ? "" : reader.GetString(43);
                        ViewData["SorabotnikNaziv"] = reader.IsDBNull(44) ? "" : reader.GetString(44);
                    }
                }

                // Add PolisiKlasa19 query
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, DateCreated, UsernameCreated, DateModified, UsernameModified,
                           PolisaId, ListaDejnostiId, BrojNaOsigurenici, PremijaZaEdnoLice,
                           OsigurenaSumaZaDozhivuvanje, OsigurenaSumaZaSmrtOdBolest,
                           OsigurenaSumaZaSmrtOdNezgoda, PrivremenaOsiguritelnaZastita,
                           PremijaGodishnaEdnokratna, OsigurenaSumaZa100PercentTraenInvaliditet,
                           OsigurenaSumaZaTraenInvaliditet, DnevenNadomest, PremijaNezgodaGodishna,
                           OsigurenaSumaZaTeskoBolniSostojbi, PremijaTeshkoBolniSostojbiGodishna,
                           OsigurenaSumaZaOperacii, PremijaZaOperaciiGodishna,
                           OsigurenaSumazaTrajnaNesposobnost, PremijaZaTrajnaNesposobnostGodishna,
                           OsigurenaSumaZaHirushkiIntervencii, PremijaZaHirushkiIntervenciiGodishna,
                           VkupnaPremijaGodishna, VkupnaPremijaEdnokratna,
                           DoplatociZaPodgodishnoPlakjanje, ProcentNaPopustZaFakturaVoRok,
                           IznosZaPlakjanjeVoRok, ProcentKomercijalenPopust, ProcentFinansiski,
                           PremijaZaNaplata, VkupnaPremija
                    FROM PolisiKlasa19
                    WHERE PolisaId = @PolisaId", connection))
                {
                    cmd.Parameters.AddWithValue("@PolisaId", id);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    if (await reader.ReadAsync())
                    {
                        InputZivot = new PolisiKlasa19ViewModel
                        {
                            Id = reader.GetInt64(0),
                            DateCreated = reader.IsDBNull(1) ? null : reader.GetDateTime(1),
                            UsernameCreated = reader.IsDBNull(2) ? null : reader.GetString(2),
                            DateModified = reader.IsDBNull(3) ? null : reader.GetDateTime(3),
                            UsernameModified = reader.IsDBNull(4) ? null : reader.GetString(4),
                            PolisaId = reader.GetInt64(5),
                            ListaDejnostiId = reader.IsDBNull(6) ? null : reader.GetInt32(6),
                            BrojNaOsigurenici = reader.IsDBNull(7) ? null : reader.GetInt32(7),
                            PremijaZaEdnoLice = reader.IsDBNull(8) ? null : reader.GetDecimal(8),
                            OsigurenaSumaZaDozhivuvanje = reader.IsDBNull(9) ? null : reader.GetDecimal(9),
                            OsigurenaSumaZaSmrtOdBolest = reader.IsDBNull(10) ? null : reader.GetDecimal(10),
                            OsigurenaSumaZaSmrtOdNezgoda = reader.IsDBNull(11) ? null : reader.GetDecimal(11),
                            PrivremenaOsiguritelnaZastita = reader.IsDBNull(12) ? null : reader.GetDecimal(12),
                            PremijaGodishnaEdnokratna = reader.IsDBNull(13) ? null : reader.GetDecimal(13),
                            OsigurenaSumaZa100PercentTraenInvaliditet = reader.IsDBNull(14) ? null : reader.GetDecimal(14),
                            OsigurenaSumaZaTraenInvaliditet = reader.IsDBNull(15) ? null : reader.GetDecimal(15),
                            DnevenNadomest = reader.IsDBNull(16) ? null : reader.GetDecimal(16),
                            PremijaNezgodaGodishna = reader.IsDBNull(17) ? null : reader.GetDecimal(17),
                            OsigurenaSumaZaTeskoBolniSostojbi = reader.IsDBNull(18) ? null : reader.GetDecimal(18),
                            PremijaTeshkoBolniSostojbiGodishna = reader.IsDBNull(19) ? null : reader.GetDecimal(19),
                            OsigurenaSumaZaOperacii = reader.IsDBNull(20) ? null : reader.GetDecimal(20),
                            PremijaZaOperaciiGodishna = reader.IsDBNull(21) ? null : reader.GetDecimal(21),
                            OsigurenaSumazaTrajnaNesposobnost = reader.IsDBNull(22) ? null : reader.GetDecimal(22),
                            PremijaZaTrajnaNesposobnostGodishna = reader.IsDBNull(23) ? null : reader.GetDecimal(23),
                            OsigurenaSumaZaHirushkiIntervencii = reader.IsDBNull(24) ? null : reader.GetDecimal(24),
                            PremijaZaHirushkiIntervenciiGodishna = reader.IsDBNull(25) ? null : reader.GetDecimal(25),
                            VkupnaPremijaGodishna = reader.IsDBNull(26) ? null : reader.GetDecimal(26),
                            VkupnaPremijaEdnokratna = reader.IsDBNull(27) ? null : reader.GetDecimal(27),
                            DoplatociZaPodgodishnoPlakjanje = reader.IsDBNull(28) ? null : reader.GetDecimal(28),
                            ProcentNaPopustZaFakturaVoRok = reader.IsDBNull(29) ? null : reader.GetDecimal(29),
                            IznosZaPlakjanjeVoRok = reader.IsDBNull(30) ? null : reader.GetDecimal(30),
                            ProcentKomercijalenPopust = reader.IsDBNull(31) ? null : reader.GetDecimal(31),
                            ProcentFinansiski = reader.IsDBNull(32) ? null : reader.GetDecimal(32),
                            PremijaZaNaplata = reader.IsDBNull(33) ? null : reader.GetDecimal(33),
                            VkupnaPremija = reader.IsDBNull(34) ? null : reader.GetDecimal(34)
                        };
                    }
                }
            }

            await LoadFiles();
            await LoadOsigureniciKolektivno();
            await LoadListaDejnosti();
            await LoadKarticaData(id);
            await LoadZadolzuvanjeData();
            await LoadOZInformation();

            return Page();
        }

        public async Task<IActionResult> OnGetSearchKlientiAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE MB LIKE @Search + '%'
                       OR EDB LIKE @Search + '%'
                       OR EMBG LIKE @Search + '%'
                       OR Naziv LIKE '%' + @Search + '%'
                       OR Ime LIKE '%' + @Search + '%'
                       OR Prezime LIKE '%' + @Search + '%'
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<IActionResult> OnGetSearchSorabotniciAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE (KlientSorabotnik = 1 or KlientVraboten = 1)
                        AND (MB LIKE @Search + '%'
                        OR EDB LIKE @Search + '%'
                        OR EMBG LIKE @Search + '%'
                        OR Naziv LIKE '%' + @Search + '%'
                        OR Ime LIKE '%' + @Search + '%'
                        OR Prezime LIKE '%' + @Search + '%')
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        [ValidateAntiForgeryToken]
        public async Task<IActionResult> OnPostAsync()
        {
            try
            {
                var debugInfo = new System.Text.StringBuilder();
                var username = _httpContextAccessor.HttpContext.Session.GetString("Username");
                debugInfo.AppendLine($"User from session: {username}");

                if (string.IsNullOrEmpty(username))
                {
                    TempData["ErrorMessage"] = "Cannot save changes - no user is logged in. Please log in again.";
                    debugInfo.AppendLine("Username not found in session - aborting save");
                    TempData["DebugInfo"] = debugInfo.ToString();
                    return Page();
                }

                debugInfo.AppendLine($"Final username: {username}");
                debugInfo.AppendLine($"ModelState.IsValid: {ModelState.IsValid}");
                debugInfo.AppendLine($"Input.Id: {Input?.Id}");

                if (!await HasPageAccess("ViewEditPolisaKlasa1"))
                {
                    TempData["ErrorMessage"] = "Access denied to ViewEditPolisaKlasa1";
                    return RedirectToAccessDenied();
                }

                if (!ModelState.IsValid)
                {
                    debugInfo.AppendLine("ModelState errors:");
                    foreach (var modelStateEntry in ModelState.Values)
                    {
                        foreach (var error in modelStateEntry.Errors)
                        {
                            debugInfo.AppendLine($"- {error.ErrorMessage}");
                        }
                    }

                    TempData["DebugInfo"] = debugInfo.ToString();
                    await LoadOsiguriteli();
                    await LoadKlasiOsiguruvanje();
                    await LoadProdukti();
                    await LoadValuti();
                    await LoadNaciniNaPlakjanje();
                    await LoadTipoviNaPlakanje();
                    await LoadBanki();
                    await LoadListaDejnosti();
                    return Page();
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    debugInfo.AppendLine("Database connection opened successfully");

                    using (SqlCommand cmd = new SqlCommand(@"
                        UPDATE Polisi 
                        SET KlientiIdOsiguritel = @KlientiIdOsiguritel,
                            KlasiOsiguruvanjeIdKlasa = @KlasiOsiguruvanjeIdKlasa,
                            ProduktiIdProizvod = @ProduktiIdProizvod,
                            BrojNaPolisa = @BrojNaPolisa,
                            BrojNaPonuda = @BrojNaPonuda,
                            KlientiIdDogovoruvac = @KlientiIdDogovoruvac,
                            KlientiIdOsigurenik = @KlientiIdOsigurenik,
                            Kolektivna = @Kolektivna,
                            KolektivnaNeodredenBrOsigurenici = @KolektivnaNeodredenBrOsigurenici,
                            NeodredenBrOsigureniciZabeleska = @NeodredenBrOsigureniciZabeleska,
                            DatumVaziOd = @DatumVaziOd,
                            DatumVaziDo = @DatumVaziDo,
                            DatumNaIzdavanje = @DatumNaIzdavanje,
                            VremetraenjeNaPolisa = @VremetraenjeNaPolisa,
                            PeriodNaUplata = @PeriodNaUplata,
                            SifrarnikValutiIdValuta = @SifrarnikValutiIdValuta,
                            KlientiIdSorabotnik = @KlientiIdSorabotnik,
                            Faktoring = @Faktoring,
                            SifrarnikValutiIdFranshizaValuta = @SifrarnikValutiIdFranshizaValuta,
                            ProcentFranshiza = @ProcentFranshiza,
                            ProcentFinansiski = @ProcentFinansiski,
                            KoregiranaStapkaNaProvizija = @KoregiranaStapkaNaProvizija,
                            SifrarnikNacinNaPlakjanjeId = @SifrarnikNacinNaPlakjanjeId,
                            SifrarnikTipNaPlakanjeId = @SifrarnikTipNaPlakanjeId,
                            SifrarnikBankiIdBanka = @SifrarnikBankiIdBanka,
                            Zabeleska = @Zabeleska,
                            FranshizaIznos = @FranshizaIznos,
                            DateModified = GETDATE(),
                            UsernameModified = @UsernameModified,
                            RokNaPlakjanjeFakturaVlezna = @RokNaPlakjanjeFakturaVlezna,
                            BrojNaFakturaVlezna = @BrojNaFakturaVlezna,
                            TipNaFaktura = @TipNaFaktura,
                            DatumNaFakturaVlezna = @DatumNaFakturaVlezna,
                            GeneriranaFakturaIzlezna = @GeneriranaFakturaIzlezna,
                            BrojNaFakturaIzlezna = @BrojNaFakturaIzlezna,
                            DatumNaIzleznaFaktura = @DatumNaIzleznaFaktura,
                            RokNaPlakjanjeFakturaIzlezna = @RokNaPlakjanjeFakturaIzlezna,
                            Storno = @Storno,
                            PricinaZaStorno = @PricinaZaStorno

                        WHERE Id = @Id", connection))
                    {
                        debugInfo.AppendLine($"Updating policy with ID: {Input.Id}");

                        cmd.Parameters.AddWithValue("@Id", Input.Id);
                        cmd.Parameters.AddWithValue("@KlientiIdOsiguritel", Input.KlientiIdOsiguritel ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@KlasiOsiguruvanjeIdKlasa", Input.KlasiOsiguruvanjeIdKlasa ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ProduktiIdProizvod", Input.ProduktiIdProizvod ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaPolisa", Input.BrojNaPolisa ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaPonuda", Input.BrojNaPonuda ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@KlientiIdDogovoruvac", Input.KlientiIdDogovoruvac ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@KlientiIdOsigurenik", Input.KlientiIdOsigurenik ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Kolektivna", Input.Kolektivna);
                        cmd.Parameters.AddWithValue("@KolektivnaNeodredenBrOsigurenici", Input.KolektivnaNeodredenBrOsigurenici);
                        cmd.Parameters.AddWithValue("@NeodredenBrOsigureniciZabeleska", 
                            string.IsNullOrEmpty(Input.NeodredenBrOsigureniciZabeleska) ? (object)DBNull.Value : Input.NeodredenBrOsigureniciZabeleska);
                        cmd.Parameters.AddWithValue("@DatumVaziOd", Input.DatumVaziOd ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumVaziDo", Input.DatumVaziDo ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumNaIzdavanje", Input.DatumNaIzdavanje ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@VremetraenjeNaPolisa", Input.VremetraenjeNaPolisa ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@PeriodNaUplata", Input.PeriodNaUplata ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SifrarnikValutiIdValuta", Input.SifrarnikValutiIdValuta);
                        cmd.Parameters.AddWithValue("@KlientiIdSorabotnik", Input.KlientiIdSorabotnik ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Faktoring", Input.Faktoring);
                        cmd.Parameters.AddWithValue("@SifrarnikValutiIdFranshizaValuta", Input.SifrarnikValutiIdFranshizaValuta ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ProcentFranshiza", Input.ProcentFranshiza ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ProcentFinansiski", Input.ProcentFinansiski ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@KoregiranaStapkaNaProvizija", Input.KoregiranaStapkaNaProvizija ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SifrarnikNacinNaPlakjanjeId", Input.SifrarnikNacinNaPlakjanjeId ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SifrarnikTipNaPlakanjeId", Input.SifrarnikTipNaPlakanjeId ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SifrarnikBankiIdBanka", Input.SifrarnikBankiIdBanka ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Zabeleska", 
                            string.IsNullOrEmpty(Input.Zabeleska) ? (object)DBNull.Value : Input.Zabeleska);
                        cmd.Parameters.AddWithValue("@FranshizaIznos", Input.FranshizaIznos ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@UsernameModified", username);
                        cmd.Parameters.AddWithValue("@RokNaPlakjanjeFakturaVlezna", Input.RokNaPlakjanjeFakturaVlezna ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaFakturaVlezna", 
                            string.IsNullOrEmpty(Input.BrojNaFakturaVlezna) ? (object)DBNull.Value : Input.BrojNaFakturaVlezna);
                        cmd.Parameters.AddWithValue("@TipNaFaktura",
                            string.IsNullOrEmpty(Input.TipNaFaktura) ? (object)DBNull.Value : Input.TipNaFaktura);
                        cmd.Parameters.AddWithValue("@DatumNaFakturaVlezna", Input.DatumNaFakturaVlezna ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@GeneriranaFakturaIzlezna", Input.GeneriranaFakturaIzlezna);
                        cmd.Parameters.AddWithValue("@BrojNaFakturaIzlezna",
                            string.IsNullOrEmpty(Input.BrojNaFakturaIzlezna) ? (object)DBNull.Value : Input.BrojNaFakturaIzlezna);
                        cmd.Parameters.AddWithValue("@DatumNaIzleznaFaktura", Input.DatumNaIzleznaFaktura ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@RokNaPlakjanjeFakturaIzlezna", Input.RokNaPlakjanjeFakturaIzlezna ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Storno", Input.Storno);
                        cmd.Parameters.AddWithValue("@PricinaZaStorno",
                            string.IsNullOrEmpty(Input.PricinaZaStorno) ? (object)DBNull.Value : Input.PricinaZaStorno);
                                     
                        

                        debugInfo.AppendLine("All parameters set");

                        var rowsAffected = await cmd.ExecuteNonQueryAsync();
                        debugInfo.AppendLine($"Rows affected: {rowsAffected}");

                        if (rowsAffected == 0)
                        {
                            TempData["ErrorMessage"] = "No records were updated. Please check if the policy exists.";
                            TempData["DebugInfo"] = debugInfo.ToString();
                            return Page();
                        }
                    }                   

                    // Update or Insert PolisiKlasa19
                    using (SqlCommand cmd = new SqlCommand(@"
                        IF EXISTS (SELECT 1 FROM PolisiKlasa19 WHERE PolisaId = @PolisaId)
                        BEGIN
                            UPDATE PolisiKlasa19 SET
                                DateModified = GETDATE(),
                                UsernameModified = @UsernameModified,
                                ListaDejnostiId = @ListaDejnostiId,
                                BrojNaOsigurenici = @BrojNaOsigurenici,
                                PremijaZaEdnoLice = @PremijaZaEdnoLice,
                                OsigurenaSumaZaDozhivuvanje = @OsigurenaSumaZaDozhivuvanje,
                                OsigurenaSumaZaSmrtOdBolest = @OsigurenaSumaZaSmrtOdBolest,
                                OsigurenaSumaZaSmrtOdNezgoda = @OsigurenaSumaZaSmrtOdNezgoda,
                                PrivremenaOsiguritelnaZastita = @PrivremenaOsiguritelnaZastita,
                                PremijaGodishnaEdnokratna = @PremijaGodishnaEdnokratna,
                                OsigurenaSumaZa100PercentTraenInvaliditet = @OsigurenaSumaZa100PercentTraenInvaliditet,
                                OsigurenaSumaZaTraenInvaliditet = @OsigurenaSumaZaTraenInvaliditet,
                                DnevenNadomest = @DnevenNadomest,
                                PremijaNezgodaGodishna = @PremijaNezgodaGodishna,
                                OsigurenaSumaZaTeskoBolniSostojbi = @OsigurenaSumaZaTeskoBolniSostojbi,
                                PremijaTeshkoBolniSostojbiGodishna = @PremijaTeshkoBolniSostojbiGodishna,
                                OsigurenaSumaZaOperacii = @OsigurenaSumaZaOperacii,
                                PremijaZaOperaciiGodishna = @PremijaZaOperaciiGodishna,
                                OsigurenaSumazaTrajnaNesposobnost = @OsigurenaSumazaTrajnaNesposobnost,
                                PremijaZaTrajnaNesposobnostGodishna = @PremijaZaTrajnaNesposobnostGodishna,
                                OsigurenaSumaZaHirushkiIntervencii = @OsigurenaSumaZaHirushkiIntervencii,
                                PremijaZaHirushkiIntervenciiGodishna = @PremijaZaHirushkiIntervenciiGodishna,
                                VkupnaPremijaGodishna = @VkupnaPremijaGodishna,
                                VkupnaPremijaEdnokratna = @VkupnaPremijaEdnokratna,
                                DoplatociZaPodgodishnoPlakjanje = @DoplatociZaPodgodishnoPlakjanje,
                                ProcentNaPopustZaFakturaVoRok = @ProcentNaPopustZaFakturaVoRok,
                                IznosZaPlakjanjeVoRok = @IznosZaPlakjanjeVoRok,
                                ProcentKomercijalenPopust = @ProcentKomercijalenPopust,
                                ProcentFinansiski = @ProcentFinansiski,
                                PremijaZaNaplata = @PremijaZaNaplata,
                                VkupnaPremija = @VkupnaPremija
                            WHERE PolisaId = @PolisaId
                        END
                        ELSE
                        BEGIN
                            INSERT INTO PolisiKlasa19 (
                                PolisaId, DateCreated, UsernameCreated,
                                ListaDejnostiId, BrojNaOsigurenici, PremijaZaEdnoLice,
                                OsigurenaSumaZaDozhivuvanje, OsigurenaSumaZaSmrtOdBolest,
                                OsigurenaSumaZaSmrtOdNezgoda, PrivremenaOsiguritelnaZastita,
                                PremijaGodishnaEdnokratna, OsigurenaSumaZa100PercentTraenInvaliditet,
                                OsigurenaSumaZaTraenInvaliditet, DnevenNadomest,
                                PremijaNezgodaGodishna, OsigurenaSumaZaTeskoBolniSostojbi,
                                PremijaTeshkoBolniSostojbiGodishna, OsigurenaSumaZaOperacii,
                                PremijaZaOperaciiGodishna, OsigurenaSumazaTrajnaNesposobnost,
                                PremijaZaTrajnaNesposobnostGodishna, OsigurenaSumaZaHirushkiIntervencii,
                                PremijaZaHirushkiIntervenciiGodishna, VkupnaPremijaGodishna,
                                VkupnaPremijaEdnokratna, DoplatociZaPodgodishnoPlakjanje,
                                ProcentNaPopustZaFakturaVoRok, IznosZaPlakjanjeVoRok,
                                ProcentKomercijalenPopust, ProcentFinansiski,
                                PremijaZaNaplata, VkupnaPremija
                            ) VALUES (
                                @PolisaId, GETDATE(), @UsernameCreated,
                                @ListaDejnostiId, @BrojNaOsigurenici, @PremijaZaEdnoLice,
                                @OsigurenaSumaZaDozhivuvanje, @OsigurenaSumaZaSmrtOdBolest,
                                @OsigurenaSumaZaSmrtOdNezgoda, @PrivremenaOsiguritelnaZastita,
                                @PremijaGodishnaEdnokratna, @OsigurenaSumaZa100PercentTraenInvaliditet,
                                @OsigurenaSumaZaTraenInvaliditet, @DnevenNadomest,
                                @PremijaNezgodaGodishna, @OsigurenaSumaZaTeskoBolniSostojbi,
                                @PremijaTeshkoBolniSostojbiGodishna, @OsigurenaSumaZaOperacii,
                                @PremijaZaOperaciiGodishna, @OsigurenaSumazaTrajnaNesposobnost,
                                @PremijaZaTrajnaNesposobnostGodishna, @OsigurenaSumaZaHirushkiIntervencii,
                                @PremijaZaHirushkiIntervenciiGodishna, @VkupnaPremijaGodishna,
                                @VkupnaPremijaEdnokratna, @DoplatociZaPodgodishnoPlakjanje,
                                @ProcentNaPopustZaFakturaVoRok, @IznosZaPlakjanjeVoRok,
                                @ProcentKomercijalenPopust, @ProcentFinansiski,
                                @PremijaZaNaplata, @VkupnaPremija
                            )
                        END", connection))
                    {
                        cmd.Parameters.AddWithValue("@PolisaId", Input.Id);
                        cmd.Parameters.AddWithValue("@UsernameCreated", username);
                        cmd.Parameters.AddWithValue("@UsernameModified", username);
                        cmd.Parameters.AddWithValue("@ListaDejnostiId", (object?)InputZivot.ListaDejnostiId ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaOsigurenici", (object?)InputZivot.BrojNaOsigurenici ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@PremijaZaEdnoLice", (object?)InputZivot.PremijaZaEdnoLice ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@OsigurenaSumaZaDozhivuvanje", (object?)InputZivot.OsigurenaSumaZaDozhivuvanje ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@OsigurenaSumaZaSmrtOdBolest", (object?)InputZivot.OsigurenaSumaZaSmrtOdBolest ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@OsigurenaSumaZaSmrtOdNezgoda", (object?)InputZivot.OsigurenaSumaZaSmrtOdNezgoda ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@PrivremenaOsiguritelnaZastita", (object?)InputZivot.PrivremenaOsiguritelnaZastita ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@PremijaGodishnaEdnokratna", (object?)InputZivot.PremijaGodishnaEdnokratna ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@OsigurenaSumaZa100PercentTraenInvaliditet", (object?)InputZivot.OsigurenaSumaZa100PercentTraenInvaliditet ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@OsigurenaSumaZaTraenInvaliditet", (object?)InputZivot.OsigurenaSumaZaTraenInvaliditet ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@DnevenNadomest", (object?)InputZivot.DnevenNadomest ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@PremijaNezgodaGodishna", (object?)InputZivot.PremijaNezgodaGodishna ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@OsigurenaSumaZaTeskoBolniSostojbi", (object?)InputZivot.OsigurenaSumaZaTeskoBolniSostojbi ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@PremijaTeshkoBolniSostojbiGodishna", (object?)InputZivot.PremijaTeshkoBolniSostojbiGodishna ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@OsigurenaSumaZaOperacii", (object?)InputZivot.OsigurenaSumaZaOperacii ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@PremijaZaOperaciiGodishna", (object?)InputZivot.PremijaZaOperaciiGodishna ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@OsigurenaSumazaTrajnaNesposobnost", (object?)InputZivot.OsigurenaSumazaTrajnaNesposobnost ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@PremijaZaTrajnaNesposobnostGodishna", (object?)InputZivot.PremijaZaTrajnaNesposobnostGodishna ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@OsigurenaSumaZaHirushkiIntervencii", (object?)InputZivot.OsigurenaSumaZaHirushkiIntervencii ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@PremijaZaHirushkiIntervenciiGodishna", (object?)InputZivot.PremijaZaHirushkiIntervenciiGodishna ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@VkupnaPremijaGodishna", (object?)InputZivot.VkupnaPremijaGodishna ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@VkupnaPremijaEdnokratna", (object?)InputZivot.VkupnaPremijaEdnokratna ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@DoplatociZaPodgodishnoPlakjanje", (object?)InputZivot.DoplatociZaPodgodishnoPlakjanje ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ProcentNaPopustZaFakturaVoRok", (object?)InputZivot.ProcentNaPopustZaFakturaVoRok ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@IznosZaPlakjanjeVoRok", (object?)InputZivot.IznosZaPlakjanjeVoRok ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ProcentKomercijalenPopust", (object?)InputZivot.ProcentKomercijalenPopust ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ProcentFinansiski", (object?)InputZivot.ProcentFinansiski ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@PremijaZaNaplata", (object?)InputZivot.PremijaZaNaplata ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@VkupnaPremija", (object?)InputZivot.VkupnaPremija ?? DBNull.Value);

                        await cmd.ExecuteNonQueryAsync();
                    }
                }

                TempData["SuccessMessage"] = "Полисата е успешно зачувана.";
                // Reload the page data
                await LoadOsiguriteli();
                await LoadKlasiOsiguruvanje();
                await LoadProdukti();
                await LoadValuti();
                await LoadNaciniNaPlakjanje();
                await LoadTipoviNaPlakanje();
                await LoadBanki();
                await LoadListaDejnosti();
                return RedirectToPage(new { id = Input.Id });
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "An error occurred while saving the policy.";
                TempData["DebugInfo"] = $"Exception: {ex.Message}\nStack Trace: {ex.StackTrace}";
                return Page();
            }
        }

        public async Task<IActionResult> OnGetDownloadFileAsync(long fileId)
        {
            if (!await HasPageAccess("ViewEditPolisaKlasa1"))
            {
                return RedirectToAccessDenied();
            }

            string filePath = null;
            string fileName = null;
            string connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand command = new SqlCommand(
                    "SELECT FilePath, FileName FROM PolisiFileSystem WHERE Id = @FileId", connection))
                {
                    command.Parameters.AddWithValue("@FileId", fileId);
                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            filePath = reader.GetString(0);
                            fileName = reader.GetString(1);
                        }
                    }
                }
            }

            if (string.IsNullOrEmpty(filePath))
            {
                return NotFound();
            }

            var sftpConfig = _configuration.GetSection("SftpConfig");
            using (var client = new SftpClient(
                sftpConfig["Host"],
                int.Parse(sftpConfig["Port"]),
                sftpConfig["Username"],
                sftpConfig["Password"]))
            {
                client.Connect();

                using (var memoryStream = new MemoryStream())
                {
                    client.DownloadFile(filePath, memoryStream);
                    client.Disconnect();
                    memoryStream.Position = 0;
                    return File(memoryStream.ToArray(), "application/octet-stream", fileName);
                }
            }
        }

        [ValidateAntiForgeryToken]
        public async Task<IActionResult> OnPostGenerirajRatiAsync()
        {
            try
            {
                if (HasAdminAccess)
                {
                    return new JsonResult(new { success = false, message = "Немате пристап за оваа операција." });
                }

                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    using (var cmd = new SqlCommand(@"
                        SELECT p.Id, p.SifrarnikValutiIdValuta, p.DatumNaIzdavanje, 
                               p.SifrarnikNacinNaPlakjanjeId, pk.VkupnaPremija
                        FROM Polisi p
                        LEFT JOIN PolisiKlasa19 pk ON p.Id = pk.PolisaId
                        WHERE p.Id = @Id", connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", Input.Id);
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                var polisaId = reader.GetInt64(0);
                                var valutiId = reader.GetValue(1);
                                var datumNaIzdavanje = reader.GetValue(2);
                                var nacinNaPlakanjeId = reader.GetValue(3);
                                var premija = reader.GetValue(4);

                                reader.Close();

                                using (var cmdRati = new SqlCommand("GenerirajRatiPolisa", connection))
                                {
                                    cmdRati.CommandType = CommandType.StoredProcedure;
                                    cmdRati.Parameters.AddWithValue("@PolisaId", polisaId);
                                    cmdRati.Parameters.AddWithValue("@SifrarnikValutiId", valutiId);
                                    cmdRati.Parameters.AddWithValue("@DatumNaIzdavanje", datumNaIzdavanje);
                                    cmdRati.Parameters.AddWithValue("@SifrarnikNacinNaPlakanjeId", nacinNaPlakanjeId);
                                    cmdRati.Parameters.AddWithValue("@Premija", premija);

                                    await cmdRati.ExecuteNonQueryAsync();
                                }
                            }
                            else
                            {
                                return new JsonResult(new { success = false, message = "Не се најдени податоци за полисата!" });
                            }
                        }
                    }
                }

                return new JsonResult(new { success = true, message = "Ратите се успешно генерирани." });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = "Грешка при генерирање на рати: " + ex.Message });
            }
        }

        public async Task<JsonResult> OnGetCheckBrojNaPolisaAsync(string brojNaPolisa)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand("SELECT dbo.ProverkaZaPostoenjeNaPolisaBezStorno(@brojNaPolisa)", connection))
                {
                    cmd.Parameters.AddWithValue("@brojNaPolisa", brojNaPolisa);
                    var result = await cmd.ExecuteScalarAsync();
                    return new JsonResult(Convert.ToInt32(result));
                }
            }
        }
    }
} 