using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using Microsoft.Data.SqlClient;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Data;

namespace NextBroker.Pages.Finansii
{
    public class PreraspredelbaNaUplatiModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public PreraspredelbaNaUplatiModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public List<UplatiZaPreraspredelbaModel> UplatiZaPreraspredelba { get; set; } = new List<UplatiZaPreraspredelbaModel>();

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("PreraspredelbaNaUplati"))
            {
                return RedirectToAccessDenied();
            }

            await LoadUplatiZaPreraspredelba();
            return Page();
        }

        [BindProperty]
        public long UplatiPreraspredelbaId { get; set; }

        [BindProperty]
        public decimal IznosPreraspredelba { get; set; }

        [BindProperty]
        public string BrojNaPolisaZaRaspredeluvanje { get; set; }

        public async Task<JsonResult> OnPostPreraspredeliUplataAsync(long uplatiPreraspredelbaId, decimal iznosPreraspredelba, string brojNaPolisaZaRaspredeluvanje)
        {
            if (!await HasPageAccess("PreraspredelbaNaUplati"))
            {
                return new JsonResult(new { success = false, message = "Немате пристап до оваа страница" });
            }

            if (iznosPreraspredelba <= 0)
            {
                return new JsonResult(new { success = false, message = "Износот мора да биде поголем од нула" });
            }

            if (string.IsNullOrWhiteSpace(brojNaPolisaZaRaspredeluvanje))
            {
                return new JsonResult(new { success = false, message = "Бројот на полиса е задолжителен" });
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Check if the entered amount is valid
                    using (SqlCommand cmd = new SqlCommand("SELECT PreostanatIznos FROM UplatiZaPreraspredelba WHERE Id = @Id", connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", uplatiPreraspredelbaId);
                        var result = await cmd.ExecuteScalarAsync();
                        
                        if (result == DBNull.Value)
                        {
                            return new JsonResult(new { success = false, message = "Не е пронајден запис со дадениот ID" });
                        }
                        
                        decimal preostanIznos = Convert.ToDecimal(result);
                        if (iznosPreraspredelba > preostanIznos)
                        {
                            return new JsonResult(new { success = false, message = "Износот не може да биде поголем од преостанатиот износ" });
                        }
                    }

                    // Call the stored procedure
                    using (SqlCommand cmd = new SqlCommand("UplatiPreraspredelbaRaspredeliSredstva", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@UplatiPreraspredelbaId", uplatiPreraspredelbaId);
                        cmd.Parameters.AddWithValue("@IznosPreraspredelba", iznosPreraspredelba);
                        cmd.Parameters.AddWithValue("@BrojNaPolisaZaRaspredeluvanje", brojNaPolisaZaRaspredeluvanje);

                        await cmd.ExecuteNonQueryAsync();
                    }

                    return new JsonResult(new { success = true });
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        private async Task LoadUplatiZaPreraspredelba()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                string query = @"
                select
                uzp.Id,
                uzp.DateCreated,
                uzp.UsernameCreated,
                uzp.DateModified,
                uzp.UsernameModified,
                uzp.UplataId,
                uzp.UplataDatum,
                siftipp.TipNaPlakanje,
                uzp.BrojIzvod,
                uzp.Iznos,
                uzp.PolisaBroj,
                uzp.Neraspredelena,
                uzp.Storno,
                uzp.DatumStorno,
                uzp.StornoZaMesec,
                uzp.StornoZaGodina,
                uzp.PovratNaSredstva,
                uzp.BrojNaKasovIzvestaj,
                sifbank.Banka,
                uzp.KasaPrimi,
                uzp.StavkaPremijaId,
                uzp.KlientIdUplakjac,
                ISNULL(CAST(klntuplk.Naziv AS varchar), '') + ' ' + 
                ISNULL(CAST(klntuplk.Ime AS varchar), '') + ' ' + 
                ISNULL(CAST(klntuplk.Prezime AS varchar), '') as [Uplakac],
                uzp.BrojNaFaktura,
                uzp.PrenesenaPremijaKonOsiguritel,
                uzp.ProvizijaPresmetkaPotvrdeno,
                uzp.PreraspredelenaUplata,
                uzp.SredstvaOdPreraspredelenaUplata,
                uzp.DatumPreraspredeluvanje,
                uzp.PreostanatIznos
                from UplatiZaPreraspredelba uzp
                left join sifrarniktipnaplakanje siftipp on uzp.sifrarniktipnaplakanjeid = siftipp.id
                left join SifrarnikBanki sifbank on uzp.sifrarnikbankiid = sifbank.id
                left join Klienti klntuplk on uzp.KlientIdUplakjac = klntuplk.id";

                using (SqlCommand cmd = new SqlCommand(query, connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        UplatiZaPreraspredelba.Add(new UplatiZaPreraspredelbaModel
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            DateCreated = reader.IsDBNull(reader.GetOrdinal("DateCreated")) ? null : reader.GetDateTime(reader.GetOrdinal("DateCreated")),
                            UsernameCreated = reader.IsDBNull(reader.GetOrdinal("UsernameCreated")) ? null : reader.GetString(reader.GetOrdinal("UsernameCreated")),
                            DateModified = reader.IsDBNull(reader.GetOrdinal("DateModified")) ? null : reader.GetDateTime(reader.GetOrdinal("DateModified")),
                            UsernameModified = reader.IsDBNull(reader.GetOrdinal("UsernameModified")) ? null : reader.GetString(reader.GetOrdinal("UsernameModified")),
                            UplataId = reader.IsDBNull(reader.GetOrdinal("UplataId")) ? null : (long?)reader.GetInt64(reader.GetOrdinal("UplataId")),
                            UplataDatum = reader.IsDBNull(reader.GetOrdinal("UplataDatum")) ? null : reader.GetDateTime(reader.GetOrdinal("UplataDatum")),
                            TipNaPlakanje = reader.IsDBNull(reader.GetOrdinal("TipNaPlakanje")) ? null : reader.GetString(reader.GetOrdinal("TipNaPlakanje")),
                            BrojIzvod = reader.IsDBNull(reader.GetOrdinal("BrojIzvod")) ? null : reader.GetString(reader.GetOrdinal("BrojIzvod")),
                            Iznos = reader.IsDBNull(reader.GetOrdinal("Iznos")) ? null : reader.GetDecimal(reader.GetOrdinal("Iznos")),
                            PolisaBroj = reader.IsDBNull(reader.GetOrdinal("PolisaBroj")) ? null : reader.GetString(reader.GetOrdinal("PolisaBroj")),
                            Neraspredelena = reader.IsDBNull(reader.GetOrdinal("Neraspredelena")) ? false : reader.GetBoolean(reader.GetOrdinal("Neraspredelena")),
                            Storno = reader.IsDBNull(reader.GetOrdinal("Storno")) ? false : reader.GetBoolean(reader.GetOrdinal("Storno")),
                            DatumStorno = reader.IsDBNull(reader.GetOrdinal("DatumStorno")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumStorno")),
                            StornoZaMesec = reader.IsDBNull(reader.GetOrdinal("StornoZaMesec")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("StornoZaMesec")),
                            StornoZaGodina = reader.IsDBNull(reader.GetOrdinal("StornoZaGodina")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("StornoZaGodina")),
                            PovratNaSredstva = reader.IsDBNull(reader.GetOrdinal("PovratNaSredstva")) ? false : reader.GetBoolean(reader.GetOrdinal("PovratNaSredstva")),
                            BrojNaKasovIzvestaj = reader.IsDBNull(reader.GetOrdinal("BrojNaKasovIzvestaj")) ? null : reader.GetString(reader.GetOrdinal("BrojNaKasovIzvestaj")),
                            Banka = reader.IsDBNull(reader.GetOrdinal("Banka")) ? null : reader.GetString(reader.GetOrdinal("Banka")),
                            KasaPrimi = reader.IsDBNull(reader.GetOrdinal("KasaPrimi")) ? null : (long?)reader.GetInt64(reader.GetOrdinal("KasaPrimi")),
                            StavkaPremijaId = reader.IsDBNull(reader.GetOrdinal("StavkaPremijaId")) ? null : (long?)reader.GetInt64(reader.GetOrdinal("StavkaPremijaId")),
                            KlientIdUplakjac = reader.IsDBNull(reader.GetOrdinal("KlientIdUplakjac")) ? null : (long?)reader.GetInt64(reader.GetOrdinal("KlientIdUplakjac")),
                            Uplakac = reader.IsDBNull(reader.GetOrdinal("Uplakac")) ? null : reader.GetString(reader.GetOrdinal("Uplakac")),
                            BrojNaFaktura = reader.IsDBNull(reader.GetOrdinal("BrojNaFaktura")) ? null : reader.GetString(reader.GetOrdinal("BrojNaFaktura")),
                            PrenesenaPremijaKonOsiguritel = reader.IsDBNull(reader.GetOrdinal("PrenesenaPremijaKonOsiguritel")) ? false : reader.GetBoolean(reader.GetOrdinal("PrenesenaPremijaKonOsiguritel")),
                            ProvizijaPresmetkaPotvrdeno = reader.IsDBNull(reader.GetOrdinal("ProvizijaPresmetkaPotvrdeno")) ? false : reader.GetBoolean(reader.GetOrdinal("ProvizijaPresmetkaPotvrdeno")),
                            PreraspredelenaUplata = reader.GetBoolean(reader.GetOrdinal("PreraspredelenaUplata")),
                            SredstvaOdPreraspredelenaUplata = reader.GetBoolean(reader.GetOrdinal("SredstvaOdPreraspredelenaUplata")),
                            DatumPreraspredeluvanje = reader.IsDBNull(reader.GetOrdinal("DatumPreraspredeluvanje")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumPreraspredeluvanje")),
                            PreostanatIznos = reader.IsDBNull(reader.GetOrdinal("PreostanatIznos")) ? null : reader.GetDecimal(reader.GetOrdinal("PreostanatIznos"))
                        });
                    }
                }
            }
        }

        public async Task<JsonResult> OnGetSearchPolisaAsync(string term)
        {
            if (string.IsNullOrWhiteSpace(term))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                string query = @"
                    SELECT TOP 10 
                        pol.BrojNaPolisa,
                        ISNULL(CAST(klnt.Naziv AS varchar), '') + ' ' +
                        ISNULL(CAST(klnt.Ime AS varchar), '') + ' ' +
                        ISNULL(CAST(klnt.Prezime AS varchar), '') as [Dogovoruvac],
                        klntosig.Naziv as [Osiguritel],
                        pol.DatumVaziOd,
                        pol.DatumVaziDo
                    FROM Polisi pol
                    LEFT JOIN Klienti klntosig ON pol.KlientiIdOsiguritel = klntosig.id
                    LEFT JOIN Klienti klnt ON pol.KlientiIdDogovoruvac = klnt.id
                    WHERE pol.BrojNaPolisa LIKE @Search
                    AND (pol.Storno IS NULL OR pol.Storno != 1)
                    ORDER BY pol.BrojNaPolisa";

                using (SqlCommand cmd = new SqlCommand(query, connection))
                {
                    cmd.Parameters.AddWithValue("@Search", "%" + term + "%");

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            brojNaPolisa = reader["BrojNaPolisa"].ToString(),
                            dogovoruvac = reader["Dogovoruvac"].ToString(),
                            osiguritel = reader["Osiguritel"].ToString(),
                            datumVaziOd = reader.IsDBNull(reader.GetOrdinal("DatumVaziOd")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumVaziOd")).ToString("dd.MM.yyyy"),
                            datumVaziDo = reader.IsDBNull(reader.GetOrdinal("DatumVaziDo")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumVaziDo")).ToString("dd.MM.yyyy")
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public class UplatiZaPreraspredelbaModel
        {
            public long Id { get; set; }
            public DateTime? DateCreated { get; set; }
            public string? UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string? UsernameModified { get; set; }
            public long? UplataId { get; set; }
            public DateTime? UplataDatum { get; set; }
            public string? TipNaPlakanje { get; set; }
            public string? BrojIzvod { get; set; }
            public decimal? Iznos { get; set; }
            public string? PolisaBroj { get; set; }
            public bool Neraspredelena { get; set; }
            public bool Storno { get; set; }
            public DateTime? DatumStorno { get; set; }
            public int? StornoZaMesec { get; set; }
            public int? StornoZaGodina { get; set; }
            public bool PovratNaSredstva { get; set; }
            public string? BrojNaKasovIzvestaj { get; set; }
            public string? Banka { get; set; }
            public long? KasaPrimi { get; set; }
            public long? StavkaPremijaId { get; set; }
            public long? KlientIdUplakjac { get; set; }
            public string? Uplakac { get; set; }
            public string? BrojNaFaktura { get; set; }
            public bool PrenesenaPremijaKonOsiguritel { get; set; }
            public bool ProvizijaPresmetkaPotvrdeno { get; set; }
            public bool PreraspredelenaUplata { get; set; }
            public bool SredstvaOdPreraspredelenaUplata { get; set; }
            public DateTime? DatumPreraspredeluvanje { get; set; }
            public decimal? PreostanatIznos { get; set; }
        }
    }
}
