using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using Microsoft.Data.SqlClient;

namespace NextBroker.Pages.Klienti
{
    public class ListaKlientiModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public ListaKlientiModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public class KlientViewModel
        {
            public long Id { get; set; }
            public DateTime? DateCreated { get; set; }
            public string? UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string? UsernameModified { get; set; }
            public string? Naziv { get; set; }
            public string? Ime { get; set; }
            public string? Prezime { get; set; }
            public string? EDB { get; set; }
            public string? MB { get; set; }
            public string? EMBG { get; set; }
            public int? ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija { get; set; }
            public string? UlicaOdDokumentZaIdentifikacija { get; set; }
            public string? BrojOdDokumentZaIdentifikacija { get; set; }
            public int? ListaOpstiniIdOpstinaZaKomunikacija { get; set; }
            public string? UlicaZaKomunikacija { get; set; }
            public string? BrojZaKomunikacija { get; set; }
            public DateTime? DatumNaTekovnaSostojba { get; set; }
            public string? BrojPasosLicnaKarta { get; set; }
            public DateTime? DatumVaziOdPasosLicnaKarta { get; set; }
            public DateTime? DatumVaziDoPasosLicnaKarta { get; set; }
            public int? ListaDejnostiIdDejnost { get; set; }
            public string? Email { get; set; }
            public string? Tel { get; set; }
            public string? Webstrana { get; set; }
            public bool SoglasnostZaDirektenMarketing { get; set; }
            public bool SoglasnostZaEmailKomunikacija { get; set; }
            public bool SoglasnostZaTelKomunikacija { get; set; }
            public DateTime? DatumNaPovlecenaSoglasnostZaDirektenMarketing { get; set; }
            public string? VistinskiSopstvenik { get; set; }
            public string? VistinskiSopstvenikIme { get; set; }
            public string? VistinskiSopstvenikPrezime { get; set; }
            public bool NositelNaJF { get; set; }
            public string? OsnovZaNositelNaJF { get; set; }
            public bool ZasilenaAnaliza { get; set; }
            public int? NivoaNaRizikIdNivoNaRizik { get; set; }
            public DateTime? DaumNaDogovor { get; set; }
            public DateTime? DogovorVaziDo { get; set; }
            public string? BrojNaDogovor { get; set; }
            public string? DogovorOpredelenoNeopredeleno { get; set; }
            public string? PlateznaSmetka { get; set; }
            public int? SifrarnikBankiIdBanka { get; set; }
            public long? SifrarnikRabotniPoziciiIdRabotnaPozicija { get; set; }
            public long? SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica { get; set; }
            public long? SifrarnikRabotniPoziciiIdNadreden { get; set; }
            public DateTime? NadredenVaziOd { get; set; }
            public DateTime? NadredenDo { get; set; }
            public bool ZadolzitelnoLicenca { get; set; }
            public string? BrojNaResenieOdASOZaLicenca { get; set; }
            public DateTime? DatumNaResenieOdASOZaLicenca { get; set; }
            public DateTime? DatumNaOdzemenaLicenca { get; set; }
            public string? BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti { get; set; }
            public DateTime? DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti { get; set; }
            public string? ZivotNezivot { get; set; }
            public string KlientTip { get; set; } = string.Empty;  // Derived from KlientFizickoPravnoLice
            public string? KlientPol { get; set; }
            public bool KlientVraboten { get; set; }
            public bool KlientSorabotnik { get; set; }
            public bool BrokerskoDrustvo { get; set; }
            public bool Osiguritel { get; set; }
            public DateTime? DatumNaOvlastuvanje { get; set; }
            public string KlientFizickoPravnoLice { get; set; }
        }

        public List<KlientViewModel> Klienti { get; set; } = new();

        public async Task<IActionResult> OnGetAsync()
        {
            if (!await HasPageAccess("ListaKlienti"))
            {
                return RedirectToAccessDenied();
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                string sql = @"
                    SELECT *
                    FROM Klienti
                    ORDER BY DateCreated DESC";

                using (SqlCommand cmd = new SqlCommand(sql, connection))
                using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        var klient = new KlientViewModel
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            DateCreated = reader.IsDBNull(reader.GetOrdinal("DateCreated")) ? null : reader.GetDateTime(reader.GetOrdinal("DateCreated")),
                            UsernameCreated = reader.IsDBNull(reader.GetOrdinal("UsernameCreated")) ? null : reader.GetString(reader.GetOrdinal("UsernameCreated")),
                            DateModified = reader.IsDBNull(reader.GetOrdinal("DateModified")) ? null : reader.GetDateTime(reader.GetOrdinal("DateModified")),
                            UsernameModified = reader.IsDBNull(reader.GetOrdinal("UsernameModified")) ? null : reader.GetString(reader.GetOrdinal("UsernameModified")),
                            Naziv = reader.IsDBNull(reader.GetOrdinal("Naziv")) ? null : reader.GetString(reader.GetOrdinal("Naziv")),
                            Ime = reader.IsDBNull(reader.GetOrdinal("Ime")) ? null : reader.GetString(reader.GetOrdinal("Ime")),
                            Prezime = reader.IsDBNull(reader.GetOrdinal("Prezime")) ? null : reader.GetString(reader.GetOrdinal("Prezime")),
                            EMBG = reader.IsDBNull(reader.GetOrdinal("EMBG")) ? null : reader.GetString(reader.GetOrdinal("EMBG")),
                            EDB = reader.IsDBNull(reader.GetOrdinal("EDB")) ? null : reader.GetString(reader.GetOrdinal("EDB")),
                            Email = reader.IsDBNull(reader.GetOrdinal("Email")) ? null : reader.GetString(reader.GetOrdinal("Email")),
                            Tel = reader.IsDBNull(reader.GetOrdinal("Tel")) ? null : reader.GetString(reader.GetOrdinal("Tel")),
                            MB = reader.IsDBNull(reader.GetOrdinal("MB")) ? null : reader.GetString(reader.GetOrdinal("MB")),
                            ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija = reader.IsDBNull(reader.GetOrdinal("ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija")) ? null : reader.GetInt32(reader.GetOrdinal("ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija")),
                            UlicaOdDokumentZaIdentifikacija = reader.IsDBNull(reader.GetOrdinal("UlicaOdDokumentZaIdentifikacija")) ? null : reader.GetString(reader.GetOrdinal("UlicaOdDokumentZaIdentifikacija")),
                            BrojOdDokumentZaIdentifikacija = reader.IsDBNull(reader.GetOrdinal("BrojOdDokumentZaIdentifikacija")) ? null : reader.GetString(reader.GetOrdinal("BrojOdDokumentZaIdentifikacija")),
                            ListaOpstiniIdOpstinaZaKomunikacija = reader.IsDBNull(reader.GetOrdinal("ListaOpstiniIdOpstinaZaKomunikacija")) ? null : reader.GetInt32(reader.GetOrdinal("ListaOpstiniIdOpstinaZaKomunikacija")),
                            UlicaZaKomunikacija = reader.IsDBNull(reader.GetOrdinal("UlicaZaKomunikacija")) ? null : reader.GetString(reader.GetOrdinal("UlicaZaKomunikacija")),
                            BrojZaKomunikacija = reader.IsDBNull(reader.GetOrdinal("BrojZaKomunikacija")) ? null : reader.GetString(reader.GetOrdinal("BrojZaKomunikacija")),
                            DatumNaTekovnaSostojba = reader.IsDBNull(reader.GetOrdinal("DatumNaTekovnaSostojba")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumNaTekovnaSostojba")),
                            BrojPasosLicnaKarta = reader.IsDBNull(reader.GetOrdinal("BrojPasosLicnaKarta")) ? null : reader.GetString(reader.GetOrdinal("BrojPasosLicnaKarta")),
                            DatumVaziOdPasosLicnaKarta = reader.IsDBNull(reader.GetOrdinal("DatumVaziOdPasosLicnaKarta")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumVaziOdPasosLicnaKarta")),
                            DatumVaziDoPasosLicnaKarta = reader.IsDBNull(reader.GetOrdinal("DatumVaziDoPasosLicnaKarta")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumVaziDoPasosLicnaKarta")),
                            ListaDejnostiIdDejnost = reader.IsDBNull(reader.GetOrdinal("ListaDejnostiIdDejnost")) ? null : reader.GetInt32(reader.GetOrdinal("ListaDejnostiIdDejnost")),
                            Webstrana = reader.IsDBNull(reader.GetOrdinal("Webstrana")) ? null : reader.GetString(reader.GetOrdinal("Webstrana")),
                            SoglasnostZaDirektenMarketing = !reader.IsDBNull(reader.GetOrdinal("SoglasnostZaDirektenMarketing")) && reader.GetBoolean(reader.GetOrdinal("SoglasnostZaDirektenMarketing")),
                            SoglasnostZaEmailKomunikacija = !reader.IsDBNull(reader.GetOrdinal("SoglasnostZaEmailKomunikacija")) && reader.GetBoolean(reader.GetOrdinal("SoglasnostZaEmailKomunikacija")),
                            SoglasnostZaTelKomunikacija = !reader.IsDBNull(reader.GetOrdinal("SoglasnostZaTelKomunikacija")) && reader.GetBoolean(reader.GetOrdinal("SoglasnostZaTelKomunikacija")),
                            DatumNaPovlecenaSoglasnostZaDirektenMarketing = reader.IsDBNull(reader.GetOrdinal("DatumNaPovlecenaSoglasnostZaDirektenMarketing")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumNaPovlecenaSoglasnostZaDirektenMarketing")),
                            VistinskiSopstvenik = reader.IsDBNull(reader.GetOrdinal("VistinskiSopstvenik")) ? null : reader.GetString(reader.GetOrdinal("VistinskiSopstvenik")),
                            VistinskiSopstvenikIme = reader.IsDBNull(reader.GetOrdinal("VistinskiSopstvenikIme")) ? null : reader.GetString(reader.GetOrdinal("VistinskiSopstvenikIme")),
                            VistinskiSopstvenikPrezime = reader.IsDBNull(reader.GetOrdinal("VistinskiSopstvenikPrezime")) ? null : reader.GetString(reader.GetOrdinal("VistinskiSopstvenikPrezime")),
                            NositelNaJF = !reader.IsDBNull(reader.GetOrdinal("NositelNaJF")) && reader.GetBoolean(reader.GetOrdinal("NositelNaJF")),
                            OsnovZaNositelNaJF = reader.IsDBNull(reader.GetOrdinal("OsnovZaNositelNaJF")) ? null : reader.GetString(reader.GetOrdinal("OsnovZaNositelNaJF")),
                            ZasilenaAnaliza = !reader.IsDBNull(reader.GetOrdinal("ZasilenaAnaliza")) && reader.GetBoolean(reader.GetOrdinal("ZasilenaAnaliza")),
                            NivoaNaRizikIdNivoNaRizik = reader.IsDBNull(reader.GetOrdinal("NivoaNaRizikIdNivoNaRizik")) ? null : reader.GetInt32(reader.GetOrdinal("NivoaNaRizikIdNivoNaRizik")),
                            DaumNaDogovor = reader.IsDBNull(reader.GetOrdinal("DaumNaDogovor")) ? null : reader.GetDateTime(reader.GetOrdinal("DaumNaDogovor")),
                            DogovorVaziDo = reader.IsDBNull(reader.GetOrdinal("DogovorVaziDo")) ? null : reader.GetDateTime(reader.GetOrdinal("DogovorVaziDo")),
                            BrojNaDogovor = reader.IsDBNull(reader.GetOrdinal("BrojNaDogovor")) ? null : reader.GetString(reader.GetOrdinal("BrojNaDogovor")),
                            DogovorOpredelenoNeopredeleno = reader.IsDBNull(reader.GetOrdinal("DogovorOpredelenoNeopredeleno")) ? null : reader.GetString(reader.GetOrdinal("DogovorOpredelenoNeopredeleno")),
                            PlateznaSmetka = reader.IsDBNull(reader.GetOrdinal("PlateznaSmetka")) ? null : reader.GetString(reader.GetOrdinal("PlateznaSmetka")),
                            SifrarnikBankiIdBanka = reader.IsDBNull(reader.GetOrdinal("SifrarnikBankiIdBanka")) ? null : reader.GetInt32(reader.GetOrdinal("SifrarnikBankiIdBanka")),
                            SifrarnikRabotniPoziciiIdRabotnaPozicija = reader.IsDBNull(reader.GetOrdinal("SifrarnikRabotniPoziciiIdRabotnaPozicija")) ? null : reader.GetInt64(reader.GetOrdinal("SifrarnikRabotniPoziciiIdRabotnaPozicija")),
                            SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica = reader.IsDBNull(reader.GetOrdinal("SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica")) ? null : reader.GetInt64(reader.GetOrdinal("SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica")),
                            SifrarnikRabotniPoziciiIdNadreden = reader.IsDBNull(reader.GetOrdinal("SifrarnikRabotniPoziciiIdNadreden")) ? null : reader.GetInt64(reader.GetOrdinal("SifrarnikRabotniPoziciiIdNadreden")),
                            NadredenVaziOd = reader.IsDBNull(reader.GetOrdinal("NadredenVaziOd")) ? null : reader.GetDateTime(reader.GetOrdinal("NadredenVaziOd")),
                            NadredenDo = reader.IsDBNull(reader.GetOrdinal("NadredenDo")) ? null : reader.GetDateTime(reader.GetOrdinal("NadredenDo")),
                            ZadolzitelnoLicenca = !reader.IsDBNull(reader.GetOrdinal("ZadolzitelnoLicenca")) && reader.GetBoolean(reader.GetOrdinal("ZadolzitelnoLicenca")),
                            BrojNaResenieOdASOZaLicenca = reader.IsDBNull(reader.GetOrdinal("BrojNaResenieOdASOZaLicenca")) ? null : reader.GetString(reader.GetOrdinal("BrojNaResenieOdASOZaLicenca")),
                            DatumNaResenieOdASOZaLicenca = reader.IsDBNull(reader.GetOrdinal("DatumNaResenieOdASOZaLicenca")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumNaResenieOdASOZaLicenca")),
                            DatumNaOdzemenaLicenca = reader.IsDBNull(reader.GetOrdinal("DatumNaOdzemenaLicenca")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumNaOdzemenaLicenca")),
                            BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti = reader.IsDBNull(reader.GetOrdinal("BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti")) ? null : reader.GetString(reader.GetOrdinal("BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti")),
                            DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti = reader.IsDBNull(reader.GetOrdinal("DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti")),
                            ZivotNezivot = reader.IsDBNull(reader.GetOrdinal("ZivotNezivot")) ? null : reader.GetString(reader.GetOrdinal("ZivotNezivot")),
                            KlientPol = reader.IsDBNull(reader.GetOrdinal("KlientPol")) ? null : reader.GetString(reader.GetOrdinal("KlientPol")),
                            KlientVraboten = !reader.IsDBNull(reader.GetOrdinal("KlientVraboten")) && reader.GetBoolean(reader.GetOrdinal("KlientVraboten")),
                            KlientSorabotnik = !reader.IsDBNull(reader.GetOrdinal("KlientSorabotnik")) && reader.GetBoolean(reader.GetOrdinal("KlientSorabotnik")),
                            BrokerskoDrustvo = !reader.IsDBNull(reader.GetOrdinal("BrokerskoDrustvo")) && reader.GetBoolean(reader.GetOrdinal("BrokerskoDrustvo")),
                            Osiguritel = !reader.IsDBNull(reader.GetOrdinal("Osiguritel")) && reader.GetBoolean(reader.GetOrdinal("Osiguritel")),
                            DatumNaOvlastuvanje = reader.IsDBNull(reader.GetOrdinal("DatumNaOvlastuvanje")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumNaOvlastuvanje")),
                            KlientFizickoPravnoLice = reader.GetString(reader.GetOrdinal("KlientFizickoPravnoLice"))
                        };
                        klient.KlientTip = klient.KlientFizickoPravnoLice == "F" ? "Физичко лице" : "Правно лице";
                        Klienti.Add(klient);
                    }
                }
            }

            return Page();
        }

        public class DeleteKlientModel
        {
            public long Id { get; set; }
        }

        public JsonResult OnPostDeleteKlient([FromBody] DeleteKlientModel model)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(
                    "DELETE FROM Klienti WHERE Id = @Id", 
                    connection))
                {
                    command.Parameters.AddWithValue("@Id", model.Id);
                    command.ExecuteNonQuery();
                }
            }
            
            return new JsonResult(new { success = true });
        }
    }
} 