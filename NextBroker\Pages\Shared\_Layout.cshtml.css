﻿a.navbar-brand {
    white-space: normal;
    text-align: center;
    word-break: break-all;
    color: #1b6ec2;
}

a {
    color: cornflowerblue;
}

    /* Navbar Link Styles */
    a.nav-link {
        color: #0077cc;
        position: relative;
        transition: color 0.3s ease-in-out;
        left: 7%;
    }

        a.nav-link:hover {
            background-color: rgba(76, 175, 80, 0.2) !important; /* Blue background */
            color: steelblue !important; /* Steel blue text */
            border-radius: 0.5rem;
        }

        a.nav-link::after {
            content: '';
            position: absolute;
            bottom: 0; /* Position the line at the bottom */
            left: 50%; /* Center the line */
            width: 0; /* Start with no width */
            height: 1px; /* Thickness of the blue line */
            background-color: #003366 !important; /* Blue color for the line */
            transition: width 0.2s ease-in-out, left 0.2s ease-in-out; /* Add transition for left */
            transform: translateX(-50%); /* Center the line horizontally */
        }

        a.nav-link:hover::after {
            width: 100%; /* Full width on hover */
            left: 50%; /* Keep it centered */
        }

/* Small faded gray lines between navbar links */
.nav-item:not(:first-child) {
    border-left: 1px solid rgba(211, 211, 211, 0.7); /* Light gray border */
}

/* Dropdown Menu Styles */
.nav-item .dropdown-menu {
    border-radius: 0.5rem; /* Rounded corners */
    border: 1px solid #e5e5e5;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: opacity 0.4s ease, visibility 0.4s ease, transform 0.4s ease;
}

.nav-item:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Dropdown Item Styles */
.navbar .dropdown-item {
    position: relative;
    color: #003366;
    transition: color 0.3s ease-in-out, background-color 0.3s ease-in-out;
}

.dropdown-item:hover {
    background-color: rgba(76, 175, 80, 0.2);
    color: #003366 !important;
    border-radius: 0.1rem;
}

.navbar .dropdown-item::after { /* Changed to ::after for the line at the bottom */
    content: '';
    position: absolute;
    bottom: 0; /* Position the line at the bottom */
    left: 0;
    width: 0;
    height: 1px; /* Thickness of the blue line */
    background-color: #003366; /* Blue color for the line */
    transition: width 0.3s ease-in-out;
}

.dropdown-item:hover::after { /* Changed to ::after for hover effect */
    width: 100%; /* Full width on hover */
}

/* Custom Styles for the Contact Dropdown */
.kontakt-dropdown {
    border-radius: 0.5rem; /* Rounded corners */
    border: 1px solid #e5e5e5;
    background-color: #ffffff; /* Background color */
    min-width: 250px; /* Minimum width for the dropdown */
    padding: 1rem; /* Padding around the content */
    z-index: 1000; /* Ensure it appears above other elements */

}

    /* Dropdown Item Styles */
    .kontakt-dropdown .dropdown-item {
        color: #003366; /* Text color */
        margin: 0.5rem 0; /* Spacing between items */
        padding: 0.5rem 1rem; /* Padding inside each item */
        position: relative; /* Needed for the underline effect */
        transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out; /* Ensure background transition */
    }

.kontakt-icon {
    width: 40px; /* Adjust size */
    height: auto; /* Maintain aspect ratio */
    margin-bottom: 10px; /* Add space between the icon and the items */
}

/* Dropdown Animation */
.nav-item .kontakt-dropdown {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: opacity 0.4s ease-in-out, visibility 0.4s ease-in-out, transform 0.4s ease-in-out;
}

.nav-item:hover .kontakt-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}


/* Button Styles */
.btn-primary {
    color: #fff;
    background-color: #1b6ec2;
    border-color: #1861ac;
}
    /* Hover effect */
    .btn-primary:hover {
        background-color: rgba(0, 51, 102, 0.3); /* Darker background on hover */
        border-radius: 8px; /* More rounded corners on hover */
        color: #003366 !important; /* Blue text on hover */
        border-color: #002a50; /* Slightly darker border on hover */
    }

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
    color: #fff;
    background-color: #1b6ec2;
    border-color: #1861ac;
}

/* Border Styles */
.navbar .border-top {
    border-top: 1px solid #e5e5e5;
}

.border-bottom {
    border-bottom: 1px solid #e5e5e5;
}

.box-shadow {
    box-shadow: 0 10px 30px rgba(0, 51, 102, 0.3);
}

/* Footer Styles */
.footer {
    position: absolute;
    bottom: 0;
    width: 10px;
}
/* Common styles for Регистрација and Најава links */
.navbar-nav .nav-link.registracija,
.navbar-nav .nav-link.najava {
    background-color: rgba(0, 51, 102, 0.8);
    color: white !important; /* Slightly darker background */
    border-radius: 5px;
    padding: 0.5rem 1rem; /* Consistent padding */
    margin-left: 0.5rem; /* Adds spacing between the two links */
    transition: background-color 0.3s ease, border-radius 0.3s ease;
    display: inline-block; /* Ensures the button fits the text */
    text-align: center; /* Center text within the button */
    flex-grow: 0; /* Prevents the button from stretching */
}

    /* Hover effect for thicker appearance */
    .navbar-nav .nav-link.registracija:hover,
    .navbar-nav .nav-link.najava:hover {
        background-color: rgba(0, 51, 102, 0.3); /* Darker background on hover */
        border-radius: 8px; /* More rounded corners on hover */
        color: #003366 !important;
    }

    /* Adding gray line between the two links */
    .navbar-nav .nav-link.registracija + .nav-link.najava {
        border-left: 1px solid rgba(211, 211, 211, 0.6); /* Light gray border */
        padding-left: 1rem; /* Adds space for the border */
    }

@media (max-width: 768px) {
    .navbar-nav {
        display: block; /* or flex, depending on your layout */
    }

    .nav-item {
        margin: 0; /* adjust margin if needed */
    }
}

/* Cookie Consent Styles */
.cookie-consent {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #333;
    color: #fff;
    padding: 15px;
    text-align: center;
    display: none; /* Hidden by default */
    z-index: 1000; /* Ensure it appears above other elements */
}

.cookie-consent a {
    color: #ffd700;
    text-decoration: underline;
}

.cookie-consent button {
    background-color: #ffd700;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    margin-left: 10px;
}

.cookie-consent-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    color: #333;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    display: none;
    max-width: 85%; /* Slightly reduced maximum width */
    width: 350px; /* Smaller default width */
    max-height: 80vh; /* Reduced maximum height */
    overflow-y: auto;
}

.cookie-consent-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.cookie-consent-text {
    background-color: #f0f0f0; /* Light gray background */
    border-radius: 15px; /* Rounded corners */
    padding: 20px; /* Add some padding */
    max-width: 90%; /* Ensure it doesn't get too wide */
    margin: 0 auto 20px; /* Center the content and add space below */
    text-align: center; /* Center the text */
}

.cookie-consent-modal h2,
.cookie-consent-modal .halk-cookies-title {
    font-size: 1.3em;
    margin-bottom: 10px;
    color: #003366; /* Assuming this is your main heading color. Adjust if different */
}

.cookie-consent-modal p {
    margin-bottom: 10px;
    font-size: 0.9em;
}

.cookie-consent-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px; /* Small gap between buttons */
}

.cookie-consent-modal button {
    background-color: rgba(76, 175, 80, 0.7);
    color: #003366;
    border: none;
    border-radius: 25px;
    padding: 12px 0;
    font-size: 18px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease, border-radius 0.3s ease;
    width: 240px;
    text-align: center;
    font-weight: bold;
    margin: 0; /* Remove any default margin */
}

.cookie-consent-modal button:hover {
    background-color: rgba(0, 51, 102, 0.3);
    transform: scale(1.05);
    border-radius: 25px;
}

#cookieSettings, #acceptCookies {
    background-color: rgba(76, 175, 80, 0.7);
}

#acceptCookies {
    color: #003366 !important;
}

#acceptCookies:hover {
    color: #003366 !important;
}

/* Adjust for smaller screens */
@media (max-width: 768px) {
    .cookie-consent-modal button {
        width: 100%;
        font-size: 16px;
        padding: 10px 0;
    }
}

/* Toggle Switch Styles */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    float: right;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #4CAF50; /* Changed to green */
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.cookie-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black */
    z-index: 999; /* Below the modal */
    display: none; /* Hidden by default */
}

/* Cookie Settings Modal Styles */
.settings-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1001;
    overflow: auto;
}

.settings-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 20px;
    border-radius: 10px;
    width: 80%;
    max-width: 600px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    position: relative;
}

.close-button {
    position: absolute;
    top: 10px;
    right: 20px;
    font-size: 24px;
    background: none;
    border: none;
    cursor: pointer;
}

.cookie-section {
    background-color: #f0f0f0; /* Light gray background */
    border-radius: 15px; /* Rounded edges */
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.cookie-description {
    flex: 1;
    padding-right: 20px;
}

.cookie-description h3 {
    margin-top: 0;
    color: #003366;
}

.cookie-description p {
    margin-bottom: 0;
    font-size: 0.9em;
}

.switch {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Adjust font sizes for smaller screens */
@media (max-width: 768px) {
    .cookie-consent-modal {
        width: 95%; /* Full width on small screens */
        padding: 15px;
    }

    .cookie-consent-modal h2,
    .cookie-consent-modal .halk-cookies-title {
        font-size: 1.4em;
    }

    .cookie-description h3 {
        font-size: 1.1em;
    }

    .cookie-description p {
        font-size: 0.8em;
    }

    .cookie-consent-modal button {
        width: 100%; /* Full width buttons on small screens */
        font-size: 16px;
    }
}

.cookie-consent-modal,
.cookie-consent-modal * {
    font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif !important;
}

.cookie-consent-text,
.cookie-consent-buttons,
.cookie-section,
.cookie-description {
    font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif !important;
}

.cookie-section {
    width: 100%;
    margin-bottom: 12px;
    padding: 10px;
}

.cookie-description h3 {
    font-size: 1em;
    margin-top: 0;
    margin-bottom: 5px;
}

.cookie-description p {
    font-size: 0.8em;
    margin-bottom: 5px;
}

.switch {
    transform: scale(0.8); /* Make the toggle switch slightly smaller */
}

.cookie-consent-modal button {
    padding: 8px 15px;
    font-size: 0.9em;
}

/* Adjust for even smaller screens */
@media (max-width: 480px) {
    .cookie-consent-modal {
        width: 90%;
        padding: 10px;
    }

    .cookie-consent-modal h2,
    .cookie-consent-modal .halk-cookies-title {
        font-size: 1.2em;
    }

    .cookie-description h3 {
        font-size: 0.9em;
    }

    .cookie-description p {
        font-size: 0.75em;
    }

    .cookie-consent-modal button {
        width: 100%;
        font-size: 0.85em;
    }
}

