﻿@page
@model IndexModel
@{
    ViewData["Title"] = "Дома";
}
<head>
    <link rel="stylesheet" href="~/css/Index.css" asp-append-version="true" />
</head>

<div class="welcome-container">
    <div class="welcome-card">
    <p class="access-message"><strong>Добредојдовте</strong></p>
    <br>
        <p class="access-message">Опциите на платформата ќе бидат достапни по најавата</p>
        <div class="login-section">
            <a href="/Login" class="login-button">
                <i class="fas fa-sign-in-alt"></i>
                Најава во системот
            </a>
        </div>
    </div>
</div>

<style type="text/css">
    .welcome-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: calc(100vh - 200px);
        padding: 20px;
    }

    .welcome-card {
        background: white;
        border-radius: 15px;
        padding: 40px;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        max-width: 500px;
        width: 100%;
    }

    .access-message {
        color: #666;
        font-size: 1.2rem;
        margin-bottom: 30px;
    }

    .login-section {
        margin-top: 20px;
    }

    .login-button {
        display: inline-block;
        background: linear-gradient(135deg, #0056b3 0%, #00326b 100%);
        color: white;
        padding: 15px 40px;
        border-radius: 30px;
        text-decoration: none;
        font-weight: bold;
        transition: all 0.3s ease;
        font-size: 1.1rem;
    }

    .login-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        color: white;
        text-decoration: none;
    }

    .login-button i {
        margin-right: 10px;
    }

    @@media (max-width: 768px) {
        .welcome-card {
            padding: 30px;
        }

        .access-message {
            font-size: 1rem;
        }
    }
</style>

