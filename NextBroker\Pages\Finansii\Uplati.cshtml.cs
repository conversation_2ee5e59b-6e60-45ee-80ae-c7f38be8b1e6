using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using Microsoft.Data.SqlClient;
using System.ComponentModel.DataAnnotations;
using OfficeOpenXml;
using System.IO;


namespace NextBroker.Pages.Finansii
{
    public class UplatiModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public bool HasAdminAccess { get; private set; }
        public bool PregledSiteUplati { get; private set; }
        public bool StorniranjeUplati { get; private set; }
        public string CurrentUsername { get; private set; }
        public List<UplataView> Uplati { get; set; } = new();
        public List<UplataView> StornoUplati { get; set; } = new();
        public List<TipNaPlakanjeView> TipoviNaPlakanje { get; set; } = new();

        [BindProperty(SupportsGet = true)]
        public DateTime? DatumOd { get; set; } = DateTime.Today;

        [BindProperty(SupportsGet = true)]
        public DateTime? DatumDo { get; set; } = DateTime.Today;

        [BindProperty(SupportsGet = true)]
        public string? SearchPolisaBroj { get; set; }

        public class UplataView
        {
            public long Id { get; set; }
            public DateTime? DateCreated { get; set; }
            public string UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string UsernameModified { get; set; }
            public DateTime UplataDatum { get; set; }
            public string TipNaPlakanje { get; set; }
            public string BrojIzvod { get; set; }
            public decimal Iznos { get; set; }
            public string? PolisaBroj { get; set; }
            public bool? Neraspredelena { get; set; }
            public bool? PovratNaSredstva { get; set; }
            public string BrojNaKasovIzvestaj { get; set; }
            public string BankaNaziv { get; set; }
            public long? KasaPrimi { get; set; }
            public string UplakjacNaziv { get; set; }
            public bool? Storno { get; set; }
            public DateTime? DatumStorno { get; set; }
            public int? StornoZaMesec { get; set; }
            public int? StornoZaGodina { get; set; }
            public long SifrarnikTipNaPlakanjeId { get; set; }
            public string BrojNaFaktura { get; set; }
            public bool? PreraspredelenaUplata { get; set; }
            public bool? SredstvaOdPreraspredelenaUplata { get; set; }
            public bool ImaPecatenaFiskalnaSmetka { get; set; }
        }

        public class TipNaPlakanjeView
        {
            public long Id { get; set; }
            public string TipNaPlakanje { get; set; }
        }

        [BindProperty]
        public UplataInputModel Input { get; set; } = new();

        public class UplataInputModel
        {
            [Required(ErrorMessage = "Внесете датум на уплата")]
            public DateTime UplataDatum { get; set; } = DateTime.Today;

            [Required(ErrorMessage = "Изберете тип на плаќање")]
            public long SifrarnikTipNaPlakanjeId { get; set; }

            [Required(ErrorMessage = "Внесете износ")]
            public decimal Iznos { get; set; }

            public string? PolisaBroj { get; set; }
            public bool Neraspredelena { get; set; }
            public bool PovratNaSredstva { get; set; }
            public long? KlientIdUplakjac { get; set; }
            public string? BrojNaFaktura { get; set; }
        }

        public class StornoUplataModel
        {
            public long UplataId { get; set; }
            public string DatumStorno { get; set; }
            public int StornoZaMesec { get; set; }
            public int StornoZaGodina { get; set; }
        }

        public class PreraspredbaUplataModel
        {
            public long UplataId { get; set; }
        }

        public class PechatiFiscalnaModel
        {
            public long UplataId { get; set; }
            public decimal Iznos { get; set; }
        }

        public UplatiModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public async Task<IActionResult> OnGetAsync()
        {
            if (!await HasPageAccess("Uplati"))
            {
                return RedirectToAccessDenied();
            }

            // Set default dates if none provided in query string
            if (!Request.Query.ContainsKey("DatumOd") && !Request.Query.ContainsKey("DatumDo"))
            {
                DatumOd = DateTime.Today;
                DatumDo = DateTime.Today;
            }

            HasAdminAccess = await HasPageAccess("UplatiAdmin");
            PregledSiteUplati = await HasPageAccess("PregledSiteUplati");
            StorniranjeUplati = await HasPageAccess("StorniranjeUplati");
            CurrentUsername = HttpContext.Session.GetString("Username");

            await LoadTipoviNaPlakanje();
            await LoadUplati();
            return Page();
        }

        private async Task LoadUplati()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                string sql = @"
                    SELECT u.Id, u.DateCreated, u.UsernameCreated, u.DateModified, u.UsernameModified,
                           u.UplataDatum, tp.TipNaPlakanje, u.BrojIzvod, u.Iznos, u.PolisaBroj,
                           u.Neraspredelena, u.PovratNaSredstva, u.BrojNaKasovIzvestaj,
                           b.Banka as BankaNaziv, u.KasaPrimi,
                           CASE 
                               WHEN k.KlientFizickoPravnoLice = 'P' THEN k.Naziv
                               WHEN k.KlientFizickoPravnoLice = 'F' THEN CONCAT(k.Ime, ' ', k.Prezime)
                               ELSE NULL
                           END as UplakjacNaziv,
                           u.SifrarnikTipNaPlakanjeId, u.BrojNaFaktura, u.PreraspredelenaUplata, u.SredstvaOdPreraspredelenaUplata,
                           dbo.VratiDaliImaPecatenaFiskalnaSmetkaPoUplataId(u.Id) as ImaPecatenaFiskalnaSmetka
                    FROM Uplati u
                    LEFT JOIN SifrarnikTipNaPlakanje tp ON u.SifrarnikTipNaPlakanjeId = tp.Id
                    LEFT JOIN SifrarnikBanki b ON u.SifrarnikBankiId = b.Id
                    LEFT JOIN Klienti k ON u.KlientIdUplakjac = k.Id
                    WHERE (u.Storno = 0 or u.Storno is null)";

                if (!PregledSiteUplati && !string.IsNullOrEmpty(CurrentUsername))
                {
                    sql += @"
                    AND (u.UsernameCreated = @Username)";
                }

                sql += @" AND (@DatumOd IS NULL OR u.UplataDatum >= @DatumOd)
                    AND (@DatumDo IS NULL OR u.UplataDatum <= @DatumDo)
                    AND (@SearchPolisaBroj IS NULL OR u.PolisaBroj = @SearchPolisaBroj)
                    ORDER BY u.DateCreated DESC";

                using (SqlCommand cmd = new SqlCommand(sql, connection))
                {
                    cmd.Parameters.AddWithValue("@DatumOd", (object)DatumOd ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@DatumDo", (object)DatumDo ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@SearchPolisaBroj", (object)SearchPolisaBroj ?? DBNull.Value);
                    if (!PregledSiteUplati && !string.IsNullOrEmpty(CurrentUsername))
                    {
                        cmd.Parameters.AddWithValue("@Username", CurrentUsername);
                    }

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        Uplati.Add(new UplataView
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            DateModified = reader["DateModified"] as DateTime?,
                            UsernameModified = reader["UsernameModified"] as string,
                            UplataDatum = reader.GetDateTime(reader.GetOrdinal("UplataDatum")),
                            TipNaPlakanje = reader["TipNaPlakanje"] as string,
                            BrojIzvod = reader["BrojIzvod"] as string,
                            Iznos = reader.GetDecimal(reader.GetOrdinal("Iznos")),
                            PolisaBroj = reader.IsDBNull(reader.GetOrdinal("PolisaBroj")) ? null : reader.GetString(reader.GetOrdinal("PolisaBroj")),
                            Neraspredelena = reader["Neraspredelena"] as bool?,
                            PovratNaSredstva = reader["PovratNaSredstva"] as bool?,
                            BrojNaKasovIzvestaj = reader["BrojNaKasovIzvestaj"] as string,
                            BankaNaziv = reader["BankaNaziv"] as string,
                            KasaPrimi = reader.IsDBNull(reader.GetOrdinal("KasaPrimi")) ? null : reader.GetInt64(reader.GetOrdinal("KasaPrimi")),
                            UplakjacNaziv = reader["UplakjacNaziv"] as string,
                            SifrarnikTipNaPlakanjeId = reader.GetInt64(reader.GetOrdinal("SifrarnikTipNaPlakanjeId")),
                            BrojNaFaktura = reader["BrojNaFaktura"] as string,
                            PreraspredelenaUplata = reader["PreraspredelenaUplata"] as bool?,
                            SredstvaOdPreraspredelenaUplata = reader["SredstvaOdPreraspredelenaUplata"] as bool?,
                            ImaPecatenaFiskalnaSmetka = reader.IsDBNull(reader.GetOrdinal("ImaPecatenaFiskalnaSmetka")) ? false : reader.GetInt32(reader.GetOrdinal("ImaPecatenaFiskalnaSmetka")) == 1
                        });
                    }
                }

                // For storno uplati
                sql = @"
                    SELECT u.Id, u.DateCreated, u.UsernameCreated, u.DateModified, u.UsernameModified,
                           u.UplataDatum, tp.TipNaPlakanje, u.BrojIzvod, u.Iznos, u.PolisaBroj,
                           u.Neraspredelena, u.PovratNaSredstva, u.BrojNaKasovIzvestaj,
                           b.Banka as BankaNaziv, u.KasaPrimi,
                           CASE 
                               WHEN k.KlientFizickoPravnoLice = 'P' THEN k.Naziv
                               WHEN k.KlientFizickoPravnoLice = 'F' THEN CONCAT(k.Ime, ' ', k.Prezime)
                               ELSE NULL
                           END as UplakjacNaziv,
                           u.Storno, u.DatumStorno, u.StornoZaMesec, u.StornoZaGodina,
                           u.SifrarnikTipNaPlakanjeId, u.BrojNaFaktura, u.PreraspredelenaUplata, u.SredstvaOdPreraspredelenaUplata,
                           dbo.VratiDaliImaPecatenaFiskalnaSmetkaPoUplataId(u.Id) as ImaPecatenaFiskalnaSmetka
                    FROM Uplati u
                    LEFT JOIN SifrarnikTipNaPlakanje tp ON u.SifrarnikTipNaPlakanjeId = tp.Id
                    LEFT JOIN SifrarnikBanki b ON u.SifrarnikBankiId = b.Id
                    LEFT JOIN Klienti k ON u.KlientIdUplakjac = k.Id
                    WHERE u.Storno = 1";

                if (!PregledSiteUplati && !string.IsNullOrEmpty(CurrentUsername))
                {
                    sql += @"
                    AND (u.UsernameCreated = @Username)";
                }

                sql += @" AND (@DatumOd IS NULL OR u.UplataDatum >= @DatumOd)
                    AND (@DatumDo IS NULL OR u.UplataDatum <= @DatumDo)
                    AND (@SearchPolisaBroj IS NULL OR u.PolisaBroj = @SearchPolisaBroj)
                    ORDER BY u.DateCreated DESC";

                using (SqlCommand cmd = new SqlCommand(sql, connection))
                {
                    cmd.Parameters.AddWithValue("@DatumOd", (object)DatumOd ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@DatumDo", (object)DatumDo ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@SearchPolisaBroj", (object)SearchPolisaBroj ?? DBNull.Value);
                    if (!PregledSiteUplati && !string.IsNullOrEmpty(CurrentUsername))
                    {
                        cmd.Parameters.AddWithValue("@Username", CurrentUsername);
                    }

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        StornoUplati.Add(new UplataView
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            DateModified = reader["DateModified"] as DateTime?,
                            UsernameModified = reader["UsernameModified"] as string,
                            UplataDatum = reader.GetDateTime(reader.GetOrdinal("UplataDatum")),
                            TipNaPlakanje = reader["TipNaPlakanje"] as string,
                            BrojIzvod = reader["BrojIzvod"] as string,
                            Iznos = reader.GetDecimal(reader.GetOrdinal("Iznos")),
                            PolisaBroj = reader.IsDBNull(reader.GetOrdinal("PolisaBroj")) ? null : reader.GetString(reader.GetOrdinal("PolisaBroj")),
                            Neraspredelena = reader["Neraspredelena"] as bool?,
                            PovratNaSredstva = reader["PovratNaSredstva"] as bool?,
                            BrojNaKasovIzvestaj = reader["BrojNaKasovIzvestaj"] as string,
                            BankaNaziv = reader["BankaNaziv"] as string,
                            KasaPrimi = reader.IsDBNull(reader.GetOrdinal("KasaPrimi")) ? null : reader.GetInt64(reader.GetOrdinal("KasaPrimi")),
                            UplakjacNaziv = reader["UplakjacNaziv"] as string,
                            Storno = reader["Storno"] as bool?,
                            DatumStorno = reader["DatumStorno"] as DateTime?,
                            StornoZaMesec = reader.IsDBNull(reader.GetOrdinal("StornoZaMesec")) ? null : reader.GetInt32(reader.GetOrdinal("StornoZaMesec")),
                            StornoZaGodina = reader.IsDBNull(reader.GetOrdinal("StornoZaGodina")) ? null : reader.GetInt32(reader.GetOrdinal("StornoZaGodina")),
                            SifrarnikTipNaPlakanjeId = reader.GetInt64(reader.GetOrdinal("SifrarnikTipNaPlakanjeId")),
                            BrojNaFaktura = reader["BrojNaFaktura"] as string,
                            PreraspredelenaUplata = reader["PreraspredelenaUplata"] as bool?,
                            SredstvaOdPreraspredelenaUplata = reader["SredstvaOdPreraspredelenaUplata"] as bool?,
                            ImaPecatenaFiskalnaSmetka = reader.IsDBNull(reader.GetOrdinal("ImaPecatenaFiskalnaSmetka")) ? false : reader.GetInt32(reader.GetOrdinal("ImaPecatenaFiskalnaSmetka")) == 1
                        });
                    }
                }
            }
        }

        private async Task LoadTipoviNaPlakanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            string username = HttpContext.Session.GetString("Username");

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, TipNaPlakanje 
                    FROM SifrarnikTipNaPlakanje 
                    WHERE TipNaPlakanje LIKE '%' + dbo.VratiImeEkspozituraPoUsername(@Username) + '%'
                    OR id = 6
                    ORDER BY TipNaPlakanje", connection))
                {
                    cmd.Parameters.AddWithValue("@Username", username ?? string.Empty);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        TipoviNaPlakanje.Add(new TipNaPlakanjeView
                        {
                            Id = reader.GetInt64(0),
                            TipNaPlakanje = reader.GetString(1)
                        });
                    }
                }
            }
        }
        

        public async Task<IActionResult> OnPostAsync()
        {
            if (!await HasPageAccess("UplatiAdmin"))
            {
                return RedirectToAccessDenied();
            }

            if (!ModelState.IsValid)
            {
                await LoadTipoviNaPlakanje();
                await LoadUplati();
                return Page();
            }

            string username = HttpContext.Session.GetString("Username");
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    INSERT INTO Uplati (
                        UplataDatum, SifrarnikTipNaPlakanjeId, Iznos, PolisaBroj,
                        Neraspredelena, PovratNaSredstva, KlientIdUplakjac, UsernameCreated,
                        BrojNaFaktura
                    ) VALUES (
                        @UplataDatum, @SifrarnikTipNaPlakanjeId, @Iznos, @PolisaBroj,
                        @Neraspredelena, @PovratNaSredstva, @KlientIdUplakjac, @UsernameCreated,
                        @BrojNaFaktura
                    )", connection))
                {
                    cmd.Parameters.AddWithValue("@UplataDatum", Input.UplataDatum);
                    cmd.Parameters.AddWithValue("@SifrarnikTipNaPlakanjeId", Input.SifrarnikTipNaPlakanjeId);
                    cmd.Parameters.AddWithValue("@Iznos", Input.Iznos);
                    cmd.Parameters.AddWithValue("@PolisaBroj", (object)Input.PolisaBroj ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@Neraspredelena", Input.Neraspredelena);
                    cmd.Parameters.AddWithValue("@PovratNaSredstva", Input.PovratNaSredstva);
                    cmd.Parameters.AddWithValue("@KlientIdUplakjac", (object)Input.KlientIdUplakjac ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@UsernameCreated", username);
                    cmd.Parameters.AddWithValue("@BrojNaFaktura", (object)Input.BrojNaFaktura ?? DBNull.Value);

                    await cmd.ExecuteNonQueryAsync();
                }
            }

            return RedirectToPage();
        }

        public async Task<JsonResult> OnPostStornoUplataAsync([FromBody] StornoUplataModel model)
        {
            if (!await HasPageAccess("UplatiAdmin"))
            {
                return new JsonResult(new { success = false, message = "Немате пристап" });
            }

            try
            {
                string username = HttpContext.Session.GetString("Username");
                if (string.IsNullOrEmpty(username))
                {
                    return new JsonResult(new { success = false, message = "Не е најавен корисник" });
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        UPDATE Uplati SET 
                            Storno = 1,
                            DatumStorno = @DatumStorno,
                            StornoZaMesec = @StornoZaMesec,
                            StornoZaGodina = @StornoZaGodina,
                            DateModified = GETDATE(),
                            UsernameModified = @UsernameModified
                        WHERE Id = @UplataId", connection))
                    {
                        cmd.Parameters.AddWithValue("@UplataId", model.UplataId);
                        cmd.Parameters.AddWithValue("@DatumStorno", DateTime.Parse(model.DatumStorno));
                        cmd.Parameters.AddWithValue("@StornoZaMesec", model.StornoZaMesec);
                        cmd.Parameters.AddWithValue("@StornoZaGodina", model.StornoZaGodina);
                        cmd.Parameters.AddWithValue("@UsernameModified", username);

                        await cmd.ExecuteNonQueryAsync();
                        return new JsonResult(new { success = true });
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        public async Task<JsonResult> OnGetSearchKlientiAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE MB LIKE @Search + '%'
                       OR EDB LIKE @Search + '%'
                       OR EMBG LIKE @Search + '%'
                       OR Naziv LIKE '%' + @Search + '%'
                       OR Ime LIKE '%' + @Search + '%'
                       OR Prezime LIKE '%' + @Search + '%'
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", searchTerm);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<IActionResult> OnGetExportExcelAsync()
        {
            if (!await HasPageAccess("Uplati"))
            {
                return RedirectToAccessDenied();
            }

            // Load the data
            await LoadTipoviNaPlakanje();
            await LoadUplati();

            // Configure EPPlus to use noncommercial license
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using (var package = new ExcelPackage())
            {
                // Create the Уплати worksheet
                var uplatiSheet = package.Workbook.Worksheets.Add("Уплати");
                
                // Add headers
                string[] headers = new string[] 
                {
                    "ИД", "Креирано на", "Креирано од", "Променето на", "Променето од",
                    "Датум на уплата", "Тип на плаќање", "Број на извод", "Износ",
                    "Број на полиса", "Нераспределена", "Поврат на средства",
                    "Број на касов извештај", "Банка", "Каса прими", "Уплаќач", "Број на фактура",
                    "Прераспределена", "Средства од прераспределена"
                };

                for (int i = 0; i < headers.Length; i++)
                {
                    uplatiSheet.Cells[1, i + 1].Value = headers[i];
                }

                // Add data
                int row = 2;
                foreach (var uplata in Uplati)
                {
                    uplatiSheet.Cells[row, 1].Value = uplata.Id;
                    uplatiSheet.Cells[row, 2].Value = uplata.DateCreated?.ToString("dd.MM.yyyy HH:mm");
                    uplatiSheet.Cells[row, 3].Value = uplata.UsernameCreated;
                    uplatiSheet.Cells[row, 4].Value = uplata.DateModified?.ToString("dd.MM.yyyy HH:mm");
                    uplatiSheet.Cells[row, 5].Value = uplata.UsernameModified;
                    uplatiSheet.Cells[row, 6].Value = uplata.UplataDatum.ToString("dd.MM.yyyy");
                    uplatiSheet.Cells[row, 7].Value = uplata.TipNaPlakanje;
                    uplatiSheet.Cells[row, 8].Value = uplata.BrojIzvod;
                    uplatiSheet.Cells[row, 9].Value = uplata.Iznos;
                    uplatiSheet.Cells[row, 10].Value = uplata.PolisaBroj;
                    uplatiSheet.Cells[row, 11].Value = uplata.Neraspredelena == true ? "Да" : "";
                    uplatiSheet.Cells[row, 12].Value = uplata.PovratNaSredstva == true ? "Да" : "";
                    uplatiSheet.Cells[row, 13].Value = uplata.BrojNaKasovIzvestaj;
                    uplatiSheet.Cells[row, 14].Value = uplata.BankaNaziv;
                    uplatiSheet.Cells[row, 15].Value = uplata.KasaPrimi;
                    uplatiSheet.Cells[row, 16].Value = uplata.UplakjacNaziv;
                    uplatiSheet.Cells[row, 17].Value = uplata.BrojNaFaktura;
                    uplatiSheet.Cells[row, 18].Value = uplata.PreraspredelenaUplata == true ? "Да" : "";
                    uplatiSheet.Cells[row, 19].Value = uplata.SredstvaOdPreraspredelenaUplata == true ? "Да" : "";
                    row++;
                }

                // Format the Уплати worksheet
                uplatiSheet.Cells[1, 1, 1, headers.Length].Style.Font.Bold = true;
                uplatiSheet.Cells[1, 1, row - 1, headers.Length].Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                uplatiSheet.Cells[1, 1, row - 1, headers.Length].Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                uplatiSheet.Cells[1, 1, row - 1, headers.Length].Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                uplatiSheet.Cells[1, 1, row - 1, headers.Length].Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                uplatiSheet.Cells[1, 9, row - 1, 9].Style.Numberformat.Format = "#,##0.00";
                uplatiSheet.Cells.AutoFitColumns();

                // Create the Сторно worksheet
                var stornoSheet = package.Workbook.Worksheets.Add("Сторно");
                
                // Add headers for Сторно
                string[] stornoHeaders = new string[] 
                {
                    "ИД", "Креирано на", "Креирано од", "Променето на", "Променето од",
                    "Датум на уплата", "Тип на плаќање", "Број на извод", "Износ",
                    "Број на полиса", "Нераспределена", "Поврат на средства",
                    "Број на касов извештај", "Банка", "Каса прими", "Уплаќач", "Број на фактура",
                    "Прераспределена", "Средства од прераспределена",
                    "Датум на сторно", "Месец", "Година"
                };

                for (int i = 0; i < stornoHeaders.Length; i++)
                {
                    stornoSheet.Cells[1, i + 1].Value = stornoHeaders[i];
                }

                // Add data to Сторно
                row = 2;
                foreach (var uplata in StornoUplati)
                {
                    stornoSheet.Cells[row, 1].Value = uplata.Id;
                    stornoSheet.Cells[row, 2].Value = uplata.DateCreated?.ToString("dd.MM.yyyy HH:mm");
                    stornoSheet.Cells[row, 3].Value = uplata.UsernameCreated;
                    stornoSheet.Cells[row, 4].Value = uplata.DateModified?.ToString("dd.MM.yyyy HH:mm");
                    stornoSheet.Cells[row, 5].Value = uplata.UsernameModified;
                    stornoSheet.Cells[row, 6].Value = uplata.UplataDatum.ToString("dd.MM.yyyy");
                    stornoSheet.Cells[row, 7].Value = uplata.TipNaPlakanje;
                    stornoSheet.Cells[row, 8].Value = uplata.BrojIzvod;
                    stornoSheet.Cells[row, 9].Value = uplata.Iznos;
                    stornoSheet.Cells[row, 10].Value = uplata.PolisaBroj;
                    stornoSheet.Cells[row, 11].Value = uplata.Neraspredelena == true ? "Да" : "";
                    stornoSheet.Cells[row, 12].Value = uplata.PovratNaSredstva == true ? "Да" : "";
                    stornoSheet.Cells[row, 13].Value = uplata.BrojNaKasovIzvestaj;
                    stornoSheet.Cells[row, 14].Value = uplata.BankaNaziv;
                    stornoSheet.Cells[row, 15].Value = uplata.KasaPrimi;
                    stornoSheet.Cells[row, 16].Value = uplata.UplakjacNaziv;
                    stornoSheet.Cells[row, 17].Value = uplata.BrojNaFaktura;
                    stornoSheet.Cells[row, 18].Value = uplata.PreraspredelenaUplata == true ? "Да" : "";
                    stornoSheet.Cells[row, 19].Value = uplata.SredstvaOdPreraspredelenaUplata == true ? "Да" : "";
                    stornoSheet.Cells[row, 20].Value = uplata.DatumStorno?.ToString("dd.MM.yyyy");
                    stornoSheet.Cells[row, 21].Value = uplata.StornoZaMesec;
                    stornoSheet.Cells[row, 22].Value = uplata.StornoZaGodina;
                    row++;
                }

                // Format the Сторно worksheet
                stornoSheet.Cells[1, 1, 1, stornoHeaders.Length].Style.Font.Bold = true;
                stornoSheet.Cells[1, 1, row - 1, stornoHeaders.Length].Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                stornoSheet.Cells[1, 1, row - 1, stornoHeaders.Length].Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                stornoSheet.Cells[1, 1, row - 1, stornoHeaders.Length].Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                stornoSheet.Cells[1, 1, row - 1, stornoHeaders.Length].Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                stornoSheet.Cells[1, 9, row - 1, 9].Style.Numberformat.Format = "#,##0.00";
                stornoSheet.Cells.AutoFitColumns();

                // Generate the file
                var content = package.GetAsByteArray();
                string fileName = $"Уплати_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx";

                return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
        }

        public async Task<JsonResult> OnGetSearchPolisiAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 5 
                        BrojNaPolisa,
                        ISNULL(BrojNaFakturaIzlezna, '') as BrojNaFakturaIzlezna
                    FROM Polisi 
                    WHERE (BrojNaPolisa LIKE '%' + @Search + '%'
                       OR BrojNaFakturaIzlezna LIKE '%' + @Search + '%')
                       AND (Storno is null or Storno = 0)
                    ORDER BY BrojNaPolisa", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", searchTerm);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            brojNaPolisa = reader["BrojNaPolisa"].ToString(),
                            brojNaFakturaIzlezna = reader["BrojNaFakturaIzlezna"]
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<JsonResult> OnPostPreraspredbaUplataAsync([FromBody] PreraspredbaUplataModel model)
        {
            if (!await HasPageAccess("UplatiAdmin"))
            {
                return new JsonResult(new { success = false, message = "Немате пристап" });
            }

            try
            {
                string username = HttpContext.Session.GetString("Username");
                if (string.IsNullOrEmpty(username))
                {
                    return new JsonResult(new { success = false, message = "Не е најавен корисник" });
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand("dbo.UplatiPreraspredelbaDodajVoLista", connection))
                    {
                        cmd.CommandType = System.Data.CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@UplataId", model.UplataId);
                        cmd.Parameters.AddWithValue("@UsernameCreated", username);

                        await cmd.ExecuteNonQueryAsync();
                        return new JsonResult(new { success = true });
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        public async Task<JsonResult> OnPostPechatiFiscalnaAsync([FromBody] PechatiFiscalnaModel model)
        {
            if (!await HasPageAccess("UplatiAdmin"))
            {
                return new JsonResult(new { success = false, message = "Немате пристап" });
            }

            try
            {
                string username = HttpContext.Session.GetString("Username");
                if (string.IsNullOrEmpty(username))
                {
                    return new JsonResult(new { success = false, message = "Не е најавен корисник" });
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    // First get the FiscalPrinter value using the function
                    int fiscalPrinterId = 0;
                    using (SqlCommand cmd = new SqlCommand("SELECT dbo.VratiTipNaPlakanjeFiscalPrinterId(@UplataId)", connection))
                    {
                        cmd.Parameters.AddWithValue("@UplataId", model.UplataId);
                        var result = await cmd.ExecuteScalarAsync();
                        if (result != null && result != DBNull.Value)
                        {
                            fiscalPrinterId = Convert.ToInt32(result);
                        }
                    }

                    // Insert into FiscalQueue table
                    using (SqlCommand cmd = new SqlCommand(@"
                        INSERT INTO FiscalQueue (UsernameCreated, UplataId, Iznos, FiscalPrinter)
                        VALUES (@UsernameCreated, @UplataId, @Iznos, @FiscalPrinter)", connection))
                    {
                        cmd.Parameters.AddWithValue("@UsernameCreated", username);
                        cmd.Parameters.AddWithValue("@UplataId", model.UplataId);
                        cmd.Parameters.AddWithValue("@Iznos", model.Iznos);
                        cmd.Parameters.AddWithValue("@FiscalPrinter", fiscalPrinterId);

                        await cmd.ExecuteNonQueryAsync();
                        return new JsonResult(new { success = true });
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }
    }
} 