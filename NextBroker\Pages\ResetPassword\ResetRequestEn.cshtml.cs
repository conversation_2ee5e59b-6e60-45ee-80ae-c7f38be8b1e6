using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using Microsoft.Data.SqlClient;
using System.Security.Cryptography;
using System.Text;

namespace RazorPortal.Pages
{
    public class ResetRequestEnModel : PageModel
    {
        private readonly IConfiguration _configuration;
        private readonly MailerService _mailerService;

        public ResetRequestEnModel(IConfiguration configuration, MailerService mailerService)
        {
            _configuration = configuration;
            _mailerService = mailerService;
        }

        [BindProperty]
        [Required(ErrorMessage = "Required field!")]
        public string EMB { get; set; }

        public string ErrorMessage { get; set; }
        public string SuccessMessage { get; set; }

        public async Task<IActionResult> OnPostAsync()
        {
            if (string.IsNullOrWhiteSpace(EMB))
            {
                ErrorMessage = "You must enter an ID number.";
                return Page();
            }

            var resetKey = GenerateRandomKey(8);
            var connectionString = _configuration.GetConnectionString("DefaultConnection");

            try
            {
                string userEmail = null;

                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    using (var selectCommand = new SqlCommand("SELECT email FROM users WHERE emb = @emb", connection))
                    {
                        selectCommand.Parameters.AddWithValue("@emb", EMB);

                        var result = await selectCommand.ExecuteScalarAsync();
                        if (result == null)
                        {
                            ErrorMessage = "User with the provided ID number does not exist.";
                            return Page();
                        }

                        userEmail = result.ToString();
                    }

                    using (var updateCommand = new SqlCommand("UPDATE users SET resetrequest = @resetrequest WHERE emb = @emb", connection))
                    {
                        updateCommand.Parameters.AddWithValue("@resetrequest", resetKey);
                        updateCommand.Parameters.AddWithValue("@emb", EMB);

                        await updateCommand.ExecuteNonQueryAsync();
                    }
                }

                var subject = "Password Reset - Online portal";
                var messageBody = GenerateEnglishEmailTemplate(resetKey);
                await _mailerService.SendEmailAsync(userEmail, subject, messageBody);

                SuccessMessage = "Password reset code has been sent to your email.";
                return RedirectToPage("/ResetPassword/ResetConfirmationEn");
            }
            catch (Exception ex)
            {
                ErrorMessage = $"An error occurred: {ex.Message}";
                return Page();
            }
        }

        private string GenerateRandomKey(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new RNGCryptoServiceProvider();
            var data = new byte[length];
            var result = new StringBuilder(length);

            random.GetBytes(data);
            foreach (var byteValue in data)
            {
                result.Append(chars[byteValue % chars.Length]);
            }

            return result.ToString();
        }

        private string GenerateEnglishEmailTemplate(string resetKey)
        {
            return $@"
<!DOCTYPE html>
<html lang=""en"">
<head>
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <title>Password Reset</title>
</head>
<body style=""font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f0f2f5;"">
    <div style=""max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 16px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,51,102,0.15);"">
        <div style=""background-color: #003366; padding: 30px; text-align: center; box-shadow: 0 4px 6px rgba(0,51,102,0.2);"">
            <h1 style=""color: white; margin: 0; font-size: 28px; text-shadow: 0 2px 4px rgba(0,0,0,0.1);"">Password Reset 🔐</h1>
        </div>
        
        <div style=""padding: 40px 30px;"">
            <p style=""font-size: 16px; line-height: 1.6; color: #333; margin-bottom: 30px;"">
                Hello,<br><br>
                We received a request to reset your password. 
                Please use the following code to reset your password:
            </p>
            
            <div style=""background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 12px; margin: 25px 0; 
                      box-shadow: 0 2px 8px rgba(0,51,102,0.08); border: 2px dashed #003366;"">
                <span style=""font-size: 32px; font-weight: bold; color: #003366; letter-spacing: 3px; text-shadow: 0 1px 2px rgba(0,51,102,0.1);
                           font-family: monospace;"">
                    {resetKey}
                </span>
            </div>
            
            <div style=""background-color: #f8f9fa; padding: 20px; border-radius: 12px; margin-top: 30px; box-shadow: 0 2px 8px rgba(0,51,102,0.08);"">
                <p style=""margin: 0; color: #666; font-size: 15px;"">
                    <span style=""color: #003366; font-weight: bold;"">🔒 For your security:</span>
                </p>
                <ul style=""list-style: none; padding: 10px 0; margin: 0;"">
                    <li style=""margin: 8px 0; color: #666; font-size: 14px;"">
                        <span style=""color: #4CAF50;"">•</span> This code is valid for 24 hours
                    </li>
                    <li style=""margin: 8px 0; color: #666; font-size: 14px;"">
                        <span style=""color: #4CAF50;"">•</span> Never share your code with others
                    </li>
                    <li style=""margin: 8px 0; color: #666; font-size: 14px;"">
                        <span style=""color: #4CAF50;"">•</span> If you didn't request a password reset, please ignore this message
                    </li>
                </ul>
            </div>
            
            <p style=""margin-top: 30px; font-size: 15px; color: #666; text-align: center; padding: 0 20px;"">
                Need help? <span style=""color: #003366; text-decoration: none; font-weight: 500;"">Feel free to contact us!</span>
            </p>
        </div>
        
        <div style=""background-color: #f8f9fa; padding: 25px; text-align: center; border-top: 1px solid #eee;"">
            <p style=""margin: 0; color: #666; font-size: 13px; line-height: 1.6;"">
                © {DateTime.Now.Year} Online Portal. All rights reserved.<br>
                <span style=""color: #999;"">This is an automated message, please do not reply to this email.</span>
            </p>
        </div>
    </div>
</body>
</html>";
        }
    }
} 