﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.Data.SqlClient;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using Microsoft.Data.SqlClient;

public class RegistrationModel : PageModel
{
    private readonly IConfiguration _configuration;

    public RegistrationModel(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    [BindProperty]
    [Display(Name = "Име")]
    [Required(ErrorMessage = "Задолжително внесете Име!")]
    public string FirstName { get; set; }

    [BindProperty]
    [Display(Name = "Презиме")]
    [Required(ErrorMessage = "Задолжително внесете Презиме!")]
    public string LastName { get; set; }

    [BindProperty]
    [Display(Name = "ЕМБ / ЕДБ")]
    [Required(ErrorMessage = "Задолжително внесете Единствен матичен број!")]
    [RegularExpression(@"^\d+$", ErrorMessage = "ЕМБ мора да содржи само броеви.")]
    public string EMB { get; set; }

    [BindProperty]
    [Display(Name = "E-Mail")]
    [Required(ErrorMessage = "Задолжително внесете E-Mail адреса!")]
    [EmailAddress(ErrorMessage = "Внесете валидна E-Mail адреса!")]
    public string Email { get; set; }

    [BindProperty]
    [Display(Name = "Телефон")]
    [Required(ErrorMessage = "Задолжително внесете Телефонски број!")]
    [RegularExpression(@"^\d+$", ErrorMessage = "Телефонскиот број мора да содржи само броеви.")]
    public string Phone { get; set; }

    [BindProperty]
    [Display(Name = "Корисничко име")]
    [Required(ErrorMessage = "Задолжително внесете Корисничко име!")]
    public string Username { get; set; }

    [BindProperty]
    [Display(Name = "Лозинка")]
    [Required(ErrorMessage = "Задолжително внесете Лозинка!")]
    [RegularExpression(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\da-zA-Z]).{8,}$", 
        ErrorMessage = "Лозинката мора да содржи најмалку 8 карактери, една голема буква, една бројка и еден специјален карактер.")]
    public string Password { get; set; }

    [BindProperty]
    [Display(Name = "Потврдете ја лозинката")]
    [Required(ErrorMessage = "Задолжително внесете Потврдена лозинка!")]
    [Compare("Password", ErrorMessage = "Лозинките не се совпаѓаат!")]
    public string ConfirmPassword { get; set; }

    public string ErrorMessage { get; set; }
    public string SuccessMessage { get; set; }

    public void OnPost()
    {
        // Validate the model
        if (!ModelState.IsValid)
        {
            ErrorMessage = "Сите полиња се задолжителни!";
            return;
        }

        string connectionString = _configuration.GetConnectionString("DefaultConnection");

        // Check if username already exists
        using (var conn = new SqlConnection(connectionString))
        {
            conn.Open();

            using (var cmd = new SqlCommand("SELECT COUNT(*) FROM [users] WHERE [username] = @Username", conn))
            {
                cmd.Parameters.AddWithValue("@Username", Username);
                int userCount = (int)cmd.ExecuteScalar();

                if (userCount > 0)
                {
                    ErrorMessage = "Одберете друго корисничко име!";
                    return;
                }
            }
        }

        // Check if emb already exists
        using (var conn = new SqlConnection(connectionString))
        {
            conn.Open();

            using (var cmd = new SqlCommand("SELECT COUNT(*) FROM [users] WHERE [emb] = @emb", conn))
            {
                cmd.Parameters.AddWithValue("@emb", EMB);
                int embCount = (int)cmd.ExecuteScalar();

                if (embCount > 0)
                {
                    ErrorMessage = "Внесете друг матичен број!";
                    return;
                }
            }
        }

        // Generate random salt (10 alphanumeric characters)
        string salt = GenerateSalt(10);

        // Generate AES key and IV
        byte[] key;
        byte[] iv;
        string passwordHash = HashPasswordAES(Password, salt, out key, out iv);

        // Convert key and IV to Base64 for storage
        string keyBase64 = Convert.ToBase64String(key);
        string ivBase64 = Convert.ToBase64String(iv);

        // Generate a reset request code (10 alphanumeric characters)
        string resetCode = GenerateSalt(10);

        // Insert user data into SQL Server
        using (var conn = new SqlConnection(connectionString))
        {
            conn.Open();

            using (var cmd = new SqlCommand(@"
            INSERT INTO users 
            (firstname, lastname, emb, username, email, phone, passwordhash, passwordsalt, passwordkey, passwordiv, resetrequest)
            VALUES (@FirstName, @LastName, @EMB, @Username, @Email, @Phone, @PasswordHash, @PasswordSalt, @PasswordKey, @PasswordIV, @ResetRequest)", conn))
            {
                cmd.Parameters.AddWithValue("@FirstName", FirstName);
                cmd.Parameters.AddWithValue("@LastName", LastName);
                cmd.Parameters.AddWithValue("@EMB", EMB);
                cmd.Parameters.AddWithValue("@Username", Username);
                cmd.Parameters.AddWithValue("@Email", Email);
                cmd.Parameters.AddWithValue("@Phone", Phone);
                cmd.Parameters.AddWithValue("@PasswordHash", passwordHash);
                cmd.Parameters.AddWithValue("@PasswordSalt", salt);
                cmd.Parameters.AddWithValue("@PasswordKey", keyBase64);
                cmd.Parameters.AddWithValue("@PasswordIV", ivBase64);
                cmd.Parameters.AddWithValue("@ResetRequest", resetCode);

                cmd.ExecuteNonQuery();
            }
        }

        // Send email with reset code using MailerService
        try
        {
            var mailerService = new MailerService(_configuration);
            string subject = "Код за потврда на регистрацијата";
            string messageBody = $@"
<!DOCTYPE html>
<html lang=""mk"">
<head>
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <title>Потврда на регистрација</title>
</head>
<body style=""font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f0f2f5;"">
    <div style=""max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 16px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,51,102,0.15);"">
        <div style=""background-color: #003366; padding: 30px; text-align: center; box-shadow: 0 4px 6px rgba(0,51,102,0.2);"">
            <h1 style=""color: white; margin: 0; font-size: 28px; text-shadow: 0 2px 4px rgba(0,0,0,0.1);"">Добредојдовте во NextBroker Core! 🎉</h1>
        </div>
        
        <div style=""padding: 40px 30px;"">
            <p style=""font-size: 16px; line-height: 1.6; color: #333; margin-bottom: 30px;"">
                Почитуван/а {FirstName} {LastName},<br><br>
                Ви благодариме за регистрацијата на NextBroker Core. 
                За да ја потврдите вашата регистрација, ве молиме користете го следниот код:
            </p>
            
            <div style=""background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 12px; margin: 25px 0; 
                      box-shadow: 0 2px 8px rgba(0,51,102,0.08); border: 2px dashed #003366;"">
                <span style=""font-size: 32px; font-weight: bold; color: #003366; letter-spacing: 3px; text-shadow: 0 1px 2px rgba(0,51,102,0.1);
                           font-family: monospace;"">
                    {resetCode}
                </span>
            </div>
            
            <div style=""background-color: #f8f9fa; padding: 20px; border-radius: 12px; margin-top: 30px; box-shadow: 0 2px 8px rgba(0,51,102,0.08);"">
                <p style=""margin: 0; color: #666; font-size: 15px;"">
                    <span style=""color: #003366; font-weight: bold;"">📋 Информации за вашата сметка:</span>
                </p>
                <ul style=""list-style: none; padding: 10px 0; margin: 0;"">
                    <li style=""margin: 8px 0; color: #666; font-size: 14px;"">
                        <span style=""color: #4CAF50;"">•</span> Корисничко име: {Username}
                    </li>
                    <li style=""margin: 8px 0; color: #666; font-size: 14px;"">
                        <span style=""color: #4CAF50;"">•</span> E-mail: {Email}
                    </li>
                </ul>
            </div>

            <div style=""background-color: #f8f9fa; padding: 20px; border-radius: 12px; margin-top: 20px; box-shadow: 0 2px 8px rgba(0,51,102,0.08);"">
                <p style=""margin: 0; color: #666; font-size: 15px;"">
                    <span style=""color: #003366; font-weight: bold;"">🔒 Важно:</span>
                </p>
                <ul style=""list-style: none; padding: 10px 0; margin: 0;"">
                    <li style=""margin: 8px 0; color: #666; font-size: 14px;"">
                        <span style=""color: #4CAF50;"">•</span> Кодот е валиден 24 часа
                    </li>
                    <li style=""margin: 8px 0; color: #666; font-size: 14px;"">
                        <span style=""color: #4CAF50;"">•</span> Никогаш не го споделувајте вашиот код со други
                    </li>
                    <li style=""margin: 8px 0; color: #666; font-size: 14px;"">
                        <span style=""color: #4CAF50;"">•</span> Ако не сте се регистрирале вие, игнорирајте ја оваа порака
                    </li>
                </ul>
            </div>
            
            <p style=""margin-top: 30px; font-size: 15px; color: #666; text-align: center; padding: 0 20px;"">
                Ви треба помош? <span style=""color: #003366; text-decoration: none; font-weight: 500;"">Слободно контактирајте нѐ!</span>
            </p>
        </div>
        
        <div style=""background-color: #f8f9fa; padding: 25px; text-align: center; border-top: 1px solid #eee;"">
            <p style=""margin: 0; color: #666; font-size: 13px; line-height: 1.6;"">
                © {DateTime.Now.Year} NextBroker Core. All rights reserved.<br>
                <span style=""color: #999;"">Ова е автоматска порака, ве молиме не одговарајте на овој емаил.</span>
            </p>
        </div>
    </div>
</body>
</html>";
            Task.Run(() => mailerService.SendEmailAsync(Email, subject, messageBody)).Wait();

            SuccessMessage = "Регистрацијата е успешна! Код за потврда е испратен на вашата е-пошта. За кратко ќе бидете автоматски пренасочени на страница за валидација.";
        }
        catch (Exception ex)
        {
            ErrorMessage = "Грешка при испраќање на е-пошта: " + ex.Message;
        }
    }

    private string GenerateSalt(int length)
    {
        const string validChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        var random = new Random();
        return new string(Enumerable.Repeat(validChars, length)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }

    private string HashPasswordAES(string password, string salt, out byte[] key, out byte[] iv)
    {
        using (var aes = Aes.Create())
        {
            // Generate a key based on the salt and password
            var keyGenerator = new Rfc2898DeriveBytes(password + salt, 16, 10000);

            key = keyGenerator.GetBytes(32); // 256 bits key
            iv = keyGenerator.GetBytes(16); // 128 bits IV

            using (var encryptor = aes.CreateEncryptor(key, iv))
            {
                using (var ms = new MemoryStream())
                {
                    using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                    {
                        using (var sw = new StreamWriter(cs))
                        {
                            sw.Write(password);
                        }
                        return Convert.ToBase64String(ms.ToArray());
                    }
                }
            }
        }
    }
}
