﻿@page
@model ResetRequestModel
@{
    ViewData["Title"] = "Промена на лозинка";
}

<link rel="stylesheet" href="~/css/login.css" asp-append-version="true" />

<div class="login-container">
    <h2>@ViewData["Title"]</h2>

    <form method="post">
        <div class="form-group">
            <label for="emb">Единствен матичен број</label>
            <input type="text" id="emb" name="EMB" class="form-control" 
                   pattern="^\d+$" title="Внесете валиден матичен број."
                   required />
            @if (!string.IsNullOrEmpty(Model.ErrorMessage))
            {
                <span class="text-danger">@Model.ErrorMessage</span>
            }
        </div>

        <button type="submit" class="btn btn-primary">Ресетирај лозинка</button>

<br />

@if (Model.ErrorMessage != null)
{
    <div class="alert alert-danger">@Model.ErrorMessage</div>
}
@if (Model.SuccessMessage != null)
{
    <div class="alert alert-success">@Model.SuccessMessage</div>
}

        <div class="links">
            <p><a asp-page="/Login">Назад кон најава</a></p>
        </div>
    </form>
</div>
