﻿@page
@model RazorPortal.Pages.UserValidationModel
@{
    ViewData["Title"] = "Потврда на корисник";
}

<link rel="stylesheet" href="~/css/login.css" asp-append-version="true" />

<div class="login-container">
    <h2>@ViewData["Title"]</h2>

    <form method="post">
        <div id="validationForm" @(Model.IsConfirmed ? "style='display:none;'" : "")>
            <div class="form-group">
                <label for="validationCode">Внесете го кодот за потврда</label>
                <input type="text" 
                       id="validationCode" 
                       name="validationCode" 
                       class="form-control" 
                       required 
                       placeholder="Внесете го кодот од E-Mail-от" />
                
                @if (!string.IsNullOrEmpty(Model.Message))
                {
                    <span class="text-danger">@Model.Message</span>
                }
            </div>

            <button type="submit" class="btn btn-primary">Потврди</button>
        </div>

        @if (Model.IsConfirmed)
        {
            <div class="alert alert-success text-center">
                <p style="color: #4CAF50; font-size: 24px; font-weight: bold; margin: 20px 0;">
                    ✅ Корисникот е потврден!
                </p>
                <p>Пренасочување за <span id="countdown" style="font-weight: bold; color: #4CAF50;">5</span> секунди...</p>
            </div>

            <script>
                let countdown = 5;
                const countdownElement = document.getElementById("countdown");

                const timer = setInterval(() => {
                    countdown--;
                    countdownElement.textContent = countdown;

                    if (countdown <= 0) {
                        clearInterval(timer);
                        window.location.href = '@Url.Page("/Login")';
                    }
                }, 1000);
            </script>
        }
    </form>
</div>
