using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.Collections.Generic;

namespace NextBroker.Pages.AdministrationPages
{
    public class ListaDejnostiModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public List<Dejnost> DejnostiList { get; set; }
        [TempData]
        public bool IsEditing { get; set; }

        public ListaDejnostiModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
            DejnostiList = new List<Dejnost>();
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("ListaDejnosti"))
            {
                return RedirectToAccessDenied();
            }

            LoadDejnosti();
            return Page();
        }

        private void LoadDejnosti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(
                    "SELECT Id, DateCreated, DateModified, SifraDejnost, NazivDejnost " +
                    "FROM ListaDejnosti ORDER BY SifraDejnost", connection))
                {
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            DejnostiList.Add(new Dejnost
                            {
                                Id = reader.GetInt32(0),
                                DateCreated = reader.IsDBNull(1) ? DateTime.MinValue : reader.GetDateTime(1),
                                DateModified = reader.IsDBNull(2) ? DateTime.MinValue : reader.GetDateTime(2),
                                SifraDejnost = reader.IsDBNull(3) ? string.Empty : reader.GetString(3),
                                NazivDejnost = reader.IsDBNull(4) ? string.Empty : reader.GetString(4)
                            });
                        }
                    }
                }
            }
        }

        public IActionResult OnPostToggleEdit()
        {
            IsEditing = !IsEditing;
            LoadDejnosti();
            return Page();
        }

        public IActionResult OnPostAddDejnost(string sifraDejnost, string nazivDejnost)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(
                    "INSERT INTO ListaDejnosti (SifraDejnost, NazivDejnost, DateCreated) " +
                    "VALUES (@SifraDejnost, @NazivDejnost, GETDATE())", 
                    connection))
                {
                    command.Parameters.AddWithValue("@SifraDejnost", sifraDejnost);
                    command.Parameters.AddWithValue("@NazivDejnost", nazivDejnost);
                    command.ExecuteNonQuery();
                }
            }
            
            return RedirectToPage();
        }

        public IActionResult OnPostDeleteDejnost([FromBody] DeleteDejnostModel model)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(
                    "DELETE FROM ListaDejnosti WHERE Id = @Id", 
                    connection))
                {
                    command.Parameters.AddWithValue("@Id", model.Id);
                    command.ExecuteNonQuery();
                }
            }
            
            return new JsonResult(new { success = true });
        }

        public IActionResult OnPostSaveChanges([FromBody] List<Dejnost> updates)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                foreach (var dejnost in updates)
                {
                    string currentValues = "SELECT SifraDejnost, NazivDejnost FROM ListaDejnosti WHERE Id = @Id";
                    using (SqlCommand getCommand = new SqlCommand(currentValues, connection))
                    {
                        getCommand.Parameters.AddWithValue("@Id", dejnost.Id);
                        using (SqlDataReader reader = getCommand.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                string currentSifra = reader.IsDBNull(0) ? string.Empty : reader.GetString(0);
                                string currentNaziv = reader.IsDBNull(1) ? string.Empty : reader.GetString(1);

                                if (currentSifra != dejnost.SifraDejnost || 
                                    currentNaziv != dejnost.NazivDejnost)
                                {
                                    reader.Close();
                                    using (SqlCommand command = new SqlCommand(
                                        "UPDATE ListaDejnosti SET " +
                                        "SifraDejnost = @SifraDejnost, " +
                                        "NazivDejnost = @NazivDejnost, " +
                                        "DateModified = GETDATE() " +
                                        "WHERE Id = @Id", connection))
                                    {
                                        command.Parameters.AddWithValue("@Id", dejnost.Id);
                                        command.Parameters.AddWithValue("@SifraDejnost", dejnost.SifraDejnost);
                                        command.Parameters.AddWithValue("@NazivDejnost", dejnost.NazivDejnost);
                                        command.ExecuteNonQuery();
                                    }
                                }
                            }
                        }
                    }
                }
            }
            IsEditing = false;
            return new JsonResult(new { success = true });
        }
    }

    public class Dejnost
    {
        public int Id { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime DateModified { get; set; }
        public string SifraDejnost { get; set; } = string.Empty;
        public string NazivDejnost { get; set; } = string.Empty;
    }

    public class DeleteDejnostModel
    {
        public int Id { get; set; }
    }
} 