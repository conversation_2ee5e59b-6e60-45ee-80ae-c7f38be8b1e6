@page
@model NextBroker.Pages.AdministrationPages.KontrolaPristapModel
@{
    ViewData["Title"] = "Контрола на пристап";
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container">
    <div class="row">
        <!-- Users Column -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Корисници</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <input type="text" 
                               id="userSearchBox" 
                               class="form-control form-control-sm" 
                               placeholder="Пребарувај корисници..."
                               autocomplete="off">
                    </div>
                    <div class="list-group" id="usersList">
                        @foreach (var user in Model.Users)
                        {
                            <a href="KontrolaPristap?username=@user" 
                               class="list-group-item list-group-item-action @(user == Model.SelectedUser ? "active" : "")"
                               data-username="@user.ToLower()">
                                @user
                            </a>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Available Pages Column -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Достапни страници</h5>
                </div>
                <div class="card-body">
                    @if (Model.SelectedUser != null)
                    {
                        <div class="list-group">
                            @foreach (var item in Model.AllPages)
                            {
                                @if (!Model.UserPages.Any(up => up.PageName == item.PageName))
                                {
                                    <div class="list-group-item">
                                        <div class="row align-items-center">
                                            <div class="col">
                                                <strong>@item.PageName</strong>
                                                @if (!string.IsNullOrEmpty(item.PageDescription))
                                                {
                                                    <br/>
                                                    <small>@item.PageDescription</small>
                                                }
                                            </div>
                                            <div class="col-auto">
                                                <form method="post" asp-page-handler="AddAccess">
                                                    <input type="hidden" name="username" value="@Model.SelectedUser" />
                                                    <input type="hidden" name="pageName" value="@item.PageName" />
                                                    <button type="submit" class="btn btn-sm btn-success">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                }
                            }
                        </div>
                    }
                    else
                    {
                        <p class="text-muted">Изберете корисник од левата колона</p>
                    }
                </div>
            </div>
        </div>

        <!-- User's Current Pages Column -->
        <div class="col-md-5">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        @if (Model.SelectedUser != null)
                        {
                            <span>Страници за корисник: @Model.SelectedUser</span>
                        }
                        else
                        {
                            <span>Страници на корисник</span>
                        }
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.SelectedUser != null)
                    {
                        <div class="list-group">
                            @foreach (var item in Model.UserPages)
                            {
                                <div class="list-group-item">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <strong>@item.PageName</strong>
                                            @if (!string.IsNullOrEmpty(item.PageDescription))
                                            {
                                                <br/>
                                                <small class="text-muted">@item.PageDescription</small>
                                            }
                                            <br/>
                                            <small>Доделено на: @item.DateGranted.ToString("dd.MM.yyyy HH:mm")</small>
                                        </div>
                                        <div class="col-auto">
                                            <form method="post" asp-page-handler="RemoveAccess">
                                                <input type="hidden" name="id" value="@item.Id" />
                                                <input type="hidden" name="username" value="@Model.SelectedUser" />
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Дали сте сигурни?')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <p class="text-muted">Изберете корисник од левата колона</p>
                    }
                </div>
            </div>
        </div>
    </div>
</div> 

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#userSearchBox').on('keyup', function() {
                var searchText = $(this).val().toLowerCase();
                
                $('#usersList a').each(function() {
                    var username = $(this).data('username');
                    $(this).toggle(username.includes(searchText));
                });
            });
        });
    </script>
} 