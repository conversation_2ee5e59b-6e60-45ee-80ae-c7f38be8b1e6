@page
@model NextBroker.Pages.Polisi.ZadolzuvanjeRazdolzuvanjeDodajModel
@{
    ViewData["Title"] = "ЗР Додај";
}

<h1>@ViewData["Title"]</h1>

<div class="row">
    <div class="col-md-6">
        <div asp-validation-summary="All" class="text-danger"></div>
        <form method="post">
            <div class="mb-3">
                <label class="form-label">Осигурител</label>
                <select asp-for="Input.KlientiIdOsiguritel" class="form-select" asp-items="Model.Osiguriteli">
                    <option value="">-- Избери осигурител --</option>
                </select>
            </div>

            <div class="mb-3">
                <label class="form-label">Класа на осигурување</label>
                <select asp-for="Input.KlasiOsiguruvanjeIdKlasa" class="form-select" asp-items="Model.KlasiOsiguruvanje" id="klasaDropdown">
                    <option value="">-- Избери класа --</option>
                </select>
            </div>

            <div class="mb-3">
                <label class="form-label">Продукт</label>
                <select asp-for="Input.ProduktiIdProdukt" class="form-select" id="produktDropdown">
                    <option value="">-- Избери продукт --</option>
                </select>
            </div>

            <div class="mb-3">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Број на полиса</label>
                        <input type="text" asp-for="Input.BrojNaPolisa" class="form-control" />
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Range на полиси</label>
                        <input type="text" asp-for="Input.RangeDo" class="form-control" />
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Број на понуда</label>
                        <input type="number" asp-for="Input.BrojNaPonuda" class="form-control" />
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Range на понуди</label>
                        <input type="number" asp-for="Input.RangePonudaDo" class="form-control" />
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label">Задолжен</label>
                <select asp-for="Input.KlientiIdZadolzen" class="form-select" asp-items="Model.Zadolzeni">
                    <option value="">-- Избери задолжен --</option>
                </select>
            </div>

            <div class="mb-3">
                <label class="form-label">Датум на задолжување</label>
                <input type="date" asp-for="Input.DatumNaZadolzuvanje" class="form-control" />
            </div>

            <button type="submit" class="btn btn-primary">Зачувај</button>
            <a href="/Polisi/ZadolzuvanjeRazdolzuvanjePolisi" class="btn btn-secondary">Назад</a>
        </form>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#klasaDropdown').change(function() {
                var klasaId = $(this).val();
                var produktDropdown = $('#produktDropdown');
                produktDropdown.empty();
                produktDropdown.append($('<option></option>').val('').text('-- Избери продукт --'));

                if (klasaId) {
                    $.getJSON(`?handler=Produkti&klasaId=${klasaId}`, function(data) {
                        $.each(data, function(index, item) {
                            produktDropdown.append($('<option></option>').val(item.id).text(item.text));
                        });
                    });
                }
            });
        });
    </script>
} 