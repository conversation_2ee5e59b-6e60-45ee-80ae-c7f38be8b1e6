using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.Threading.Tasks;

namespace NextBroker.Pages.Polisi
{
    public class ViewEditPolisaKlasa21Model : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public bool HasAdminAccess { get; private set; }

        public ViewEditPolisaKlasa21Model(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public async Task<IActionResult> OnGetAsync(long id)
        {
            if (!await HasPageAccess("ViewEditPolisaKlasa21"))
            {
                return RedirectToAccessDenied();
            }

            HasAdminAccess = await HasPageAccess("ViewEditPolisaKlasa21Admin");
            return Page();
        }
    }
}
