using RazorPortal.Services; // Ensure this using directive is included
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.IO;
using Microsoft.AspNetCore.DataProtection;
using NextBroker.Hubs;
using NextBroker.Services;

var builder = WebApplication.CreateBuilder(args);

/*
// Configure data protection to persist keys
var keyStoragePath = builder.Configuration["DataProtection:KeyStoragePath"] ?? Path.Combine(builder.Environment.ContentRootPath, "keys");
builder.Services.AddDataProtection()
    .PersistKeysToFileSystem(new DirectoryInfo(keyStoragePath))
    .SetApplicationName("RazorPortal");
*/


// Load HTTPS Certificate from file (for Docker)
if (builder.Environment.IsProduction() || Environment.GetEnvironmentVariable("DOCKER_ENV") == "true")
{
    var certPath = "/https/certificate.pfx"; // Path in Docker
    var certPassword = "latropenilno"; // Use the password you set

    builder.WebHost.ConfigureKestrel(serverOptions =>
    {
        // Set the application to listen on port 80 for HTTP and port 443 for HTTPS
        serverOptions.ListenAnyIP(80); // Listen on HTTP port
        serverOptions.ListenAnyIP(443, listenOptions =>
        {
            listenOptions.UseHttps(certPath, certPassword); // Use HTTPS with the certificate
        });
    });
}

// Add services to the container.
builder.Services.AddRazorPages();
builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(20);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

// Register the MailerService
builder.Services.AddTransient<MailerService>();

// Add configuration for email settings
builder.Services.Configure<EmailSettings>(builder.Configuration.GetSection("EmailSettings"));

// Add these lines if they're not already present
builder.Services.AddHttpContextAccessor();
builder.Services.AddSingleton<IConfiguration>(builder.Configuration);

// Add before var app = builder.Build();
builder.Services.AddSignalR();
builder.Services.AddScoped<NotificationService>();
builder.Services.AddHostedService<NotificationTimerService>();
builder.Services.AddHostedService<NotificationBackgroundService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
}

// app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();
app.UseSession();
app.UseAuthorization();

app.MapRazorPages();

// Add after app.MapRazorPages();
app.MapHub<NotificationHub>("/notificationHub");

app.Run();

