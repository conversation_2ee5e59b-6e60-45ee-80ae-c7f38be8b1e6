upatstvo za promena na docker container
rushenje container:


docker stop nextbroker_app 
docker rm nextbroker_app 


za build na image:

docker build -t nextbroker_secure:latest .

 
za krevanje container za aplikacija:

docker run -d \
  --network razorportalnet \
  -p 5082:80 \
  -p 5083:443 \
  --name nextbroker_app \
  --restart unless-stopped \
  -e DOCKER_ENV=true \
   nextbroker_secure:latest



-- ova e ostanato od onlineportal aplikacijata

za krevanje container za filesystem (prethodno e potrebno da se napravi load na image online_portal_sftp):

docker run -d --name online_portal_filesystem \
  -p 5090:22 \
  -v /ONLINE_PORTAL_FILESYSTEM:/home/<USER>/upload \
  --network razorportalnet \
  online_portal_sftp


filesystem credentials:   user: sftpuser  pass: latropenilno




za run na ngrok persistent:

cd /home/<USER>/scripts

 nohup ./start_ngrok_persistent.sh  &

