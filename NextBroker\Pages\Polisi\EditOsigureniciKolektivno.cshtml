@page "{id:long}"
@model NextBroker.Pages.Polisi.EditOsigureniciKolektivnoModel
@{
    ViewData["Title"] = "Уредување на осигуреници колективно";
}

<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h4>@ViewData["Title"]</h4>
            <h6>Полиса ID: @Model.PolisaId</h6>
        </div>
        <div class="card-body">
            @Html.AntiForgeryToken()
            @if (!ModelState.IsValid)
            {
                <div class="alert alert-danger">
                    <h5>Model State Errors:</h5>
                    <div asp-validation-summary="All" class="text-danger"></div>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger">
                    <h5>Error:</h5>
                    <p>@TempData["ErrorMessage"]</p>
                </div>
            }

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success">
                    <h5>Success:</h5>
                    <p>@TempData["SuccessMessage"]</p>
                </div>
            }

            @if (TempData["DebugInfo"] != null)
            {
                <div class="alert alert-info">
                    <h5>Debug Info:</h5>
                    <pre>@TempData["DebugInfo"]</pre>
                </div>
            }

            <!-- Monthly Calculation Section -->
            @if (Model.MesecnaPrestmetka != null && Model.MesecnaPrestmetka.Any())
            {
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Пресметка по месеци</h5>
                    </div>
                    <div class="card-body">
                        <!-- Summary Information -->
                        @if (Model.MesecnaPrestmetkaSumarno != null)
                        {
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <strong>Вкупно месеци во полиса:</strong><br />
                                                @Model.MesecnaPrestmetkaSumarno.VkupnoMesecinaPolisa
                                            </div>
                                            <div class="col-md-3">
                                                <strong>Вкупна премија за едно лице:</strong><br />
                                                @Model.MesecnaPrestmetkaSumarno.VkupnaPremijaZaEdnoLice.ToString("N2") ден.
                                            </div>
                                            <div class="col-md-3">
                                                <strong>Месечна премија за едно лице:</strong><br />
                                                @Model.MesecnaPrestmetkaSumarno.MesecnaPremijaZaEdnoLice.ToString("N2") ден.
                                            </div>
                                            <div class="col-md-3">
                                                <strong>Вкупна премија полиса:</strong><br />
                                                @Model.MesecnaPrestmetkaSumarno.VkupnaPremijaPolisa.ToString("N2") ден.
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-md-3">
                                                <strong>Вкупно месеци:</strong><br />
                                                @Model.MesecnaPrestmetkaSumarno.VkupnoMesecina
                                            </div>
                                            <div class="col-md-3">
                                                <strong>Вкупно осигуреници по месеци:</strong><br />
                                                @Model.MesecnaPrestmetkaSumarno.VkupnoOsigureniciMesecina
                                            </div>
                                            <div class="col-md-3">
                                                <strong>Просек осигуреници по месец:</strong><br />
                                                @Model.MesecnaPrestmetkaSumarno.ProsekOsigureniciPoMesec.ToString("N2")
                                            </div>
                                            <div class="col-md-3">
                                                @if (Model.MesecnaPrestmetkaSumarno.Razlika > 0)
                                                {
                                                    <strong>Разлика одобрување:</strong>
                                                }
                                                else if (Model.MesecnaPrestmetkaSumarno.Razlika < 0)
                                                {
                                                    <strong>Разлика задолжување:</strong>
                                                }
                                                else
                                                {
                                                    <strong>Разлика:</strong>
                                                }
                                                <br />
                                                @((Model.MesecnaPrestmetkaSumarno.Razlika * -1).ToString("N2")) ден.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }

                        <!-- Monthly Details Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>Месец бр.</th>
                                        <th>Месец почеток</th>
                                        <th>Месец крај</th>
                                        <th>Активни осигуреници бр.</th>
                                        <th>Месечна премија</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var mesec in Model.MesecnaPrestmetka)
                                    {
                                        <tr>
                                            <td>@mesec.MesecBroj</td>
                                            <td>@mesec.MesecPocetok.ToString("dd.MM.yyyy")</td>
                                            <td>@mesec.MesecKraj.ToString("dd.MM.yyyy")</td>
                                            <td>@mesec.AktivniOsigureniciBroj</td>
                                            <td>@mesec.MesecnaPremija.ToString("N2") ден.</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }

            <!-- Collective Insureds Table -->
            <div class="card mb-4">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Листа на осигуреници</h5>
                        <div class="btn-group">
                            <button class="btn btn-primary add-btn">
                                <i class="bi bi-plus"></i> Додај осигуреник
                            </button>
                            <button class="btn btn-success" id="uploadExcelBtn">
                                <i class="bi bi-file-earmark-excel"></i> Вчитај од Excel
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Име</th>
                                    <th>Презиме</th>
                                    <th>ЕМБГ</th>
                                    <th>Број на лична карта</th>
                                    <th>Број на пасош</th>
                                    <th>Општина</th>
                                    <th>Адреса</th>
                                    <th>Број</th>
                                    <th>Телефон</th>
                                    <th>Email</th>
                                    <th>Датум од</th>
                                    <th>Датум до</th>
                                    <th>Акции</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.OsigureniciKolektivno != null && Model.OsigureniciKolektivno.Any())
                                {
                                    @foreach (var osigurenik in Model.OsigureniciKolektivno)
                                    {
                                        <tr data-id="@osigurenik.Id" class="view-mode">
                                            <td>@osigurenik.Id</td>
                                            <td>@osigurenik.Ime</td>
                                            <td>@osigurenik.Prezime</td>
                                            <td>@osigurenik.EMBG</td>
                                            <td>@osigurenik.BrojNaLicnaKarta</td>
                                            <td>@osigurenik.BrojNaPasos</td>
                                            <td>@osigurenik.OpstinaNaziv</td>
                                            <td>@osigurenik.Adresa</td>
                                            <td>@osigurenik.Broj</td>
                                            <td>@osigurenik.Telefon</td>
                                            <td>@osigurenik.Email</td>
                                            <td>@(osigurenik.DatumOsiguruvanjeVaziOd?.ToString("dd.MM.yyyy"))</td>
                                            <td>@(osigurenik.DatumOsiguruvanjeVaziDo?.ToString("dd.MM.yyyy"))</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button class="btn btn-sm btn-primary edit-btn">
                                                        <i class="bi bi-pencil"></i> Измени
                                                    </button>
                                                    <button class="btn btn-sm btn-danger delete-btn">
                                                        <i class="bi bi-trash"></i> Избриши
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr data-id="@osigurenik.Id" class="edit-mode" style="display: none;">
                                            <td>@osigurenik.Id</td>
                                            <td><input type="text" class="form-control form-control-sm" name="Ime" value="@osigurenik.Ime" /></td>
                                            <td><input type="text" class="form-control form-control-sm" name="Prezime" value="@osigurenik.Prezime" /></td>
                                            <td><input type="text" class="form-control form-control-sm" name="EMBG" value="@osigurenik.EMBG" /></td>
                                            <td><input type="text" class="form-control form-control-sm" name="BrojNaLicnaKarta" value="@osigurenik.BrojNaLicnaKarta" /></td>
                                            <td><input type="text" class="form-control form-control-sm" name="BrojNaPasos" value="@osigurenik.BrojNaPasos" /></td>
                                            <td>
                                                <select class="form-control form-control-sm" name="ListaOpstiniId" asp-items="Model.Opstini">
                                                    <option value="">-- Изберете општина --</option>
                                                </select>
                                            </td>
                                            <td><input type="text" class="form-control form-control-sm" name="Adresa" value="@osigurenik.Adresa" /></td>
                                            <td><input type="text" class="form-control form-control-sm" name="Broj" value="@osigurenik.Broj" /></td>
                                            <td><input type="text" class="form-control form-control-sm" name="Telefon" value="@osigurenik.Telefon" /></td>
                                            <td><input type="text" class="form-control form-control-sm" name="Email" value="@osigurenik.Email" /></td>
                                            <td><input type="date" class="form-control form-control-sm" name="DatumOsiguruvanjeVaziOd" value="@osigurenik.DatumOsiguruvanjeVaziOd?.ToString("yyyy-MM-dd")" /></td>
                                            <td><input type="date" class="form-control form-control-sm" name="DatumOsiguruvanjeVaziDo" value="@osigurenik.DatumOsiguruvanjeVaziDo?.ToString("yyyy-MM-dd")" /></td>
                                            <td>
                                                <div class="btn-group">
                                                    <button class="btn btn-sm btn-success save-btn" type="button">
                                                        <i class="bi bi-check"></i> Зачувај
                                                    </button>
                                                    <button class="btn btn-sm btn-secondary cancel-btn" type="button">
                                                        <i class="bi bi-x"></i> Откажи
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="14" class="text-center text-muted">Нема внесени осигуреници.</td>
                                    </tr>
                                }
                                <!-- New row template (always present) -->
                                <tr id="newRowTemplate" class="edit-mode" style="display: none;">
                                    <td>Нов</td>
                                    <td><input type="text" class="form-control form-control-sm" name="Ime" /></td>
                                    <td><input type="text" class="form-control form-control-sm" name="Prezime" /></td>
                                    <td><input type="text" class="form-control form-control-sm" name="EMBG" /></td>
                                    <td><input type="text" class="form-control form-control-sm" name="BrojNaLicnaKarta" /></td>
                                    <td><input type="text" class="form-control form-control-sm" name="BrojNaPasos" /></td>
                                    <td>
                                        <select class="form-control form-control-sm" name="ListaOpstiniId" asp-items="Model.Opstini">
                                            <option value="">-- Изберете општина --</option>
                                        </select>
                                    </td>
                                    <td><input type="text" class="form-control form-control-sm" name="Adresa" /></td>
                                    <td><input type="text" class="form-control form-control-sm" name="Broj" /></td>
                                    <td><input type="text" class="form-control form-control-sm" name="Telefon" /></td>
                                    <td><input type="text" class="form-control form-control-sm" name="Email" /></td>
                                    <td><input type="date" class="form-control form-control-sm" name="DatumOsiguruvanjeVaziOd" /></td>
                                    <td><input type="date" class="form-control form-control-sm" name="DatumOsiguruvanjeVaziDo" /></td>
                                    <td>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-success save-new-btn" type="button">
                                                <i class="bi bi-check"></i> Зачувај
                                            </button>
                                            <button class="btn btn-sm btn-secondary cancel-new-btn" type="button">
                                                <i class="bi bi-x"></i> Откажи
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-md-12">
                    <a href="/Polisi/ListaPolisi" class="btn btn-secondary">Назад</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Excel Upload Modal -->
<div class="modal fade" id="excelUploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Вчитај осигуреници од Excel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="excelUploadForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label class="form-label">Excel документ</label>
                        <input type="file" class="form-control" name="excelFile" accept=".xls,.xlsx" required />
                    </div>
                    <div class="alert alert-info">
                        <h6>Формат на Excel документот:</h6>
                        <p class="mb-0">
                            Excel документот треба да ги содржи следните колони:
                            <ul>
                                <li>Ime</li>
                                <li>Prezime</li>
                                <li>EMBG</li>
                                <li>BrojNaLicnaKarta</li>
                                <li>BrojNaPasos</li>
                                <li>OpstinaKod</li>
                                <li>Adresa</li>
                                <li>Broj</li>
                                <li>Telefon</li>
                                <li>Email</li>
                                <li>DatumOsiguruvanjeVaziOd</li>
                                <li>DatumOsiguruvanjeVaziDo</li>
                            </ul>
                        </p>
                        <br/>
                        <div class="mt-3">
                            <a href="/templates/ListaOsigureniciKolektivnoTemplate.xlsx" 
                               class="btn btn-outline-primary" download>
                                <i class="fas fa-download me-1"></i> Превземи шаблон
                            </a>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Откажи</button>
                <button type="button" class="btn btn-primary" id="uploadExcelSubmit">Вчитај</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        const KLASA_ID = @Model.KlasaId;
        @if (Model.PolisaDatumVaziOd.HasValue)
        {
            <text>const POLISA_DATUM_VAZI_OD = '@Model.PolisaDatumVaziOd.Value.ToString("yyyy-MM-dd")';</text>
        }
        else
        {
            <text>const POLISA_DATUM_VAZI_OD = null;</text>
        }

        @if (Model.PolisaDatumVaziDo.HasValue)
        {
            <text>const POLISA_DATUM_VAZI_DO = '@Model.PolisaDatumVaziDo.Value.ToString("yyyy-MM-dd")';</text>
        }
        else
        {
            <text>const POLISA_DATUM_VAZI_DO = null;</text>
        }

        function validateFields(row) {
            if (KLASA_ID === 18) {
                const pasos = row.find('input[name="BrojNaPasos"]').val();
                if (!pasos) {
                    alert('За класа 18, бројот на пасош е задолжителен!');
                    return false;
                }
            } else {
                const licnaKarta = row.find('input[name="BrojNaLicnaKarta"]').val();
                if (!licnaKarta) {
                    alert('Бројот на лична карта е задолжителен!');
                    return false;
                }
            }
            return true;
        }

        $(document).ready(function() {
            // Auto-hide success message after 10 seconds
            const successMessage = $('.alert-success');
            if (successMessage.length > 0) {
                setTimeout(function() {
                    successMessage.fadeOut('slow');
                }, 10000);
            }

            // Function to setup date constraints
            function setupDateConstraints(container) {
                if (POLISA_DATUM_VAZI_OD && POLISA_DATUM_VAZI_DO) {
                    const policyStartDate = new Date(POLISA_DATUM_VAZI_OD);
                    const policyEndDate = new Date(POLISA_DATUM_VAZI_DO);
                    const policyDay = policyStartDate.getDate();
                    
                    const dateInputs = container.find('input[name="DatumOsiguruvanjeVaziOd"], input[name="DatumOsiguruvanjeVaziDo"]');
                    
                    dateInputs.each(function() {
                        const input = $(this);
                        
                        // Set min and max dates
                        input.attr('min', POLISA_DATUM_VAZI_OD);
                        input.attr('max', POLISA_DATUM_VAZI_DO);
                        
                        // Add change event to enforce day constraint
                        input.on('change', function() {
                            const selectedDate = new Date(this.value);
                            if (selectedDate && !isNaN(selectedDate.getTime())) {
                                // Adjust to policy day
                                selectedDate.setDate(policyDay);
                                
                                // Ensure it's still within policy range
                                if (selectedDate < policyStartDate) {
                                    selectedDate.setTime(policyStartDate.getTime());
                                } else if (selectedDate > policyEndDate) {
                                    selectedDate.setTime(policyEndDate.getTime());
                                }
                                
                                // Format back to YYYY-MM-DD
                                const formattedDate = selectedDate.getFullYear() + '-' + 
                                    String(selectedDate.getMonth() + 1).padStart(2, '0') + '-' + 
                                    String(selectedDate.getDate()).padStart(2, '0');
                                
                                this.value = formattedDate;
                            }
                        });
                    });
                }
            }

            // Edit button click
            $('.edit-btn').click(function() {
                const row = $(this).closest('tr');
                const id = row.data('id');
                const editRow = $(`tr.edit-mode[data-id="${id}"]`);
                
                // Set the current municipality in the dropdown
                const opstinaNaziv = row.find('td:eq(6)').text();
                const opstinaSelect = editRow.find('select[name="ListaOpstiniId"]');
                opstinaSelect.find('option').each(function() {
                    if ($(this).text() === opstinaNaziv) {
                        $(this).prop('selected', true);
                    }
                });
                
                // Setup date constraints for edit mode
                setupDateConstraints(editRow);
                
                row.hide();
                editRow.show();
            });

            // Cancel button click
            $('.cancel-btn').click(function() {
                const row = $(this).closest('tr');
                const id = row.data('id');
                row.hide();
                $(`tr.view-mode[data-id="${id}"]`).show();
            });

            // Save button click
            $('.save-btn').click(function() {
                const row = $(this).closest('tr');
                
                if (!validateFields(row)) {
                    return;
                }

                const id = row.data('id');
                const data = {
                    Id: id,
                    PolisaId: @Model.PolisaId,
                    Ime: row.find('input[name="Ime"]').val(),
                    Prezime: row.find('input[name="Prezime"]').val(),
                    EMBG: row.find('input[name="EMBG"]').val(),
                    BrojNaLicnaKarta: row.find('input[name="BrojNaLicnaKarta"]').val(),
                    BrojNaPasos: row.find('input[name="BrojNaPasos"]').val(),
                    ListaOpstiniId: row.find('select[name="ListaOpstiniId"]').val(),
                    Adresa: row.find('input[name="Adresa"]').val(),
                    Broj: row.find('input[name="Broj"]').val(),
                    Telefon: row.find('input[name="Telefon"]').val(),
                    Email: row.find('input[name="Email"]').val(),
                    DatumOsiguruvanjeVaziOd: row.find('input[name="DatumOsiguruvanjeVaziOd"]').val() || null,
                    DatumOsiguruvanjeVaziDo: row.find('input[name="DatumOsiguruvanjeVaziDo"]').val() || null
                };

                $.ajax({
                    url: '?handler=UpdateOsigurenik',
                    type: 'POST',
                    data: JSON.stringify(data),
                    contentType: 'application/json',
                    headers: {
                        "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Грешка при зачувување на промените: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('Грешка при зачувување на промените');
                    }
                });
            });

            // Delete button click
            $('.delete-btn').click(function() {
                if (!confirm('Дали сте сигурни дека сакате да го избришете овој осигуреник?')) {
                    return;
                }

                const row = $(this).closest('tr');
                const id = row.data('id');
                const data = { id: id };

                $.ajax({
                    url: '?handler=DeleteOsigurenik',
                    type: 'POST',
                    data: JSON.stringify(data),
                    contentType: 'application/json',
                    headers: {
                        "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Грешка при бришење на осигуреникот: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('Грешка при бришење на осигуреникот');
                    }
                });
            });

            // Add button click
            $('.add-btn').click(function() {
                $('#newRowTemplate').show();
                
                // Set default dates from policy
                if (POLISA_DATUM_VAZI_OD) {
                    $('#newRowTemplate').find('input[name="DatumOsiguruvanjeVaziOd"]').val(POLISA_DATUM_VAZI_OD);
                }
                if (POLISA_DATUM_VAZI_DO) {
                    $('#newRowTemplate').find('input[name="DatumOsiguruvanjeVaziDo"]').val(POLISA_DATUM_VAZI_DO);
                }
                
                // Setup date constraints for new row
                setupDateConstraints($('#newRowTemplate'));
            });

            // Save new button click
            $('.save-new-btn').click(function() {
                const row = $('#newRowTemplate');
                
                if (!validateFields(row)) {
                    return;
                }
                
                const data = {
                    PolisaId: @Model.PolisaId,
                    Ime: row.find('input[name="Ime"]').val(),
                    Prezime: row.find('input[name="Prezime"]').val(),
                    EMBG: row.find('input[name="EMBG"]').val(),
                    BrojNaLicnaKarta: row.find('input[name="BrojNaLicnaKarta"]').val(),
                    BrojNaPasos: row.find('input[name="BrojNaPasos"]').val(),
                    ListaOpstiniId: row.find('select[name="ListaOpstiniId"]').val(),
                    Adresa: row.find('input[name="Adresa"]').val(),
                    Broj: row.find('input[name="Broj"]').val(),
                    Telefon: row.find('input[name="Telefon"]').val(),
                    Email: row.find('input[name="Email"]').val(),
                    DatumOsiguruvanjeVaziOd: row.find('input[name="DatumOsiguruvanjeVaziOd"]').val() || null,
                    DatumOsiguruvanjeVaziDo: row.find('input[name="DatumOsiguruvanjeVaziDo"]').val() || null
                };

                $.ajax({
                    url: '?handler=AddOsigurenik',
                    type: 'POST',
                    data: JSON.stringify(data),
                    contentType: 'application/json',
                    headers: {
                        "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Грешка при зачувување на осигуреник: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('Грешка при зачувување на осигуреник');
                    }
                });
            });

            // Cancel new button click
            $('.cancel-new-btn').click(function() {
                $('#newRowTemplate').hide();
            });

            // Excel Upload
            $('#uploadExcelBtn').click(function() {
                $('#excelUploadModal').modal('show');
            });

            $('#uploadExcelSubmit').click(function() {
                var formData = new FormData();
                var fileInput = $('#excelUploadForm input[type="file"]')[0];
                
                if (fileInput.files.length === 0) {
                    alert('Ве молиме изберете Excel документ');
                    return;
                }
                
                formData.append('excelFile', fileInput.files[0]);
                
                $.ajax({
                    url: `?handler=ImportExcel&id=@Model.PolisaId`,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    headers: {
                        "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#excelUploadModal').modal('hide');
                            location.reload();
                        } else {
                            alert('Грешка при вчитување на Excel: ' + response.message);
                            if (response.invalidRows && response.invalidRows.length > 0) {
                                alert('Проблематични редови:\n' + response.invalidRows.join('\n'));
                            }
                        }
                    },
                    error: function() {
                        alert('Грешка при вчитување на Excel');
                    }
                });
            });
        });
    </script>
} 