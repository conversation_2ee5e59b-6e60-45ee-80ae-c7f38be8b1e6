using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Data;
using Microsoft.Data.SqlClient;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Collections.Generic;
using OfficeOpenXml;
using System.IO;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Drawing;
using OfficeOpenXml.Style;
using iText.Html2pdf;
using iText.Kernel.Pdf;
using System.Text;

namespace NextBroker.Pages.Pregledi
{
    public class ASORealiziranaProvizijaRow
    {
        public string BrojNaPolisa { get; set; }
        public decimal IznosNaPremija { get; set; }
        public string Naziv { get; set; }
        public string Klasa { get; set; }
        public string Produkt { get; set; }
        public string Klient { get; set; }
        public decimal ProcentNaProvizija { get; set; }
        public decimal IznosNaProvizija { get; set; }
        public string BrojFakturaZaProvizija { get; set; }
        public DateTime? DatumNaFaktura { get; set; }
        public decimal PremijaZaOsnovnaKlasa { get; set; }
        public decimal DopolnitelnoOsiguruvanjeKlasa1 { get; set; }
        public decimal DopolnitelnoOsiguruvanjeKlasa8 { get; set; }
        public decimal DopolnitelnoOsiguruvanjeAsistencaKlas10 { get; set; }
        public decimal VkupnaPremija { get; set; }
        public decimal UcestvoPremijaZaOsnovnaKlasa { get; set; }
        public decimal UcestvoDopolnitelnoOsiguruvanjeKlasa1 { get; set; }
        public decimal UcestvoDopolnitelnoOsiguruvanjeKlasa8 { get; set; }
        public decimal UcestvoDopolnitelnoOsiguruvanjeAsistencaKlasa10 { get; set; }
        public decimal ProvizijaZaOsnovnaKlasa { get; set; }
        public decimal ProvizijaDopolnitelnoOsiguruvanjeKlasa1 { get; set; }
        public decimal ProvizijaDopolnitelnoOsiguruvanjeKlasa8 { get; set; }
        public decimal ProvizijaDopolnitelnoOsiguruvanjeAsistencaKlasa10 { get; set; }
    }

    public class ASORealiziranaProvizija : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public ASORealiziranaProvizija(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
            InitializeQuarterOptions();
            InitializeYearOptions();
        }

        [BindProperty]
        public List<ASORealiziranaProvizijaRow> TableRows { get; set; } = new();
        [BindProperty(SupportsGet = true)]
        public string SelectedQuarter { get; set; }
        [BindProperty(SupportsGet = true)]
        public int SelectedYear { get; set; }
        public SelectList QuarterOptions { get; set; }
        public SelectList YearOptions { get; set; }
        public bool IsSaved { get; set; }
        public bool ShowConfirmButton { get; set; }
        public string SavedStatusMessage { get; set; }

        public int TotalBrojPolisi 
        { 
            get => TableRows?.Count ?? 0;
        }
        
        public decimal TotalPremija 
        { 
            get => TableRows?.Sum(r => r.IznosNaPremija) ?? 0;
        }
        
        public decimal TotalProvizija 
        { 
            get => TableRows?.Sum(r => r.IznosNaProvizija) ?? 0;
        }
        
        public decimal TotalProvizijaZaOsnovnaKlasa 
        { 
            get => TableRows?.Sum(r => r.ProvizijaZaOsnovnaKlasa) ?? 0;
        }
        
        public decimal TotalProvizijaDopolnitelnoKlasa1 
        { 
            get => TableRows?.Sum(r => r.ProvizijaDopolnitelnoOsiguruvanjeKlasa1) ?? 0;
        }
        
        public decimal TotalProvizijaDopolnitelnoKlasa8 
        { 
            get => TableRows?.Sum(r => r.ProvizijaDopolnitelnoOsiguruvanjeKlasa8) ?? 0;
        }
        
        public decimal TotalProvizijaDopolnitelnoAsistenca 
        { 
            get => TableRows?.Sum(r => r.ProvizijaDopolnitelnoOsiguruvanjeAsistencaKlasa10) ?? 0;
        }

        public void InitializeQuarterOptions()
        {
            var quarters = new List<SelectListItem>
            {
                new SelectListItem { Value = "1", Text = "1-ви квартал (01.01-31.03)" },
                new SelectListItem { Value = "2", Text = "2-ри квартал (01.01-30.06)" },
                new SelectListItem { Value = "3", Text = "3-ти квартал (01.01-30.09)" },
                new SelectListItem { Value = "4", Text = "4-ти квартал (01.01-31.12)" }
            };
            QuarterOptions = new SelectList(quarters, "Value", "Text");
        }

        public void InitializeYearOptions()
        {
            int currentYear = DateTime.Now.Year;
            int baseYear = 2025; // Base year to start from
            int endYear = currentYear; // Go forward 2 years from current year
            var years = new List<SelectListItem>();
            
            for (int year = baseYear; year <= endYear; year++)
            {
                years.Add(new SelectListItem { Value = year.ToString(), Text = year.ToString() });
            }
            
            YearOptions = new SelectList(years, "Value", "Text");
            if (SelectedYear == 0)
                SelectedYear = currentYear;
        }

        private (DateTime StartDate, DateTime EndDate) GetQuarterDateRange(string quarter, int year)
        {
            return quarter switch
            {
                "1" => (new DateTime(year, 1, 1), new DateTime(year, 3, 31)),
                "2" => (new DateTime(year, 1, 1), new DateTime(year, 6, 30)),
                "3" => (new DateTime(year, 1, 1), new DateTime(year, 9, 30)),
                "4" => (new DateTime(year, 1, 1), new DateTime(year, 12, 31)),
                _ => (new DateTime(year, 1, 1), new DateTime(year, 12, 31))
            };
        }

        public async Task<IActionResult> OnGetAsync()
        {
            if (!await HasPageAccess("ASORealiziranaProvizija"))
            {
                return RedirectToAccessDenied();
            }

            // Check if data is already saved for the selected period
            if (!string.IsNullOrEmpty(SelectedQuarter) && SelectedYear != 0)
            {
                var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);
                
                // First check if ASO data already exists
                var asoDataExists = await CheckIfASODataExistsAsync(startDate, endDate);
                
                if (asoDataExists)
                {
                    IsSaved = true;
                    SavedStatusMessage = "Податоците се веќе зачувани во ASO Realizirana Provizija таблицата.";
                    ShowConfirmButton = false;
                    
                                    // Load data for display
                TableRows = await LoadDataFromStoredProcedureAsync(startDate, endDate);
                }
                else
                {
                    IsSaved = false;
                    SavedStatusMessage = "";
                    ShowConfirmButton = false;
                }
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!await HasPageAccess("ASORealiziranaProvizija"))
            {
                return RedirectToAccessDenied();
            }

            if (string.IsNullOrEmpty(SelectedQuarter) || SelectedYear == 0)
            {
                ModelState.AddModelError("", "Молиме изберете година и квартал.");
                return Page();
            }

            try
            {
                // First, test if the stored procedure exists
                var storedProcedureExists = await TestStoredProcedureExistsAsync();
                
                if (!storedProcedureExists)
                {
                    ModelState.AddModelError("", "Сторед процедурата PresmetanaProvizija_ASO_SP не постои во базата на податоци.");
                    return Page();
                }
                
                var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);
                
                TableRows = await LoadDataFromStoredProcedureAsync(startDate, endDate);
                
                if (TableRows == null || TableRows.Count == 0)
                {
                    ModelState.AddModelError("", "Нема податоци за избраниот период.");
                }
                else
                {
                    // First check if ASO data already exists
                    var asoDataExists = await CheckIfASODataExistsAsync(startDate, endDate);
                    
                    if (asoDataExists)
                    {
                        IsSaved = true;
                        SavedStatusMessage = "Податоците се веќе зачувани во ASO Realizirana Provizija таблицата.";
                        ShowConfirmButton = false;
                    }
                    else
                    {
                        // Check if OBD1 data exists for this quarter
                        var obd1Exists = await CheckIfOBD1DataExistsAsync(startDate, endDate);
                        
                        if (obd1Exists)
                        {
                            try
                            {
                                // Auto-save ASO data since OBD1 exists
                                await SaveDataToDatabaseAsync(TableRows, startDate, endDate);
                                IsSaved = true;
                                SavedStatusMessage = "Податоците се автоматски зачувани бидејќи постои OBD1 извештај за овој период.";
                                ShowConfirmButton = false;
                            }
                            catch (Exception saveEx)
                            {
                                IsSaved = false;
                                SavedStatusMessage = "";
                                ShowConfirmButton = TableRows.Count > 0;
                            }
                        }
                        else
                        {
                            IsSaved = false;
                            SavedStatusMessage = "";
                            ShowConfirmButton = TableRows.Count > 0;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", $"Грешка при вчитување на податоците: {ex.Message}");
            }

            return Page();
        }

        public async Task<IActionResult> OnPostConfirmAsync()
        {
            if (!await HasPageAccess("ASORealiziranaProvizija"))
            {
                return RedirectToAccessDenied();
            }

            if (string.IsNullOrEmpty(SelectedQuarter) || SelectedYear == 0)
            {
                ModelState.AddModelError("", "Молиме изберете година и квартал.");
                return Page();
            }

            try
            {
                var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);
                
                // Check if ASO data already exists
                var asoDataExists = await CheckIfASODataExistsAsync(startDate, endDate);
                if (asoDataExists)
                {
                    ModelState.AddModelError("", "Податоците се веќе зачувани во ASO Realizirana Provizija таблицата.");
                    return Page();
                }

                // Load data from stored procedure
                var data = await LoadDataFromStoredProcedureAsync(startDate, endDate);
                
                if (data == null || data.Count == 0)
                {
                    ModelState.AddModelError("", "Нема податоци за зачувување.");
                    return Page();
                }

                // Save data to database
                await SaveDataToDatabaseAsync(data, startDate, endDate);
                
                // Update flags
                IsSaved = true;
                ShowConfirmButton = false;
                TableRows = data;
                SavedStatusMessage = "Податоците се успешно зачувани во базата на податоци.";
                
                ModelState.AddModelError("", "Податоците се успешно зачувани во базата на податоци.");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", $"Грешка при зачувување на податоците: {ex.Message}");
            }

            return Page();
        }

        public async Task<IActionResult> OnPostExportExcelAsync()
        {
            if (!await HasPageAccess("ASORealiziranaProvizija"))
            {
                return RedirectToAccessDenied();
            }

            if (string.IsNullOrEmpty(SelectedQuarter) || SelectedYear == 0)
            {
                ModelState.AddModelError("", "Молиме изберете година и квартал.");
                return Page();
            }

            try
            {
                var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);
                var data = await LoadDataFromStoredProcedureAsync(startDate, endDate);
                
                if (data == null || data.Count == 0)
                {
                    ModelState.AddModelError("", "Нема податоци за експорт.");
                    return Page();
                }

                var quarterName = GetQuarterDisplayName(SelectedQuarter);
                var fileName = $"ASO_Realizirana_Provizija_{SelectedYear}_{quarterName}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("ASO Realizirana Provizija");

                // Set up headers
                var headers = new[]
                {
                    "Број на полиса", "Износ на Премија", "Назив", "Класа", "Продукт", "Клиент",
                    "Процент на провизија", "Износ на провизија", "Бр. Фактура за провизија", "Датум на Фактура",
                    "Премија за основна класа", "Дополнително осигурување класа 1", "Дополнително осигурување класа 8",
                    "Дополнително осигурување асистенција класа 10", "Вкупна премија",
                    "% Учество премија за основна класа", "% Учество дополнително осигурување класа 1",
                    "% Учество дополнително осигурување класа 8", "% Учество дополнително осигурување асистенција класа 10",
                    "Провизија за основна класа", "Провизија дополнително осигурување класа 1",
                    "Провизија дополнително осигурување класа 8", "Провизија дополнително осигурување асистенција класа 10"
                };

                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[1, i + 1].Value = headers[i];
                    worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                    worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                }

                // Add data
                for (int row = 0; row < data.Count; row++)
                {
                    var item = data[row];
                    int col = 1;
                    
                    worksheet.Cells[row + 2, col++].Value = item.BrojNaPolisa;
                    worksheet.Cells[row + 2, col++].Value = item.IznosNaPremija;
                    worksheet.Cells[row + 2, col++].Value = item.Naziv;
                    worksheet.Cells[row + 2, col++].Value = item.Klasa;
                    worksheet.Cells[row + 2, col++].Value = item.Produkt;
                    worksheet.Cells[row + 2, col++].Value = item.Klient;
                    worksheet.Cells[row + 2, col++].Value = item.ProcentNaProvizija;
                    worksheet.Cells[row + 2, col++].Value = item.IznosNaProvizija;
                    worksheet.Cells[row + 2, col++].Value = item.BrojFakturaZaProvizija;
                    worksheet.Cells[row + 2, col++].Value = item.DatumNaFaktura?.ToString("dd.MM.yyyy");
                    worksheet.Cells[row + 2, col++].Value = item.PremijaZaOsnovnaKlasa;
                    worksheet.Cells[row + 2, col++].Value = item.DopolnitelnoOsiguruvanjeKlasa1;
                    worksheet.Cells[row + 2, col++].Value = item.DopolnitelnoOsiguruvanjeKlasa8;
                    worksheet.Cells[row + 2, col++].Value = item.DopolnitelnoOsiguruvanjeAsistencaKlas10;
                    worksheet.Cells[row + 2, col++].Value = item.VkupnaPremija;
                    worksheet.Cells[row + 2, col++].Value = item.UcestvoPremijaZaOsnovnaKlasa;
                    worksheet.Cells[row + 2, col++].Value = item.UcestvoDopolnitelnoOsiguruvanjeKlasa1;
                    worksheet.Cells[row + 2, col++].Value = item.UcestvoDopolnitelnoOsiguruvanjeKlasa8;
                    worksheet.Cells[row + 2, col++].Value = item.UcestvoDopolnitelnoOsiguruvanjeAsistencaKlasa10;
                    worksheet.Cells[row + 2, col++].Value = item.ProvizijaZaOsnovnaKlasa;
                    worksheet.Cells[row + 2, col++].Value = item.ProvizijaDopolnitelnoOsiguruvanjeKlasa1;
                    worksheet.Cells[row + 2, col++].Value = item.ProvizijaDopolnitelnoOsiguruvanjeKlasa8;
                    worksheet.Cells[row + 2, col++].Value = item.ProvizijaDopolnitelnoOsiguruvanjeAsistencaKlasa10;
                }

                // Add totals row
                int totalRow = data.Count + 2;
                worksheet.Cells[totalRow, 1].Value = "ВКУПНО";
                worksheet.Cells[totalRow, 1].Style.Font.Bold = true;
                worksheet.Cells[totalRow, 2].Value = data.Sum(r => r.IznosNaPremija);
                worksheet.Cells[totalRow, 8].Value = data.Sum(r => r.IznosNaProvizija);
                worksheet.Cells[totalRow, 15].Value = data.Sum(r => r.VkupnaPremija);
                worksheet.Cells[totalRow, 21].Value = data.Sum(r => r.ProvizijaZaOsnovnaKlasa);
                worksheet.Cells[totalRow, 22].Value = data.Sum(r => r.ProvizijaDopolnitelnoOsiguruvanjeKlasa1);
                worksheet.Cells[totalRow, 23].Value = data.Sum(r => r.ProvizijaDopolnitelnoOsiguruvanjeKlasa8);
                worksheet.Cells[totalRow, 24].Value = data.Sum(r => r.ProvizijaDopolnitelnoOsiguruvanjeAsistencaKlasa10);

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                var content = package.GetAsByteArray();
                return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", $"Грешка при експорт: {ex.Message}");
                return Page();
            }
        }

        public async Task<IActionResult> OnPostExportPdfAsync()
        {
            if (!await HasPageAccess("ASORealiziranaProvizija"))
            {
                return RedirectToAccessDenied();
            }

            if (string.IsNullOrEmpty(SelectedQuarter) || SelectedYear == 0)
            {
                ModelState.AddModelError("", "Молиме изберете година и квартал.");
                return Page();
            }

            try
            {
                var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear);
                var data = await LoadDataFromStoredProcedureAsync(startDate, endDate);
                
                if (data == null || data.Count == 0)
                {
                    ModelState.AddModelError("", "Нема податоци за експорт.");
                    return Page();
                }

                var quarterName = GetQuarterDisplayName(SelectedQuarter);
                var fileName = $"ASO_Realizirana_Provizija_{SelectedYear}_{quarterName}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";

                // Create HTML content
                var htmlContent = GeneratePdfHtml(data, SelectedYear, quarterName);

                // Convert HTML to PDF using a more robust approach
                byte[] pdfBytes;
                try
                {
                    using var pdfStream = new MemoryStream();
                    var converterProperties = new ConverterProperties();
                    HtmlConverter.ConvertToPdf(htmlContent, pdfStream, converterProperties);
                    pdfBytes = pdfStream.ToArray();
                }
                catch (Exception pdfEx)
                {
                    Console.WriteLine($"ASORealiziranaProvizija: PDF conversion error: {pdfEx.Message}");
                    // Fallback: return HTML content instead
                    return Content(htmlContent, "text/html");
                }

                return File(pdfBytes, "application/pdf", fileName);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ASORealiziranaProvizija: PDF export error: {ex.Message}");
                ModelState.AddModelError("", $"Грешка при експорт: {ex.Message}");
                return Page();
            }
        }

        private async Task<List<ASORealiziranaProvizijaRow>> LoadDataFromStoredProcedureAsync(DateTime startDate, DateTime endDate)
        {
            var rows = new List<ASORealiziranaProvizijaRow>();

            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                
                // Now call the stored procedure
                using var command = new SqlCommand("EXEC dbo.PresmetanaProvizija_ASO_SP @DatumOd, @DatumDo", connection);
                command.Parameters.AddWithValue("@DatumOd", startDate);
                command.Parameters.AddWithValue("@DatumDo", endDate);

                using var reader = await command.ExecuteReaderAsync();
                
                while (await reader.ReadAsync())
                {
                    try
                    {
                        rows.Add(new ASORealiziranaProvizijaRow
                        {
                            BrojNaPolisa = GetReaderValueByIndex(reader, 0), // Број на полиса
                            IznosNaPremija = GetReaderDecimalByIndex(reader, 1), // Износ на Премија за Исплата на провизија
                            Naziv = GetReaderValueByIndex(reader, 2), // Назив
                            Klasa = GetReaderValueByIndex(reader, 3), // Класа
                            Produkt = GetReaderValueByIndex(reader, 4), // Продукт
                            Klient = GetReaderValueByIndex(reader, 5), // Клиент
                            ProcentNaProvizija = GetReaderDecimalByIndex(reader, 6), // Процент на провизија
                            IznosNaProvizija = GetReaderDecimalByIndex(reader, 7), // Износ на провизија
                            BrojFakturaZaProvizija = GetReaderValueByIndex(reader, 8), // Бр. Фактура за провизија
                            DatumNaFaktura = GetReaderDateTimeByIndex(reader, 9), // Datum na Faktura
                            PremijaZaOsnovnaKlasa = GetReaderDecimalByIndex(reader, 10), // PremijaZaOsnovnaKlasa
                            DopolnitelnoOsiguruvanjeKlasa1 = GetReaderDecimalByIndex(reader, 11), // DopolnitelnoOsiguruvanjeKlasa1
                            DopolnitelnoOsiguruvanjeKlasa8 = GetReaderDecimalByIndex(reader, 12), // DopolnitelnoOsiguruvanjeKlasa8
                            DopolnitelnoOsiguruvanjeAsistencaKlas10 = GetReaderDecimalByIndex(reader, 13), // DopolnitelnoOsiguruvanjeAsistencaKlas10
                            VkupnaPremija = GetReaderDecimalByIndex(reader, 14), // VkupnaPremija
                            UcestvoPremijaZaOsnovnaKlasa = GetReaderDecimalByIndex(reader, 15), // %UcestvoPremijaZaOsnovnaKlasa
                            UcestvoDopolnitelnoOsiguruvanjeKlasa1 = GetReaderDecimalByIndex(reader, 16), // %UcestvoDopolnitelnoOsiguruvanjeKlasa1
                            UcestvoDopolnitelnoOsiguruvanjeKlasa8 = GetReaderDecimalByIndex(reader, 17), // %UcestvoDopolnitelnoOsiguruvanjeKlasa8
                            UcestvoDopolnitelnoOsiguruvanjeAsistencaKlasa10 = GetReaderDecimalByIndex(reader, 18), // %UcestvoDopolnitelnoOsiguruvanjeAsistencaKlasa10
                            ProvizijaZaOsnovnaKlasa = GetReaderDecimalByIndex(reader, 19), // ProvizijaZaOsnovnaKlasa
                            ProvizijaDopolnitelnoOsiguruvanjeKlasa1 = GetReaderDecimalByIndex(reader, 20), // ProvizijaProvizijaDopolnitelnoOsiguruvanjeKlasa1
                            ProvizijaDopolnitelnoOsiguruvanjeKlasa8 = GetReaderDecimalByIndex(reader, 21), // ProvizijaDopolnitelnoOsiguruvanjeKlasa8
                            ProvizijaDopolnitelnoOsiguruvanjeAsistencaKlasa10 = GetReaderDecimalByIndex(reader, 22) // ProvizijaDopolnitelnoOsiguruvanjeAsistencaKlasa10
                        });
                    }
                    catch (Exception ex)
                    {
                        throw;
                    }
                }
            }

            return rows;
        }

        private string GetReaderValueByIndex(SqlDataReader reader, int index)
        {
            try
            {
                return reader[index]?.ToString();
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        private decimal GetReaderDecimalByIndex(SqlDataReader reader, int index)
        {
            try
            {
                var value = reader[index];
                if (value == DBNull.Value || value == null)
                    return 0;
                return Convert.ToDecimal(value);
            }
            catch (Exception ex)
            {
                return 0;
            }
        }

        private DateTime? GetReaderDateTimeByIndex(SqlDataReader reader, int index)
        {
            try
            {
                var value = reader[index];
                if (value == DBNull.Value || value == null)
                    return null;
                return Convert.ToDateTime(value);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        private string GetQuarterDisplayName(string quarter)
        {
            return quarter switch
            {
                "1" => "1-ви_квартал",
                "2" => "2-ри_квартал",
                "3" => "3-ти_квартал",
                "4" => "4-ти_квартал",
                _ => "непознат_квартал"
            };
        }

        private async Task<bool> TestStoredProcedureExistsAsync()
        {
            try
            {
                using var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection"));
                await connection.OpenAsync();
                
                // Check if stored procedure exists
                using var checkCommand = new SqlCommand(@"
                    SELECT COUNT(*) 
                    FROM INFORMATION_SCHEMA.ROUTINES 
                    WHERE ROUTINE_NAME = 'PresmetanaProvizija_ASO_SP' 
                    AND ROUTINE_SCHEMA = 'dbo'
                    AND ROUTINE_TYPE = 'PROCEDURE'", connection);
                
                var count = await checkCommand.ExecuteScalarAsync();
                
                if (Convert.ToInt32(count) == 0)
                {
                    // Let's also check without schema
                    using var checkCommand2 = new SqlCommand(@"
                        SELECT COUNT(*) 
                        FROM INFORMATION_SCHEMA.ROUTINES 
                        WHERE ROUTINE_NAME = 'PresmetanaProvizija_ASO_SP'
                        AND ROUTINE_TYPE = 'PROCEDURE'", connection);
                    
                    var count2 = await checkCommand2.ExecuteScalarAsync();
                }
                
                return Convert.ToInt32(count) > 0;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        private string GeneratePdfHtml(List<ASORealiziranaProvizijaRow> data, int year, string quarterName)
        {
            var html = new StringBuilder();
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html>");
            html.AppendLine("<head>");
            html.AppendLine("<meta charset='utf-8'>");
            html.AppendLine("<style>");
            html.AppendLine("body { font-family: Arial, sans-serif; font-size: 6px; margin: 5px; }");
            html.AppendLine("table { width: 100%; border-collapse: collapse; margin-top: 5px; table-layout: fixed; }");
            html.AppendLine("th, td { border: 1px solid #ddd; padding: 1px; text-align: left; vertical-align: top; word-wrap: break-word; }");
            html.AppendLine("th { background-color: #f2f2f2; font-weight: bold; font-size: 5px; }");
            html.AppendLine("h1 { text-align: center; color: #333; font-size: 12px; margin-bottom: 5px; }");
            html.AppendLine(".total-row { font-weight: bold; background-color: #f9f9f9; }");
            html.AppendLine(".number-cell { text-align: right; }");
            html.AppendLine(".date-cell { text-align: center; }");
            html.AppendLine(".text-cell { text-align: left; }");
            html.AppendLine("</style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");
            html.AppendLine($"<h1>АСО Реализирана Провизија - {year} {quarterName}</h1>");

            html.AppendLine("<table>");
            html.AppendLine("<thead>");
            html.AppendLine("<tr>");
            html.AppendLine("<th style='width: 4%;'>Бр. полиса</th>");
            html.AppendLine("<th style='width: 4%;'>Износ премија</th>");
            html.AppendLine("<th style='width: 6%;'>Назив</th>");
            html.AppendLine("<th style='width: 3%;'>Класа</th>");
            html.AppendLine("<th style='width: 4%;'>Продукт</th>");
            html.AppendLine("<th style='width: 6%;'>Клиент</th>");
            html.AppendLine("<th style='width: 3%;'>% пров</th>");
            html.AppendLine("<th style='width: 4%;'>Износ пров</th>");
            html.AppendLine("<th style='width: 4%;'>Бр. факт</th>");
            html.AppendLine("<th style='width: 3%;'>Датум</th>");
            html.AppendLine("<th style='width: 4%;'>Премија осн</th>");
            html.AppendLine("<th style='width: 4%;'>Доп. кл.1</th>");
            html.AppendLine("<th style='width: 4%;'>Доп. кл.8</th>");
            html.AppendLine("<th style='width: 4%;'>Доп. асист</th>");
            html.AppendLine("<th style='width: 4%;'>Вкупна прем</th>");
            html.AppendLine("<th style='width: 3%;'>% уч. осн</th>");
            html.AppendLine("<th style='width: 3%;'>% уч. кл.1</th>");
            html.AppendLine("<th style='width: 3%;'>% уч. кл.8</th>");
            html.AppendLine("<th style='width: 3%;'>% уч. асист</th>");
            html.AppendLine("<th style='width: 4%;'>Пров. осн</th>");
            html.AppendLine("<th style='width: 4%;'>Пров. кл.1</th>");
            html.AppendLine("<th style='width: 4%;'>Пров. кл.8</th>");
            html.AppendLine("<th style='width: 4%;'>Пров. асист</th>");
            html.AppendLine("</tr>");
            html.AppendLine("</thead>");
            html.AppendLine("<tbody>");

            foreach (var row in data)
            {
                html.AppendLine("<tr>");
                html.AppendLine($"<td class='text-cell'>{row.BrojNaPolisa}</td>");
                html.AppendLine($"<td class='number-cell'>{row.IznosNaPremija.ToString("N0")}</td>");
                html.AppendLine($"<td class='text-cell'>{row.Naziv}</td>");
                html.AppendLine($"<td class='text-cell'>{row.Klasa}</td>");
                html.AppendLine($"<td class='text-cell'>{row.Produkt}</td>");
                html.AppendLine($"<td class='text-cell'>{row.Klient}</td>");
                html.AppendLine($"<td class='number-cell'>{row.ProcentNaProvizija.ToString("N0")}%</td>");
                html.AppendLine($"<td class='number-cell'>{row.IznosNaProvizija.ToString("N0")}</td>");
                html.AppendLine($"<td class='text-cell'>{row.BrojFakturaZaProvizija}</td>");
                html.AppendLine($"<td class='date-cell'>{row.DatumNaFaktura?.ToString("dd.MM")}</td>");
                html.AppendLine($"<td class='number-cell'>{row.PremijaZaOsnovnaKlasa.ToString("N0")}</td>");
                html.AppendLine($"<td class='number-cell'>{row.DopolnitelnoOsiguruvanjeKlasa1.ToString("N0")}</td>");
                html.AppendLine($"<td class='number-cell'>{row.DopolnitelnoOsiguruvanjeKlasa8.ToString("N0")}</td>");
                html.AppendLine($"<td class='number-cell'>{row.DopolnitelnoOsiguruvanjeAsistencaKlas10.ToString("N0")}</td>");
                html.AppendLine($"<td class='number-cell'>{row.VkupnaPremija.ToString("N0")}</td>");
                html.AppendLine($"<td class='number-cell'>{row.UcestvoPremijaZaOsnovnaKlasa.ToString("P0")}</td>");
                html.AppendLine($"<td class='number-cell'>{row.UcestvoDopolnitelnoOsiguruvanjeKlasa1.ToString("P0")}</td>");
                html.AppendLine($"<td class='number-cell'>{row.UcestvoDopolnitelnoOsiguruvanjeKlasa8.ToString("P0")}</td>");
                html.AppendLine($"<td class='number-cell'>{row.UcestvoDopolnitelnoOsiguruvanjeAsistencaKlasa10.ToString("P0")}</td>");
                html.AppendLine($"<td class='number-cell'>{row.ProvizijaZaOsnovnaKlasa.ToString("N0")}</td>");
                html.AppendLine($"<td class='number-cell'>{row.ProvizijaDopolnitelnoOsiguruvanjeKlasa1.ToString("N0")}</td>");
                html.AppendLine($"<td class='number-cell'>{row.ProvizijaDopolnitelnoOsiguruvanjeKlasa8.ToString("N0")}</td>");
                html.AppendLine($"<td class='number-cell'>{row.ProvizijaDopolnitelnoOsiguruvanjeAsistencaKlasa10.ToString("N0")}</td>");
                html.AppendLine("</tr>");
            }

            // Add totals row
            html.AppendLine("<tr class='total-row'>");
            html.AppendLine("<td>ВКУПНО</td>");
            html.AppendLine($"<td class='number-cell'>{data.Sum(r => r.IznosNaPremija).ToString("N0")}</td>");
            html.AppendLine("<td></td><td></td><td></td><td></td>");
            html.AppendLine($"<td></td><td class='number-cell'>{data.Sum(r => r.IznosNaProvizija).ToString("N0")}</td>");
            html.AppendLine("<td></td><td></td>");
            html.AppendLine($"<td class='number-cell'>{data.Sum(r => r.PremijaZaOsnovnaKlasa).ToString("N0")}</td>");
            html.AppendLine($"<td class='number-cell'>{data.Sum(r => r.DopolnitelnoOsiguruvanjeKlasa1).ToString("N0")}</td>");
            html.AppendLine($"<td class='number-cell'>{data.Sum(r => r.DopolnitelnoOsiguruvanjeKlasa8).ToString("N0")}</td>");
            html.AppendLine($"<td class='number-cell'>{data.Sum(r => r.DopolnitelnoOsiguruvanjeAsistencaKlas10).ToString("N0")}</td>");
            html.AppendLine($"<td class='number-cell'>{data.Sum(r => r.VkupnaPremija).ToString("N0")}</td>");
            html.AppendLine("<td></td><td></td><td></td><td></td>");
            html.AppendLine($"<td class='number-cell'>{data.Sum(r => r.ProvizijaZaOsnovnaKlasa).ToString("N0")}</td>");
            html.AppendLine($"<td class='number-cell'>{data.Sum(r => r.ProvizijaDopolnitelnoOsiguruvanjeKlasa1).ToString("N0")}</td>");
            html.AppendLine($"<td class='number-cell'>{data.Sum(r => r.ProvizijaDopolnitelnoOsiguruvanjeKlasa8).ToString("N0")}</td>");
            html.AppendLine($"<td class='number-cell'>{data.Sum(r => r.ProvizijaDopolnitelnoOsiguruvanjeAsistencaKlasa10).ToString("N0")}</td>");
            html.AppendLine("</tr>");

            html.AppendLine("</tbody>");
            html.AppendLine("</table>");
            html.AppendLine("</body>");
            html.AppendLine("</html>");

            return html.ToString();
        }

        private async Task<bool> CheckIfOBD1DataExistsAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                using var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection"));
                await connection.OpenAsync();
                
                using var command = new SqlCommand(@"
                    SELECT COUNT(*) 
                    FROM ASOKvartalenIzvestajArhiva 
                    WHERE StartDate = @StartDate AND EndDate = @EndDate", connection);
                
                command.Parameters.AddWithValue("@StartDate", startDate);
                command.Parameters.AddWithValue("@EndDate", endDate);
                
                var count = await command.ExecuteScalarAsync();
                return Convert.ToInt32(count) > 0;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        private async Task<bool> CheckIfASODataExistsAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                using var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection"));
                await connection.OpenAsync();
                
                using var command = new SqlCommand(@"
                    SELECT COUNT(*) 
                    FROM ASO_RealiziranaProvizija 
                    WHERE DatumOd = @DatumOd AND DatumDo = @DatumDo", connection);
                
                command.Parameters.AddWithValue("@DatumOd", startDate);
                command.Parameters.AddWithValue("@DatumDo", endDate);
                
                var count = await command.ExecuteScalarAsync();
                return Convert.ToInt32(count) > 0;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        private async Task SaveDataToDatabaseAsync(List<ASORealiziranaProvizijaRow> data, DateTime startDate, DateTime endDate)
        {
            using var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection"));
            await connection.OpenAsync();
            
            var username = HttpContext.Session.GetString("Username") ?? "System";
            
            foreach (var row in data)
            {
                using var command = new SqlCommand(@"
                    INSERT INTO ASO_RealiziranaProvizija (
                        DateCreated, UsernameCreated, DatumOd, DatumDo,
                        [Број на полиса], [Износ на Премија за Исплата на провизија], [Назив], [Класа], [Продукт], [Клиент],
                        [Процент на провизија], [Износ на провизија], [Бр. Фактура за провизија], [Datum na Faktura],
                        PremijaZaOsnovnaKlasa, DopolnitelnoOsiguruvanjeKlasa1, DopolnitelnoOsiguruvanjeKlasa8, 
                        DopolnitelnoOsiguruvanjeAsistencaKlas10, VkupnaPremija,
                        [%UcestvoPremijaZaOsnovnaKlasa], [%UcestvoDopolnitelnoOsiguruvanjeKlasa1], 
                        [%UcestvoDopolnitelnoOsiguruvanjeKlasa8], [%UcestvoDopolnitelnoOsiguruvanjeAsistencaKlasa10],
                        ProvizijaZaOsnovnaKlasa, ProvizijaProvizijaDopolnitelnoOsiguruvanjeKlasa1,
                        ProvizijaDopolnitelnoOsiguruvanjeKlasa8, ProvizijaDopolnitelnoOsiguruvanjeAsistencaKlas10
                    ) VALUES (
                        @DateCreated, @UsernameCreated, @DatumOd, @DatumDo,
                        @BrojNaPolisa, @IznosNaPremija, @Naziv, @Klasa, @Produkt, @Klient,
                        @ProcentNaProvizija, @IznosNaProvizija, @BrojFakturaZaProvizija, @DatumNaFaktura,
                        @PremijaZaOsnovnaKlasa, @DopolnitelnoOsiguruvanjeKlasa1, @DopolnitelnoOsiguruvanjeKlasa8,
                        @DopolnitelnoOsiguruvanjeAsistencaKlas10, @VkupnaPremija,
                        @UcestvoPremijaZaOsnovnaKlasa, @UcestvoDopolnitelnoOsiguruvanjeKlasa1,
                        @UcestvoDopolnitelnoOsiguruvanjeKlasa8, @UcestvoDopolnitelnoOsiguruvanjeAsistencaKlasa10,
                        @ProvizijaZaOsnovnaKlasa, @ProvizijaDopolnitelnoOsiguruvanjeKlasa1,
                        @ProvizijaDopolnitelnoOsiguruvanjeKlasa8, @ProvizijaDopolnitelnoOsiguruvanjeAsistencaKlasa10
                    )", connection);

                command.Parameters.AddWithValue("@DateCreated", DateTime.Now);
                command.Parameters.AddWithValue("@UsernameCreated", username);
                command.Parameters.AddWithValue("@DatumOd", startDate);
                command.Parameters.AddWithValue("@DatumDo", endDate);
                command.Parameters.AddWithValue("@BrojNaPolisa", (object)row.BrojNaPolisa ?? DBNull.Value);
                command.Parameters.AddWithValue("@IznosNaPremija", row.IznosNaPremija);
                command.Parameters.AddWithValue("@Naziv", (object)row.Naziv ?? DBNull.Value);
                command.Parameters.AddWithValue("@Klasa", (object)row.Klasa ?? DBNull.Value);
                command.Parameters.AddWithValue("@Produkt", (object)row.Produkt ?? DBNull.Value);
                command.Parameters.AddWithValue("@Klient", (object)row.Klient ?? DBNull.Value);
                command.Parameters.AddWithValue("@ProcentNaProvizija", row.ProcentNaProvizija);
                command.Parameters.AddWithValue("@IznosNaProvizija", row.IznosNaProvizija);
                command.Parameters.AddWithValue("@BrojFakturaZaProvizija", (object)row.BrojFakturaZaProvizija ?? DBNull.Value);
                command.Parameters.AddWithValue("@DatumNaFaktura", (object)row.DatumNaFaktura ?? DBNull.Value);
                command.Parameters.AddWithValue("@PremijaZaOsnovnaKlasa", row.PremijaZaOsnovnaKlasa);
                command.Parameters.AddWithValue("@DopolnitelnoOsiguruvanjeKlasa1", row.DopolnitelnoOsiguruvanjeKlasa1);
                command.Parameters.AddWithValue("@DopolnitelnoOsiguruvanjeKlasa8", row.DopolnitelnoOsiguruvanjeKlasa8);
                command.Parameters.AddWithValue("@DopolnitelnoOsiguruvanjeAsistencaKlas10", row.DopolnitelnoOsiguruvanjeAsistencaKlas10);
                command.Parameters.AddWithValue("@VkupnaPremija", row.VkupnaPremija);
                command.Parameters.AddWithValue("@UcestvoPremijaZaOsnovnaKlasa", row.UcestvoPremijaZaOsnovnaKlasa);
                command.Parameters.AddWithValue("@UcestvoDopolnitelnoOsiguruvanjeKlasa1", row.UcestvoDopolnitelnoOsiguruvanjeKlasa1);
                command.Parameters.AddWithValue("@UcestvoDopolnitelnoOsiguruvanjeKlasa8", row.UcestvoDopolnitelnoOsiguruvanjeKlasa8);
                command.Parameters.AddWithValue("@UcestvoDopolnitelnoOsiguruvanjeAsistencaKlasa10", row.UcestvoDopolnitelnoOsiguruvanjeAsistencaKlasa10);
                command.Parameters.AddWithValue("@ProvizijaZaOsnovnaKlasa", row.ProvizijaZaOsnovnaKlasa);
                command.Parameters.AddWithValue("@ProvizijaDopolnitelnoOsiguruvanjeKlasa1", row.ProvizijaDopolnitelnoOsiguruvanjeKlasa1);
                command.Parameters.AddWithValue("@ProvizijaDopolnitelnoOsiguruvanjeKlasa8", row.ProvizijaDopolnitelnoOsiguruvanjeKlasa8);
                command.Parameters.AddWithValue("@ProvizijaDopolnitelnoOsiguruvanjeAsistencaKlasa10", row.ProvizijaDopolnitelnoOsiguruvanjeAsistencaKlasa10);

                await command.ExecuteNonQueryAsync();
            }
        }
    }
}
