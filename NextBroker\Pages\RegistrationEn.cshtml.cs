using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;
using RazorPortal.Services;
using Microsoft.Data.SqlClient;
using System.Security.Cryptography;
using System.Text;

namespace RazorPortal.Pages
{
    public class RegistrationEnModel : PageModel
    {
        private readonly IConfiguration _configuration;
        private readonly MailerService _mailerService;

        public RegistrationEnModel(IConfiguration configuration, MailerService mailerService)
        {
            _configuration = configuration;
            _mailerService = mailerService;
        }

        [BindProperty]
        [Display(Name = "First Name")]
        [Required(ErrorMessage = "First Name is required!")]
        public string FirstName { get; set; }

        [BindProperty]
        [Display(Name = "Last Name")]
        [Required(ErrorMessage = "Last Name is required!")]
        public string LastName { get; set; }

        [BindProperty]
        [Display(Name = "ID Number")]
        [Required(ErrorMessage = "ID Number is required!")]
        [RegularExpression(@"^\d+$", ErrorMessage = "ID Number must contain only numbers.")]
        public string EMB { get; set; }

        [BindProperty]
        [Display(Name = "Email")]
        [Required(ErrorMessage = "Email address is required!")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address!")]
        public string Email { get; set; }

        [BindProperty]
        [Display(Name = "Phone")]
        [Required(ErrorMessage = "Phone number is required!")]
        [RegularExpression(@"^\d+$", ErrorMessage = "Phone number must contain only numbers.")]
        public string Phone { get; set; }

        [BindProperty]
        [Display(Name = "Username")]
        [Required(ErrorMessage = "Username is required!")]
        public string Username { get; set; }

        [BindProperty]
        [Display(Name = "Password")]
        [Required(ErrorMessage = "Password is required!")]
        [RegularExpression(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\da-zA-Z]).{8,}$",
            ErrorMessage = "Password must contain at least 8 characters, one uppercase letter, one number, and one special character.")]
        public string Password { get; set; }

        [BindProperty]
        [Display(Name = "Confirm Password")]
        [Required(ErrorMessage = "Password confirmation is required!")]
        [Compare("Password", ErrorMessage = "Passwords do not match!")]
        public string ConfirmPassword { get; set; }

        public string ErrorMessage { get; set; }
        public string SuccessMessage { get; set; }

        public void OnPost()
        {
            if (!ModelState.IsValid)
            {
                ErrorMessage = "All fields are required!";
                return;
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");

            // Check if username already exists
            using (var conn = new SqlConnection(connectionString))
            {
                conn.Open();

                using (var cmd = new SqlCommand("SELECT COUNT(*) FROM [users] WHERE [username] = @Username", conn))
                {
                    cmd.Parameters.AddWithValue("@Username", Username);
                    int userCount = (int)cmd.ExecuteScalar();

                    if (userCount > 0)
                    {
                        ErrorMessage = "Please choose a different username!";
                        return;
                    }
                }
            }

            // Check if emb already exists
            using (var conn = new SqlConnection(connectionString))
            {
                conn.Open();

                using (var cmd = new SqlCommand("SELECT COUNT(*) FROM [users] WHERE [emb] = @emb", conn))
                {
                    cmd.Parameters.AddWithValue("@emb", EMB);
                    int embCount = (int)cmd.ExecuteScalar();

                    if (embCount > 0)
                    {
                        ErrorMessage = "Please enter a different ID number!";
                        return;
                    }
                }
            }

            // Generate salt and hash password
            string salt = GenerateSalt(10);
            byte[] key;
            byte[] iv;
            string hashedPassword = HashPasswordAES(Password, salt, out key, out iv);
            string keyBase64 = Convert.ToBase64String(key);
            string ivBase64 = Convert.ToBase64String(iv);
            string resetCode = GenerateSalt(10);

            // Insert user data
            using (var conn = new SqlConnection(connectionString))
            {
                conn.Open();

                using (var cmd = new SqlCommand(@"
                    INSERT INTO users 
                    (firstname, lastname, emb, username, email, phone, passwordhash, passwordsalt, passwordkey, passwordiv, resetrequest, locked)
                    VALUES (@FirstName, @LastName, @EMB, @Username, @Email, @Phone, @PasswordHash, @PasswordSalt, @PasswordKey, @PasswordIV, @ResetRequest, 1)", conn))
                {
                    cmd.Parameters.AddWithValue("@FirstName", FirstName);
                    cmd.Parameters.AddWithValue("@LastName", LastName);
                    cmd.Parameters.AddWithValue("@EMB", EMB);
                    cmd.Parameters.AddWithValue("@Username", Username);
                    cmd.Parameters.AddWithValue("@Email", Email);
                    cmd.Parameters.AddWithValue("@Phone", Phone);
                    cmd.Parameters.AddWithValue("@PasswordHash", hashedPassword);
                    cmd.Parameters.AddWithValue("@PasswordSalt", salt);
                    cmd.Parameters.AddWithValue("@PasswordKey", keyBase64);
                    cmd.Parameters.AddWithValue("@PasswordIV", ivBase64);
                    cmd.Parameters.AddWithValue("@ResetRequest", resetCode);

                    cmd.ExecuteNonQuery();
                }
            }

            // Send confirmation email
            try
            {
                string subject = "Registration Confirmation Code";
                string messageBody = GenerateEnglishEmailTemplate(resetCode);
                Task.Run(() => _mailerService.SendEmailAsync(Email, subject, messageBody)).Wait();

                SuccessMessage = "Registration successful! A confirmation code has been sent to your email. You will be automatically redirected to the validation page.";
            }
            catch (Exception ex)
            {
                ErrorMessage = "Error sending email: " + ex.Message;
            }
        }

        private string GenerateSalt(int length)
        {
            const string validChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(validChars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        private string HashPasswordAES(string password, string salt, out byte[] key, out byte[] iv)
        {
            using (var aes = Aes.Create())
            {
                aes.KeySize = 256;
                aes.BlockSize = 128;
                var keyGenerator = new Rfc2898DeriveBytes(password + salt, 16, 10000);
                key = keyGenerator.GetBytes(32);
                iv = keyGenerator.GetBytes(16);
                aes.Mode = CipherMode.CBC;

                using (var encryptor = aes.CreateEncryptor(key, iv))
                using (var ms = new MemoryStream())
                using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                using (var sw = new StreamWriter(cs))
                {
                    sw.Write(password);
                    sw.Flush();
                    cs.FlushFinalBlock();
                    return Convert.ToBase64String(ms.ToArray());
                }
            }
        }

        private string GenerateEnglishEmailTemplate(string resetCode)
        {
            return $@"
<!DOCTYPE html>
<html lang=""en"">
<head>
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <title>Registration Confirmation</title>
</head>
<body style=""font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f0f2f5;"">
    <div style=""max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 16px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,51,102,0.15);"">
        <div style=""background-color: #003366; padding: 30px; text-align: center; box-shadow: 0 4px 6px rgba(0,51,102,0.2);"">
            <h1 style=""color: white; margin: 0; font-size: 28px; text-shadow: 0 2px 4px rgba(0,0,0,0.1);"">Welcome to Online Portal! 🎉</h1>
        </div>
        
        <div style=""padding: 40px 30px;"">
            <p style=""font-size: 16px; line-height: 1.6; color: #333; margin-bottom: 30px;"">
                Dear {FirstName} {LastName},<br><br>
                Thank you for registering with Online Portal. 
                To confirm your registration, please use the following code:
            </p>
            
            <div style=""background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 12px; margin: 25px 0; 
                      box-shadow: 0 2px 8px rgba(0,51,102,0.08); border: 2px dashed #003366;"">
                <span style=""font-size: 32px; font-weight: bold; color: #003366; letter-spacing: 3px; text-shadow: 0 1px 2px rgba(0,51,102,0.1);
                           font-family: monospace;"">
                    {resetCode}
                </span>
            </div>
            
            <div style=""background-color: #f8f9fa; padding: 20px; border-radius: 12px; margin-top: 30px; box-shadow: 0 2px 8px rgba(0,51,102,0.08);"">
                <p style=""margin: 0; color: #666; font-size: 15px;"">
                    <span style=""color: #003366; font-weight: bold;"">📋 Account Information:</span>
                </p>
                <ul style=""list-style: none; padding: 10px 0; margin: 0;"">
                    <li style=""margin: 8px 0; color: #666; font-size: 14px;"">
                        <span style=""color: #4CAF50;"">•</span> Username: {Username}
                    </li>
                    <li style=""margin: 8px 0; color: #666; font-size: 14px;"">
                        <span style=""color: #4CAF50;"">•</span> Email: {Email}
                    </li>
                </ul>
            </div>

            <div style=""background-color: #f8f9fa; padding: 20px; border-radius: 12px; margin-top: 20px; box-shadow: 0 2px 8px rgba(0,51,102,0.08);"">
                <p style=""margin: 0; color: #666; font-size: 15px;"">
                    <span style=""color: #003366; font-weight: bold;"">🔒 Important:</span>
                </p>
                <ul style=""list-style: none; padding: 10px 0; margin: 0;"">
                    <li style=""margin: 8px 0; color: #666; font-size: 14px;"">
                        <span style=""color: #4CAF50;"">•</span> This code is valid for 24 hours
                    </li>
                    <li style=""margin: 8px 0; color: #666; font-size: 14px;"">
                        <span style=""color: #4CAF50;"">•</span> Never share your code with others
                    </li>
                    <li style=""margin: 8px 0; color: #666; font-size: 14px;"">
                        <span style=""color: #4CAF50;"">•</span> If you didn't register, please ignore this message
                    </li>
                </ul>
            </div>
            
            <p style=""margin-top: 30px; font-size: 15px; color: #666; text-align: center; padding: 0 20px;"">
                Need help? <span style=""color: #003366; text-decoration: none; font-weight: 500;"">Feel free to contact us!</span>
            </p>
        </div>
        
        <div style=""background-color: #f8f9fa; padding: 25px; text-align: center; border-top: 1px solid #eee;"">
            <p style=""margin: 0; color: #666; font-size: 13px; line-height: 1.6;"">
                © {DateTime.Now.Year} Online Portal. All rights reserved.<br>
                <span style=""color: #999;"">This is an automated message, please do not reply to this email.</span>
            </p>
        </div>
    </div>
</body>
</html>";
        }
    }
} 