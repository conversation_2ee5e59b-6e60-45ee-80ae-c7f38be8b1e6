using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using RazorPortal.Services;

namespace NextBroker.Pages.AdministrationPages
{
    public class KlasiOsiguruvanjeModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public List<KlasiOsiguruvanje> KlasiList { get; set; }
        [TempData]
        public bool IsEditing { get; set; }

        public KlasiOsiguruvanjeModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
            KlasiList = new List<KlasiOsiguruvanje>();
        } 

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("KlasiOsiguruvanje"))
            {
                return RedirectToAccessDenied();
            }

            LoadKlasi();
            return Page();
        }

        private void LoadKlasi()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(
                    "SELECT Id, DateCreated, DateModified, KlasaBroj, KlasaIme, KlasaOpis, Disabled " +
                    "FROM KlasiOsiguruvanje ORDER BY KlasaBroj", connection))
                {
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            KlasiList.Add(new KlasiOsiguruvanje
                            {
                                Id = reader.GetInt32(0),
                                DateCreated = reader.IsDBNull(1) ? DateTime.MinValue : reader.GetDateTime(1),
                                DateModified = reader.IsDBNull(2) ? DateTime.MinValue : reader.GetDateTime(2),
                                KlasaBroj = reader.GetInt32(3),
                                KlasaIme = reader.IsDBNull(4) ? string.Empty : reader.GetString(4),
                                KlasaOpis = reader.IsDBNull(5) ? string.Empty : reader.GetString(5),
                                Disabled = reader.GetBoolean(6)
                            });
                        }
                    }
                }
            }
        }

        public IActionResult OnPostToggleEdit()
        {
            IsEditing = !IsEditing;
            LoadKlasi();
            return Page();
        }

        public IActionResult OnPostAddKlasa(int klasaBroj, string klasaIme, string klasaOpis)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(
                    "INSERT INTO KlasiOsiguruvanje (KlasaBroj, KlasaIme, KlasaOpis, DateCreated, Disabled) " +
                    "VALUES (@KlasaBroj, @KlasaIme, @KlasaOpis, GETDATE(), 0)", 
                    connection))
                {
                    command.Parameters.AddWithValue("@KlasaBroj", klasaBroj);
                    command.Parameters.AddWithValue("@KlasaIme", klasaIme);
                    command.Parameters.AddWithValue("@KlasaOpis", klasaOpis);
                    command.ExecuteNonQuery();
                }
            }
            
            return RedirectToPage();
        }

        public IActionResult OnPostDeleteKlasa([FromBody] DeleteKlasaModel model)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(
                    "DELETE FROM KlasiOsiguruvanje WHERE Id = @Id", 
                    connection))
                {
                    command.Parameters.AddWithValue("@Id", model.Id);
                    command.ExecuteNonQuery();
                }
            }
            
            return new JsonResult(new { success = true });
        }

        public IActionResult OnPostSaveChanges([FromBody] List<KlasiOsiguruvanje> updates)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                foreach (var klasa in updates)
                {
                    // First, get the current values from the database
                    string currentValues = "SELECT KlasaBroj, KlasaIme, KlasaOpis, Disabled FROM KlasiOsiguruvanje WHERE Id = @Id";
                    using (SqlCommand getCommand = new SqlCommand(currentValues, connection))
                    {
                        getCommand.Parameters.AddWithValue("@Id", klasa.Id);
                        using (SqlDataReader reader = getCommand.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                int currentKlasaBroj = reader.GetInt32(0);
                                string currentKlasaIme = reader.IsDBNull(1) ? string.Empty : reader.GetString(1);
                                string currentKlasaOpis = reader.IsDBNull(2) ? string.Empty : reader.GetString(2);
                                bool currentDisabled = reader.GetBoolean(3);

                                // Only update if values have changed
                                if (currentKlasaBroj != klasa.KlasaBroj || 
                                    currentKlasaIme != klasa.KlasaIme || 
                                    currentKlasaOpis != klasa.KlasaOpis || 
                                    currentDisabled != klasa.Disabled)
                                {
                                    reader.Close(); // Close reader before executing update
                                    using (SqlCommand command = new SqlCommand(
                                        "UPDATE KlasiOsiguruvanje SET " +
                                        "KlasaBroj = @KlasaBroj, " +
                                        "KlasaIme = @KlasaIme, " +
                                        "KlasaOpis = @KlasaOpis, " +
                                        "Disabled = @Disabled, " +
                                        "DateModified = GETDATE() " +
                                        "WHERE Id = @Id", connection))
                                    {
                                        command.Parameters.AddWithValue("@Id", klasa.Id);
                                        command.Parameters.AddWithValue("@KlasaBroj", klasa.KlasaBroj);
                                        command.Parameters.AddWithValue("@KlasaIme", klasa.KlasaIme);
                                        command.Parameters.AddWithValue("@KlasaOpis", klasa.KlasaOpis);
                                        command.Parameters.AddWithValue("@Disabled", klasa.Disabled);
                                        command.ExecuteNonQuery();
                                    }
                                }
                            }
                        }
                    }
                }
            }
            IsEditing = false;
            return new JsonResult(new { success = true });
        }
    }

    public class DeleteKlasaModel
    {
        public int Id { get; set; }
    }
} 