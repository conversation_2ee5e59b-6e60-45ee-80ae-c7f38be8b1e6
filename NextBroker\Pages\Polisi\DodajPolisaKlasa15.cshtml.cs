using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;

namespace NextBroker.Pages.Polisi
{
    public class DodajPolisaKlasa15Model : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public DodajPolisaKlasa15Model(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("DodajPolisaKlasa15"))
            {
                return RedirectToAccessDenied();
            }

            return Page();
        }
    }
}
