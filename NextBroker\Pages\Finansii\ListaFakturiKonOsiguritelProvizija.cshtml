@page
@model NextBroker.Pages.Finansii.ListaFakturiKonOsiguritelProvizija
@{
    ViewData["Title"] = "Листа на фактури за провизија кон осигурител";
}

<div class="container-fluid">
    <h2>@ViewData["Title"]</h2>

    <div class="card">
        <div class="card-body">
            <form method="get" asp-page-handler="Search">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label asp-for="Filter.DatumNaFakturaOd"></label>
                            <input asp-for="Filter.DatumNaFakturaOd" class="form-control" type="date" />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label asp-for="Filter.DatumNaFakturaDo"></label>
                            <input asp-for="Filter.DatumNaFakturaDo" class="form-control" type="date" />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label asp-for="Filter.FakturaDo"></label>
                            <select asp-for="Filter.FakturaDo" asp-items="Model.Osiguriteli" class="form-control"></select>
                        </div>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary mr-2">Пребарај</button>
                        <button type="button" onclick="exportToExcel()" class="btn btn-success">Експорт во Excel</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="card mt-3">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Датум на креирање</th>
                            <th>Креирано од</th>
                            <th>Број на фактура</th>
                            <th>Фактура до</th>
                            <th>Датум на фактура</th>
                            <th>Рок на плаќање</th>
                            <th>Износ</th>
                            <th>Износ одобрување / задолжување по фактура</th>
                            <th>Број на документ ОЗ по фактура</th>
                            <th>Износ одобрување / задолжување по полиси</th>
                            <th>ОЗ за провизија по полиси</th>
                            <th>Датум од</th>
                            <th>Датум до</th>
                            <th>Ставка Премија ID</th>
                            <th>Број на извод</th>
                            <th>Датум на извод</th>
                            <th>Банка</th>
                            <th>Износ на ставка</th>
                            <th>Долг</th>
                            <th>Статус</th>
                            <th>Акции</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var faktura in Model.Fakturi)
                        {
                            <tr>
                                <td>@faktura.Id</td>
                                <td>@faktura.Datecreated?.ToString("dd.MM.yyyy HH:mm")</td>
                                <td>@faktura.UsernameCreated</td>
                                <td>@faktura.BrojNaFaktura</td>
                                <td>@faktura.FakturaDo</td>
                                <td>@faktura.DatumNaFaktura?.ToString("dd.MM.yyyy")</td>
                                <td>@faktura.RokNaPlakanje?.ToString("dd.MM.yyyy")</td>
                                <td>@faktura.Iznos?.ToString("N2")</td>
                                <td>@faktura.IznosOZ?.ToString("N2")</td>
                                <td>@faktura.BrojOZ</td>
                                <td>@faktura.IznosOZPoPolisi?.ToString("N2")</td>
                                <td>@faktura.BroeviOZPoPolisi</td>
                                <td>@faktura.DatumOd?.ToString("dd.MM.yyyy")</td>
                                <td>@faktura.DatumDo?.ToString("dd.MM.yyyy")</td>
                                <td>@faktura.StavkaPremijaId</td>
                                <td>@faktura.BrojNaIzvod</td>
                                <td>@faktura.DatumNaIzvod?.ToString("dd.MM.yyyy")</td>
                                <td>@faktura.Banka</td>
                                <td>@faktura.IznosStavka?.ToString("N2")</td>
                                <td>@faktura.Dolg?.ToString("N2")</td>
                                <td>
                                    @if (faktura.Status == "Платена")
                                    {
                                        <span class="badge badge-success" style="color: green">@faktura.Status</span>
                                    }
                                    else
                                    {
                                        <span class="badge badge-warning" style="color: red">@faktura.Status</span>
                                    }
                                </td>
                                <td class="text-center">
                                    <div class="btn-group">
                                        @if (!string.IsNullOrEmpty(faktura.FilePath) && !string.IsNullOrEmpty(faktura.FileName))
                                        {
                                            <a asp-page-handler="DownloadFile" asp-route-id="@faktura.Id" class="btn btn-sm btn-primary mr-1">
                                                Превземи
                                            </a>
                                        }
                                        <button type="button" class="btn btn-sm btn-info" onclick="openPovrzuvanje(@faktura.Id)">
                                            Поврзи ставка за прилив
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<form id="exportForm" method="post" asp-page-handler="ExportExcel">
    <input type="hidden" name="Filter.DatumNaFakturaOd" id="hiddenDatumOd" />
    <input type="hidden" name="Filter.DatumNaFakturaDo" id="hiddenDatumDo" />
    <input type="hidden" name="Filter.FakturaDo" id="hiddenFakturaDo" />
</form>

<!-- Modal for linking invoice with payment -->
<div class="modal fade" id="povrzuvanjeModal" tabindex="-1" aria-labelledby="povrzuvanjeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="povrzuvanjeModalLabel">Поврзи ставка за прилив</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeModal()">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="fakturaId" />
                
                <div class="form-group">
                    <label for="searchIzvodPremija">Пребарај извод за премија:</label>
                    <input type="text" id="searchIzvodPremija" class="form-control" placeholder="Внесете текст за пребарување..." />
                    <div id="izvodpremijaResults" class="search-results mt-2"></div>
                </div>
                
                <div class="form-group" id="stavkaPremijaGroup" style="display: none;">
                    <label for="stavkaPremija">Одберете ставка:</label>
                    <div id="stavkaPremijaResults" class="search-results mt-2"></div>
                </div>
                
                <div class="alert alert-success" id="successMessage" style="display: none;">
                    Успешно поврзување!
                </div>
                <div class="alert alert-danger" id="errorMessage" style="display: none;">
                    Грешка при поврзување!
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Затвори</button>
                <button type="button" class="btn btn-primary" id="poveziBtn" style="display: none;">Поврзи</button>
            </div>
        </div>
    </div>
</div>

<script>
    function exportToExcel() {
        document.getElementById('hiddenDatumOd').value = document.getElementById('Filter_DatumNaFakturaOd').value;
        document.getElementById('hiddenDatumDo').value = document.getElementById('Filter_DatumNaFakturaDo').value;
        document.getElementById('hiddenFakturaDo').value = document.getElementById('Filter_FakturaDo').value;
        document.getElementById('exportForm').submit();
    }

    // Variables for storing selected IDs
    let selectedIzvodPremijaId = null;
    let selectedStavkaPremijaId = null;
    let currentFakturaId = null;

    function openPovrzuvanje(fakturaId) {
        currentFakturaId = fakturaId;
        document.getElementById('fakturaId').value = fakturaId;
        document.getElementById('searchIzvodPremija').value = '';
        document.getElementById('izvodpremijaResults').innerHTML = '';
        document.getElementById('stavkaPremijaGroup').style.display = 'none';
        document.getElementById('stavkaPremijaResults').innerHTML = '';
        document.getElementById('poveziBtn').style.display = 'none';
        document.getElementById('successMessage').style.display = 'none';
        document.getElementById('errorMessage').style.display = 'none';
        
        // Reset selections
        selectedIzvodPremijaId = null;
        selectedStavkaPremijaId = null;
        
        // Use jQuery to show the modal
        $('#povrzuvanjeModal').modal('show');
    }
    
    function closeModal() {
        // Use jQuery to hide the modal
        $('#povrzuvanjeModal').modal('hide');
    }

    // Search for IzvodPremija as user types
    document.getElementById('searchIzvodPremija').addEventListener('input', function(e) {
        const searchTerm = e.target.value.trim();
        
        if (searchTerm.length >= 1) {
            fetchIzvodPremija(searchTerm);
        } else {
            document.getElementById('izvodpremijaResults').innerHTML = '';
        }
    });

    // Fetch IzvodPremija data
    function fetchIzvodPremija(searchTerm) {
        fetch(`?handler=SearchIzvodPremija&searchTerm=${encodeURIComponent(searchTerm)}`)
            .then(response => response.json())
            .then(data => {
                const resultsDiv = document.getElementById('izvodpremijaResults');
                resultsDiv.innerHTML = '';
                
                if (data.length === 0) {
                    resultsDiv.innerHTML = '<div class="alert alert-info">Нема пронајдени резултати</div>';
                    return;
                }
                
                const table = document.createElement('table');
                table.className = 'table table-bordered table-sm table-hover';
                
                // Add header
                const thead = document.createElement('thead');
                thead.innerHTML = `
                    <tr>
                        <th>ID</th>
                        <th>Број на извод</th>
                        <th>Датум на извод</th>
                        <th>Прилив</th>
                    </tr>
                `;
                table.appendChild(thead);
                
                // Add body
                const tbody = document.createElement('tbody');
                data.forEach(item => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>${item.id}</td>
                        <td>${item.brojNaIzvod}</td>
                        <td>${new Date(item.datumNaIzvod).toLocaleDateString('mk-MK')}</td>
                        <td>${item.priliv.toLocaleString('mk-MK', { minimumFractionDigits: 2 })}</td>
                    `;
                    tr.style.cursor = 'pointer';
                    tr.onclick = function() {
                        selectIzvodPremija(item.id);
                    };
                    tbody.appendChild(tr);
                });
                table.appendChild(tbody);
                
                resultsDiv.appendChild(table);
            })
            .catch(error => {
                console.error('Error fetching data:', error);
                document.getElementById('izvodpremijaResults').innerHTML = 
                    '<div class="alert alert-danger">Грешка при вчитување на податоци</div>';
            });
    }

    // Select IzvodPremija and load StavkaPremija options
    function selectIzvodPremija(izvodPremijaId) {
        selectedIzvodPremijaId = izvodPremijaId;
        
        // Highlight selected row
        const rows = document.querySelectorAll('#izvodpremijaResults tbody tr');
        rows.forEach(row => {
            row.classList.remove('table-primary');
            if (row.cells[0].textContent == izvodPremijaId) {
                row.classList.add('table-primary');
            }
        });
        
        // Show stavka premija selection and fetch data
        document.getElementById('stavkaPremijaGroup').style.display = 'block';
        fetchStavkaPremija(izvodPremijaId);
    }

    // Fetch StavkaPremija data
    function fetchStavkaPremija(izvodPremijaId) {
        fetch(`?handler=SearchStavkaPremija&izvodPremijaId=${izvodPremijaId}`)
            .then(response => response.json())
            .then(data => {
                const resultsDiv = document.getElementById('stavkaPremijaResults');
                resultsDiv.innerHTML = '';
                
                if (data.length === 0) {
                    resultsDiv.innerHTML = '<div class="alert alert-info">Нема пронајдени ставки</div>';
                    return;
                }
                
                const table = document.createElement('table');
                table.className = 'table table-bordered table-sm table-hover';
                
                // Add header
                const thead = document.createElement('thead');
                thead.innerHTML = `
                    <tr>
                        <th>ID</th>
                        <th>Цел на дознака</th>
                        <th>Износ</th>
                    </tr>
                `;
                table.appendChild(thead);
                
                // Add body
                const tbody = document.createElement('tbody');
                data.forEach(item => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>${item.id}</td>
                        <td>${item.celNaDoznaka}</td>
                        <td>${item.iznos.toLocaleString('mk-MK', { minimumFractionDigits: 2 })}</td>
                    `;
                    tr.style.cursor = 'pointer';
                    tr.onclick = function() {
                        selectStavkaPremija(item.id);
                    };
                    tbody.appendChild(tr);
                });
                table.appendChild(tbody);
                
                resultsDiv.appendChild(table);
            })
            .catch(error => {
                console.error('Error fetching stavka data:', error);
                document.getElementById('stavkaPremijaResults').innerHTML = 
                    '<div class="alert alert-danger">Грешка при вчитување на ставки</div>';
            });
    }

    // Select StavkaPremija and enable Povrzi button
    function selectStavkaPremija(stavkaId) {
        selectedStavkaPremijaId = stavkaId;
        
        // Highlight selected row
        const rows = document.querySelectorAll('#stavkaPremijaResults tbody tr');
        rows.forEach(row => {
            row.classList.remove('table-primary');
            if (row.cells[0].textContent == stavkaId) {
                row.classList.add('table-primary');
            }
        });
        
        // Show connect button
        document.getElementById('poveziBtn').style.display = 'block';
    }

    // Connect button click handler
    document.getElementById('poveziBtn').addEventListener('click', function() {
        if (!selectedStavkaPremijaId || !currentFakturaId) {
            document.getElementById('errorMessage').style.display = 'block';
            document.getElementById('errorMessage').textContent = 'Грешка: Неизбрани потребни вредности';
            return;
        }
        
        // Prepare data for sending
        const data = {
            fakturaId: currentFakturaId,
            stavkaPremijaId: selectedStavkaPremijaId
        };
        
        // Send request using fetch API
        fetch('?handler=PovrziFaktura', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(result => {
            if (result.success) {
                document.getElementById('successMessage').style.display = 'block';
                document.getElementById('errorMessage').style.display = 'none';
                
                // Refresh the page after 2 seconds to show updated data
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                document.getElementById('errorMessage').style.display = 'block';
                document.getElementById('errorMessage').textContent = result.message || 'Грешка при поврзување';
                document.getElementById('successMessage').style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error connecting invoice:', error);
            document.getElementById('errorMessage').style.display = 'block';
            document.getElementById('errorMessage').textContent = 'Грешка при поврзување: ' + error.message;
            document.getElementById('successMessage').style.display = 'none';
        });
    });
</script>

<style>
    .search-results {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        padding: 0.5rem;
    }
    
    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.1);
    }
    
    .table-primary {
        background-color: rgba(0, 123, 255, 0.2) !important;
    }
</style>
