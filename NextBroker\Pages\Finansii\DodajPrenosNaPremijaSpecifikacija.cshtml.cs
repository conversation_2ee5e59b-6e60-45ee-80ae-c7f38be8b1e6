using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;

namespace NextBroker.Pages.Finansii
{
    public class DodajPrenosNaPremijaSpecifikacijaModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public DodajPrenosNaPremijaSpecifikacijaModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("PrenosNaPremijaSpecifikacii"))
            {
                return RedirectToAccessDenied();
            }

            return Page();
        }
    }
}
