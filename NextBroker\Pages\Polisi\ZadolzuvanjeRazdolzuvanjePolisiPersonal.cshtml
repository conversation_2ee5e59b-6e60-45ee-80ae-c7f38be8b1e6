@page
@model NextBroker.Pages.Polisi.ZadolzuvanjeRazdolzuvanjePolisiPersonalModel
@{
    ViewData["Title"] = "Задолжување/Раздолжување";
}

<style>
    /* Hide specific buttons and table column */
    .add-records-btn,
    .bulk-edit-btn,
    .zadolzuvanje-doc-btn,
    .actions-column,
    td:first-child {  /* Hides the first column (Акции) in table rows */
        display: none !important;
    }
</style>

<h1>@ViewData["Title"]</h1>

<div class="mb-3">
    <a href="/Polisi/ZadolzuvanjeRazdolzuvanjeDodaj" class="btn btn-primary add-records-btn">
        <i class="fas fa-plus"></i> Додај записи во задолжување/раздолжување
    </a>
</div>

<div class="card mb-3">
    <div class="card-body">
        <form method="post">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label class="form-label">Датум на креирање од</label>
                    <input type="date" asp-for="Filter.DateCreatedFrom" class="form-control" />
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Датум на креирање до</label>
                    <input type="date" asp-for="Filter.DateCreatedTo" class="form-control" />
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Датум на промена од</label>
                    <input type="date" asp-for="Filter.DateModifiedFrom" class="form-control" />
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Датум на промена до</label>
                    <input type="date" asp-for="Filter.DateModifiedTo" class="form-control" />
                </div>
            </div>

            <div class="row">
                <div class="col-md-3 mb-3">
                    <label class="form-label">Осигурител</label>
                    <div class="dropdown">
                        <button class="btn btn-secondary dropdown-toggle form-select" type="button" id="osiguritelDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            Избери осигурители
                        </button>
                        <ul class="dropdown-menu w-100" aria-labelledby="osiguritelDropdown" style="max-height: 300px; overflow-y: auto;">
                            <li>
                                <label class="dropdown-item">
                                    <input type="checkbox" id="selectAllOsiguriteli" class="me-2"> Избери ги сите
                                </label>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            @foreach (var item in Model.Osiguriteli)
                            {
                                <li>
                                    <label class="dropdown-item">
                                        <input type="checkbox" name="Filter.KlientiIdOsiguritel" value="@item.Value" class="osiguritel-checkbox me-2">
                                        @item.Text
                                    </label>
                                </li>
                            }
                        </ul>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Класа на осигурување</label>
                    <select asp-for="Filter.KlasiOsiguruvanjeIdKlasa" class="form-select" asp-items="Model.KlasiOsiguruvanje" id="klasaDropdown">
                        <option value="">-- Сите --</option>
                    </select>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3 mb-3">
                    <label class="form-label">Продукт</label>
                    <select asp-for="Filter.ProduktiIdProdukt" class="form-select" id="produktDropdown">
                        <option value="">-- Сите --</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Број на полиса од-до</label>
                    <div class="input-group">
                        <input type="text" asp-for="Filter.BrojNaPolisa" class="form-control" placeholder="Од" />
                        <input type="text" asp-for="Filter.BrojNaPolisaTo" class="form-control" placeholder="До" />
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Број на понуда од-до</label>
                    <div class="input-group">
                        <input type="number" asp-for="Filter.BrojNaPonuda" class="form-control" placeholder="Од" />
                        <input type="number" asp-for="Filter.BrojNaPonudaTo" class="form-control" placeholder="До" />
                    </div>
                </div>
                 <div class="col-md-3 mb-3">
                    <label class="form-label">Исклучи броеви на полиси</label>
                    <input type="text" asp-for="Filter.ExcludeBrojNaPolisa" class="form-control" placeholder="Пр: 1234,5678,9012" />
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Задолжен</label>
                    <select asp-for="Filter.KlientiIdZadolzen" class="form-select" asp-items="Model.Zadolzeni"></select>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3 mb-3">
                    <label class="form-label">Датум на задолжување</label>
                    <input type="date" asp-for="Filter.DatumNaZadolzuvanje" class="form-control" />
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Основ за раздолжување</label>
                    <select asp-for="Filter.SifrarnikOsnovZaRazdolzuvanjeId" class="form-select" asp-items="Model.OsnoviZaRazdolzuvanje">
                        <option value="">-- Сите --</option>
                        <option value="-1">Празно</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Потврдено раздолжување кај брокер</label>
                    <select asp-for="Filter.PotvrdenoRazdolzuvanjeKajBroker" class="form-select">
                        <option value="">-- Сите --</option>
                        <option value="true">Да</option>
                        <option value="false">Не</option>
                    </select>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3 mb-3">
                    <label class="form-label">Датум на раздолжување кај брокер од</label>
                    <input type="date" asp-for="Filter.DatumNaRazdolzuvanjeKajBrokerFrom" class="form-control" />
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Датум на раздолжување кај брокер до</label>
                    <input type="date" asp-for="Filter.DatumNaRazdolzuvanjeKajBrokerTo" class="form-control" />
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Потврдено раздолжување кај осигурител</label>
                    <select asp-for="Filter.PotvrdenoRazdolzuvanjeKajOsiguritel" class="form-select">
                        <option value="">-- Сите --</option>
                        <option value="true">Да</option>
                        <option value="false">Не</option>
                    </select>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3 mb-3">
                    <label class="form-label">Датум на раздолжување кај осигурител од</label>
                    <input type="date" asp-for="Filter.DatumNaRazdolzuvanjeKajOsiguritelFrom" class="form-control" />
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Датум на раздолжување кај осигурител до</label>
                    <input type="date" asp-for="Filter.DatumNaRazdolzuvanjeKajOsiguritelTo" class="form-control" />
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label">Број на персонална раздолжница</label>
                    <input type="text" asp-for="Filter.BrojNaPersonalnaRazdolznica" class="form-control" placeholder="Внесете еден или повеќе броеви одделени со запирка (пр: 123,456,789)" />
                    <small class="form-text text-muted">Можете да внесете повеќе броеви одделени со запирка</small>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3 mb-3">
                    <label class="form-label">Датум на издавање од</label>
                    <input type="date" asp-for="Filter.DatumNaIzdavanjeFrom" class="form-control" />
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Датум на издавање до</label>
                    <input type="date" asp-for="Filter.DatumNaIzdavanjeTo" class="form-control" />
                </div>
            </div>

            <button type="submit" class="btn btn-primary">Генерирај преглед</button>
        </form>
    </div>
</div>

@if (Model.Results != null)
{
    <div class="mb-3">
        <button type="button" class="btn btn-secondary bulk-edit-btn" data-bs-toggle="modal" data-bs-target="#bulkEditModal">
            <i class="fas fa-edit"></i> Групно уредување
        </button>
        <button type="button" class="btn btn-primary ms-2 zadolzuvanje-doc-btn" id="generateDocumentBtn">
            <i class="fas fa-file-pdf"></i> Генерирај Документ за задолжување
        </button>
        <button type="button" class="btn btn-primary ms-2 @(Model.HasAnyRazdolznicaNumbers ? "disabled" : "")" 
                id="generateRazdolzuvanjeDocumentBtn" 
                @(Model.HasAnyRazdolznicaNumbers ? "disabled" : "")
                @(Model.HasAnyRazdolznicaNumbers ? "title=\"Документот не може да се генерира бидејќи некои записи веќе имаат број на персонална раздолжница\"" : "")>
            <i class="fas fa-file-pdf"></i> Генерирај Документ за раздолжување
        </button>
        <button type="button" class="btn btn-success ms-2" id="generateExcelBtn">
            <i class="fas fa-file-excel"></i> Генерирај Excel од прегледот
        </button>
    </div>

 /*
        <div id="pdfDebugInfo" class="alert alert-info mt-3" style="white-space: pre-wrap;">
            <strong>PDF Debug Info:</strong>
            <div id="pdfWhereClause"></div>
            <div id="pdfFilteredIds"></div>
        </div>
    */

    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th class="actions-column">Акции</th>
                    <th class="sortable" data-sort="dateCreated">Датум на креирање <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="usernameCreated">Креирал <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="dateModified">Датум на промена <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="usernameModified">Променил <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="osiguritel">Осигурител <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="klasa">Класа <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="produkt">Продукт <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="brojNaPolisa">Број на полиса <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="brojNaPonuda">Број на понуда <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="zadolzen">Задолжен <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="datumNaZadolzuvanje">Датум на задолжување <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="osnovZaRazdolzuvanje">Основ за раздолжување <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="potvrdenoKajBroker">Потврдено кај брокер <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="datumKajBroker">Датум на раздолжување кај брокер <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="potvrdenoKajOsiguritel">Потврдено кај осигурител <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="datumKajOsiguritel">Датум на раздолжување кај осигурител <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="datumNaIzdavanje">Датум на издавање <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="status">Статус <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="brojNaPersonalnaRazdolznica">Број на персонална раздолжница <i class="fas fa-sort"></i></th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.Results)
                {
                    <tr>
                        <td>
                            <button type="button" class="btn btn-sm btn-outline-primary edit-row" data-id="@item.Id">
                                <i class="fas fa-pencil-alt"></i>
                            </button>
                        </td>
                        <td>@item.DateCreated?.ToString("dd.MM.yyyy")</td>
                        <td>@item.UsernameCreated</td>
                        <td>@item.DateModified?.ToString("dd.MM.yyyy")</td>
                        <td>@item.UsernameModified</td>
                        <td>@item.OsiguritelNaziv</td>
                        <td>@item.KlasaIme</td>
                        <td>@item.ProduktIme</td>
                        <td>@item.BrojNaPolisa</td>
                        <td>@item.BrojNaPonuda</td>
                        <td>@item.ZadolzenIme</td>
                        <td>@item.DatumNaZadolzuvanje?.ToString("dd.MM.yyyy")</td>
                        <td>@item.OsnovZaRazdolzuvanje</td>
                        <td>@(item.PotvrdenoRazdolzuvanjeKajBroker ? "Да" : "Не")</td>
                        <td>@item.DatumNaRazdolzuvanjeKajBroker?.ToString("dd.MM.yyyy")</td>
                        <td>@(item.PotvrdenoRazdolzuvanjeKajOsiguritel ? "Да" : "Не")</td>
                        <td>@item.DatumNaRazdolzuvanjeKajOsiguritel?.ToString("dd.MM.yyyy")</td>
                        <td>@item.DatumNaIzdavanje?.ToString("dd.MM.yyyy")</td>
                        <td>@item.Status</td>
                        <td>@item.BrojNaPersonalnaRazdolznica</td>
                    </tr>
                }
            </tbody>
        </table>
    </div>

    <!-- Bulk Edit Modal -->
    <div class="modal fade" id="bulkEditModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Групно уредување</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Избери колона за уредување</label>
                        <select id="bulkEditColumn" class="form-select">
                            <option value="">-- Избери --</option>
                            <option value="KlientiIdZadolzen">Задолжен</option>
                            <option value="DatumNaZadolzuvanje">Датум на задолжување</option>
                            <option value="SifrarnikOsnovZaRazdolzuvanjeId">Основ за раздолжување</option>
                            <option value="PotvrdenoRazdolzuvanjeKajBroker">Потврдено раздолжување кај брокер</option>
                            <option value="DatumNaRazdolzuvanjeKajBroker">Датум на раздолжување кај брокер</option>
                            <option value="PotvrdenoRazdolzuvanjeKajOsiguritel">Потврдено раздолжување кај осигурител</option>
                            <option value="DatumNaRazdolzuvanjeKajOsiguritel">Датум на раздолжување кај осигурител</option>
                        </select>
                    </div>
                    <div id="bulkEditValueContainer" class="mb-3" style="display: none;">
                        <label class="form-label">Нова вредност</label>
                        <select id="bulkEditValue" class="form-select">
                            <option value="">-- Избери --</option>
                        </select>
                    </div>
                    <div id="recordCount" class="text-muted">
                        Број на записи кои ќе бидат променети: @Model.Results.Count
                    </div>
                    <div id="debugWhereClause" class="text-muted mt-2" style="font-family: monospace; font-size: 12px;">
                        WHERE clause will appear here...
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Откажи</button>
                    <button type="button" class="btn btn-primary" id="bulkEditConfirm">Потврди</button>
                </div>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#klasaDropdown').change(function() {
                var klasaId = $(this).val();
                var produktDropdown = $('#produktDropdown');
                produktDropdown.empty();
                produktDropdown.append($('<option></option>').val('').text('-- Сите --'));

                if (klasaId) {
                    $.getJSON(`?handler=Produkti&klasaId=${klasaId}`, function(data) {
                        $.each(data, function(index, item) {
                            produktDropdown.append($('<option></option>').val(item.id).text(item.text));
                        });
                    });
                }
            });

            $('.edit-row').click(function() {
                var row = $(this).closest('tr');
                var id = $(this).data('id');
                
                if (!row.hasClass('editing')) {
                    row.find('td:not(:first)').each(function() {
                        var cell = $(this);
                        var value = cell.text();
                        var cellIndex = cell.index();
                        
                        if (cellIndex <= 4) {
                            return true;
                        }
                        
                        // Handle different columns with appropriate controls
                        switch(cellIndex) {
                            case 5: // Osiguritel dropdown
                                $.getJSON('?handler=Osiguriteli', function(data) {
                                    var select = $('<select class="form-select form-select-sm">');
                                    select.append('<option value="">-- Избери --</option>');
                                    data.forEach(function(item) {
                                        var option = $('<option></option>')
                                            .val(item.id)
                                            .text(item.text);
                                        if (item.text === value) {
                                            option.prop('selected', true);
                                        }
                                        select.append(option);
                                    });
                                    select.prop('disabled', true);
                                    cell.html(select);
                                });
                                break;
                                
                            case 6: // Klasa dropdown
                                $.getJSON('?handler=KlasiOsiguruvanje', function(data) {
                                    var select = $('<select class="form-select form-select-sm edit-klasa">');
                                    select.append('<option value="">-- Избери --</option>');
                                    data.forEach(function(item) {
                                        var option = $('<option></option>')
                                            .val(item.id)
                                            .text(item.text);
                                        if (item.text === value) {
                                            option.prop('selected', true);
                                        }
                                        select.append(option);
                                    });
                                    select.prop('disabled', true);
                                    cell.html(select);

                                    // Add change event to update Produkti dropdown
                                    select.change(function() {
                                        var produktCell = row.find('td:eq(7)');
                                        var klasaId = $(this).val();
                                        
                                        $.getJSON(`?handler=Produkti&klasaId=${klasaId}`, function(data) {
                                            var produktSelect = $('<select class="form-select form-select-sm">');
                                            produktSelect.append('<option value="">-- Избери --</option>');
                                            data.forEach(function(item) {
                                                produktSelect.append($('<option></option>')
                                                    .val(item.id)
                                                    .text(item.text));
                                            });
                                            produktCell.html(produktSelect);
                                        });
                                    });
                                });
                                break;
                                
                            case 7: // Produkt dropdown (depends on Klasa)
                                $.getJSON('?handler=Produkti', function(data) {
                                    var select = $('<select class="form-select form-select-sm">');
                                    select.append('<option value="">-- Избери --</option>');
                                    data.forEach(function(item) {
                                        var option = $('<option></option>')
                                            .val(item.id)
                                            .text(item.text);
                                        if (item.text === value) {
                                            option.prop('selected', true);
                                        }
                                        select.append(option);
                                    });
                                    select.prop('disabled', true);
                                    cell.html(select);
                                });
                                break;
                                
                            case 8: // BrojNaPolisa
                                cell.html($('<input type="text" class="form-control form-control-sm" readonly>').val(value));
                                break;
                            case 9: // BrojNaPonuda
                                cell.html($('<input type="number" class="form-control form-control-sm" readonly>').val(value));
                                break;
                                
                            case 10: // Zadolzen dropdown
                                $.getJSON('?handler=Zadolzeni', function(data) {
                                    var select = $('<select class="form-select form-select-sm">');
                                    select.append('<option value="">-- Избери --</option>');
                                    data.forEach(function(item) {
                                        var option = $('<option></option>')
                                            .val(item.id)
                                            .text(item.text);
                                        if (item.text === value) {
                                            option.prop('selected', true);
                                        }
                                        select.append(option);
                                    });
                                    cell.html(select);
                                });
                                break;
                                
                            case 11: // DatumNaZadolzuvanje
                            case 14: // DatumNaRazdolzuvanjeKajBroker
                            case 16: // DatumNaRazdolzuvanjeKajOsiguritel
                                var datePicker = $('<input type="date" class="form-control form-control-sm">');
                                if (value) {
                                    var parts = value.split('.');
                                    var isoDate = `${parts[2]}-${parts[1]}-${parts[0]}`;
                                    datePicker.val(isoDate);
                                }
                                cell.html(datePicker);
                                break;
                                
                            case 12: // OsnovZaRazdolzuvanje dropdown
                                $.getJSON('?handler=OsnoviZaRazdolzuvanje', function(data) {
                                    var select = $('<select class="form-select form-select-sm">');
                                    select.append('<option value="">-- Избери --</option>');
                                    data.forEach(function(item) {
                                        var option = $('<option></option>')
                                            .val(item.id)
                                            .text(item.text);
                                        if (item.text === value) {
                                            option.prop('selected', true);
                                        }
                                        select.append(option);
                                    });
                                    cell.html(select);
                                });
                                break;
                                
                            case 13: // PotvrdenoRazdolzuvanjeKajBroker
                            case 15: // PotvrdenoRazdolzuvanjeKajOsiguritel
                                var select = $('<select class="form-select form-select-sm">')
                                    .append('<option value="true">Да</option>')
                                    .append('<option value="false">Не</option>')
                                    .val(value === 'Да' ? 'true' : 'false');
                                cell.html(select);
                                break;
                        }
                    });
                    
                    $(this).html('<i class="fas fa-save"></i>')
                        .removeClass('btn-outline-primary')
                        .addClass('btn-outline-success');
                    
                    row.addClass('editing');
                } else {
                    var data = {
                        id: id,
                        klientiIdOsiguritel: parseInt(row.find('td:eq(5) select').val()) || null,
                        klasiOsiguruvanjeIdKlasa: parseInt(row.find('td:eq(6) select').val()) || null,
                        produktiIdProdukt: parseInt(row.find('td:eq(7) select').val()) || null,
                        brojNaPolisa: row.find('td:eq(8) input').val() || null,
                        brojNaPonuda: parseInt(row.find('td:eq(9) input').val()) || null,
                        klientiIdZadolzen: parseInt(row.find('td:eq(10) select').val()) || null,
                        datumNaZadolzuvanje: row.find('td:eq(11) input').val() || null,
                        sifrarnikOsnovZaRazdolzuvanjeId: parseInt(row.find('td:eq(12) select').val()) || null,
                        potvrdenoRazdolzuvanjeKajBroker: row.find('td:eq(13) select').val() === 'true',
                        datumNaRazdolzuvanjeKajBroker: row.find('td:eq(14) input').val() || null,
                        potvrdenoRazdolzuvanjeKajOsiguritel: row.find('td:eq(15) select').val() === 'true',
                        datumNaRazdolzuvanjeKajOsiguritel: row.find('td:eq(16) input').val() || null
                    };
                    
                    $.ajax({
                        url: '?handler=SaveRow',
                        type: 'POST',
                        data: JSON.stringify(data),
                        contentType: 'application/json',
                        headers: {
                            "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                        },
                        beforeSend: function(xhr) {
                            var token = $('input[name="__RequestVerificationToken"]').val();
                            xhr.setRequestHeader("XSRF-TOKEN", token);
                        },
                        success: function(response) {
                            if (!response.success) {
                                alert(response.message || 'Грешка при зачувување на промените');
                                return;
                            }
                            // Reload the page with existing filters
                            $('form').submit();
                        },
                        error: function() {
                            alert('Грешка при зачувување на промените');
                        }
                    });
                }
            });

            // Bulk Edit Functionality
            $('#bulkEditColumn').change(function() {
                var column = $(this).val();
                var valueContainer = $('#bulkEditValueContainer');
                var valueSelect = $('#bulkEditValue');

                valueContainer.show();
                valueSelect.empty().append('<option value="">-- Избери --</option>');

                if (column === 'KlientiIdZadolzen') {
                    $.getJSON('?handler=Zadolzeni', function(data) {
                        data.forEach(function(item) {
                            valueSelect.append($('<option></option>')
                                .val(item.id)
                                .text(item.text));
                        });
                    });
                } else if (column === 'SifrarnikOsnovZaRazdolzuvanjeId') {
                    $.getJSON('?handler=OsnoviZaRazdolzuvanje', function(data) {
                        data.forEach(function(item) {
                            valueSelect.append($('<option></option>')
                                .val(item.id)
                                .text(item.text));
                        });
                    });
                } else if (column === 'PotvrdenoRazdolzuvanjeKajBroker' || 
                          column === 'PotvrdenoRazdolzuvanjeKajOsiguritel') {
                    valueSelect.replaceWith(
                        $('<select id="bulkEditValue" class="form-select">')
                            .append('<option value="">-- Избери --</option>')
                            .append('<option value="true">Да</option>')
                            .append('<option value="false">Не</option>')
                    );
                } else if (column === 'DatumNaZadolzuvanje' || 
                          column === 'DatumNaRazdolzuvanjeKajBroker' || 
                          column === 'DatumNaRazdolzuvanjeKajOsiguritel') {
                    valueSelect.replaceWith(
                        $('<input type="date" id="bulkEditValue" class="form-control">')
                    );
                } else {
                    valueContainer.hide();
                }
            });

            $('#bulkEditConfirm').click(function() {
                var column = $('#bulkEditColumn').val();
                var value = $('#bulkEditValue').val();

                if (!column || !value) {
                    alert('Ве молиме изберете колона и вредност');
                    return;
                }

                // Get current filter values
                var filterData = $('form').serialize();

                $.ajax({
                    url: '?handler=BulkEdit',
                    type: 'POST',
                    data: JSON.stringify({
                        column: column,
                        value: value,
                        filter: filterData
                    }),
                    contentType: 'application/json',
                    headers: {
                        "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#debugWhereClause').html('<strong>WHERE clause:</strong><br>WHERE 1=1 ' + 
                                response.whereClause.replace(/AND/g, '<br>AND') + 
                                '<br><br><strong>Affected IDs:</strong><br>' + 
                                response.affectedIds.join(', '));
                            
                            $('#bulkEditModal').modal('hide');
                            $('form').submit();
                        } else {
                            alert(response.message || 'Грешка при зачувување на промените');
                        }
                    },
                    error: function() {
                        alert('Грешка при зачувување на промените');
                    }
                });
            });

            // Handle document generation
            $('#generateDocumentBtn').click(function() {
                // Get all form values
                var formData = {};
                $('form').find('input, select').each(function() {
                    var input = $(this);
                    var name = input.attr('name');
                    if (name && name.startsWith('Filter.')) {
                        if (name === 'Filter.KlientiIdOsiguritel') {
                            // Skip the original input as we'll handle checkboxes separately
                            return;
                        }
                        formData[name] = input.val();
                    }
                });
                
                // Handle multiple osiguriteli selections
                $('.osiguritel-checkbox:checked').each(function(index) {
                    formData[`Filter.KlientiIdOsiguritel[${index}]`] = $(this).val();
                });
                
                // First get debug info
                $.get('?handler=PdfDebugInfo', { filter: JSON.stringify(formData) }, function(response) {
                    if (response.success) {
                        $('#pdfWhereClause').html('<strong>WHERE clause:</strong><br>WHERE 1=1 ' + 
                            response.whereClause.replace(/AND/g, '<br>AND'));
                        $('#pdfFilteredIds').html('<strong>Filtered IDs:</strong><br>' + 
                            response.filteredIds.join(', '));
                        $('#pdfWhereClause').append('<br><br><strong>Filter Values:</strong><br>' + 
                            response.filterValues.replace(/\n/g, '<br>'));
                        $('#pdfWhereClause').append('<br><br><strong>Parameter Values:</strong><br>' + 
                            response.parameterValues.join('<br>'));
                    }
                    
                    // Then generate PDF
                    window.location.href = '?handler=GenerateDocument&filter=' + encodeURIComponent(JSON.stringify(formData));
                });
            });

            // Add this after the existing generateDocumentBtn click handler
            $('#generateRazdolzuvanjeDocumentBtn').click(function() {
                // Check if button is disabled
                if ($(this).hasClass('disabled') || $(this).prop('disabled')) {
                    return false;
                }
                
                // Get all form values
                var formData = {};
                $('form').find('input, select').each(function() {
                    var input = $(this);
                    var name = input.attr('name');
                    if (name && name.startsWith('Filter.')) {
                        if (name === 'Filter.KlientiIdOsiguritel') {
                            // Skip the original input as we'll handle checkboxes separately
                            return;
                        }
                        formData[name] = input.val();
                    }
                });
                
                // Handle multiple osiguriteli selections
                $('.osiguritel-checkbox:checked').each(function(index) {
                    formData[`Filter.KlientiIdOsiguritel[${index}]`] = $(this).val();
                });
                
                // Generate PDF
                window.location.href = '?handler=GenerateRazdolzuvanjeDocument&filter=' + encodeURIComponent(JSON.stringify(formData));
            });

            // Add this after the existing button handlers
            $('#generateExcelBtn').click(function() {
                // Get all form values
                var formData = {};
                $('form').find('input, select').each(function() {
                    var input = $(this);
                    var name = input.attr('name');
                    if (name && name.startsWith('Filter.')) {
                        if (name === 'Filter.KlientiIdOsiguritel') {
                            // Skip the original input as we'll handle checkboxes separately
                            return;
                        }
                        formData[name] = input.val();
                    }
                });
                
                // Handle multiple osiguriteli selections
                $('.osiguritel-checkbox:checked').each(function(index) {
                    formData[`Filter.KlientiIdOsiguritel[${index}]`] = $(this).val();
                });
                
                // Generate Excel
                window.location.href = '?handler=GenerateExcel&filter=' + encodeURIComponent(JSON.stringify(formData));
            });

            // Handle "Select All" checkbox
            $('#selectAllOsiguriteli').change(function() {
                var isChecked = $(this).prop('checked');
                $('.osiguritel-checkbox').prop('checked', isChecked);
                updateOsiguritelDropdownText();
            });

            // Handle individual checkboxes
            $('.osiguritel-checkbox').change(function() {
                updateOsiguritelDropdownText();
                
                // Update "Select All" checkbox
                var allChecked = $('.osiguritel-checkbox:checked').length === $('.osiguritel-checkbox').length;
                $('#selectAllOsiguriteli').prop('checked', allChecked);
            });

            function updateOsiguritelDropdownText() {
                var selectedCount = $('.osiguritel-checkbox:checked').length;
                var buttonText = selectedCount === 0 ? 'Избери осигурители' : 
                               selectedCount === $('.osiguritel-checkbox').length ? 'Сите осигурители' :
                               selectedCount + ' избрани осигурители';
                $('#osiguritelDropdown').text(buttonText);
            }

            // Preserve selected values after postback
            var selectedValues = @Html.Raw(Json.Serialize(Model.Filter.KlientiIdOsiguritel ?? new List<long>()));
            if (selectedValues && selectedValues.length > 0) {
                selectedValues.forEach(function(value) {
                    $('.osiguritel-checkbox[value="' + value + '"]').prop('checked', true);
                });
                updateOsiguritelDropdownText();
            }

            // Table sorting functionality
            $('.sortable').click(function() {
                const th = $(this);
                const table = th.closest('table');
                const tbody = table.find('tbody');
                const rows = tbody.find('tr').toArray();
                const index = th.index();
                const type = th.data('sort');
                const direction = th.hasClass('asc') ? -1 : 1;

                // Remove asc/desc classes from all headers
                table.find('th').removeClass('asc desc');
                
                // Add appropriate class to clicked header
                th.addClass(direction === 1 ? 'asc' : 'desc');

                // Sort the rows
                rows.sort(function(a, b) {
                    let aValue = $(a).find('td').eq(index).text().trim();
                    let bValue = $(b).find('td').eq(index).text().trim();

                    // Handle date sorting
                    if (type.includes('date') || type.includes('datum')) {
                        // Convert DD.MM.YYYY to YYYY-MM-DD for proper comparison
                        const convertDate = (dateStr) => {
                            if (!dateStr) return '';
                            const parts = dateStr.split('.');
                            return parts.length === 3 ? `${parts[2]}-${parts[1]}-${parts[0]}` : '';
                        };
                        aValue = convertDate(aValue);
                        bValue = convertDate(bValue);
                    }
                    // Handle number sorting
                    else if (type.includes('broj')) {
                        aValue = parseFloat(aValue) || 0;
                        bValue = parseFloat(bValue) || 0;
                    }
                    // Handle boolean sorting
                    else if (type.includes('potvrdeno')) {
                        aValue = aValue === 'Да' ? 1 : 0;
                        bValue = bValue === 'Да' ? 1 : 0;
                    }
                    // Handle status sorting - "Сторнирана" should be at the end
                    else if (type === 'status') {
                        aValue = aValue === 'Сторнирана' ? 1 : 0;
                        bValue = bValue === 'Сторнирана' ? 1 : 0;
                    }

                    if (aValue < bValue) return -1 * direction;
                    if (aValue > bValue) return 1 * direction;
                    return 0;
                });

                // Reattach sorted rows to tbody
                tbody.empty();
                rows.forEach(row => tbody.append(row));
            });
        });
    </script>

    <style>
        .sortable {
            cursor: pointer;
            user-select: none;
        }
        .sortable i {
            margin-left: 5px;
            color: #ccc;
        }
        .sortable.asc i:before {
            content: "\f0de";
            color: #000;
        }
        .sortable.desc i:before {
            content: "\f0dd";
            color: #000;
        }
        
        /* Style for disabled razdolzuvanje button */
        .btn.disabled {
            opacity: 0.6;
            cursor: not-allowed;
            pointer-events: none;
        }
    </style>
}
