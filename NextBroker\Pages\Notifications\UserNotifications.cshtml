@page
@model NextBroker.Pages.Notifications.UserNotificationsModel
@{
    ViewData["Title"] = "Корисничи известувања";
}

<div class="container">
    <h2>Корисничи известувања</h2>
    <div id="notifications-container">
        <!-- Notifications will be inserted here -->
    </div>
</div>

@section Scripts {
    <script src="~/lib/microsoft/signalr/dist/browser/signalr.min.js"></script>
    <script>
        // Add this at the start of your script section, outside DOMContentLoaded
        const expandedNotifications = new Set();

        // Add this function outside the DOMContentLoaded event
        async function markAsRead(id, collapseId, event) {
            // Prevent the card header click event from firing
            event.stopPropagation();
            
            try {
                const token = document.querySelector('input[name="__RequestVerificationToken"]').value;
                const response = await fetch('/Notifications/UserNotifications?handler=MarkAsRead', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'RequestVerificationToken': token
                    },
                    body: `id=${id}`
                });
                
                if (response.ok) {
                    // Collapse the notification
                    const collapseElement = document.getElementById(collapseId);
                    if (collapseElement) {
                        bootstrap.Collapse.getOrCreateInstance(collapseElement).hide();
                    }
                    // Refresh notifications after a short delay
                    setTimeout(() => loadNotifications(), 300);
                }
            } catch (error) {
                console.error('Error marking notification as read:', error);
            }
        }

        document.addEventListener('DOMContentLoaded', function () {
            const connection = new signalR.HubConnectionBuilder()
                .withUrl("/notificationHub")
                .withAutomaticReconnect()
                .build();

            async function loadNotifications() {
                try {
                    console.log('Loading notifications...');
                    const response = await fetch('/Notifications/UserNotifications?handler=Notifications');
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    const notifications = await response.json();
                    console.log('Received notifications:', notifications);
                    updateNotificationsUI(notifications);
                } catch (error) {
                    console.error('Error loading notifications:', error);
                }
            }

            function updateNotificationsUI(notifications) {
                const container = document.getElementById('notifications-container');
                if (!container) {
                    console.error('Container not found!');
                    return;
                }
                container.innerHTML = '';
                
                if (!notifications || notifications.length === 0) {
                    container.innerHTML = '<div class="alert alert-info">Нема пронајдени известувања.</div>';
                    return;
                }
                
                notifications.forEach(notification => {
                    const div = document.createElement('div');
                    div.className = `notification ${notification.isRead ? 'read' : 'unread'}`;
                    const collapseId = `collapse-${notification.id}`;
                    
                    // Check if this notification should be expanded
                    const isExpanded = expandedNotifications.has(notification.id) || !notification.isRead;
                    
                    div.innerHTML = `
                        <div class="card mb-3">
                            <div class="card-header p-2 d-flex align-items-center gap-2" 
                                 style="cursor: pointer;" 
                                 data-bs-toggle="collapse" 
                                 data-bs-target="#${collapseId}"
                                 aria-expanded="${isExpanded}"
                                 aria-controls="${collapseId}">
                                <span class="badge ${notification.isRead ? 'bg-secondary' : 'bg-primary'}">
                                    ${notification.isRead ? 'Прочитано' : 'Непрочитано'}
                                </span>
                                ${!notification.isRead ? 
                                    `<span class="badge bg-light text-primary" 
                                           onclick="markAsRead(${notification.id}, '${collapseId}', event)">
                                        Означи како прочитано
                                    </span>` : 
                                    ''}
                                <h6 class="mb-0 ms-2">${notification.title || 'Нема наслов'}</h6>
                                <small class="text-muted ms-auto">${new Date(notification.dateCreated).toLocaleString()}</small>
                                <span class="collapse-indicator"></span>
                            </div>
                            <div class="collapse ${isExpanded ? 'show' : ''}" 
                                 id="${collapseId}">
                                <div class="card-body">
                                    <p class="card-text">${notification.content || 'Нема содржина'}</p>
                                </div>
                            </div>
                        </div>
                    `;
                    container.appendChild(div);

                    // Add collapse event listener to track state
                    const collapseElement = document.getElementById(collapseId);
                    collapseElement.addEventListener('show.bs.collapse', () => {
                        expandedNotifications.add(notification.id);
                    });
                    collapseElement.addEventListener('hide.bs.collapse', () => {
                        expandedNotifications.delete(notification.id);
                    });
                });
            }

            function toggleCollapse(collapseId) {
                const collapseElement = document.getElementById(collapseId);
                if (collapseElement) {
                    bootstrap.Collapse.getOrCreateInstance(collapseElement).toggle();
                }
            }

            connection.on("ReceiveNotifications", function () {
                console.log('Received SignalR notification update');
                loadNotifications();
            });

            connection.start()
                .then(function () {
                    console.log("SignalR Connected");
                    loadNotifications();
                })
                .catch(function (err) {
                    console.error('SignalR Connection Error:', err.toString());
                });
        });
    </script>

    <style>
        .notification.unread {
            border-left: 4px solid #007bff;
        }
        .notification.read {
            border-left: 4px solid #6c757d;
        }
        .card {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .card-header {
            background-color: rgba(0,0,0,.03);
            transition: background-color 0.2s;
        }
        .card-header:hover {
            background-color: rgba(0,0,0,.06);
        }
        .collapse {
            transition: all 0.3s ease;
        }
        .collapse-indicator::after {
            content: '▼';
            margin-left: 8px;
            font-size: 12px;
            transition: transform 0.3s;
        }
        .collapsed .collapse-indicator::after {
            transform: rotate(-90deg);
        }
    </style>

    @Html.AntiForgeryToken()
} 