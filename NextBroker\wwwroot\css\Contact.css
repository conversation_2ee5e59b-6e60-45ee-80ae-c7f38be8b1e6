﻿.icon-container {
    width: 100px; /* Fixed width for uniformity */
    height: 100px; /* Fixed height for uniformity */
    display: flex; /* Flexbox for centering */
    justify-content: center; /* Center horizontally */
    align-items: center; /* Center vertically */
    overflow: hidden; /* Hide overflow */
}

    .icon-container img {
        width: 100%; /* Scale to fit container width */
        height: auto; /* Maintain aspect ratio */
        object-fit: contain; /* Contain within the div */
    }
/* Card styling */
.card {
    border-radius: 15px; /* Smooth rounded corners */
    height: auto !important; /* Change from fixed height to auto */
    min-height: 300px; /* Set a minimum height instead of fixed */
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Prevent overflow */
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
    font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif !important;
    padding: 15px !important;
    justify-content: space-between; /* Spread content evenly */
    align-items: center; /* Center content horizontally */
    text-align: center; /* Center text within the card */
    position: relative; /* Keep content inside the card */
    margin-bottom: 0; /* Remove bottom margin */
}
.card-title {
    font-size: 1.25rem; /* Smaller title font size */
    color: #003366 !important; /* Dark blue color */
    font-weight: bold !important; /* Bold text */
    font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif !important;
}
/* Keep buttons from affecting card size */
.card-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between; /* Spread content, including buttons */
    flex-grow: 1; /* Allow body to grow and fill space */
    width: 100%; /* Ensure it takes full card width */
    padding: 10px; /* Adjust padding to avoid overflow */
    box-sizing: border-box;
}
.card-text {
    overflow: visible; /* Add ellipsis (...) to overflowing text */
    display: -webkit-box; /* Display as a flexbox */
    -webkit-box-orient: vertical; /* Set the orientation to vertical */
    -webkit-line-clamp: 3; /* Limit to 3 lines of text */
    max-height: 4.5em; /* Fixed height for text */
    font-size: 15px; /* Smaller text size */ /* Fixed height for text */
    color: #003366 !important; /* Dark blue color */
    font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif !important;
}

/* Hover effect on card */
.card:hover {
    background-color: rgba(100, 120, 180, 0.1); /* Desaturated blue */
    box-shadow: 0 5px 15px rgba(0, 51, 102, 0.1); /* Stronger shadow on hover */
}
.row {
    margin-left: -15px;
    margin-right: -15px;
    margin-bottom: 0; /* Remove bottom margin */
}

.col-12.col-md-6 {
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 30px; /* Add bottom padding to create gap */
}


.btn {
    background-color: rgba(76, 175, 80, 0.7) !important; /* Default color */
    color: #003366 !important;
    border: none !important;
    border-radius: 20px !important; /* Make buttons rounded */
    padding: 5px 10px; /* Reduced padding for smaller buttons */
    font-size: 14px; /* Smaller font size for buttons */
    transition: background-color 0.3s ease, transform 0.3s ease !important; /* Added transform transition */
    align-self: center;
    margin-top: auto; /* Push button to the bottom */
    margin-bottom: 5px; /* Add some space between buttons */
    position: relative; /* Added position */
    z-index: 1; /* Ensure the button is on top */
    overflow: visible; /* Allow overflow */
    white-space: normal; /* Allow text to wrap */
    height: auto; /* Allow height to adjust based on content */
}

    .btn:hover {
        background-color: rgba(0, 51, 102, 0.6) !important;
        color: white !important;
        border: none;
        border-radius: 25px !important; /* Make buttons rounded */
        transform: scale(1.05) translateY(-2px) !important; /* Scale up button and move it slightly up */
    }



.contact-info-container {
    background: rgba(76, 175, 220, 0.1);
    border-radius: 10px;
    box-shadow: 0 4px 5px rgba(50, 0, 139, 0.1);
    text-align: center;
    margin: 20px 0;
    width: 100%;
    max-width: 700px;
    padding: 20px;
}
.image-container {
    display: flex; /* Ensure the icons are laid out in a row */
    justify-content: center; /* Center the content horizontally */
    margin-bottom: 20px; /* Add space below the image container */
    overflow: visible; /* Allow images to overflow if scaled */
}

.icon-container {
    width: 100px; /* Fixed width for uniformity */
    height: 100px; /* Fixed height for uniformity */
    display: flex; /* Flexbox for centering */
    justify-content: center; /* Center horizontally */
    align-items: center; /* Center vertically */
    overflow: visible; /* Hide overflow */
    transition: transform 0.3s ease; /* Smooth transition for scaling */
}

    .icon-container img {
        width: 100%; /* Scale to fit container width */
        height: auto; /* Maintain aspect ratio */
        object-fit: contain; /* Contain within the div */
        transition: transform 0.3s ease; /* Smooth transition for scaling */
    }

    /* Scale image on hover */
    .icon-container:hover img {
        transform: scale(1.1); /* Slightly enlarge the image on hover */
        overflow: visible; /* Allow overflow on hover */
    }


h1, h2, h3, h4 {
    color: #003366 !important; /* Dark blue color */
    font-weight: bold !important; /* Bold text */
    font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif !important;
    text-align: center;
    
}

h1{
    font-size: 60px !important;
    text-align:center;
}

h4 a {
    color: #003366; /* Default text color */
    transition: background-color 0.3s ease; /* Smooth transition for background color */
    padding: 5px; /* Optional: Add some padding for better click area */
    border-radius: 5px; /* Optional: Add rounded corners */
}

    h4 a:hover {
        background-color: rgba(76, 175, 80, 0.7) !important; /* Green background on hover */
        color: white !important; /* Change text color to white for better contrast */
    }

.contact-info-container h1, .contact-info-container h2 {
    margin-bottom: 15px; /* Spacing below headers */
}

.container-fluid {
    max-width: 80%; /* Limit the container width */
    display: flex;
    justify-content: flex-start; /* Align cards to the left */
    flex-wrap: wrap; /* Allow wrapping for responsive design */
    gap: 10px; /* Space between cards */
    margin: 40px;
}




.btn-xlarge {
    padding: 30px 20px !important;
    font-size: 30px !important;
    padding: 15px !important;
    margin: 20px;
}

.scroll-top {
    position: fixed;
    bottom: 20px; /* Changed from 40px to 20px */
    right: 40px;
    width: 60px;
    height: 60px;
    background-color: rgba(0, 51, 102, 0.2) !important;
    border-radius: 50% !important;
    border: none;
    display: flex; /* Added to enable centering */
    justify-content: center;
    align-items: center;
    opacity: 0.5;
    cursor: pointer;
    transition: background-color 0.3s ease, opacity 0.3s ease, transform 0.3s ease;
    z-index: 1000;
}

    .scroll-top:hover {
        background-color: rgba(0, 51, 102, 0.8); /* Change background on hover */
        opacity: 1; /* Fully visible on hover */
        border-radius: 50% !important;
        transform: scale(1.15); /* Scale up slightly on hover */
    }

    .scroll-top:focus {
        outline: 2px solid rgba(76, 175, 80, 0.8) !important; /* Custom outline */
        outline-offset: 2px !important; /* Space between the outline and the button */
    }

    .scroll-top i {
        font-size: 24px; /* Increased from 20px to 24px for better visibility */
        color: #fff;
        display: flex; /* Added to ensure proper centering */
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
    }

.location-title {
    text-align: center;
    font-size: 40px;
    margin: 30px;
}

@media (min-width: 768px) {
    .col-md-6 {
        flex: 0 0 48%; /* Ensure two cards fit on larger screens */
        max-width: 48%;
    }
}

/* Add this new class for centering content */
.contact-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 1200px; /* Adjust this value as needed */
    margin: 0 auto;
}

.maps-container {
    margin-bottom: 20px; /* Add 20px space at the bottom */
}

/* Adjust the scroll-top button position */
.scroll-top {
    position: fixed;
    bottom: 55px; /* Changed from 40px to 20px */
    right: 40px;
    /* ... rest of the properties remain the same ... */
}

/* Remove or adjust negative margin if present */
.row {
    margin-left: 0; /* Changed from -25px */
    margin-right: 0; /* Changed from -25px */
    margin-bottom: 20px; /* Add 20px space at the bottom */
}

/* Ensure the last row of cards has proper spacing */
.col-12.col-md-6:last-child {
    margin-bottom: 0;
}

/* Adjust the container padding if necessary */
.container {
    padding-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .card {
        padding: 10px !important;
    }

    .card-text {
        font-size: 14px;
    }

    .btn {
        font-size: 12px;
        padding: 4px 8px;
    }

    .btn-dark.btn-lg {
        font-size: 14px;
        padding: 4px 8px;
    }
}

/* Specific styling for the location button */
.btn-dark.btn-lg {
    padding: 5px 10px;
    font-size: 16px;
    margin-top: 5px;
}

/* Adjust the row margins and padding */
.row {
    margin-left: -15px;
    margin-right: -15px;
    margin-bottom: 0; /* Remove bottom margin */
}

/* Adjust the column padding */
.col-12.col-md-6 {
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 30px; /* Add bottom padding to create gap */
}

/* Remove bottom margin from cards */
.card {
    margin-bottom: 0;
}

/* Adjust the container padding if necessary */
.container {
    padding-bottom: 0;
}

/* Ensure the last row of cards doesn't have extra bottom padding */
.col-12.col-md-6:last-child,
.col-12.col-md-6:nth-last-child(2) {
    padding-bottom: 0;
}

/* For mobile view, adjust the bottom padding */
@media (max-width: 767px) {
    .col-12.col-md-6 {
        padding-bottom: 15px;
    }

    .col-12.col-md-6:last-child {
        padding-bottom: 0;
    }
}
