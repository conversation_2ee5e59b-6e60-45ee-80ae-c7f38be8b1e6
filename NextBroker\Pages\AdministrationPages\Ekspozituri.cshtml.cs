using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using RazorPortal.Services;

namespace NextBroker.Pages.AdministrationPages
{
    public class Ekspozitura
    {
        public int Id { get; set; }
        public DateTime DateCreated { get; set; }
        public string Ime { get; set; }
        public string Adresa { get; set; }
        public string Telefon { get; set; }
    }

    public class EkspozituriModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public List<Ekspozitura> EkspozituriList { get; set; }
        [TempData]
        public bool IsEditing { get; set; }

        public EkspozituriModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
            EkspozituriList = new List<Ekspozitura>();
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("Ekspozituri"))
            {
                return RedirectToAccessDenied();
            }

            LoadEkspozituri();
            return Page();
        }

        private void LoadEkspozituri()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(
                    "SELECT Id, DateCreated, Ime, Adresa, Telefon " +
                    "FROM Ekspozituri ORDER BY Id", connection))
                {
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            EkspozituriList.Add(new Ekspozitura
                            {
                                Id = reader.GetInt32(0),
                                DateCreated = reader.IsDBNull(1) ? DateTime.MinValue : reader.GetDateTime(1),
                                Ime = reader.IsDBNull(2) ? string.Empty : reader.GetString(2),
                                Adresa = reader.IsDBNull(3) ? string.Empty : reader.GetString(3),
                                Telefon = reader.IsDBNull(4) ? string.Empty : reader.GetString(4)
                            });
                        }
                    }
                }
            }
        }

        public IActionResult OnPostToggleEdit()
        {
            IsEditing = !IsEditing;
            LoadEkspozituri();
            return Page();
        }

        public IActionResult OnPostAddEkspozitura(string ime, string adresa, string telefon)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(
                    "INSERT INTO Ekspozituri (Ime, Adresa, Telefon, DateCreated) " +
                    "VALUES (@Ime, @Adresa, @Telefon, GETDATE())", 
                    connection))
                {
                    command.Parameters.AddWithValue("@Ime", ime);
                    command.Parameters.AddWithValue("@Adresa", adresa);
                    command.Parameters.AddWithValue("@Telefon", telefon);
                    command.ExecuteNonQuery();
                }
            }
            
            return RedirectToPage();
        }

        public IActionResult OnPostDeleteEkspozitura([FromBody] DeleteEkspozituraModel model)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(
                    "DELETE FROM Ekspozituri WHERE Id = @Id", 
                    connection))
                {
                    command.Parameters.AddWithValue("@Id", model.Id);
                    command.ExecuteNonQuery();
                }
            }
            
            return new JsonResult(new { success = true });
        }

        public IActionResult OnPostSaveChanges([FromBody] List<Ekspozitura> updates)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                foreach (var ekspozitura in updates)
                {
                    using (SqlCommand command = new SqlCommand(
                        "UPDATE Ekspozituri SET " +
                        "Ime = @Ime, " +
                        "Adresa = @Adresa, " +
                        "Telefon = @Telefon " +
                        "WHERE Id = @Id", connection))
                    {
                        command.Parameters.AddWithValue("@Id", ekspozitura.Id);
                        command.Parameters.AddWithValue("@Ime", ekspozitura.Ime);
                        command.Parameters.AddWithValue("@Adresa", ekspozitura.Adresa);
                        command.Parameters.AddWithValue("@Telefon", ekspozitura.Telefon);
                        command.ExecuteNonQuery();
                    }
                }
            }
            IsEditing = false;
            return new JsonResult(new { success = true });
        }
    }

    public class DeleteEkspozituraModel
    {
        public int Id { get; set; }
    }
} 