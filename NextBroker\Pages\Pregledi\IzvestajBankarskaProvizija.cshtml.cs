using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.Data;
using Microsoft.Data.SqlClient;
using OfficeOpenXml;
using OfficeOpenXml.Style;

namespace NextBroker.Pages.Pregledi
{
    public class IzvestajBankarskaProvizijaModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public IzvestajBankarskaProvizijaModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        [BindProperty]
        public DateTime DatumOd { get; set; } = DateTime.Now.AddDays(-30);

        [BindProperty]
        public DateTime DatumDo { get; set; } = DateTime.Now;

        public DataTable ReportData { get; set; }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("IzvestajBankarskaProvizija"))
            {
                return RedirectToAccessDenied();
            }
            
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!await HasPageAccess("IzvestajBankarskaProvizija"))
            {
                return RedirectToAccessDenied();
            }

            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();

                using (var command = new SqlCommand(@"
                    select 
                    dbo.VratiImeNaBankaPoStavkaOdIzvod(stavprem.Id) as [Banka],
                    izvprem.BrojNaIzvod,
                    stavprem.Id as [BrojNaStavkaVoIzvod],                   
                    izvprem.BrojNaSmetka,
                    izvprem.DatumNaIzvod,
                    stavprem.CelNaDoznaka,
                    kasizv.BrojNaKasovIzvestaj,
                    kasizv.VkupenIznos as [IznosOdKasovIzvestaj],
                    izvprem.Priliv as [IzvodIznosPriliv],
                    izvprem.Odliv as [IzvodIznosOdliv],
                    case when  kasizv.BrojNaKasovIzvestaj is null
                     then stavprem.Iznos
                    else 
                    kasizv.VkupenIznos - stavprem.Iznos                    
                     end as [Iznos]

                    from StavkaPremija stavprem
                    left join IzvodPremija izvprem on stavprem.IzvodPremijaId = izvprem.Id
                    left join KasovIzvestaj kasizv on stavprem.Id = kasizv.StavkaPremijaId
                    WHERE 
                        (dbo.CalculateStringSimilarity(LOWER(stavprem.CelNaDoznaka), 'дневна провизија за налози') >= 0.6
                        OR dbo.CalculateStringSimilarity(LOWER(stavprem.CelNaDoznaka), 'банкарска пров') >= 0.6
                        OR dbo.CalculateStringSimilarity(LOWER(stavprem.CelNaDoznaka), 'dnevna provizija za nalozi') >= 0.6
                        OR dbo.CalculateStringSimilarity(LOWER(stavprem.CelNaDoznaka), 'bankarska prov') >= 0.6
                        /*
                        OR stavprem.CelNaDoznaka like '%kasov%'
                        OR stavprem.CelNaDoznaka like '%касов%'
                        */
                        )
                        AND izvprem.DatumNaIzvod between @DatumOd and @DatumDo", connection))
                {
                    command.Parameters.AddWithValue("@DatumOd", DatumOd);
                    command.Parameters.AddWithValue("@DatumDo", DatumDo);

                    using (var adapter = new SqlDataAdapter(command))
                    {
                        ReportData = new DataTable();
                        adapter.Fill(ReportData);
                    }
                }
            }

            return Page();
        }

        public async Task<IActionResult> OnPostExportExcelAsync()
        {
            if (!await HasPageAccess("IzvestajBankarskaProvizija"))
            {
                return RedirectToAccessDenied();
            }

            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();

                using (var command = new SqlCommand(@"
                    select 
                    dbo.VratiImeNaBankaPoStavkaOdIzvod(stavprem.Id) as [Banka],
                    izvprem.BrojNaIzvod,
                    stavprem.Id as [BrojNaStavkaVoIzvod],                   
                    izvprem.BrojNaSmetka,
                    izvprem.DatumNaIzvod,
                    stavprem.CelNaDoznaka,
                    kasizv.BrojNaKasovIzvestaj,
                    kasizv.VkupenIznos as [IznosOdKasovIzvestaj],
                    izvprem.Priliv as [IzvodIznosPriliv],
                    izvprem.Odliv as [IzvodIznosOdliv],
                    case when  kasizv.BrojNaKasovIzvestaj is null
                     then stavprem.Iznos
                    else 
                    kasizv.VkupenIznos - stavprem.Iznos                    
                     end as [Iznos]

                    from StavkaPremija stavprem
                    left join IzvodPremija izvprem on stavprem.IzvodPremijaId = izvprem.Id
                    left join KasovIzvestaj kasizv on stavprem.Id = kasizv.StavkaPremijaId
                    WHERE 
                        (dbo.CalculateStringSimilarity(LOWER(stavprem.CelNaDoznaka), 'дневна провизија за налози') >= 0.6
                        OR dbo.CalculateStringSimilarity(LOWER(stavprem.CelNaDoznaka), 'банкарска пров') >= 0.6
                        OR dbo.CalculateStringSimilarity(LOWER(stavprem.CelNaDoznaka), 'dnevna provizija za nalozi') >= 0.6
                        OR dbo.CalculateStringSimilarity(LOWER(stavprem.CelNaDoznaka), 'bankarska prov') >= 0.6
                        /*
                        OR stavprem.CelNaDoznaka like '%kasov%'
                        OR stavprem.CelNaDoznaka like '%касов%'
                        */
                        )
                        AND izvprem.DatumNaIzvod between @DatumOd and @DatumDo", connection))
                {
                    command.Parameters.AddWithValue("@DatumOd", DatumOd);
                    command.Parameters.AddWithValue("@DatumDo", DatumDo);

                    using (var adapter = new SqlDataAdapter(command))
                    {
                        var dt = new DataTable();
                        adapter.Fill(dt);

                        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                        using (var package = new ExcelPackage())
                        {
                            var worksheet = package.Workbook.Worksheets.Add("Банкарска Провизија");

                            // Macedonian headers mapping
                            var mkHeaders = new Dictionary<string, string> {
                                {"Banka", "Банка"},
                                {"BrojNaIzvod", "Број на извод"},
                                {"BrojNaSmetka", "Број на сметка"},
                                {"DatumNaIzvod", "Датум на извод"},
                                {"CelNaDoznaka", "Цел на дознака"},
                                {"BrojNaKasovIzvestaj", "Број на касов извештај"},
                                {"IznosOdKasovIzvestaj", "Износ од касов извештај"},
                                {"IzvodIznosPriliv", "Износ прилив (извод)"},
                                {"IzvodIznosOdliv", "Износ одлив (извод)"},
                                {"Iznos", "Износ"}
                            };

                            // Add headers
                            for (int i = 0; i < dt.Columns.Count; i++)
                            {
                                var colName = dt.Columns[i].ColumnName;
                                worksheet.Cells[1, i + 1].Value = mkHeaders.ContainsKey(colName) ? mkHeaders[colName] : colName;
                                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                                worksheet.Cells[1, i + 1].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                            }

                            // Add data
                            for (int row = 0; row < dt.Rows.Count; row++)
                            {
                                for (int col = 0; col < dt.Columns.Count; col++)
                                {
                                    var value = dt.Rows[row][col];
                                    var cell = worksheet.Cells[row + 2, col + 1];
                                    if (value is DateTime dtValue)
                                    {
                                        cell.Value = dtValue.ToString("dd.MM.yyyy");
                                    }
                                    else
                                    {
                                        cell.Value = value;
                                    }
                                    cell.Style.Border.BorderAround(ExcelBorderStyle.Thin);
                                }
                            }

                            // Add totals row
                            int totalRow = dt.Rows.Count + 2;
                            for (int col = 0; col < dt.Columns.Count; col++)
                            {
                                var column = dt.Columns[col];
                                if (column.ColumnName == "Iznos")
                                {
                                    decimal sum = 0;
                                    foreach (DataRow row in dt.Rows)
                                    {
                                        if (row[column] != DBNull.Value)
                                        {
                                            try { sum += Convert.ToDecimal(row[column]); } catch { }
                                        }
                                    }
                                    worksheet.Cells[totalRow, col + 1].Value = sum;
                                    worksheet.Cells[totalRow, col + 1].Style.Font.Bold = true;
                                }
                                else
                                {
                                    worksheet.Cells[totalRow, col + 1].Value = string.Empty;
                                }
                            }

                            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                            var content = package.GetAsByteArray();
                            var fileName = $"Izvestaj_Bankarska_Provizija_{DatumOd:yyyyMMdd}_{DatumDo:yyyyMMdd}.xlsx";
                            return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                        }
                    }
                }
            }
        }
    }
}
