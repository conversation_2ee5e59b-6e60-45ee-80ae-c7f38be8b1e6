@page
@model NextBroker.Pages.Pregledi.ASORealiziranaProvizija
@{
    ViewData["Title"] = "ASO Realizirana Provizija";
}

<div class="container-fluid">
    <h2 class="mb-4">АСО Реализирана Провизија</h2>
    <form method="post" class="row g-3 mb-4">
        <div class="col-auto">
            <label asp-for="SelectedYear" class="form-label">Година</label>
            <select asp-for="SelectedYear" asp-items="Model.YearOptions" class="form-select"></select>
        </div>
        <div class="col-auto">
            <label asp-for="SelectedQuarter" class="form-label">Квартал</label>
            <select asp-for="SelectedQuarter" asp-items="Model.QuarterOptions" class="form-select"></select>
        </div>
        <div class="col-auto align-self-end">
            @if (Model.TableRows == null || Model.TableRows.Count == 0)
            {
                <button type="submit" class="btn btn-primary">Преглед</button>
            }
        </div>
    </form>
    
    @if (Model.TableRows != null && Model.TableRows.Count > 0)
    {
        <div class="row mb-3">
            @if (Model.ShowConfirmButton)
            {
                <div class="col-auto">
                    <form method="post" asp-page-handler="Confirm" id="confirmForm" style="display:inline;">
                        <input type="hidden" name="SelectedYear" value="@Model.SelectedYear" />
                        <input type="hidden" name="SelectedQuarter" value="@Model.SelectedQuarter" />
                        <button type="submit" class="btn btn-success me-2">Потврди</button>
                    </form>
                </div>
            }
            <div class="col-auto">
                <form method="post" asp-page-handler="ExportExcel" id="exportExcelForm" style="display:inline;">
                    <input type="hidden" name="SelectedYear" value="@Model.SelectedYear" />
                    <input type="hidden" name="SelectedQuarter" value="@Model.SelectedQuarter" />
                    <button type="submit" class="btn btn-outline-primary me-2">Export to Excel</button>
                </form>
            </div>
            <div class="col-auto">
                <form method="post" asp-page-handler="ExportPdf" id="exportPdfForm" style="display:inline;">
                    <input type="hidden" name="SelectedYear" value="@Model.SelectedYear" />
                    <input type="hidden" name="SelectedQuarter" value="@Model.SelectedQuarter" />
                    <button type="submit" class="btn btn-outline-danger">Export to PDF</button>
                </form>
            </div>
        </div>

        @if (Model.IsSaved)
        {
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i> 
                @if (!string.IsNullOrEmpty(Model.SavedStatusMessage))
                {
                    @Model.SavedStatusMessage
                }
                else
                {
                    <span>Податоците за @Model.SelectedYear година, @(Model.SelectedQuarter switch { "1" => "1-ви квартал", "2" => "2-ри квартал", "3" => "3-ти квартал", "4" => "4-ти квартал", _ => "непознат квартал" }) се зачувани во базата на податоци.</span>
                }
            </div>
        }
        else
        {
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> 
                Прикажани се податоци за @Model.SelectedYear година, @(Model.SelectedQuarter switch { "1" => "1-ви квартал", "2" => "2-ри квартал", "3" => "3-ти квартал", "4" => "4-ти квартал", _ => "непознат квартал" })
                @if (Model.ShowConfirmButton)
                {
                    <br /><strong>Кликнете "Потврди" за да ги зачувате податоците во базата.</strong>
                }
            </div>
        }

        <div class="table-responsive">
            <table class="table table-bordered table-striped align-middle">
                <thead class="table-light">
                    <tr>
                        <th>Број на полиса</th>
                        <th>Износ на Премија</th>
                        <th>Назив</th>
                        <th>Класа</th>
                        <th>Продукт</th>
                        <th>Клиент</th>
                        <th>Процент на провизија</th>
                        <th>Износ на провизија</th>
                        <th>Бр. Фактура за провизија</th>
                        <th>Датум на Фактура</th>
                        <th>Премија за основна класа</th>
                        <th>Доп. осигурување класа 1</th>
                        <th>Доп. осигурување класа 8</th>
                        <th>Доп. осигурување асистенција</th>
                        <th>Вкупна премија</th>
                        <th>% Учество основна класа</th>
                        <th>% Учество доп. класа 1</th>
                        <th>% Учество доп. класа 8</th>
                        <th>% Учество доп. асистенција</th>
                        <th>Провизија за основна класа</th>
                        <th>Провизија доп. класа 1</th>
                        <th>Провизија доп. класа 8</th>
                        <th>Провизија доп. асистенција</th>
                    </tr>
                </thead>
                <tbody>
                @foreach (var row in Model.TableRows)
                {
                    <tr>
                        <td>@row.BrojNaPolisa</td>
                        <td>@row.IznosNaPremija.ToString("N2")</td>
                        <td>@row.Naziv</td>
                        <td>@row.Klasa</td>
                        <td>@row.Produkt</td>
                        <td>@row.Klient</td>
                        <td>@row.ProcentNaProvizija.ToString("N2")%</td>
                        <td>@row.IznosNaProvizija.ToString("N2")</td>
                        <td>@row.BrojFakturaZaProvizija</td>
                        <td>@row.DatumNaFaktura?.ToString("dd.MM.yyyy")</td>
                        <td>@row.PremijaZaOsnovnaKlasa.ToString("N2")</td>
                        <td>@row.DopolnitelnoOsiguruvanjeKlasa1.ToString("N2")</td>
                        <td>@row.DopolnitelnoOsiguruvanjeKlasa8.ToString("N2")</td>
                        <td>@row.DopolnitelnoOsiguruvanjeAsistencaKlas10.ToString("N2")</td>
                        <td>@row.VkupnaPremija.ToString("N2")</td>
                        <td>@row.UcestvoPremijaZaOsnovnaKlasa.ToString("P2")</td>
                        <td>@row.UcestvoDopolnitelnoOsiguruvanjeKlasa1.ToString("P2")</td>
                        <td>@row.UcestvoDopolnitelnoOsiguruvanjeKlasa8.ToString("P2")</td>
                        <td>@row.UcestvoDopolnitelnoOsiguruvanjeAsistencaKlasa10.ToString("P2")</td>
                        <td>@row.ProvizijaZaOsnovnaKlasa.ToString("N2")</td>
                        <td>@row.ProvizijaDopolnitelnoOsiguruvanjeKlasa1.ToString("N2")</td>
                        <td>@row.ProvizijaDopolnitelnoOsiguruvanjeKlasa8.ToString("N2")</td>
                        <td>@row.ProvizijaDopolnitelnoOsiguruvanjeAsistencaKlasa10.ToString("N2")</td>
                    </tr>
                }
                </tbody>
                <tfoot>
                    <tr class="table-info">
                        <td><strong>ВКУПНО</strong></td>
                        <td><strong>@Model.TotalPremija.ToString("N2")</strong></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td><strong>@Model.TotalProvizija.ToString("N2")</strong></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td><strong>@Model.TotalProvizijaZaOsnovnaKlasa.ToString("N2")</strong></td>
                        <td><strong>@Model.TotalProvizijaDopolnitelnoKlasa1.ToString("N2")</strong></td>
                        <td><strong>@Model.TotalProvizijaDopolnitelnoKlasa8.ToString("N2")</strong></td>
                        <td><strong>@Model.TotalProvizijaDopolnitelnoAsistenca.ToString("N2")</strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>

        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Резиме</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Број на полиси:</strong></td>
                                <td>@Model.TotalBrojPolisi</td>
                            </tr>
                            <tr>
                                <td><strong>Вкупна премија:</strong></td>
                                <td>@Model.TotalPremija.ToString("N2")</td>
                            </tr>
                            <tr>
                                <td><strong>Вкупна провизија:</strong></td>
                                <td>@Model.TotalProvizija.ToString("N2")</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Провизија по класи</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Основна класа:</strong></td>
                                <td>@Model.TotalProvizijaZaOsnovnaKlasa.ToString("N2")</td>
                            </tr>
                            <tr>
                                <td><strong>Дополнително осигурување класа 1:</strong></td>
                                <td>@Model.TotalProvizijaDopolnitelnoKlasa1.ToString("N2")</td>
                            </tr>
                            <tr>
                                <td><strong>Дополнително осигурување класа 8:</strong></td>
                                <td>@Model.TotalProvizijaDopolnitelnoKlasa8.ToString("N2")</td>
                            </tr>
                            <tr>
                                <td><strong>Дополнително осигурување асистенција:</strong></td>
                                <td>@Model.TotalProvizijaDopolnitelnoAsistenca.ToString("N2")</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    }
    else if (Model.TableRows != null && Model.TableRows.Count == 0)
    {
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i> Нема податоци за избраниот период.
        </div>
    }
</div>


