using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Text;
using OfficeOpenXml;

namespace NextBroker.Pages.Pregledi
{
    public class IzvestajPrenosNaNaplataVlezniFakturiModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public IzvestajPrenosNaNaplataVlezniFakturiModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        [BindProperty]
        [Display(Name = "Датум од")]
        [DataType(DataType.Date)]
        public DateTime? DatumOd { get; set; }

        [BindProperty]
        [Display(Name = "Датум до")]
        [DataType(DataType.Date)]
        public DateTime? DatumDo { get; set; }

        public List<string> ColumnNames { get; set; } = new();
        public List<List<object>> Data { get; set; } = new();

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("IzvestajPrenosNaNaplataVlezniFakturi"))
            {
                return RedirectToAccessDenied();
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!await HasPageAccess("IzvestajPrenosNaNaplataVlezniFakturi"))
            {
                return RedirectToAccessDenied();
            }

            if (!DatumOd.HasValue || !DatumDo.HasValue)
            {
                ModelState.AddModelError("", "Датумите се задолжителни");
                return Page();
            }

            await LoadData();
            return Page();
        }

        public async Task<IActionResult> OnPostExportToCsvAsync()
        {
            if (!await HasPageAccess("IzvestajPrenosNaNaplataVlezniFakturi"))
            {
                return RedirectToAccessDenied();
            }

            if (!DatumOd.HasValue || !DatumDo.HasValue)
            {
                ModelState.AddModelError("", "Датумите се задолжителни");
                return Page();
            }

            await LoadData();

            var csv = new StringBuilder();

            // Add BOM for proper UTF-8 encoding with Cyrillic characters
            var bom = Encoding.UTF8.GetPreamble();
            var bomString = Encoding.UTF8.GetString(bom);
            csv.Append(bomString);

            // Add headers
            csv.AppendLine(string.Join(",", ColumnNames.Select(EscapeCsvField)));

            // Add data rows
            foreach (var row in Data)
            {
                var formattedFields = row.Select(field =>
                {
                    if (field == null)
                        return "";
                    else if (field is DateTime date)
                        return date.ToString("dd/MM/yyyy");
                    else if (field is decimal decimalValue)
                        return decimalValue.ToString("N2");
                    else if (field is double doubleValue)
                        return doubleValue.ToString("N2");
                    else if (field is float floatValue)
                        return floatValue.ToString("N2");
                    else
                        return field.ToString();
                }).Select(EscapeCsvField);

                csv.AppendLine(string.Join(",", formattedFields));
            }

            string fileName = $"IzvestajPrenosNaNaplataVlezniFakturi_{DateTime.Now:yyyyMMdd}.csv";
            byte[] bytes = Encoding.UTF8.GetBytes(csv.ToString());
            return File(bytes, "text/csv", fileName);
        }

        public async Task<IActionResult> OnPostExportToExcelAsync()
        {
            if (!await HasPageAccess("IzvestajPrenosNaNaplataVlezniFakturi"))
            {
                return RedirectToAccessDenied();
            }

            if (!DatumOd.HasValue || !DatumDo.HasValue)
            {
                ModelState.AddModelError("", "Датумите се задолжителни");
                return Page();
            }

            await LoadData();

            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("Извештај");

                // Add headers
                for (int i = 0; i < ColumnNames.Count; i++)
                {
                    worksheet.Cells[1, i + 1].Value = ColumnNames[i];
                    worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                    worksheet.Cells[1, i + 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                }

                // Add data rows
                for (int row = 0; row < Data.Count; row++)
                {
                    for (int col = 0; col < Data[row].Count; col++)
                    {
                        var value = Data[row][col];
                        if (value != null)
                        {
                            if (value is DateTime date)
                            {
                                worksheet.Cells[row + 2, col + 1].Value = date;
                                worksheet.Cells[row + 2, col + 1].Style.Numberformat.Format = "dd/mm/yyyy";
                            }
                            else if (value is decimal || value is double || value is float)
                            {
                                worksheet.Cells[row + 2, col + 1].Value = value;
                                worksheet.Cells[row + 2, col + 1].Style.Numberformat.Format = "#,##0.00";
                            }
                            else
                            {
                                worksheet.Cells[row + 2, col + 1].Value = value.ToString();
                            }
                        }
                    }
                }

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                // Add borders to all cells with data
                var dataRange = worksheet.Cells[1, 1, Data.Count + 1, ColumnNames.Count];
                dataRange.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                dataRange.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                dataRange.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                dataRange.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;

                string fileName = $"IzvestajPrenosNaNaplataVlezniFakturi_{DateTime.Now:yyyyMMdd}.xlsx";
                byte[] bytes = package.GetAsByteArray();
                return File(bytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
        }

        private string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field))
                return "";

            if (field.Contains(",") || field.Contains("\"") || field.Contains("\n") || field.Contains("\r"))
            {
                // Escape quotes by doubling them and wrap in quotes
                field = field.Replace("\"", "\"\"");
                return $"\"{field}\"";
            }

            return field;
        }

        private async Task LoadData()
        {
            // Clear existing data
            ColumnNames.Clear();
            Data.Clear();

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand("IzvestajPrenosNaNaplataVlezniFakturi", connection))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@DatumOd", DatumOd.Value);
                    cmd.Parameters.AddWithValue("@DatumDo", DatumDo.Value);

                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        // Get column names
                        for (int i = 0; i < reader.FieldCount; i++)
                        {
                            ColumnNames.Add(reader.GetName(i));
                        }

                        // Get data
                        while (await reader.ReadAsync())
                        {
                            var row = new List<object>();
                            for (int i = 0; i < reader.FieldCount; i++)
                            {
                                row.Add(reader.IsDBNull(i) ? null : reader.GetValue(i));
                            }
                            Data.Add(row);
                        }
                    }
                }
            }
        }
    }
}
