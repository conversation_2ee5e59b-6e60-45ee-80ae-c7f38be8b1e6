@page "{id:long}"
@model NextBroker.Pages.Finansii.ViewEditIzvodModel
@{
    ViewData["Title"] = "Преглед/Измена на извод";
}

@Html.AntiForgeryToken()

<div class="container-fluid mt-4 px-4">
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0 me-2">@ViewData["Title"]</h5>
                    <span class="badge bg-primary">ИД: @Model.Input.Id</span>
                </div>
                <a href="/Finansii/Izvodi" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Назад
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-6">
                    <div class="d-flex flex-column">
                        <small class="text-muted mb-1">Системски информации</small>
                        <div class="d-flex flex-wrap gap-3">
                            <div>
                                <small class="text-muted d-block">Креирано:</small>
                                <span>@Model.Input.DateCreated?.ToString("dd.MM.yyyy HH:mm")</span>
                                <small class="text-muted ms-1">од</small>
                                <span>@Model.Input.UsernameCreated</span>
                            </div>
                            @if (Model.Input.DateModified.HasValue)
                            {
                                <div>
                                    <small class="text-muted d-block">Променето:</small>
                                    <span>@Model.Input.DateModified?.ToString("dd.MM.yyyy HH:mm")</span>
                                    <small class="text-muted ms-1">од</small>
                                    <span>@Model.Input.UsernameModified</span>
                                </div>
                            }
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex flex-column">
                        <small class="text-muted mb-1">Детали за извод</small>
                        <div class="d-flex flex-wrap gap-3">
                            <div>
                                <small class="text-muted d-block">Банка:</small>
                                <span>@Model.Input.BankaNaziv</span>
                            </div>
                            <div>
                                <small class="text-muted d-block">Број на сметка:</small>
                                <span>@Model.Input.BrojNaSmetka</span>
                            </div>
                            <div>
                                <small class="text-muted d-block">Број на извод:</small>
                                <span>@Model.Input.BrojNaIzvod</span>
                            </div>
                            <div>
                                <small class="text-muted d-block">Датум:</small>
                                <span>@Model.Input.DatumNaIzvod.ToString("dd.MM.yyyy")</span>
                            </div>
                            <div>
                                <small class="text-muted d-block">Прилив:</small>
                                <span class="text-primary fw-bold">@Model.Input.Priliv.ToString("N2")</span>
                            </div>
                            <div>
                                <small class="text-muted d-block">Раскнижен Прилив:</small>
                                @if (Model.Input.Rasknizen == true)
                                {
                                    <span class="badge bg-success">Да</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">Не</span>
                                }
                            </div>
                            <div>
                                <small class="text-muted d-block">Одлив:</small>
                                <span class="text-primary fw-bold">@Model.Input.Odliv.ToString("N2")</span>
                            </div>
                            <div>
                                <small class="text-muted d-block">Раскнижен Одлив:</small>
                                @if (Model.Input.RasknizenOdliv == true)
                                {
                                    <span class="badge bg-success">Да</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">Не</span>
                                }
                            </div>
                            
                            
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Ставки прилив</h5>
                <button type="button" class="btn btn-primary btn-sm" 
                        onclick="openAddStavkaModal()"
                        @(Model.HasAdminAccess && Model.Input.Rasknizen != true ? "" : "disabled")
                        title="@(Model.Input.Rasknizen == true ? "Не може да се додаваат ставки на раскнижен извод" : 
                               !Model.HasAdminAccess ? "Немате пристап" : "")">
                    <i class="fas fa-plus me-1"></i> Додај ставка прилив
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ИД</th>
                            <th>Креирано на</th>
                            <th>Креирано од</th>
                            <th>Референца уплаќач</th>
                            <th>Повик број</th>
                            <th>Цел на дознака</th>
                            <th class="text-end">Износ</th>
                            <th>Број на полиса</th>
                            <th>Број на касов извештај</th>
                            <th>Број на фактура</th>
                            <th class="text-center">Нераспределена</th>
                            <th class="text-center">Поврат на средства</th>
                            <th class="text-center">Евидентирање уплата</th>
                            <th class="text-center">Акции</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.Stavki.Any())
                        {
                            foreach (var stavka in Model.Stavki)
                            {
                                <tr>
                                    <td>@stavka.Id</td>
                                    <td>@stavka.DateCreated?.ToString("dd.MM.yyyy HH:mm")</td>
                                    <td>@stavka.UsernameCreated</td>
                                    <td>@stavka.ReferencaUplakjac</td>
                                    <td>@stavka.PovikBroj</td>
                                    <td>@stavka.CelNaDoznaka</td>
                                    <td class="text-end">@stavka.Iznos.ToString("N2")</td>
                                    <td>@stavka.PolisaBroj</td>
                                    <td>@stavka.BrojNaKasoviZvestaj</td>
                                    <td>@stavka.BrojNaFaktura</td>
                                    <td class="text-center">
                                        @if (stavka.Neraspredelena == true)
                                        {
                                            <i class="fas fa-check text-success"></i>
                                        }
                                    </td>
                                    <td class="text-center">
                                        @if (stavka.PovratNaSredstva == true)
                                        {
                                            <i class="fas fa-check text-success"></i>
                                        }
                                    </td>
                                    <td class="text-center">
                                        @if (stavka.EvidentiranjeUplata == true)
                                        {
                                            <i class="fas fa-check text-success"></i>
                                        }
                                    </td>
                                    <td class="text-center">
                                        <button type="button" class="btn btn-sm btn-danger" 
                                                onclick="openStornoModal(@stavka.Id)"
                                                @(Model.HasAdminAccess ? "" : "disabled")>
                                            Сторнирај
                                        </button>
                                    </td>
                                </tr>
                            }
                        }
                        else
                        {
                            <tr>
                                <td colspan="12" class="text-center">Нема ставки</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Сторнирани ставки прилив</h5>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ИД</th>
                            <th>Креирано на</th>
                            <th>Креирано од</th>
                            <th>Референца уплаќач</th>
                            <th>Повик број</th>
                            <th>Цел на дознака</th>
                            <th class="text-end">Износ</th>
                            <th>Број на полиса</th>
                            <th>Број на касов извештај</th>
                            <th>Број на фактура</th>
                            <th class="text-center">Нераспределена</th>
                            <th class="text-center">Поврат на средства</th>
                            <th class="text-center">Евидентирање уплата</th>
                            <th class="text-center">Сторно</th>
                            <th>Датум на сторно</th>
                            <th>Месец</th>
                            <th>Година</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.StornoStavki.Any())
                        {
                            foreach (var stavka in Model.StornoStavki)
                            {
                                <tr>
                                    <td>@stavka.Id</td>
                                    <td>@stavka.DateCreated?.ToString("dd.MM.yyyy HH:mm")</td>
                                    <td>@stavka.UsernameCreated</td>
                                    <td>@stavka.ReferencaUplakjac</td>
                                    <td>@stavka.PovikBroj</td>
                                    <td>@stavka.CelNaDoznaka</td>
                                    <td class="text-end">@stavka.Iznos.ToString("N2")</td>
                                    <td>@stavka.PolisaBroj</td>
                                    <td>@stavka.BrojNaKasoviZvestaj</td>
                                    <td>@stavka.BrojNaFaktura</td>
                                    <td class="text-center">
                                        @if (stavka.Neraspredelena == true)
                                        {
                                            <i class="fas fa-check text-success"></i>
                                        }
                                    </td>
                                    <td class="text-center">
                                        @if (stavka.PovratNaSredstva == true)
                                        {
                                            <i class="fas fa-check text-success"></i>
                                        }
                                    </td>
                                    <td class="text-center">
                                        @if (stavka.EvidentiranjeUplata == true)
                                        {
                                            <i class="fas fa-check text-success"></i>
                                        }
                                    </td>
                                    <td class="text-center">
                                        @if (stavka.Storno == true)
                                        {
                                            <i class="fas fa-check text-danger"></i>
                                        }
                                    </td>
                                    <td>@stavka.DatumStorno?.ToString("dd.MM.yyyy")</td>
                                    <td>@stavka.StornoZaMesec</td>
                                    <td>@stavka.StornoZaGodina</td>
                                </tr>
                            }
                        }
                        else
                        {
                            <tr>
                                <td colspan="15" class="text-center">Нема сторнирани ставки</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Ставки одлив</h5>
                <button type="button" class="btn btn-primary btn-sm" 
                        onclick="openAddStavkaOdlivModal()"
                        @(Model.HasAdminAccess && Model.Input.RasknizenOdliv != true ? "" : "disabled")
                        title="@(Model.Input.RasknizenOdliv == true ? "Не може да се додаваат ставки на раскнижен извод" : 
                               !Model.HasAdminAccess ? "Немате пристап" : "")">
                    <i class="fas fa-plus me-1"></i> Додај ставка одлив
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ИД</th>
                            <th>Креирано на</th>
                            <th>Креирано од</th>
                            <th>Референца уплаќач</th>
                            <th>Повик број</th>
                            <th>Цел на дознака</th>
                            <th class="text-end">Износ</th>
                            <th>Број на полиса</th>
                            <th>Број на касов извештај</th>
                            <th>Број на фактура</th>
                            <th class="text-center">Нераспределена</th>
                            <th class="text-center">Поврат на средства</th>
                            <th class="text-center">Евидентирање исплата</th>
                            <th class="text-center">Акции</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.StavkiOdliv.Any())
                        {
                            foreach (var stavka in Model.StavkiOdliv)
                            {
                                <tr>
                                    <td>@stavka.Id</td>
                                    <td>@stavka.DateCreated?.ToString("dd.MM.yyyy HH:mm")</td>
                                    <td>@stavka.UsernameCreated</td>
                                    <td>@stavka.ReferencaUplakjac</td>
                                    <td>@stavka.PovikBroj</td>
                                    <td>@stavka.CelNaDoznaka</td>
                                    <td class="text-end">@stavka.Iznos.ToString("N2")</td>
                                    <td>@stavka.PolisaBroj</td>
                                    <td>@stavka.BrojNaKasoviZvestaj</td>
                                    <td>@stavka.BrojNaFaktura</td>
                                    <td class="text-center">
                                        @if (stavka.Neraspredelena == true)
                                        {
                                            <i class="fas fa-check text-success"></i>
                                        }
                                    </td>
                                    <td class="text-center">
                                        @if (stavka.PovratNaSredstva == true)
                                        {
                                            <i class="fas fa-check text-success"></i>
                                        }
                                    </td>
                                    <td class="text-center">
                                        @if (stavka.EvidentiranjeUplata == true)
                                        {
                                            <i class="fas fa-check text-success"></i>
                                        }
                                    </td>
                                    <td class="text-center">
                                        <button type="button" class="btn btn-sm btn-danger" 
                                                onclick="openStornoModal(@stavka.Id)"
                                                @(Model.HasAdminAccess ? "" : "disabled")>
                                            Сторнирај
                                        </button>
                                    </td>
                                </tr>
                            }
                        }
                        else
                        {
                            <tr>
                                <td colspan="14" class="text-center">Нема ставки</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0">Сторнирани ставки одлив</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ИД</th>
                            <th>Креирано на</th>
                            <th>Креирано од</th>
                            <th>Референца уплаќач</th>
                            <th>Повик број</th>
                            <th>Цел на дознака</th>
                            <th class="text-end">Износ</th>
                            <th>Број на полиса</th>
                            <th>Број на касов извештај</th>
                            <th>Број на фактура</th>
                            <th class="text-center">Нераспределена</th>
                            <th class="text-center">Поврат на средства</th>
                            <th class="text-center">Евидентирање исплата</th>
                            <th class="text-center">Сторно</th>
                            <th>Датум на сторно</th>
                            <th>Месец</th>
                            <th>Година</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.StornoStavkiOdliv.Any())
                        {
                            foreach (var stavka in Model.StornoStavkiOdliv)
                            {
                                <tr>
                                    <td>@stavka.Id</td>
                                    <td>@stavka.DateCreated?.ToString("dd.MM.yyyy HH:mm")</td>
                                    <td>@stavka.UsernameCreated</td>
                                    <td>@stavka.ReferencaUplakjac</td>
                                    <td>@stavka.PovikBroj</td>
                                    <td>@stavka.CelNaDoznaka</td>
                                    <td class="text-end">@stavka.Iznos.ToString("N2")</td>
                                    <td>@stavka.PolisaBroj</td>
                                    <td>@stavka.BrojNaKasoviZvestaj</td>
                                    <td>@stavka.BrojNaFaktura</td>
                                    <td class="text-center">
                                        @if (stavka.Neraspredelena == true)
                                        {
                                            <i class="fas fa-check text-success"></i>
                                        }
                                    </td>
                                    <td class="text-center">
                                        @if (stavka.PovratNaSredstva == true)
                                        {
                                            <i class="fas fa-check text-success"></i>
                                        }
                                    </td>
                                    <td class="text-center">
                                        @if (stavka.EvidentiranjeUplata == true)
                                        {
                                            <i class="fas fa-check text-success"></i>
                                        }
                                    </td>
                                    <td class="text-center">
                                        @if (stavka.Storno == true)
                                        {
                                            <i class="fas fa-check text-danger"></i>
                                        }
                                    </td>
                                    <td>@stavka.DatumStorno?.ToString("dd.MM.yyyy")</td>
                                    <td>@stavka.StornoZaMesec</td>
                                    <td>@stavka.StornoZaGodina</td>
                                </tr>
                            }
                        }
                        else
                        {
                            <tr>
                                <td colspan="17" class="text-center">Нема сторнирани ставки</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Storno Modal -->
<div class="modal fade" id="stornoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Сторнирај ставка</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="stavkaId" />
                <div class="mb-3">
                    <label class="form-label">Датум на сторно</label>
                    <input type="date" class="form-control" id="datumStorno" required />
                </div>
                <div class="mb-3">
                    <label class="form-label">Месец</label>
                    <input type="number" class="form-control" id="stornoZaMesec" min="1" max="12" required />
                </div>
                <div class="mb-3">
                    <label class="form-label">Година</label>
                    <input type="number" class="form-control" id="stornoZaGodina" min="2000" max="2100" required />
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Откажи</button>
                <button type="button" class="btn btn-danger" onclick="saveStorno()">Зачувај</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Stavka Modal -->
<div class="modal fade" id="addStavkaModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Додај нова ставка</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Референца уплаќач</label>
                        <input type="text" class="form-control" id="referencaUplakjac" />
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Повик број</label>
                        <input type="text" class="form-control" id="povikBroj" />
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-12">
                        <label class="form-label">Цел на дознака</label>
                        <input type="text" class="form-control" id="celNaDoznaka" />
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Износ *</label>
                        <input type="number" class="form-control" id="iznos" step="0.0001" required />
                        <small id="debugNerasknizenPriliv" class="text-muted"></small>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Број на полиса</label>
                        <input type="text" class="form-control" id="polisaBroj" />
                        <div id="polisaSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Број на фактура</label>
                        <input type="text" class="form-control" id="brojNaFaktura" />
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="neraspredelena" />
                            <label class="form-check-label">Нераспределена</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="povratNaSredstva" />
                            <label class="form-check-label">Поврат на средства</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="evidentiranjeUplata" />
                            <label class="form-check-label">Евидентирање уплата</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Откажи</button>
                <button type="button" class="btn btn-primary" onclick="saveStavka()">Зачувај</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Stavka Odliv Modal -->
<div class="modal fade" id="addStavkaOdlivModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Додај нова ставка одлив</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Референца уплаќач</label>
                        <input type="text" class="form-control" id="referencaUplakjacOdliv" />
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Повик број</label>
                        <input type="text" class="form-control" id="povikBrojOdliv" />
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-12">
                        <label class="form-label">Цел на дознака</label>
                        <input type="text" class="form-control" id="celNaDoznakaOdliv" />
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Износ *</label>
                        <input type="number" class="form-control" id="iznosOdliv" step="0.0001" required />
                        <small id="debugNerasknizenOdliv" class="text-muted"></small>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Број на полиса</label>
                        <input type="text" class="form-control" id="polisaBrojOdliv" />
                        <div id="polisaSearchResultsOdliv" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Број на фактура</label>
                        <input type="text" class="form-control" id="brojNaFakturaOdliv" />
                        <div id="fakturaSearchResultsOdliv" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="neraspredelenaOdliv" />
                            <label class="form-check-label">Нераспределена</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="povratNaSredstvaOdliv" />
                            <label class="form-check-label">Поврат на средства</label>
                        </div>
                    </div>
                    <div class="col-md-3" style="display: none;">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="evidentiranjeUplataOdliv" />
                            <label class="form-check-label">Евидентирање уплата</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Откажи</button>
                <button type="button" class="btn btn-primary" onclick="saveStavkaOdliv()">Зачувај</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <style>
        .gap-3 {
            gap: 1rem !important;
        }
        .badge {
            font-size: 0.85em;
        }
    </style>
    <script>
        let addStavkaModal;
        let stornoModal;
        let addStavkaOdlivModal;
        
        $(document).ready(function() {
            stornoModal = new bootstrap.Modal(document.getElementById('stornoModal'));
            addStavkaModal = new bootstrap.Modal(document.getElementById('addStavkaModal'));
            addStavkaOdlivModal = new bootstrap.Modal(document.getElementById('addStavkaOdlivModal'));

            // Policy search functionality
            let policySearchTimeout;
            $('#polisaBroj').on('input', function() {
                clearTimeout(policySearchTimeout);
                const searchText = $(this).val();
                const resultsDiv = $('#polisaSearchResults');
                
                if (searchText.length < 3) {
                    resultsDiv.hide();
                    return;
                }
                
                policySearchTimeout = setTimeout(function() {
                    fetch(`?handler=SearchPolisi&searchTerm=${encodeURIComponent(searchText)}`, {
                        headers: {
                            'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        resultsDiv.empty();
                        
                        data.forEach(policy => {
                            const displayText = `Полиса: ${policy.brojNaPolisa}${policy.brojNaFakturaIzlezna ? ` | Фактура: ${policy.brojNaFakturaIzlezna}` : ''}`;
                            
                            const div = $('<div>')
                                .addClass('p-2 search-result-item')
                                .text(displayText)
                                .css('cursor', 'pointer')
                                .hover(
                                    function() { $(this).addClass('bg-light'); },
                                    function() { $(this).removeClass('bg-light'); }
                                )
                                .click(function() {
                                    $('#polisaBroj').val(policy.brojNaPolisa);
                                    $('#brojNaFaktura').val(policy.brojNaFakturaIzlezna || '');
                                    resultsDiv.hide();
                                });
                            
                            resultsDiv.append(div);
                        });
                        
                        if (data.length > 0) {
                            resultsDiv.show();
                        } else {
                            resultsDiv.hide();
                        }
                    });
                }, 300);
            });
            
            $(document).click(function(e) {
                if (!$(e.target).closest('#polisaBroj, #polisaSearchResults').length) {
                    $('#polisaSearchResults').hide();
                }
            });

            // Policy search functionality for odliv
            let policySearchTimeoutOdliv;
            $('#polisaBrojOdliv').on('input', function() {
                clearTimeout(policySearchTimeoutOdliv);
                const searchText = $(this).val();
                const resultsDiv = $('#polisaSearchResultsOdliv');
                
                if (searchText.length < 3) {
                    resultsDiv.hide();
                    return;
                }
                
                policySearchTimeoutOdliv = setTimeout(function() {
                    fetch(`?handler=SearchPolisi&searchTerm=${encodeURIComponent(searchText)}`, {
                        headers: {
                            'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        resultsDiv.empty();
                        
                        data.forEach(policy => {
                            const displayText = `Полиса: ${policy.brojNaPolisa}${policy.brojNaFakturaIzlezna ? ` | Фактура: ${policy.brojNaFakturaIzlezna}` : ''}`;
                            
                            const div = $('<div>')
                                .addClass('p-2 search-result-item')
                                .text(displayText)
                                .css('cursor', 'pointer')
                                .hover(
                                    function() { $(this).addClass('bg-light'); },
                                    function() { $(this).removeClass('bg-light'); }
                                )
                                .click(function() {
                                    $('#polisaBrojOdliv').val(policy.brojNaPolisa);
                                    $('#brojNaFakturaOdliv').val(policy.brojNaFakturaIzlezna || '');
                                    resultsDiv.hide();
                                });
                            
                            resultsDiv.append(div);
                        });
                        
                        if (data.length > 0) {
                            resultsDiv.show();
                        } else {
                            resultsDiv.hide();
                        }
                    });
                }, 300);
            });
            
            $(document).click(function(e) {
                if (!$(e.target).closest('#polisaBrojOdliv, #polisaSearchResultsOdliv').length) {
                    $('#polisaSearchResultsOdliv').hide();
                }
            });
            
            // Invoice search functionality for odliv
            let fakturaSearchTimeoutOdliv;
            $('#brojNaFakturaOdliv').on('input', function() {
                clearTimeout(fakturaSearchTimeoutOdliv);
                const searchText = $(this).val();
                const resultsDiv = $('#fakturaSearchResultsOdliv');
                
                if (searchText.length < 2) {
                    resultsDiv.hide();
                    return;
                }
                
                fakturaSearchTimeoutOdliv = setTimeout(function() {
                    fetch(`?handler=SearchFaktura&searchTerm=${encodeURIComponent(searchText)}`, {
                        headers: {
                            'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        resultsDiv.empty();
                        
                        data.forEach(faktura => {
                            const displayText = `${faktura.brojNaFaktura} | ${faktura.naziv || 'Н/А'}${faktura.datumNaFaktura ? ` | ${faktura.datumNaFaktura}` : ''}`;
                            
                            const div = $('<div>')
                                .addClass('p-2 search-result-item')
                                .text(displayText)
                                .css('cursor', 'pointer')
                                .hover(
                                    function() { $(this).addClass('bg-light'); },
                                    function() { $(this).removeClass('bg-light'); }
                                )
                                .click(function() {
                                    $('#brojNaFakturaOdliv').val(faktura.brojNaFaktura);
                                    resultsDiv.hide();
                                });
                            
                            resultsDiv.append(div);
                        });
                        
                        if (data.length > 0) {
                            resultsDiv.show();
                        } else {
                            resultsDiv.hide();
                        }
                    });
                }, 300);
            });
            
            $(document).click(function(e) {
                if (!$(e.target).closest('#brojNaFakturaOdliv, #fakturaSearchResultsOdliv').length) {
                    $('#fakturaSearchResultsOdliv').hide();
                }
            });
        });
        
        function openStornoModal(stavkaId) {
            document.getElementById('stavkaId').value = stavkaId;
            document.getElementById('datumStorno').value = new Date().toISOString().split('T')[0];
            document.getElementById('stornoZaMesec').value = new Date().getMonth() + 1;
            document.getElementById('stornoZaGodina').value = new Date().getFullYear();
            stornoModal.show();
        }
        
        function saveStorno() {
            const data = {
                stavkaId: document.getElementById('stavkaId').value,
                datumStorno: document.getElementById('datumStorno').value,
                stornoZaMesec: document.getElementById('stornoZaMesec').value,
                stornoZaGodina: document.getElementById('stornoZaGodina').value
            };
            
            fetch('?handler=StornoStavka', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        throw new Error('Invalid JSON response');
                    }
                });
            })
            .then(result => {
                if (result.success) {
                    location.reload();
                } else {
                    alert('Грешка при сторнирање на ставката: ' + result.message);
                }
            })
            .catch(error => {
                alert('Се појави грешка при сторнирање на ставката');
            });
        }

        function openAddStavkaModal() {
            // Clear form
            document.getElementById('referencaUplakjac').value = '';
            document.getElementById('povikBroj').value = '';
            document.getElementById('celNaDoznaka').value = '';
            document.getElementById('iznos').value = '';
            document.getElementById('polisaBroj').value = '';
            document.getElementById('brojNaFaktura').value = '';
            document.getElementById('neraspredelena').checked = false;
            document.getElementById('povratNaSredstva').checked = false;
            document.getElementById('evidentiranjeUplata').checked = false;
            addStavkaModal.show();
        }
        
        function saveStavka() {
            const iznos = document.getElementById('iznos').value;
            if (!iznos) {
                alert('Внесете износ');
                return;
            }

            const data = {
                referencaUplakjac: document.getElementById('referencaUplakjac').value,
                povikBroj: document.getElementById('povikBroj').value,
                celNaDoznaka: document.getElementById('celNaDoznaka').value,
                iznos: parseFloat(iznos),
                polisaBroj: document.getElementById('polisaBroj').value || null,
                brojNaFaktura: document.getElementById('brojNaFaktura').value || null,
                neraspredelena: document.getElementById('neraspredelena').checked,
                povratNaSredstva: document.getElementById('povratNaSredstva').checked,
                evidentiranjeUplata: document.getElementById('evidentiranjeUplata').checked
            };

            fetch('?handler=AddStavka', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    location.reload();
                } else {
                    alert('Грешка при зачувување на ставката: ' + result.message);
                }
            })
            .catch(error => {
                alert('Се појави грешка при зачувување на ставката');
            });
        }

        // Add new code for checkbox control
        document.addEventListener('DOMContentLoaded', function() {
            const checkboxes = [
                document.getElementById('neraspredelena'),
                document.getElementById('povratNaSredstva'),
                document.getElementById('evidentiranjeUplata')
            ];

            // Add click handler to each checkbox
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    if (this.checked) {
                        // Uncheck other checkboxes
                        checkboxes.forEach(cb => {
                            if (cb !== this) cb.checked = false;
                        });
                    } else {
                        // If unchecking, verify at least one is still checked
                        const anyChecked = checkboxes.some(cb => cb.checked);
                        if (!anyChecked) {
                            this.checked = true; // Prevent unchecking if it's the only one checked
                        }
                    }
                });
            });

            // Ensure one checkbox is checked when opening modal
            const originalOpenAddStavkaModal = openAddStavkaModal;
            openAddStavkaModal = function() {
                originalOpenAddStavkaModal();
                const anyChecked = checkboxes.some(cb => cb.checked);
                if (!anyChecked) {
                    checkboxes[0].checked = true;
                }
            };

            // Add validation before saving
            const originalSaveStavka = saveStavka;
            saveStavka = function() {
                const anyChecked = checkboxes.some(cb => cb.checked);
                if (!anyChecked) {
                    alert('Мора да изберете една од опциите: Нераспределена, Поврат на средства или Евидентирање уплата');
                    return;
                }
                originalSaveStavka();
            };
        });

        // Add new validation function for payment amount
        async function validatePaymentAmount() {
            const evidentiranjeUplata = document.getElementById('evidentiranjeUplata');
            const polisaBroj = document.getElementById('polisaBroj');
            const iznos = document.getElementById('iznos');

            if (!evidentiranjeUplata.checked) {
                return true; // Skip validation if checkbox not checked
            }

            if (!polisaBroj.value) {
                alert('Мора да внесете број на полиса');
                return false;
            }

            try {
                const response = await fetch(`?handler=DolznaPremija&brojNaPolisa=${polisaBroj.value}`, {
                    headers: {
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    }
                });
                
                const result = await response.json();
                const dolznaPremija = result.dolznaPremija;

                if (parseFloat(iznos.value) > dolznaPremija) {
                    alert('Износот на уплата не може да биде поголем од вкупната должна премија за избраната полиса');
                    return false;
                }
                
                return true;
            } catch (error) {
                console.error('Error validating payment amount:', error);
                alert('Грешка при проверка на должна премија');
                return false;
            }
        }

        // Add this helper function to get ID from URL
        function getIzvodIdFromUrl() {
            const pathParts = window.location.pathname.split('/');
            return pathParts[pathParts.length - 1];
        }

        // Modify the updateDebugDisplay function
        async function updateDebugDisplay() {
            try {
                const izvodId = getIzvodIdFromUrl();
                const response = await fetch(`?handler=NerasknizenPriliv&id=${izvodId}`, {
                    headers: {
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    }
                });
                
                const result = await response.json();
                document.getElementById('debugNerasknizenPriliv').textContent = 
                    `Преостанат нераскнижен прилив: ${result.nerasknizenPriliv}`;
            } catch (error) {
                console.error('Error fetching debug info:', error);
                document.getElementById('debugNerasknizenPriliv').textContent = 
                    'Error fetching remaining amount';
            }
        }

        // Also update validateRemainingAmount to use the ID
        async function validateRemainingAmount() {
            const iznos = document.getElementById('iznos');
            
            if (!iznos.value) {
                return true;
            }

            try {
                const izvodId = getIzvodIdFromUrl();
                const response = await fetch(`?handler=NerasknizenPriliv&id=${izvodId}`, {
                    headers: {
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    }
                });
                
                const result = await response.json();
                const nerasknizenPriliv = result.nerasknizenPriliv;

                if (parseFloat(iznos.value) > nerasknizenPriliv) {
                    alert('Вкупната сума на ставките не може да биде поголема од вкупниот прилив на изводот!');
                    return false;
                }
                
                return true;
            } catch (error) {
                console.error('Error validating remaining amount:', error);
                alert('Грешка при проверка на преостанат прилив');
                return false;
            }
        }

        // Add new validation function for policy existence
        async function validatePolicyExists() {
            const evidentiranjeUplata = document.getElementById('evidentiranjeUplata');
            const polisaBroj = document.getElementById('polisaBroj');

            if (!evidentiranjeUplata.checked) {
                return true; // Skip validation if checkbox not checked
            }

            if (!polisaBroj.value) {
                alert('Мора да внесете број на полиса');
                return false;
            }

            try {
                const response = await fetch(`?handler=ProverkaPolisa&brojNaPolisa=${polisaBroj.value}`, {
                    headers: {
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    }
                });
                
                const result = await response.json();
                
                if (result.exists !== 1) {
                    alert('Полисата не постои во системот');
                    return false;
                }
                
                return true;
            } catch (error) {
                console.error('Error checking policy:', error);
                alert('Грешка при проверка на полиса');
                return false;
            }
        }

        // Update the wrapped saveStavka function to include new validation first
        const originalValidatedSaveStavka = saveStavka;
        saveStavka = async function() {
            if (!(await validatePolicyExists())) {
                return;
            }
            if (!(await validatePaymentAmount())) {
                return;
            }
            if (!(await validateRemainingAmount())) {
                return;
            }
            originalValidatedSaveStavka();
        };

        // Modify the openAddStavkaModal function to include debug update
        const originalOpenAddStavkaModal = openAddStavkaModal;
        openAddStavkaModal = function() {
            originalOpenAddStavkaModal();
            updateDebugDisplay();
        };

        function openAddStavkaOdlivModal() {
            // Clear form
            document.getElementById('referencaUplakjacOdliv').value = '';
            document.getElementById('povikBrojOdliv').value = '';
            document.getElementById('celNaDoznakaOdliv').value = '';
            document.getElementById('iznosOdliv').value = '';
            document.getElementById('polisaBrojOdliv').value = '';
            document.getElementById('brojNaFakturaOdliv').value = '';
            document.getElementById('neraspredelenaOdliv').checked = false;
            document.getElementById('povratNaSredstvaOdliv').checked = false;
            document.getElementById('evidentiranjeUplataOdliv').checked = false;
            addStavkaOdlivModal.show();
            updateDebugDisplayOdliv();
        }
        
        function saveStavkaOdliv() {
            const iznos = document.getElementById('iznosOdliv').value;
            if (!iznos) {
                alert('Внесете износ');
                return;
            }

            validateRemainingAmountOdliv().then(isValid => {
                if (!isValid) return;
                
                const data = {
                    referencaUplakjac: document.getElementById('referencaUplakjacOdliv').value,
                    povikBroj: document.getElementById('povikBrojOdliv').value,
                    celNaDoznaka: document.getElementById('celNaDoznakaOdliv').value,
                    iznos: parseFloat(iznos),
                    polisaBroj: document.getElementById('polisaBrojOdliv').value || null,
                    brojNaFaktura: document.getElementById('brojNaFakturaOdliv').value || null,
                    neraspredelena: document.getElementById('neraspredelenaOdliv').checked,
                    povratNaSredstva: document.getElementById('povratNaSredstvaOdliv').checked,
                    evidentiranjeUplata: document.getElementById('evidentiranjeUplataOdliv').checked,
                    isOdliv: true
                };

                fetch('?handler=AddStavka', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        location.reload();
                    } else {
                        alert('Грешка при зачувување на ставката: ' + result.message);
                    }
                })
                .catch(error => {
                    alert('Се појави грешка при зачувување на ставката');
                });
            });
        }

        // Add new code for checkbox control for odliv
        document.addEventListener('DOMContentLoaded', function() {
            const checkboxesOdliv = [
                document.getElementById('neraspredelenaOdliv'),
                document.getElementById('povratNaSredstvaOdliv'),
                document.getElementById('evidentiranjeUplataOdliv')
            ];

            // Add click handler to each checkbox
            checkboxesOdliv.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    if (this.checked) {
                        // Uncheck other checkboxes
                        checkboxesOdliv.forEach(cb => {
                            if (cb !== this) cb.checked = false;
                        });
                    }
                });
            });
        });

        // Add new function for odliv debug display
        async function updateDebugDisplayOdliv() {
            try {
                const izvodId = getIzvodIdFromUrl();
                const response = await fetch(`?handler=NerasknizenOdliv&id=${izvodId}`, {
                    headers: {
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    }
                });
                
                const result = await response.json();
                document.getElementById('debugNerasknizenOdliv').textContent = 
                    `Преостанат одлив: ${result.nerasknizenOdliv.toFixed(2)} (Вкупно: ${result.debug.odliv.toFixed(2)}, Искористено: ${result.debug.usedOdliv.toFixed(2)})`;
            } catch (error) {
                console.error('Error fetching debug info:', error);
                document.getElementById('debugNerasknizenOdliv').textContent = 
                    'Error fetching remaining amount';
            }
        }

        // Also update validateRemainingAmountOdliv to use the ID
        async function validateRemainingAmountOdliv() {
            const iznos = document.getElementById('iznosOdliv');
            
            if (!iznos.value) {
                return true;
            }

            try {
                const izvodId = getIzvodIdFromUrl();
                const response = await fetch(`?handler=NerasknizenOdliv&id=${izvodId}`, {
                    headers: {
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    }
                });
                
                const result = await response.json();
                const nerasknizenOdliv = result.nerasknizenOdliv;

                if (parseFloat(iznos.value) > nerasknizenOdliv) {
                    alert('Вкупната сума на ставките не може да биде поголема од вкупниот одлив на изводот!');
                    return false;
                }
                
                return true;
            } catch (error) {
                console.error('Error validating remaining amount:', error);
                alert('Грешка при проверка на преостанат одлив');
                return false;
            }
        }
    </script>
} 