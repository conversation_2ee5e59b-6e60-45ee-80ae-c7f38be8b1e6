using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Data.SqlClient;
using Microsoft.AspNetCore.Http;

namespace RazorPortal.Pages
{
    public class IndexModel : PageModel
    {
        private readonly ILogger<IndexModel> _logger;
        private readonly IConfiguration _configuration;

        public IndexModel(ILogger<IndexModel> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
        }

        public void OnGet()
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    connection.Open();
                    string sql = "SELECT VariableValue FROM EnvironmentVariables WHERE VariableName = 'SystemEnvironment'";
                    
                    using (SqlCommand command = new SqlCommand(sql, connection))
                    {
                        var result = command.ExecuteScalar();
                        if (result != null)
                        {
                            HttpContext.Session.SetString("NextBrokerSystemEnvironment", result.ToString());
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error loading environment variable: {ex.Message}");
            }
        }
    }
}
