﻿@page
@model ResetConfirmationModel
@{
    ViewData["Title"] = "Промена на лозинка";
}

<link rel="stylesheet" href="~/css/login.css" asp-append-version="true" />

<div class="login-container">
    <h2>@ViewData["Title"]</h2>

    @if (Model.SuccessMessage == null)
    {
        <form method="post" id="resetForm">
            <div class="form-group">
                <label for="emb">Матичен број</label>
                <input type="text" 
                       id="emb" 
                       name="EMB" 
                       class="form-control" 
                       pattern="^\d+$" 
                       title="Внесете валиден матичен број."
                       required />
            </div>

            <div class="form-group">
                <label for="newPassword">Нова лозинка</label>
                <input type="password" 
                       id="newPassword" 
                       name="NewPassword" 
                       class="form-control" 
                       pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\da-zA-Z]).{8,}$" 
                       title="Лозинката мора да содржи најмалку 8 карактери, една голема буква, една бројка и еден специјален карактер." 
                       required />
            </div>

            <div class="form-group">
                <label for="confirmPassword">Потврда на лозинка</label>
                <input type="password" 
                       id="confirmPassword" 
                       name="ConfirmPassword" 
                       class="form-control" 
                       required />
            </div>

            <div class="form-group">
                <label for="resetRequest">Код за потврда</label>
                <input type="text" 
                       id="resetRequest" 
                       name="ResetRequest" 
                       class="form-control" 
                       placeholder="Внесете го кодот од E-Mail-от"
                       required />
            </div>

            <button type="submit" class="btn btn-primary">Промени лозинка</button>

            @if (Model.ErrorMessage != null)
            {
                <div class="alert alert-danger mt-3">@Model.ErrorMessage</div>
            }
        </form>
    }
    else
    {
        <div class="alert alert-success mt-3">@Model.SuccessMessage</div>
        
        <button onclick="window.location.href='@Url.Page("/Login")'" class="btn btn-primary">
            Продолжи кон најава
        </button>
    }
</div>
