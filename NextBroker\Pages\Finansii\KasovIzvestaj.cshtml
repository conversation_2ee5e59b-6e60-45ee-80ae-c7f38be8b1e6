@page
@model NextBroker.Pages.Finansii.KasovIzvestajModel
@{
    ViewData["Title"] = "Касов извештај";
    var formClass = !Model.HasAdminAccess ? "form-disabled" : "";
}

<link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.js"></script>

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container-fluid mt-4 px-4">
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Филтри</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3 align-items-end" id="filterForm" name="filterForm">
                <div class="col-md-4">
                    <label class="form-label">Датум од</label>
                    <input type="date" class="form-control" name="DatumOd" value="@(Model.DatumOd?.ToString("yyyy-MM-dd"))" />
                </div>
                <div class="col-md-4">
                    <label class="form-label">Датум до</label>
                    <input type="date" class="form-control" name="DatumDo" value="@(Model.DatumDo?.ToString("yyyy-MM-dd"))" />
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i> Филтрирај
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearDates()">
                        <i class="fas fa-times me-1"></i> Исчисти
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container-fluid mt-4 px-4">
    @if (!Model.HasAdminAccess)
    {
        <style>
            .form-disabled input:not([type="hidden"]),
            .form-disabled select,
            .form-disabled textarea {
                pointer-events: none;
                background-color: #e9ecef;
                opacity: 1;
            }
        </style>
    }

    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">@ViewData["Title"]</h5>
                <button type="button" class="btn btn-primary btn-sm" 
                        onclick="openAddModal()"
                        @(Model.HasAdminAccess ? "" : "disabled")>
                    <i class="fas fa-plus me-1"></i> Додај касов извештај
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="kasovIzvestajTable" class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ИД</th>
                            <th>Креирано на</th>
                            <th>Креирано од</th>
                            <th>Променето на</th>
                            <th>Променето од</th>
                            <th>Датум на касов извештај</th>
                            <th>Број на касов извештај</th>
                            <th>Тип на плаќање</th>
                            <th class="text-end">Вкупен износ</th>
                            <th class="text-end">Износ на банкарски извод</th>
                            <th>Број на извод</th>
                            <th>Број на ставка во извод</th>
                            <th>Статус</th>
                            <th style="width: 120px; min-width: 120px;">Акции</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var izvestaj in Model.KasovIzvestai)
                        {
                            <tr>
                                <td>@izvestaj.Id</td>
                                <td>@izvestaj.DateCreated?.ToString("dd.MM.yyyy HH:mm")</td>
                                <td>@izvestaj.UsernameCreated</td>
                                <td>@izvestaj.DateModified?.ToString("dd.MM.yyyy HH:mm")</td>
                                <td>@izvestaj.UsernameModified</td>
                                <td>@izvestaj.DatumNaKasovIzvestaj.ToString("dd.MM.yyyy")</td>
                                <td>@izvestaj.BrojNaKasovIzvestaj</td>
                                <td>@izvestaj.TipNaPlakanje</td>
                                <td class="text-end">@izvestaj.VkupenIznos.ToString("N2")</td>
                                <td class="text-end">@(izvestaj.IznosNaBankarskiIzvod?.ToString("N2") ?? "")</td>
                                <td>@izvestaj.BrojNaIzvod</td>
                                <td>@izvestaj.StavkaPremijaId</td>
                                <td>@(izvestaj.Zatvoren ? "Затворен" : "Отворен")</td>
                                <td style="width: 120px; min-width: 120px;">
                                    <div class="d-flex gap-1">
                                        @if (Model.HasAdminAccess)
                                        {
                                            <button class="btn btn-sm btn-primary" 
                                                    onclick="openEditModal(@izvestaj.Id)"
                                                    @(izvestaj.Zatvoren ? "disabled" : "")>
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-warning" 
                                                    onclick="closeKasovIzvestaj(@izvestaj.Id)"
                                                    @(izvestaj.Zatvoren ? "disabled" : "")>
                                                <i class="fas fa-lock"></i>
                                            </button>
                                        }
                                        @if (!string.IsNullOrEmpty(izvestaj.BrojNaKasovIzvestaj))
                                        {
                                            <button class="btn btn-sm btn-info" onclick="openViewUplatiModal('@izvestaj.BrojNaKasovIzvestaj')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <a class="btn btn-sm btn-danger" href="?handler=DownloadPdf&brojNaKasovIzvestaj=@izvestaj.BrojNaKasovIzvestaj">
                                                <i class="fas fa-file-pdf"></i>
                                            </a>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Измени податоци</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editForm" method="post">
                    @Html.AntiForgeryToken()
                    <input type="hidden" id="editId" name="id" />
                    
                    <div class="mb-3">
                        <label class="form-label">Број на извод</label>
                        <div class="position-relative">
                            <input type="text" 
                                   class="form-control" 
                                   id="editBrojNaIzvod" 
                                   name="brojNaIzvod"
                                   autocomplete="off" />
                            <div id="brojNaIzvodSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                                 style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Број на ставка во извод</label>
                        <div class="position-relative">
                            <input type="number" 
                                   class="form-control" 
                                   id="editStavkaPremijaId" 
                                   name="stavkaPremijaId"
                                   readonly />
                            <div id="stavkaPremijaSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                                 style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Износ на банкарски извод</label>
                        <input type="number" 
                               class="form-control" 
                               id="editIznosNaBankarskiIzvod" 
                               name="iznosNaBankarskiIzvod" 
                               step="0.01"
                               readonly />
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Откажи</button>
                <button type="button" class="btn btn-primary" onclick="saveEdit()">Зачувај</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Modal -->
<div class="modal fade" id="addModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Додај касов извештај</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addForm" method="post">
                    @Html.AntiForgeryToken()
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Тип на плаќање</label>
                            <select class="form-select" 
                                    id="addTipNaPlakanje" 
                                    name="sifrarnikTipNaPlakanjeId"
                                    onchange="loadUplatiPreview()">
                                <option value="">-- Избери тип на плаќање --</option>
                                @foreach (var tip in Model.TipoviNaPlakanje)
                                {
                                    <option value="@tip.Value">@tip.Text</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Датум на касов извештај</label>
                            <input type="date" 
                                   class="form-control" 
                                   id="addDatumNaKasovIzvestaj" 
                                   name="datumNaKasovIzvestaj"
                                   onchange="loadUplatiPreview()" />
                        </div>
                    </div>

                    <!-- Preview Table -->
                    <div class="table-responsive mt-4">
                        <h6>Преглед на уплати</h6>
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Креирано на</th>
                                    <th>Креирано од</th>
                                    <th>Датум на уплата</th>
                                    <th>Тип на плаќање</th>
                                    <th>Број на извод</th>
                                    <th class="text-end">Износ</th>
                                    <th>Број на полиса</th>
                                    <th>Нераспределена</th>
                                    <th>Поврат на средства</th>
                                    <th>Каса прими</th>
                                    <th>Уплаќач</th>
                                </tr>
                            </thead>
                            <tbody id="uplatiPreviewTableBody">
                                <tr>
                                    <td colspan="11" class="text-center">Изберете тип на плаќање и датум</td>
                                </tr>
                            </tbody>
                        </table>
                        
                        <!-- Add this div after the table -->
                        <div class="d-flex justify-content-end mt-2">
                            <h5>Вкупно: <span id="uplatiPreviewTotal" class="text-primary">0.00</span> ден.</h5>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Откажи</button>
                <button type="button" class="btn btn-primary" onclick="saveAdd()">Зачувај</button>
            </div>
        </div>
    </div>
</div>

<!-- View Uplati Modal -->
<div class="modal fade" id="viewUplatiModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Преглед на уплати</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <h6>Основни уплати</h6>
                    <table id="uplatiTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Креирано на</th>
                                <th>Креирано од</th>
                                <th>Датум на уплата</th>
                                <th>Тип на плаќање</th>
                                <th>Број на извод</th>
                                <th>Износ</th>
                                <th>Број на полиса</th>
                                <th>Нераспределена</th>
                                <th>Поврат на средства</th>
                                <th>Банка</th>
                                <th>Каса прими</th>
                                <th>Уплаќач</th>
                            </tr>
                        </thead>
                        <tbody id="uplatiTableBody">
                        </tbody>
                    </table>
                    <!-- Add total sum display -->
                    <div class="d-flex justify-content-end mt-2">
                        <h5>Вкупно: <span id="viewUplatiTotal" class="text-primary">0.00</span> ден.</h5>
                    </div>
                </div>
                
                <!-- Preraspredeleni Uplati section -->
                <div class="table-responsive mt-4">
                    <h6>Прераспределени уплати</h6>
                    <table id="preraspredeleniUplatiTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Креирано на</th>
                                <th>Креирано од</th>
                                <th>Датум на уплата</th>
                                <th>Датум на прераспределба</th>
                                <th>Тип на плаќање</th>
                                <th>Број на извод</th>
                                <th>Износ</th>
                                <th>Број на полиса</th>
                                <th>Нераспределена</th>
                                <th>Поврат на средства</th>
                                <th>Банка</th>
                                <th>Каса прими</th>
                                <th>Уплаќач</th>
                            </tr>
                        </thead>
                        <tbody id="preraspredeleniUplatiTableBody">
                        </tbody>
                    </table>
                    <!-- Add total sum display for preraspredeleni -->
                    <div class="d-flex justify-content-end mt-2">
                        <h5>Вкупно прераспределени: <span id="preraspredeleniUplatiTotal" class="text-primary">0.00</span> ден.</h5>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let editModal;
        let addModal;
        let viewUplatiModal;
        
        $(document).ready(function() {
            editModal = new bootstrap.Modal(document.getElementById('editModal'));
            addModal = new bootstrap.Modal(document.getElementById('addModal'));
            viewUplatiModal = new bootstrap.Modal(document.getElementById('viewUplatiModal'));
            
            $('#kasovIzvestajTable').DataTable({
                language: {
                    "emptyTable": "Нема податоци во табелата",
                    "info": "Прикажани _START_ до _END_ од _TOTAL_ записи",
                    "infoEmpty": "Прикажани 0 до 0 од 0 записи",
                    "infoFiltered": "(филтрирано од вкупно _MAX_ записи)",
                    "lengthMenu": "Прикажи _MENU_ записи",
                    "loadingRecords": "Вчитување...",
                    "processing": "Процесирање...",
                    "search": "Пребарај:",
                    "zeroRecords": "Не се пронајдени записи",
                    "paginate": {
                        "first": "Прва",
                        "last": "Последна",
                        "next": "Следна",
                        "previous": "Претходна"
                    }
                },
                order: [[0, 'desc']]
            });

            // Prevent the filter form from triggering validation on the add form
            $('#filterForm').on('submit', function(e) {
                e.preventDefault();
                this.submit();
            });

            // Add search functionality for BrojNaIzvod
            let searchTimeout;
            const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();

            $('#editBrojNaIzvod').on('input', function() {
                clearTimeout(searchTimeout);
                const searchTerm = $(this).val();
                const resultsDiv = $('#brojNaIzvodSearchResults');

                if (searchTerm.length < 1) {
                    resultsDiv.hide();
                    return;
                }

                searchTimeout = setTimeout(function() {
                    $.ajax({
                        url: `?handler=SearchIzvodPremija`,
                        type: 'GET',
                        data: { term: searchTerm },
                        headers: {
                            "RequestVerificationToken": antiForgeryToken
                        },
                        success: function(data) {
                            if (data && data.length > 0) {
                                let html = '<div class="list-group">';
                                data.forEach(item => {
                                    const displayText = `${item.bankaNaziv} - ${item.brojNaIzvod} - ${new Date(item.datumNaIzvod).toLocaleDateString()} - ${item.priliv.toLocaleString('mk', { minimumFractionDigits: 2 })} ден.`;
                                    html += `<a href="#" class="list-group-item list-group-item-action" data-broj="${item.brojNaIzvod}">
                                              ${displayText}
                                           </a>`;
                                });
                                html += '</div>';
                                resultsDiv.html(html).show();
                            } else {
                                resultsDiv.html(`
                                    <div class="list-group">
                                        <div class="list-group-item text-center">
                                            <p class="mb-2">Нема пронајдени резултати</p>
                                        </div>
                                    </div>
                                `).show();
                            }
                        },
                        error: function() {
                            resultsDiv.html(`
                                <div class="list-group">
                                    <div class="list-group-item text-center">
                                        <p class="text-danger mb-2">Грешка при пребарување</p>
                                    </div>
                                </div>
                            `).show();
                        }
                    });
                }, 300);
            });

            // Handle selection
            $(document).on('click', '#brojNaIzvodSearchResults .list-group-item', function(e) {
                e.preventDefault();
                const brojNaIzvod = $(this).data('broj');
                $('#editBrojNaIzvod').val(brojNaIzvod);
                $('#brojNaIzvodSearchResults').hide();
            });

            // Hide results when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('#editBrojNaIzvod, #brojNaIzvodSearchResults').length) {
                    $('#brojNaIzvodSearchResults').hide();
                }
            });

            // Update the StavkaPremija selection handler
            $(document).on('click', '#stavkaPremijaSearchResults .list-group-item', function(e) {
                e.preventDefault();
                const id = $(this).data('id');
                const iznos = $(this).data('iznos');
                $('#editStavkaPremijaId').val(id);
                $('#editIznosNaBankarskiIzvod').val(iznos);
                $('#stavkaPremijaSearchResults').hide();
            });

            // Update the StavkaPremija results display
            $('#editStavkaPremijaId').on('click', function() {
                const brojNaIzvod = $('#editBrojNaIzvod').val();
                const resultsDiv = $('#stavkaPremijaSearchResults');

                if (!brojNaIzvod) {
                    alert('Прво изберете број на извод');
                    return;
                }

                $.ajax({
                    url: `?handler=SearchStavkaPremija`,
                    type: 'GET',
                    data: { brojNaIzvod: brojNaIzvod },
                    headers: {
                        "RequestVerificationToken": antiForgeryToken
                    },
                    success: function(data) {
                        if (data && data.length > 0) {
                            let html = '<div class="list-group">';
                            data.forEach(item => {
                                const displayText = `ID: ${item.id} - ${item.referencaUplakjac || ''} ${item.povikBroj ? '- ПБ: ' + item.povikBroj : ''} - ${item.iznos.toLocaleString('mk', { minimumFractionDigits: 2 })} ден.`;
                                html += `<a href="#" class="list-group-item list-group-item-action" 
                                          data-id="${item.id}" 
                                          data-iznos="${item.iznos}">
                                          ${displayText}
                                          ${item.celNaDoznaka ? '<br><small class="text-muted">' + item.celNaDoznaka + '</small>' : ''}
                                          ${item.polisaBroj ? '<br><small class="text-muted">Полиса: ' + item.polisaBroj + '</small>' : ''}
                                       </a>`;
                            });
                            html += '</div>';
                            resultsDiv.html(html).show();
                        } else {
                            resultsDiv.html(`
                                <div class="list-group">
                                    <div class="list-group-item text-center">
                                        <p class="mb-2">Нема пронајдени ставки за овој извод</p>
                                    </div>
                                </div>
                            `).show();
                        }
                    },
                    error: function() {
                        resultsDiv.html(`
                            <div class="list-group">
                                <div class="list-group-item text-center">
                                    <p class="text-danger mb-2">Грешка при пребарување</p>
                                </div>
                            </div>
                        `).show();
                    }
                });
            });

            // Clear IznosNaBankarskiIzvod when BrojNaIzvod changes
            $('#editBrojNaIzvod').on('change', function() {
                $('#editIznosNaBankarskiIzvod').val('');
                $('#editStavkaPremijaId').val('');
            });
        });
        
        function openEditModal(id) {
            fetch(`?handler=GetKasovIzvestaj&id=${id}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('editId').value = data.id;
                    document.getElementById('editIznosNaBankarskiIzvod').value = data.iznosNaBankarskiIzvod || '';
                    document.getElementById('editBrojNaIzvod').value = data.brojNaIzvod || '';
                    document.getElementById('editStavkaPremijaId').value = data.stavkaPremijaId || '';
                    editModal.show();
                });
        }
        
        function saveEdit() {
            const formData = {
                id: document.getElementById('editId').value,
                iznosNaBankarskiIzvod: document.getElementById('editIznosNaBankarskiIzvod').value,
                brojNaIzvod: document.getElementById('editBrojNaIzvod').value,
                stavkaPremijaId: document.getElementById('editStavkaPremijaId').value
            };
            
            fetch('?handler=UpdateIznos', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    editModal.hide();
                    location.reload();
                } else {
                    alert('Грешка при зачувување на промените.');
                }
            });
        }

        function openAddModal() {
            document.getElementById('addForm').reset();
            document.getElementById('addDatumNaKasovIzvestaj').value = new Date().toISOString().split('T')[0];
            addModal.show();
        }
        
        function saveAdd() {
            const formData = {
                sifrarnikTipNaPlakanjeId: document.getElementById('addTipNaPlakanje').value,
                datumNaKasovIzvestaj: document.getElementById('addDatumNaKasovIzvestaj').value
            };
            
            if (!formData.sifrarnikTipNaPlakanjeId || !formData.datumNaKasovIzvestaj) {
                alert('Ве молиме пополнете ги сите задолжителни полиња');
                return;
            }
            
            fetch('?handler=AddKasovIzvestaj', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addModal.hide();
                    location.reload();
                } else {
                    alert(data.message || 'Грешка при зачувување.');
                }
            });
        }

        function clearDates() {
            document.querySelector('input[name="DatumOd"]').value = '';
            document.querySelector('input[name="DatumDo"]').value = '';
            document.getElementById('filterForm').submit();
        }

        function openViewUplatiModal(brojNaKasovIzvestaj) {
            // First, fetch regular uplati
            fetch(`?handler=UplatiForKasovIzvestaj&brojNaKasovIzvestaj=${encodeURIComponent(brojNaKasovIzvestaj)}`)
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        const tbody = document.getElementById('uplatiTableBody');
                        tbody.innerHTML = '';
                        let total = 0;
                        
                        result.data.forEach(uplata => {
                            const tr = document.createElement('tr');
                            tr.innerHTML = `
                                <td>${uplata.dateCreated ? new Date(uplata.dateCreated).toLocaleString() : ''}</td>
                                <td>${uplata.usernameCreated || ''}</td>
                                <td>${new Date(uplata.uplataDatum).toLocaleDateString()}</td>
                                <td>${uplata.tipNaPlakanje || ''}</td>
                                <td>${uplata.brojIzvod || ''}</td>
                                <td>${uplata.iznos.toLocaleString('mk', { minimumFractionDigits: 2 })}</td>
                                <td>${uplata.polisaBroj || ''}</td>
                                <td>${uplata.neraspredelena ? 'Да' : 'Не'}</td>
                                <td>${uplata.povratNaSredstva ? 'Да' : 'Не'}</td>
                                <td>${uplata.bankaNaziv || ''}</td>
                                <td>${uplata.kasaPrimi ? 'Да' : 'Не'}</td>
                                <td>${uplata.uplakjacNaziv || ''}</td>
                            `;
                            tbody.appendChild(tr);
                            total += uplata.iznos;
                        });
                        
                        // Update the total
                        document.getElementById('viewUplatiTotal').textContent = total.toLocaleString('mk', { minimumFractionDigits: 2 });
                        
                        // Now fetch preraspredeleni uplati
                        fetchPreraspredeleniUplati(brojNaKasovIzvestaj);
                        
                        // Show the modal
                        viewUplatiModal.show();
                    }
                });
        }
        
        function fetchPreraspredeleniUplati(brojNaKasovIzvestaj) {
            fetch(`?handler=PreraspredeleniUplati&brojNaKasovIzvestaj=${encodeURIComponent(brojNaKasovIzvestaj)}`)
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        const tbody = document.getElementById('preraspredeleniUplatiTableBody');
                        tbody.innerHTML = '';
                        let total = 0;
                        
                        if (result.data && result.data.length > 0) {
                            result.data.forEach(uplata => {
                                const tr = document.createElement('tr');
                                tr.innerHTML = `
                                    <td>${uplata.dateCreated ? new Date(uplata.dateCreated).toLocaleString() : ''}</td>
                                    <td>${uplata.usernameCreated || ''}</td>
                                    <td>${new Date(uplata.uplataDatum).toLocaleDateString()}</td>
                                    <td>${uplata.preraspredelbaDatum ? new Date(uplata.preraspredelbaDatum).toLocaleDateString() : ''}</td>
                                    <td>${uplata.tipNaPlakanje || ''}</td>
                                    <td>${uplata.brojIzvod || ''}</td>
                                    <td>${uplata.iznos.toLocaleString('mk', { minimumFractionDigits: 2 })}</td>
                                    <td>${uplata.polisaBroj || ''}</td>
                                    <td>${uplata.neraspredelena ? 'Да' : 'Не'}</td>
                                    <td>${uplata.povratNaSredstva ? 'Да' : 'Не'}</td>
                                    <td>${uplata.bankaNaziv || ''}</td>
                                    <td>${uplata.kasaPrimi ? 'Да' : 'Не'}</td>
                                    <td>${uplata.uplakjacNaziv || ''}</td>
                                `;
                                tbody.appendChild(tr);
                                total += uplata.iznos;
                            });
                        } else {
                            // If no data, show a message
                            const tr = document.createElement('tr');
                            tr.innerHTML = `<td colspan="13" class="text-center">Нема прераспределени уплати</td>`;
                            tbody.appendChild(tr);
                        }
                        
                        // Update the total
                        document.getElementById('preraspredeleniUplatiTotal').textContent = total.toLocaleString('mk', { minimumFractionDigits: 2 });
                    }
                });
        }

        function closeKasovIzvestaj(id) {
            if (confirm('Дали сте сигурни дека сакате да го затворите овој касов извештај?')) {
                fetch('?handler=CloseKasovIzvestaj', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Грешка при затворање на касовиот извештај.');
                    }
                });
            }
        }

        function loadUplatiPreview() {
            const tipNaPlakanjeId = $('#addTipNaPlakanje').val();
            const datum = $('#addDatumNaKasovIzvestaj').val();
            const tbody = $('#uplatiPreviewTableBody');

            if (!tipNaPlakanjeId || !datum) {
                tbody.html('<tr><td colspan="11" class="text-center">Изберете тип на плаќање и датум</td></tr>');
                return;
            }

            fetch(`?handler=UplatiPreview&tipNaPlakanjeId=${tipNaPlakanjeId}&datum=${datum}`, {
                headers: {
                    "RequestVerificationToken": document.querySelector('input[name="__RequestVerificationToken"]').value
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.length > 0) {
                    let html = '';
                    let total = 0;
                    data.data.forEach(uplata => {
                        html += `
                            <tr>
                                <td>${uplata.dateCreated ? new Date(uplata.dateCreated).toLocaleString() : ''}</td>
                                <td>${uplata.usernameCreated || ''}</td>
                                <td>${new Date(uplata.uplataDatum).toLocaleDateString()}</td>
                                <td>${uplata.tipNaPlakanje || ''}</td>
                                <td>${uplata.brojIzvod || ''}</td>
                                <td class="text-end">${uplata.iznos.toLocaleString('mk', { minimumFractionDigits: 2 })}</td>
                                <td>${uplata.polisaBroj || ''}</td>
                                <td>${uplata.neraspredelena ? 'Да' : 'Не'}</td>
                                <td>${uplata.povratNaSredstva ? 'Да' : 'Не'}</td>
                                <td>${uplata.kasaPrimi ? 'Да' : 'Не'}</td>
                                <td>${uplata.uplakjacNaziv || ''}</td>
                            </tr>
                        `;
                        total += uplata.iznos;
                    });
                    tbody.html(html);
                    
                    // Update the total
                    document.getElementById('uplatiPreviewTotal').textContent = total.toLocaleString('mk', { minimumFractionDigits: 2 });
                } else {
                    tbody.html('<tr><td colspan="11" class="text-center">Нема пронајдени уплати</td></tr>');
                    // Reset the total when no data
                    document.getElementById('uplatiPreviewTotal').textContent = '0.00';
                }
            });
        }

        // Add this new function to check if row can be closed
        function canCloseKasovIzvestaj(id) {
            const row = $(`#kasovIzvestajTable tr`).filter(function() {
                return $(this).find('td:first').text() == id;
            });
            
            if (row.length === 0) return false;
            
            const iznosNaBankarskiIzvod = row.find('td:eq(9)').text().trim();
            const brojNaIzvod = row.find('td:eq(10)').text().trim();
            const brojStavka = row.find('td:eq(11)').text().trim();
            
            if (!iznosNaBankarskiIzvod || !brojNaIzvod || !brojStavka) {
                alert('Не може да се затвори касов извештај без внес на банкарски извод.');
                return false;
            }
            
            return true;
        }

        // Store reference to original function
        const originalCloseKasovIzvestaj = window.closeKasovIzvestaj;

        // Replace with new function that includes validation
        window.closeKasovIzvestaj = function(id) {
            if (!canCloseKasovIzvestaj(id)) {
                return;
            }
            originalCloseKasovIzvestaj.call(this, id);
        };
    </script>
}