using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Renci.SshNet;
using System.Data;
using System.Text;

namespace NextBroker.Pages.Polisi
{
    public class PolisiFileUploadModel : PageModel
    {
        private readonly IConfiguration _configuration;

        public long PolisaId 
        { 
            get
            {
                if (RouteData?.Values["id"] != null && long.TryParse(RouteData.Values["id"].ToString(), out long id))
                    return id;
                return 0;
            }
        }
        public List<FileInfo> Files { get; set; }

        public class FileInfo
        {
            public long Id { get; set; }
            public string FileName { get; set; }
            public DateTime DateCreated { get; set; }
            public string UsernameCreated { get; set; }
        }

        public PolisiFileUploadModel(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public async Task OnGetAsync()
        {
            await LoadFiles();
        }

        private async Task LoadFiles()
        {
            Files = new List<FileInfo>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand command = new SqlCommand(
                    "SELECT Id, FileName, DateCreated, UsernameCreated " +
                    "FROM PolisiFileSystem " +
                    "WHERE PolisaId = @PolisaId " +
                    "ORDER BY DateCreated DESC", connection))
                {
                    command.Parameters.AddWithValue("@PolisaId", PolisaId);

                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            Files.Add(new FileInfo
                            {
                                Id = reader.GetInt64(0),
                                FileName = reader.GetString(1),
                                DateCreated = reader.GetDateTime(2),
                                UsernameCreated = reader.GetString(3)
                            });
                        }
                    }
                }
            }
        }

        public async Task<IActionResult> OnPostUploadFileAsync(List<IFormFile> files)
        {
            if (files == null || !files.Any())
            {
                TempData["ErrorMessage"] = "Не се избрани документи за прикачување.";
                return RedirectToPage();
            }

            var sftpConfig = _configuration.GetSection("SftpConfig");
            var host = sftpConfig["Host"];
            var port = int.Parse(sftpConfig["Port"]);
            var username = sftpConfig["Username"];
            var password = sftpConfig["Password"];
            string currentUsername = HttpContext.Session.GetString("Username");

            try
            {
                var debugInfo = new StringBuilder();
                debugInfo.AppendLine($"PolisaId: {PolisaId}");
                debugInfo.AppendLine($"Current user: {HttpContext.Session.GetString("Username")}");
                
                using (var client = new SftpClient(host, port, username, password))
                {
                    client.Connect();
                    debugInfo.AppendLine("SFTP Connected successfully");

                    // Use single polisi directory under upload
                    string remotePath = "/upload/polisi";
                    debugInfo.AppendLine($"Using directory: {remotePath}");

                    if (!client.Exists(remotePath))
                    {
                        try
                        {
                            debugInfo.AppendLine("Creating polisi directory");
                            client.CreateDirectory(remotePath);
                        }
                        catch (Exception ex)
                        {
                            TempData["ErrorMessage"] = $"Грешка при креирање на директориумот: {ex.Message}";
                            TempData["DebugInfo"] = debugInfo.ToString();
                            return RedirectToPage();
                        }
                    }

                    string connectionString = _configuration.GetConnectionString("DefaultConnection");
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        await connection.OpenAsync();

                        foreach (var file in files)
                        {
                            if (file.Length > 0)
                            {
                                string fileName = $"{DateTime.Now:yyyyMMddHHmmss}_{file.FileName}";
                                string remoteFilePath = $"{remotePath}/{fileName}";
                                debugInfo.AppendLine($"Uploading file to: {remoteFilePath}");

                                using (var stream = file.OpenReadStream())
                                {
                                    client.UploadFile(stream, remoteFilePath);
                                }

                                using (SqlCommand cmd = new SqlCommand(
                                    "INSERT INTO PolisiFileSystem (PolisaId, FilePath, FileName, DateCreated, UsernameCreated) " +
                                    "VALUES (@PolisaId, @FilePath, @FileName, GETDATE(), @UsernameCreated)", connection))
                                {
                                    cmd.Parameters.AddWithValue("@PolisaId", PolisaId);
                                    cmd.Parameters.AddWithValue("@FilePath", remoteFilePath);
                                    cmd.Parameters.AddWithValue("@FileName", file.FileName);
                                    cmd.Parameters.AddWithValue("@UsernameCreated", currentUsername);
                                    await cmd.ExecuteNonQueryAsync();
                                }
                            }
                        }
                    }

                    client.Disconnect();
                }

                TempData["SuccessMessage"] = "Документите се успешно прикачени.";
                TempData["DebugInfo"] = debugInfo.ToString();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Грешка при прикачување на документите: {ex.Message}";
                TempData["DebugInfo"] = ex.StackTrace;
            }

            return RedirectToPage();
        }

        public async Task<IActionResult> OnGetDownloadFileAsync(long fileId)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            string filePath = null;
            string fileName = null;

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand command = new SqlCommand(
                    "SELECT FilePath, FileName FROM PolisiFileSystem WHERE Id = @FileId", connection))
                {
                    command.Parameters.AddWithValue("@FileId", fileId);
                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            filePath = reader.GetString(0);
                            fileName = reader.GetString(1);
                        }
                    }
                }
            }

            if (string.IsNullOrEmpty(filePath))
            {
                return NotFound();
            }

            var sftpConfig = _configuration.GetSection("SftpConfig");
            using (var client = new SftpClient(
                sftpConfig["Host"],
                int.Parse(sftpConfig["Port"]),
                sftpConfig["Username"],
                sftpConfig["Password"]))
            {
                client.Connect();

                using (var memoryStream = new MemoryStream())
                {
                    client.DownloadFile(filePath, memoryStream);
                    client.Disconnect();
                    memoryStream.Position = 0;
                    return File(memoryStream.ToArray(), "application/octet-stream", fileName);
                }
            }
        }

        public async Task<IActionResult> OnPostDeleteFileAsync(long fileId)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            string filePath = null;

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                
                // First get the file path
                using (SqlCommand command = new SqlCommand(
                    "SELECT FilePath FROM PolisiFileSystem WHERE Id = @FileId", connection))
                {
                    command.Parameters.AddWithValue("@FileId", fileId);
                    filePath = (string)await command.ExecuteScalarAsync();
                }

                if (!string.IsNullOrEmpty(filePath))
                {
                    // Delete from database
                    using (SqlCommand command = new SqlCommand(
                        "DELETE FROM PolisiFileSystem WHERE Id = @FileId", connection))
                    {
                        command.Parameters.AddWithValue("@FileId", fileId);
                        await command.ExecuteNonQueryAsync();
                    }

                    // Delete from SFTP
                    var sftpConfig = _configuration.GetSection("SftpConfig");
                    using (var client = new SftpClient(
                        sftpConfig["Host"],
                        int.Parse(sftpConfig["Port"]),
                        sftpConfig["Username"],
                        sftpConfig["Password"]))
                    {
                        client.Connect();
                        if (client.Exists(filePath))
                        {
                            client.DeleteFile(filePath);
                        }
                        client.Disconnect();
                    }

                    TempData["SuccessMessage"] = "Документот е успешно избришан.";
                }
            }

            return RedirectToPage();
        }
    }
} 