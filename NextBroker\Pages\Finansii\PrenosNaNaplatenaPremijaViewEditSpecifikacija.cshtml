@page
@model NextBroker.Pages.Finansii.PrenosNaNaplatenaPremijaViewEditSpecifikacijaModel
@{
    ViewData["Title"] = "Пренос на наплатена премија - Преглед/Измена на спецификација";
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container-fluid mt-4 px-4">
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Преглед/Измена на спецификација</h5>
                <div>
                    <a asp-page-handler="ExportToExcel" asp-route-id="@Model.Id" class="btn btn-success me-2">
                        <i class="fas fa-file-excel"></i> Експортирај во Excel
                    </a>
                    <button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#povrziStavkaModal">
                        <i class="fas fa-link"></i> Поврзи ставка за одлив
                    </button>
                    <a asp-page="./PrenosNaNaplatenaPremijaListaSpecifikacii" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Назад кон листа
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header">
                            <h6 class="mb-0">Основни информации</h6>
                        </div>
                        <div class="card-body">
                            <div class="row mb-2">
                                <div class="col-sm-4 text-muted">ID:</div>
                                <div class="col-sm-8">@Model.Specifikacija.Id</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4 text-muted">Креирано на:</div>
                                <div class="col-sm-8">@Model.Specifikacija.DateCreated.ToString("dd.MM.yyyy")</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4 text-muted">Креирал:</div>
                                <div class="col-sm-8">@Model.Specifikacija.UsernameCreated</div>
                            </div>
                            @if (Model.Specifikacija.DateModified.HasValue)
                            {
                                <div class="row mb-2">
                                    <div class="col-sm-4 text-muted">Променето на:</div>
                                    <div class="col-sm-8">@Model.Specifikacija.DateModified?.ToString("dd.MM.yyyy")</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-sm-4 text-muted">Променил:</div>
                                    <div class="col-sm-8">@Model.Specifikacija.UsernameModified</div>
                                </div>
                            }
                            <div class="row mb-2">
                                <div class="col-sm-4 text-muted">Назив:</div>
                                <div class="col-sm-8">@Model.Specifikacija.Naziv</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header">
                            <h6 class="mb-0">Финансиски детали</h6>
                        </div>
                        <div class="card-body">
                            <div class="row mb-2">
                                <div class="col-sm-4 text-muted">Број на фактура:</div>
                                <div class="col-sm-8">@Model.Specifikacija.BrojNaFaktura</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4 text-muted">Тип на фактура:</div>
                                <div class="col-sm-8">@Model.Specifikacija.TipNaFaktura</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4 text-muted">Датум на фактура:</div>
                                <div class="col-sm-8">@(Model.Specifikacija.DatumNaVleznaFaktura?.ToString("dd.MM.yyyy"))</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4 text-muted">Рок за плаќање:</div>
                                <div class="col-sm-8">@(Model.Specifikacija.RokNaPlakjanjeFakturaVlezna?.ToString("dd.MM.yyyy"))</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4 text-muted">Износ на фактура:</div>
                                <div class="col-sm-8">@(Model.Specifikacija.IznosNaFaktura?.ToString("N2"))</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4 text-muted">Износ на фактура во рок:</div>
                                <div class="col-sm-8">
                                    @(Model.Specifikacija.IznosNaFakturaVoRok?.ToString("N2"))
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4 text-muted">Платено од договорувач:</div>
                                <div class="col-sm-8">@(Model.Specifikacija.PlatenoOdDogovoruvac?.ToString("N2"))</div>
                            </div>                                 
<!--
                            <div class="row mb-2">
                                <div class="col-sm-4 text-muted">Должи:</div>
                                <div class="col-sm-8 @(Model.Specifikacija.Dolzi > 0 ? "text-danger" : "")">
                                    @(Model.Specifikacija.Dolzi?.ToString("N2"))
                                </div>
                            </div>
-->
                            <div class="row mb-2">
                                <div class="col-sm-4 text-muted">Пренесено кон осигурител:</div>
                                <div class="col-sm-8">@(Model.Specifikacija.PrenesenoKonOsiguritel?.ToString("N2"))</div>
                            </div>
                            
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Информации за извод</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Банка</th>
                                            <th>Број на извод</th>
                                            <th>Датум на извод</th>
                                            <th>Ставка во извод</th>
                                            <th>Износ</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var stavka in Model.StavkiIzvod)
                                        {
                                            <tr>
                                                <td>@stavka.Banka</td>
                                                <td>@stavka.BrojNaIzvod</td>
                                                <td>@(stavka.DatumNaIzvod?.ToString("dd.MM.yyyy"))</td>
                                                <td>@stavka.StavkaPremijaId</td>
                                                <td class="text-end">@stavka.Iznos.ToString("N2")</td>
                                                <td>
                                                    <form method="post" asp-page-handler="OdvrziStavka" style="display: inline;">
                                                        <input type="hidden" name="stavkaPremijaId" value="@stavka.StavkaPremijaId" />
                                                        <input type="hidden" name="specifikacijaId" value="@Model.Id" />
                                                        <button type="submit" class="btn btn-danger btn-sm" style="padding: 0.1rem 0.3rem; font-size: 0.75rem;"
                                                                onclick="return confirm('Дали сте сигурни дека сакате да ја одврзете оваа ставка?')">
                                                            Одврзи ставка
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0">Полиси</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Број на полиса</th>
                            <th>Износ за плакање</th>
                            <th>Платено од договорувач</th>
                            <th>Износ за плакање во рок</th>
                            <th>Износ Одобрување/Задолжување</th>
                            <th>Број на документ ОЗ</th>
                            <th>Должи договорувач</th>
                            <th>Платено по фактура</th>
                            <th>Класа на осигурување</th>
                            <th>Производ</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var polisa in Model.Polisi)
                        {
                            <tr>
                                <td>@polisa.Id</td>
                                <td>@polisa.BrojNaPolisa</td>
                                <td>@polisa.VkupnaPremijaZaPlakanje</td>
                                <td>@polisa.PlatenoOdDogovoruvac</td>
                                <td>@polisa.IznosZaPlakanjeVoRok</td>
                                <td>@polisa.IznosOdobruvanjeZadolzuvanje</td>
                                <td>@polisa.BrojNaDokumentOdobruvanjeZadolzuvanje</td>
                                <td>@polisa.DolziDogovoruvac</td>
                                <td>@polisa.PlatenoPoFaktura</td>
                                <td>@polisa.KlasaIme</td>
                                <td>@polisa.Ime</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="povrziStavkaModal" tabindex="-1" aria-labelledby="povrziStavkaModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" asp-page-handler="PovrziStavka">
                <div class="modal-header">
                    <h5 class="modal-title" id="povrziStavkaModalLabel">Поврзи ставка за одлив</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="specifikacijaId" value="@Model.Id" />
                    <div class="mb-3">
                        <label for="brojNaIzvod" class="form-label">Број на извод</label>
                        <input type="text" class="form-control" id="brojNaIzvod" name="brojNaIzvod" autocomplete="off">
                        <div id="izvodSearchResults" class="search-results mt-2" style="max-height: 200px; overflow-y: auto; display: none;">
                            <table class="table table-sm table-hover">
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="stavkaVoIzvod" class="form-label">Број на Ставка во извод</label>
                        <input type="hidden" id="stavkaVoIzvod" name="stavkaVoIzvod" required>
                        <div id="stavkeList" class="mt-2">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Износ</th>
                                        <th>Цел на дознака</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Откажи</button>
                    <button type="submit" class="btn btn-primary">Зачувај</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let searchTimeout;
        const brojNaIzvodInput = document.getElementById('brojNaIzvod');
        const izvodSearchResults = document.getElementById('izvodSearchResults');
        const stavkeList = document.getElementById('stavkeList');
        const stavkaVoIzvodInput = document.getElementById('stavkaVoIzvod');

        // Handle izvod search
        brojNaIzvodInput.addEventListener('input', function(e) {
            clearTimeout(searchTimeout);
            const searchTerm = e.target.value.trim();
            
            if (searchTerm.length >= 1) {
                searchTimeout = setTimeout(() => {
                    fetch(`?handler=SearchIzvod&searchTerm=${encodeURIComponent(searchTerm)}`)
                        .then(response => response.json())
                        .then(data => {
                            const tbody = izvodSearchResults.querySelector('tbody');
                            tbody.innerHTML = '';
                            
                            data.forEach(item => {
                                const tr = document.createElement('tr');
                                tr.innerHTML = `
                                    <td>
                                        <a href="#" class="izvod-item" data-id="${item.id}">
                                            ${item.brojNaIzvod} (${item.datumNaIzvod})
                                        </a>
                                    </td>
                                `;
                                tbody.appendChild(tr);
                            });
                            
                            izvodSearchResults.style.display = data.length > 0 ? 'block' : 'none';
                        });
                }, 300);
            } else {
                izvodSearchResults.style.display = 'none';
            }
        });

        // Handle izvod selection
        izvodSearchResults.addEventListener('click', function(e) {
            if (e.target.classList.contains('izvod-item') || e.target.closest('.izvod-item')) {
                e.preventDefault();
                const link = e.target.classList.contains('izvod-item') ? e.target : e.target.closest('.izvod-item');
                const izvodId = link.dataset.id;
                const izvodText = link.textContent.trim();
                
                brojNaIzvodInput.value = izvodText;
                izvodSearchResults.style.display = 'none';
                
                // Load stavke for selected izvod
                fetch(`?handler=SearchStavke&izvodId=${izvodId}`)
                    .then(response => response.json())
                    .then(data => {
                        const tbody = stavkeList.querySelector('tbody');
                        tbody.innerHTML = '';
                        
                        data.forEach(item => {
                            const tr = document.createElement('tr');
                            tr.innerHTML = `
                                <td><a href="#" class="stavka-item" data-id="${item.id}">${item.id}</a></td>
                                <td>${item.iznos.toLocaleString('mk-MK', { minimumFractionDigits: 2 })}</td>
                                <td>${item.celNaDoznaka}</td>
                            `;
                            tbody.appendChild(tr);
                        });
                    });
            }
        });

        // Handle stavka selection
        stavkeList.addEventListener('click', function(e) {
            if (e.target.classList.contains('stavka-item') || e.target.closest('.stavka-item')) {
                e.preventDefault();
                const link = e.target.classList.contains('stavka-item') ? e.target : e.target.closest('.stavka-item');
                const stavkaId = link.dataset.id;
                
                // Remove previous selection
                stavkeList.querySelectorAll('tr.selected').forEach(tr => tr.classList.remove('selected'));
                // Add selection to current row
                link.closest('tr').classList.add('selected');
                
                stavkaVoIzvodInput.value = stavkaId;
            }
        });

        // Add anti-forgery token to fetch requests
        document.addEventListener('DOMContentLoaded', function() {
            const tokenElement = document.querySelector('input[name="__RequestVerificationToken"]');
            if (tokenElement) {
                const token = tokenElement.value;
                const originalFetch = window.fetch;
                window.fetch = function(url, options = {}) {
                    if (url.includes('handler=')) {
                        options.headers = {
                            ...options.headers,
                            'RequestVerificationToken': token
                        };
                    }
                    return originalFetch(url, options);
                };
            }
        });
    </script>

    <style>
        .search-results {
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
            background-color: white;
            z-index: 1000;
        }
        
        .search-results .table {
            margin-bottom: 0;
        }
        
        .search-results td {
            padding: 0.5rem;
        }
        
        .search-results a {
            display: block;
            text-decoration: none;
            color: inherit;
        }
        
        .search-results tr:hover {
            background-color: #f8f9fa;
        }
        
        #stavkeList tr.selected {
            background-color: #e2e6ea;
        }
    </style>
}
