@page
@model NextBroker.Pages.AdministrationPages.OpstiniMkModel
@{
    ViewData["Title"] = "Листа на општини";
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container">
    <div class="mb-3">
        <form method="post" asp-page-handler="ToggleEdit" class="d-flex gap-2">
            <button type="submit" class="btn btn-primary" onclick="return confirmCancel()">
                @(Model.IsEditing ? "Откажи" : "Промени ги податоците")
            </button>
            @if (Model.IsEditing)
            {
                <button type="button" class="btn btn-success" id="saveChanges">Зачувај промени</button>
            }
            <button type="button" class="btn btn-primary" onclick="toggleAddForm()">Додади општина</button>
        </form>
    </div>

    <div id="addForm" style="display: none;" class="mb-3">
        <div class="card">
            <div class="card-body">
                <form method="post" asp-page-handler="AddOpstina">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Општина</label>
                            <input type="text" class="form-control" name="opstina" required />
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Код</label>
                            <input type="number" class="form-control" name="kod" required />
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="submit" class="btn btn-success">Додади</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="mb-3">
        <input type="text" id="searchBox" class="form-control" placeholder="Пребарувај..." />
    </div>

    <div class="table-responsive">
        <table class="table table-striped" id="opstiniTable">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Датум на креирање</th>
                    <th>Датум на промена</th>
                    <th>Општина</th>
                    <th>Код</th>
                    @if (Model.IsEditing)
                    {
                        <th>Акции</th>
                    }
                </tr>
            </thead>
            <tbody>
                @foreach (var opstina in Model.OpstiniList)
                {
                    <tr data-id="@opstina.Id">
                        <td>@opstina.Id</td>
                        <td>@(opstina.DateCreated == DateTime.MinValue ? "" : opstina.DateCreated.ToString("dd.MM.yyyy HH:mm:ss"))</td>
                        <td>@(opstina.DateModified == DateTime.MinValue ? "" : opstina.DateModified.ToString("dd.MM.yyyy HH:mm:ss"))</td>
                        <td>
                            @if (Model.IsEditing)
                            {
                                <input type="text" class="form-control opstina-edit" value="@opstina.Opstina" />
                            }
                            else
                            {
                                @opstina.Opstina
                            }
                        </td>
                        <td>
                            @if (Model.IsEditing)
                            {
                                <input type="number" class="form-control kod-edit" value="@opstina.Kod" />
                            }
                            else
                            {
                                @opstina.Kod
                            }
                        </td>
                        @if (Model.IsEditing)
                        {
                            <td>
                                <button type="button" class="btn btn-danger btn-sm delete-opstina" onclick="deleteOpstina(@opstina.Id)">Избриши</button>
                            </td>
                        }
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            var table = $('#opstiniTable').DataTable({
                "dom": "t",
                "language": {
                    "emptyTable": "Нема пронајдени записи",
                    "zeroRecords": "Нема пронајдени записи"
                },
                "ordering": false,
                "paging": false,
                "pageLength": -1
            });

            $('#searchBox').on('keyup', function() {
                table.search(this.value).draw();
            });
        });

        function toggleAddForm() {
            const form = document.getElementById('addForm');
            if (form.style.display === 'none' || form.style.display === '') {
                form.style.display = 'block';
            } else {
                form.style.display = 'none';
            }
        }
    </script>

    @if (Model.IsEditing)
    {
        <script>
            function confirmCancel() {
                if (@Json.Serialize(Model.IsEditing)) {
                    window.location.href = '/AdministrationPages/OpstiniMk';
                }
                return false;
            }

            function deleteOpstina(id) {
                if (confirm('Дали сте сигурни дека сакате да ја избришете оваа општина?')) {
                    fetch('?handler=DeleteOpstina', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                        },
                        body: JSON.stringify({ id: id })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        }
                    })
                    .catch(error => console.error('Error:', error));
                }
            }

            document.getElementById('saveChanges').addEventListener('click', function() {
                const rows = document.querySelectorAll('#opstiniTable tbody tr');
                const updates = [];

                rows.forEach(row => {
                    const id = row.getAttribute('data-id');
                    const opstina = row.querySelector('.opstina-edit').value;
                    const kod = row.querySelector('.kod-edit').value;

                    updates.push({
                        id: parseInt(id),
                        opstina: opstina,
                        kod: parseInt(kod)
                    });
                });

                fetch('?handler=SaveChanges', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify(updates)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = window.location.pathname;
                    }
                })
                .catch(error => console.error('Error:', error));
            });
        </script>
    }
} 