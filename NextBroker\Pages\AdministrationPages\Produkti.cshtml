@page
@model NextBroker.Pages.AdministrationPages.ProduktiModel
@{
    ViewData["Title"] = "Продукти";
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container">
    <div class="mb-3">
        <form method="post" asp-page-handler="ToggleEdit" class="d-flex gap-2">
            <button type="submit" class="btn btn-primary" onclick="return confirmCancel()">
                @(Model.IsEditing ? "Откажи" : "Промени ги податоците")
            </button>
            @if (Model.IsEditing)
            {
                <button type="button" class="btn btn-success" id="saveChanges">Зачувај промени</button>
            }
            <button type="button" class="btn btn-primary" onclick="toggleAddForm()">Додади продукт</button>
        </form>
    </div>

    <div id="addForm" style="display: none;" class="mb-3">
        <div class="card">
            <div class="card-body">
                <form method="post" asp-page-handler="AddProdukt">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Име</label>
                            <input type="text" class="form-control" name="ime" required />
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Категорија</label>
                            <input type="text" class="form-control" name="kategorija" required />
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="submit" class="btn btn-success">Додади</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="mb-3">
        <input type="text" id="searchBox" class="form-control" placeholder="Пребарувај..." />
    </div>

    <div class="table-responsive">
        <table class="table table-striped" id="produktiTable">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Датум на креирање</th>
                    <th>Име</th>
                    <th>Категорија</th>
                    @if (Model.IsEditing)
                    {
                        <th>Акции</th>
                    }
                </tr>
            </thead>
            <tbody>
                @foreach (var produkt in Model.ProduktiList)
                {
                    <tr data-id="@produkt.Id">
                        <td>@produkt.Id</td>
                        <td>@(produkt.DateCreated == DateTime.MinValue ? "" : produkt.DateCreated.ToString("dd.MM.yyyy HH:mm:ss"))</td>
                        <td>
                            @if (Model.IsEditing)
                            {
                                <input type="text" class="form-control ime-edit" value="@produkt.Ime" />
                            }
                            else
                            {
                                @produkt.Ime
                            }
                        </td>
                        <td>
                            @if (Model.IsEditing)
                            {
                                <input type="text" class="form-control kategorija-edit" value="@produkt.Kategorija" />
                            }
                            else
                            {
                                @produkt.Kategorija
                            }
                        </td>
                        @if (Model.IsEditing)
                        {
                            <td>
                                <button type="button" class="btn btn-danger btn-sm delete-produkt" onclick="deleteProdukt(@produkt.Id)">Избриши</button>
                            </td>
                        }
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            var table = $('#produktiTable').DataTable({
                "dom": "t",
                "language": {
                    "emptyTable": "Нема пронајдени записи",
                    "zeroRecords": "Нема пронајдени записи"
                },
                "ordering": false,
                "paging": false,
                "pageLength": -1
            });

            $('#searchBox').on('keyup', function() {
                table.search(this.value).draw();
            });
        });

        function toggleAddForm() {
            const form = document.getElementById('addForm');
            if (form.style.display === 'none' || form.style.display === '') {
                form.style.display = 'block';
            } else {
                form.style.display = 'none';
            }
        }

        function confirmCancel() {
            if (@Json.Serialize(Model.IsEditing)) {
                return confirm("Дали сте сигурни дека сакате да го откажете уредувањето? Сите незачувани промени ќе бидат изгубени.");
            }
            return true;
        }

        function deleteProdukt(id) {
            if (confirm('Дали сте сигурни дека сакате да го избришете овој продукт?')) {
                fetch('?handler=DeleteProdukt', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    }
                })
                .catch(error => console.error('Error:', error));
            }
        }

        @if (Model.IsEditing)
        {
            <text>
            document.getElementById('saveChanges').addEventListener('click', function() {
                const rows = document.querySelectorAll('#produktiTable tbody tr');
                const updates = [];

                rows.forEach(row => {
                    const id = row.getAttribute('data-id');
                    const ime = row.querySelector('.ime-edit').value;
                    const kategorija = row.querySelector('.kategorija-edit').value;

                    updates.push({
                        id: parseInt(id),
                        ime: ime,
                        kategorija: kategorija
                    });
                });

                fetch('?handler=SaveChanges', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify(updates)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = window.location.pathname;
                    }
                })
                .catch(error => console.error('Error:', error));
            });
            </text>
        }
    </script>
} 