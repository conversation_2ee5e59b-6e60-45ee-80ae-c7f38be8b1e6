@page
@model NextBroker.Pages.AnalizaNaKlient.RiziciModel
@{
    ViewData["Title"] = "Анализа на клиент - Управување со ризици и правно-економски односи";
}

<!-- Add Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <!-- Main Accordion -->
            <div class="accordion" id="mainAccordion">
                <!-- Rizici Section -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingRizici">
                        <button class="accordion-button collapsed fs-4 fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseRizici" aria-expanded="false" aria-controls="collapseRizici" style="background-color: #f8f9fa;">
                            Ризици
                        </button>
                    </h2>
                    <div id="collapseRizici" class="accordion-collapse collapse" aria-labelledby="headingRizici" data-bs-parent="#mainAccordion">
                        <div class="accordion-body p-4">
                            <!-- Nested Accordion for Rizici -->
                            <div class="accordion" id="riziciAccordion">
                                <!-- Dodaj nov rizik section -->
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingDodajRizik">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseDodajRizik" aria-expanded="false" aria-controls="collapseDodajRizik">
                                            Додај нов ризик
                                        </button>
                                    </h2>
                                    <div id="collapseDodajRizik" class="accordion-collapse collapse" aria-labelledby="headingDodajRizik" data-bs-parent="#riziciAccordion">
                                        <div class="accordion-body">
                                            @Html.AntiForgeryToken()
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="newRizikNaziv">Назив на ризик</label>
                                                        <input type="text" class="form-control" id="newRizikNaziv" placeholder="Внесете назив на ризик">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="newRizikTip">Тип на ризик</label>
                                                        <select class="form-control" id="newRizikTip" required>
                                                            <option value="">-- Изберете тип --</option>
                                                            <option value="Ризик">Ризик</option>
                                                            <option value="Дополнителен ризик">Дополнителен ризик</option>
                                                            <option value="Исклучок">Исклучок</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="newRizikKlasa">Класа на осигурување</label>
                                                        <select class="form-control" id="newRizikKlasa" required>
                                                            <option value="">-- Изберете класа --</option>
                                                            @foreach (var klasa in Model.KlasiOsiguruvanje)
                                                            {
                                                                <option value="@klasa.Value">@klasa.Text</option>
                                                            }
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row mt-3">
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="newRizikProdukt">Продукт</label>
                                                        <select class="form-control" id="newRizikProdukt" required disabled>
                                                            <option value="">-- Изберете продукт --</option>
                                                            @foreach (var produkt in Model.Produkti)
                                                            {
                                                                <option value="@produkt.Value" data-klasa="">@produkt.Text</option>
                                                            }
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row mt-3">
                                                <div class="col-12">
                                                    <button type="button" class="btn btn-primary" id="saveNewRizik">Зачувај</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Lista rizici section -->
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingListaRizici">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseListaRizici" aria-expanded="false" aria-controls="collapseListaRizici">
                                            Листа ризици
                                        </button>
                                    </h2>
                                    <div id="collapseListaRizici" class="accordion-collapse collapse" aria-labelledby="headingListaRizici" data-bs-parent="#riziciAccordion">
                                        <div class="accordion-body">
                                            <div class="card">
                                                <div class="card-body">
                                                    <div class="row mb-3">
                                                        <div class="col-md-4">
                                                            <div class="form-group">
                                                                <label for="klasaFilterRizici">Филтрирај по класа</label>
                                                                <select class="form-control" id="klasaFilterRizici">
                                                                    <option value="">-- Сите класи --</option>
                                                                    @foreach (var klasa in Model.KlasiOsiguruvanje)
                                                                    {
                                                                        <option value="@klasa.Value">@klasa.Text</option>
                                                                    }
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="form-group">
                                                                <label for="produktFilterRizici">Филтрирај по продукт</label>
                                                                <select class="form-control" id="produktFilterRizici">
                                                                    <option value="">-- Сите продукти --</option>
                                                                    @foreach (var produkt in Model.Produkti)
                                                                    {
                                                                        <option value="@produkt.Value">@produkt.Text</option>
                                                                    }
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="form-group">
                                                                <label for="searchBox">Пребарувај</label>
                                                                <input type="text" id="searchBox" class="form-control" placeholder="Пребарувај..." />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="table-responsive">
                                                        <table id="riziciTable" class="table table-striped table-hover">
                                                            <thead>
                                                                <tr>
                                                                    <th>ID</th>
                                                                    <th>Назив</th>
                                                                    <th>Тип</th>
                                                                    <th>Класа</th>
                                                                    <th>Продукт</th>
                                                                    <th>Акции</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                @foreach (var rizik in Model.ListaRizici)
                                                                {
                                                                    <tr>
                                                                        <td>@rizik.Id</td>
                                                                        <td>@rizik.Naziv</td>
                                                                        <td>@rizik.Tip</td>
                                                                        <td data-klasa-id="@rizik.KlasiOsiguruvanjeIdRizik">@rizik.KlasaIme</td>
                                                                        <td>@rizik.ProduktIme</td>
                                                                        <td class="actions-column">
                                                                            <button class="btn btn-sm btn-light-primary edit-rizik" data-id="@rizik.Id">
                                                                                <i class="fas fa-edit"></i>
                                                                            </button>
                                                                            <button class="btn btn-sm btn-light-danger delete-rizik" data-id="@rizik.Id">
                                                                                <i class="fas fa-trash"></i>
                                                                            </button>
                                                                        </td>
                                                                    </tr>
                                                                }
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pravno Ekonomski Section -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingPravnoEkonomski">
                        <button class="accordion-button collapsed fs-4 fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#collapsePravnoEkonomskiMain" aria-expanded="false" aria-controls="collapsePravnoEkonomskiMain" style="background-color: #f8f9fa;">
                            Правно-економски односи
                        </button>
                    </h2>
                    <div id="collapsePravnoEkonomskiMain" class="accordion-collapse collapse" aria-labelledby="headingPravnoEkonomski" data-bs-parent="#mainAccordion">
                        <div class="accordion-body p-4">
                            <!-- Nested Accordion for Pravno Ekonomski -->
                            <div class="accordion" id="pravnoEkonomskiAccordion">
                                <!-- Dodaj Pravno Ekonomski section -->
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingDodajPravnoEkonomski">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseDodajPravnoEkonomski" aria-expanded="false" aria-controls="collapseDodajPravnoEkonomski">
                                            Додај нов правно-економски однос
                                        </button>
                                    </h2>
                                    <div id="collapseDodajPravnoEkonomski" class="accordion-collapse collapse" aria-labelledby="headingDodajPravnoEkonomski" data-bs-parent="#pravnoEkonomskiAccordion">
                                        <div class="accordion-body">
                                            <div class="card">
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-12">
                                                            <div class="form-group">
                                                                <label>Изберете осигурител</label>
                                                                <select class="form-control form-control-sm select2-osiguritel" id="osiguritelSelect">
                                                                    <option value="">-- Изберете осигурител --</option>
                                                                    @foreach (var osiguritel in Model.Osiguriteli)
                                                                    {
                                                                        <option value="@osiguritel.Value">@osiguritel.Text</option>
                                                                    }
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row mt-3">
                                                        <div class="col-md-12">
                                                            <div class="form-group">
                                                                <label>Податок за правно-економски односи</label>
                                                                <textarea class="form-control" id="pravnoEkonomskiText" rows="3"></textarea>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row mt-3">
                                                        <div class="col-md-12">
                                                            <button type="button" class="btn btn-primary" id="savePravnoEkonomski">Зачувај</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Lista Pravno Ekonomski section -->
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingListaPravnoEkonomski">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseListaPravnoEkonomski" aria-expanded="false" aria-controls="collapseListaPravnoEkonomski">
                                            Листа правно-економски односи
                                        </button>
                                    </h2>
                                    <div id="collapseListaPravnoEkonomski" class="accordion-collapse collapse" aria-labelledby="headingListaPravnoEkonomski" data-bs-parent="#pravnoEkonomskiAccordion">
                                        <div class="accordion-body">
                                            <div class="card">
                                                <div class="card-body">
                                                    <div class="mb-3">
                                                        <input type="text" id="searchBoxPravnoEkonomski" class="form-control" placeholder="Пребарувај..." />
                                                    </div>
                                                    <div class="table-responsive">
                                                        <table id="pravnoEkonomskiTable" class="table table-striped table-hover">
                                                            <thead>
                                                                <tr>
                                                                    <th>ID</th>
                                                                    <th>Осигурител</th>
                                                                    <th>Правно-економски односи</th>
                                                                    <th>Акции</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                @foreach (var item in Model.ListaPravnoEkonomskiOdnosi)
                                                                {
                                                                    <tr>
                                                                        <td>@item.Id</td>
                                                                        <td>@item.OsiguritelNaziv</td>
                                                                        <td>@item.PravnoEkonomskiOdnosi</td>
                                                                        <td class="actions-column">
                                                                            <button class="btn btn-sm btn-light-primary edit-pravno-ekonomski" data-id="@item.Id">
                                                                                <i class="fas fa-edit"></i>
                                                                            </button>
                                                                            <button class="btn btn-sm btn-light-danger delete-pravno-ekonomski" data-id="@item.Id">
                                                                                <i class="fas fa-trash"></i>
                                                                            </button>
                                                                        </td>
                                                                    </tr>
                                                                }
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Rizici po Osiguritel Section -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingRiziciPoOsiguritel">
                        <button class="accordion-button collapsed fs-4 fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseRiziciPoOsiguritel" aria-expanded="false" aria-controls="collapseRiziciPoOsiguritel" style="background-color: #f8f9fa;">
                            Едитирај ризици по осигурител
                        </button>
                    </h2>
                    <div id="collapseRiziciPoOsiguritel" class="accordion-collapse collapse" aria-labelledby="headingRiziciPoOsiguritel" data-bs-parent="#mainAccordion">
                        <div class="accordion-body p-4">
                            <!-- Nested Accordion for Rizici po Osiguritel -->
                            <div class="accordion" id="riziciPoOsiguritelAccordion">
                                <!-- Dodaj Rizik po Osiguritel section -->
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingDodajRizikPoOsiguritel">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseDodajRizikPoOsiguritel" aria-expanded="false" aria-controls="collapseDodajRizikPoOsiguritel">
                                            Додај ризик по осигурител
                                        </button>
                                    </h2>
                                    <div id="collapseDodajRizikPoOsiguritel" class="accordion-collapse collapse" aria-labelledby="headingDodajRizikPoOsiguritel" data-bs-parent="#riziciPoOsiguritelAccordion">
                                        <div class="accordion-body">
                                            @Html.AntiForgeryToken()
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="form-control">
                                                        <label for="newRizikPoOsiguritelOsiguritel">Осигурител</label>
                                                        <select class="form-control form-control-sm select2-osiguritel" id="newRizikPoOsiguritelOsiguritel" required>
                                                            <option value="">-- Изберете осигурител --</option>
                                                            @foreach (var osiguritel in Model.Osiguriteli)
                                                            {
                                                                <option value="@osiguritel.Value">@osiguritel.Text</option>
                                                            }
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="newRizikPoOsiguritelRizik">Ризик</label>
                                                        <select class="form-control" id="newRizikPoOsiguritelRizik" required>
                                                            <option value="">-- Изберете ризик --</option>
                                                            @foreach (var rizik in Model.ListaRizici)
                                                            {
                                                                <option value="@rizik.Id">@rizik.Naziv</option>
                                                            }
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="newRizikPoOsiguritelPokrieno">Покриено</label>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="newRizikPoOsiguritelPokrieno">
                                                            <label class="form-check-label" for="newRizikPoOsiguritelPokrieno">
                                                                Да
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row mt-3">
                                                <div class="col-md-12">
                                                    <div class="form-group">
                                                        <label for="newRizikPoOsiguritelBeleshka">Белешка</label>
                                                        <textarea class="form-control" id="newRizikPoOsiguritelBeleshka" rows="3"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row mt-3">
                                                <div class="col-12">
                                                    <button type="button" class="btn btn-primary" id="saveNewRizikPoOsiguritel">Зачувај</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Lista Rizici po Osiguritel section -->
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingListaRiziciPoOsiguritel">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseListaRiziciPoOsiguritel" aria-expanded="false" aria-controls="collapseListaRiziciPoOsiguritel">
                                            Листа ризици по осигурител
                                        </button>
                                    </h2>
                                    <div id="collapseListaRiziciPoOsiguritel" class="accordion-collapse collapse" aria-labelledby="headingListaRiziciPoOsiguritel" data-bs-parent="#riziciPoOsiguritelAccordion">
                                        <div class="accordion-body">
                                            <div class="row mb-3">
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label for="klasaFilterRiziciPoOsiguritel">Филтрирај по класа</label>
                                                        <select class="form-control" id="klasaFilterRiziciPoOsiguritel">
                                                            <option value="">-- Сите класи --</option>
                                                            @foreach (var klasa in Model.KlasiOsiguruvanje)
                                                            {
                                                                <option value="@klasa.Value">@klasa.Text</option>
                                                            }
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label for="produktFilterRiziciPoOsiguritel">Филтрирај по продукт</label>
                                                        <select class="form-control" id="produktFilterRiziciPoOsiguritel">
                                                            <option value="">-- Сите продукти --</option>
                                                            @foreach (var produkt in Model.Produkti)
                                                            {
                                                                <option value="@produkt.Value">@produkt.Text</option>
                                                            }
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label for="osiguritelFilterRiziciPoOsiguritel">Филтрирај по осигурител</label>
                                                        <select class="form-control select2-osiguritel" id="osiguritelFilterRiziciPoOsiguritel">
                                                            <option value="">-- Сите осигурители --</option>
                                                            @foreach (var osiguritel in Model.Osiguriteli)
                                                            {
                                                                <option value="@osiguritel.Value">@osiguritel.Text</option>
                                                            }
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label for="searchBoxRiziciPoOsiguritel">Пребарувај</label>
                                                        <input type="text" id="searchBoxRiziciPoOsiguritel" class="form-control" placeholder="Пребарувај..." />
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="table-responsive">
                                                <table id="riziciPoOsiguritelTable" class="table table-striped table-hover">
                                                    <thead>
                                                        <tr>
                                                            <th>ID</th>
                                                            <th>Осигурител</th>
                                                            <th>Ризик</th>
                                                            <th>Класа</th>
                                                            <th>Продукт</th>
                                                            <th>Покриено</th>
                                                            <th>Белешка</th>
                                                            <th>Акции</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach (var item in Model.ListaRiziciPoOsiguritel)
                                                        {
                                                            <tr>
                                                                <td>@item.Id</td>
                                                                <td>@item.OsiguritelNaziv</td>
                                                                <td>@item.RizikNaziv</td>
                                                                <td>@item.KlasaIme</td>
                                                                <td>@item.ProduktIme</td>
                                                                <td>
                                                                    @if (item.Pokrieno)
                                                                    {
                                                                        <span class="badge bg-success">Да</span>
                                                                    }
                                                                    else
                                                                    {
                                                                        <span class="badge bg-danger">Не</span>
                                                                    }
                                                                </td>
                                                                <td>@item.Beleshka</td>
                                                                <td class="actions-column">
                                                                    <button class="btn btn-sm btn-light-primary edit-rizik-po-osiguritel" data-id="@item.Id">
                                                                        <i class="fas fa-edit"></i>
                                                                    </button>
                                                                    <button class="btn btn-sm btn-light-danger delete-rizik-po-osiguritel" data-id="@item.Id">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                </td>
                                                            </tr>
                                                        }
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Edit Risk Modal -->
<div class="modal fade" id="editRizikModal" tabindex="-1" aria-labelledby="editRizikModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editRizikModalLabel">Измени ризик</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editRizikForm">
                    <input type="hidden" id="editRizikId">
                    <div class="mb-3">
                        <label for="editRizikNaziv" class="form-label">Назив на ризик</label>
                        <input type="text" class="form-control" id="editRizikNaziv" required>
                    </div>
                    <div class="mb-3">
                        <label for="editRizikTip" class="form-label">Тип на ризик</label>
                        <select class="form-control" id="editRizikTip" required>
                            <option value="">-- Изберете тип --</option>
                            <option value="Ризик">Ризик</option>
                            <option value="Дополнителен ризик">Дополнителен ризик</option>
                            <option value="Исклучок">Исклучок</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editRizikKlasa" class="form-label">Класа на осигурување</label>
                        <select class="form-control" id="editRizikKlasa" required>
                            <option value="">-- Изберете класа --</option>
                            @foreach (var klasa in Model.KlasiOsiguruvanje)
                            {
                                <option value="@klasa.Value">@klasa.Text</option>
                            }
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editRizikProdukt" class="form-label">Продукт</label>
                        <select class="form-control" id="editRizikProdukt" required>
                            <option value="">-- Изберете продукт --</option>
                            @foreach (var produkt in Model.Produkti)
                            {
                                <option value="@produkt.Value">@produkt.Text</option>
                            }
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Откажи</button>
                <button type="button" class="btn btn-primary" id="saveEditRizik">Зачувај</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Pravno Ekonomski Odnosi Modal -->
<div class="modal fade" id="editPravnoEkonomskiModal" tabindex="-1" aria-labelledby="editPravnoEkonomskiModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPravnoEkonomskiModalLabel">Измени правно-економски односи</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editPravnoEkonomskiForm">
                    <input type="hidden" id="editPravnoEkonomskiId">
                    <div class="mb-3">
                        <label for="editPravnoEkonomskiOsiguritel" class="form-label">Осигурител</label>
                        <select class="form-control form-control-sm select2-osiguritel" id="editPravnoEkonomskiOsiguritel" required>
                            <option value="">-- Изберете осигурител --</option>
                            @foreach (var osiguritel in Model.Osiguriteli)
                            {
                                <option value="@osiguritel.Value">@osiguritel.Text</option>
                            }
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editPravnoEkonomskiText" class="form-label">Правно-економски односи</label>
                        <textarea class="form-control" id="editPravnoEkonomskiText" rows="3" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Откажи</button>
                <button type="button" class="btn btn-primary" id="saveEditPravnoEkonomski">Зачувај</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Rizik Po Osiguritel Modal -->
<div class="modal fade" id="editRizikPoOsiguritelModal" tabindex="-1" aria-labelledby="editRizikPoOsiguritelModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editRizikPoOsiguritelModalLabel">Измени ризик по осигурител</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editRizikPoOsiguritelForm">
                    <input type="hidden" id="editRizikPoOsiguritelId">
                    <div class="mb-3">
                        <label for="editRizikPoOsiguritelOsiguritel" class="form-label">Осигурител</label>
                        <select class="form-control form-control-sm select2-osiguritel" id="editRizikPoOsiguritelOsiguritel" required>
                            <option value="">-- Изберете осигурител --</option>
                            @foreach (var osiguritel in Model.Osiguriteli)
                            {
                                <option value="@osiguritel.Value">@osiguritel.Text</option>
                            }
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editRizikPoOsiguritelRizik" class="form-label">Ризик</label>
                        <select class="form-control" id="editRizikPoOsiguritelRizik" required>
                            <option value="">-- Изберете ризик --</option>
                            @foreach (var rizik in Model.ListaRizici)
                            {
                                <option value="@rizik.Id">@rizik.Naziv</option>
                            }
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editRizikPoOsiguritelPokrieno">
                            <label class="form-check-label" for="editRizikPoOsiguritelPokrieno">
                                Покриено
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editRizikPoOsiguritelBeleshka" class="form-label">Белешка</label>
                        <textarea class="form-control" id="editRizikPoOsiguritelBeleshka" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Откажи</button>
                <button type="button" class="btn btn-primary" id="saveEditRizikPoOsiguritel">Зачувај</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <!-- Add Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" />
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css" />
    <style>
        /* Main accordion styling */
        .accordion-button.fs-4 {
            padding: 1.5rem;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa !important;
            transition: all 0.3s ease;
            color: inherit !important;
        }

        .accordion-button:not(.collapsed) {
            color: inherit !important;
            background-color: #f1f3f5 !important;
            box-shadow: none;
            border-bottom: 1px solid #eee;
        }

        .accordion-button.collapsed {
            background-color: #f8f9fa;
            transition: all 0.3s ease;
            color: inherit !important;
        }

        /* Add this to ensure the title color is consistent */
        h1, .accordion-button.fs-4 {
            color: #333;
        }

        /* Nested accordion styling */
        #riziciAccordion .accordion-button,
        #pravnoEkonomskiAccordion .accordion-button,
        #riziciPoOsiguritelAccordion .accordion-button {
            background-color: #ffffff;
            padding: 1rem;
            color: #555;
            transition: all 0.3s ease;
        }

        #riziciAccordion .accordion-button:not(.collapsed),
        #pravnoEkonomskiAccordion .accordion-button:not(.collapsed),
        #riziciPoOsiguritelAccordion .accordion-button:not(.collapsed) {
            color: #333;
            background-color: #f8f9fa !important;
            font-weight: 500;
        }

        #riziciAccordion .accordion-button:hover,
        #pravnoEkonomskiAccordion .accordion-button:hover,
        #riziciPoOsiguritelAccordion .accordion-button:hover {
            background-color: #f1f3f5;
        }

        /* Main section hover effects */
        .accordion-button.fs-4:hover {
            background-color: #f1f3f5 !important;
        }

        /* Card styling */
        .card {
            border: none;
            box-shadow: none;
            background-color: transparent;
        }

        .card-body {
            padding: 1.25rem 0;
        }

        .accordion-item {
            border: 1px solid #e9ecef;
            margin-bottom: 0.5rem;
            border-radius: 6px !important;
            overflow: hidden;
            background-color: #ffffff;
        }

        .accordion-item:last-of-type {
            margin-bottom: 0;
        }

        /* Table styling */
        .table {
            margin-bottom: 0;
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table th {
            white-space: nowrap;
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            color: #333;
        }

        .table td {
            vertical-align: middle;
        }

        /* Form controls */
        .form-control {
            border-radius: 4px;
            border: 1px solid #dee2e6;
            padding: 0.5rem 0.75rem;
            transition: border-color 0.15s ease-in-out;
        }

        .form-control:focus {
            border-color: #ced4da;
            box-shadow: 0 0 0 0.2rem rgba(206, 212, 218, 0.25);
        }

        /* Button styling */
        .btn-primary {
            background-color: #5c6ac4;
            border-color: #5c6ac4;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: #4d5ab3;
            border-color: #4d5ab3;
        }

        .btn-light-primary {
            background-color: rgba(92, 106, 196, 0.1) !important;
            border-color: transparent !important;
            color: #5c6ac4 !important;
        }

        .btn-light-danger {
            background-color: rgba(220, 53, 69, 0.1) !important;
            border-color: transparent !important;
            color: #dc3545 !important;
        }

        .btn-light-primary:hover {
            background-color: rgba(92, 106, 196, 0.2) !important;
        }

        .btn-light-danger:hover {
            background-color: rgba(220, 53, 69, 0.2) !important;
        }

        /* Existing styles */
        /* Remove sorting indicators */
        table.dataTable thead th.sorting:before,
        table.dataTable thead th.sorting:after,
        table.dataTable thead th.sorting_asc:before,
        table.dataTable thead th.sorting_asc:after,
        table.dataTable thead th.sorting_desc:before,
        table.dataTable thead th.sorting_desc:after {
            display: none !important;
        }

        .actions-column {
            width: 70px !important;
            min-width: 70px !important;
            white-space: nowrap;
        }

        .actions-column .btn {
            padding: 0.25rem;
            margin-right: 0.15rem;
            width: 28px;
            height: 28px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .table-responsive {
            cursor: grab;
            user-select: none;
        }
        
        .table-responsive.grabbing {
            cursor: grabbing;
        }
    </style>

    <script>
        $(document).ready(function() {
            // Initialize Select2 for osiguritel dropdowns
            $('.select2-osiguritel').select2({
                theme: 'bootstrap-5',
                width: '100%'
            });

            // Initialize DataTable for Rizici
            var riziciTable = $('#riziciTable').DataTable({
                dom: 'Bfrtip',
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel"></i> Excel',
                        className: 'btn btn-success',
                        exportOptions: {
                            columns: [0, 1, 2, 3]
                        }
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="fas fa-file-pdf"></i> PDF',
                        className: 'btn btn-danger',
                        exportOptions: {
                            columns: [0, 1, 2, 3]
                        }
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print"></i> Print',
                        className: 'btn btn-info',
                        exportOptions: {
                            columns: [0, 1, 2, 3]
                        }
                    }
                ],
                pageLength: 25,
                order: [[0, 'desc']],
                language: {
                    url: '/lib/datatables/macedonian.json'
                },
                columnDefs: [
                    { orderable: false, targets: 4 }
                ]
            });

            // Handle search boxes
            $('#searchBox').on('keyup', function() {
                riziciTable.search(this.value).draw();
            });

            // Add drag scroll functionality
            $('.table-responsive').each(function() {
                const container = this;
                let isDown = false;
                let startX;
                let scrollLeft;

                container.addEventListener('mousedown', (e) => {
                    isDown = true;
                    container.classList.add('grabbing');
                    startX = e.pageX - container.offsetLeft;
                    scrollLeft = container.scrollLeft;
                });

                container.addEventListener('mouseleave', () => {
                    isDown = false;
                    container.classList.remove('grabbing');
                });

                container.addEventListener('mouseup', () => {
                    isDown = false;
                    container.classList.remove('grabbing');
                });

                container.addEventListener('mousemove', (e) => {
                    if (!isDown) return;
                    e.preventDefault();
                    const x = e.pageX - container.offsetLeft;
                    const walk = (x - startX) * 2;
                    container.scrollLeft = scrollLeft - walk;
                });
            });

            var editModal = new bootstrap.Modal(document.getElementById('editRizikModal'));

            // Handle edit button click
            $('.edit-rizik').on('click', function() {
                var id = $(this).data('id');
                
                // Fetch risk data
                $.ajax({
                    url: '?handler=Rizik&id=' + id,
                    type: 'GET',
                    success: function(response) {
                        if (response.success) {
                            $('#editRizikId').val(response.rizik.id);
                            $('#editRizikNaziv').val(response.rizik.naziv);
                            $('#editRizikTip').val(response.rizik.tip);
                            $('#editRizikKlasa').val(response.rizik.klasaId);
                            $('#editRizikProdukt').val(response.rizik.produktId);
                            editModal.show();
                        } else {
                            alert(response.message || 'Грешка при вчитување на ризик');
                        }
                    },
                    error: function() {
                        alert('Грешка при вчитување на ризик');
                    }
                });
            });

            // Handle save edit button click
            $('#saveEditRizik').on('click', function() {
                var id = $('#editRizikId').val();
                var naziv = $('#editRizikNaziv').val().trim();
                var tip = $('#editRizikTip').val();
                var klasaId = $('#editRizikKlasa').val();
                var produktId = $('#editRizikProdukt').val();
                var klasaText = $('#editRizikKlasa option:selected').text();
                var produktText = $('#editRizikProdukt option:selected').text();

                if (!naziv) {
                    alert('Внесете назив на ризик');
                    return;
                }

                if (!tip) {
                    alert('Изберете тип на ризик');
                    return;
                }

                if (!klasaId) {
                    alert('Изберете класа');
                    return;
                }

                if (!produktId) {
                    alert('Изберете продукт');
                    return;
                }

                // Disable the save button to prevent double submission
                $(this).prop('disabled', true);

                $.ajax({
                    url: '?handler=UpdateRizik',
                    type: 'POST',
                    data: JSON.stringify({
                        id: id,
                        naziv: naziv,
                        tip: tip,
                        klasaId: klasaId,
                        produktId: produktId
                    }),
                    contentType: 'application/json',
                    headers: {
                        "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response && response.success) {
                            // Close the modal
                            editModal.hide();
                            
                            // Update the row in the DataTable
                            var table = $('#riziciTable').DataTable();
                            var row = table.row($('button[data-id="' + id + '"]').closest('tr'));
                            var rowData = row.data();
                            rowData[1] = naziv;
                            rowData[2] = tip;
                            rowData[3] = klasaText;
                            rowData[4] = produktText;
                            row.data(rowData).draw(false);

                            // Also update the dropdown in the "Додај ризик по осигурител" section
                            var rizikOption = $('#newRizikPoOsiguritelRizik option[value="' + id + '"]');
                            if (rizikOption.length) {
                                rizikOption.text(naziv);
                            }

                            // Also update the dropdown in the edit modal for "Ризици по осигурител"
                            var editRizikOption = $('#editRizikPoOsiguritelRizik option[value="' + id + '"]');
                            if (editRizikOption.length) {
                                editRizikOption.text(naziv);
                            }

                            // Show success message
                            alert('Ризикот е успешно ажуриран');
                        } else {
                            alert(response?.message || 'Грешка при зачувување на ризик');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error:', error);
                        alert('Грешка при зачувување на ризик. Ве молиме обидете се повторно.');
                    },
                    complete: function() {
                        // Re-enable the save button
                        $('#saveEditRizik').prop('disabled', false);
                    }
                });
            });

            // Handle delete button click
            $(document).on('click', '.delete-rizik', function() {
                var button = $(this);
                var id = button.data('id');
                var row = button.closest('tr');
                var rizikNaziv = row.find('td:eq(1)').text(); // Get the risk name from the current row
                
                if (confirm('Дали сте сигурни дека сакате да го избришете овој ризик? Ова ќе ги избрише и сите комбинации со овој ризик.')) {
                    $.ajax({
                        url: '?handler=DeleteRizik',
                        type: 'POST',
                        data: JSON.stringify({
                            id: parseInt(id)
                        }),
                        contentType: 'application/json',
                        headers: {
                            "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: function(response) {
                            if (response.success) {
                                // Remove the row from the riziciTable
                                var riziciTable = $('#riziciTable').DataTable();
                                riziciTable.row(row).remove().draw(false);

                                // Remove the option from the dropdowns
                                $('#newRizikPoOsiguritelRizik option[value="' + id + '"]').remove();
                                $('#editRizikPoOsiguritelRizik option[value="' + id + '"]').remove();

                                // Get all rows from riziciPoOsiguritelTable that match the deleted risk
                                var riziciPoOsiguritelTable = $('#riziciPoOsiguritelTable').DataTable();
                                var rowsToRemove = [];
                                
                                riziciPoOsiguritelTable.rows().every(function(rowIdx) {
                                    var rowData = this.data();
                                    if (rowData[2] === rizikNaziv) { // Column index 2 contains the risk name
                                        rowsToRemove.push(rowIdx);
                                    }
                                });

                                // Remove the rows in reverse order to maintain correct indices
                                rowsToRemove.reverse().forEach(function(rowIdx) {
                                    riziciPoOsiguritelTable.row(rowIdx).remove();
                                });

                                // Redraw the table once after all removals
                                riziciPoOsiguritelTable.draw(false);
                                
                                // Show success message
                                alert(response.message);
                            } else {
                                alert(response.message || 'Грешка при бришење на ризик');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Error:', error);
                            console.error('Status:', status);
                            console.error('Response:', xhr.responseText);
                            alert('Грешка при бришење на ризик');
                        }
                    });
                }
            });

            // Handle osiguritel selection change
            $('#osiguritelSelect').on('change', function() {
                var selectedInsurer = $(this).val();
                if (!selectedInsurer) {
                    $('#pravnoEkonomskiText').val('');
                    $('#pravnoEkonomskiText').prop('readonly', false);
                    $('#savePravnoEkonomski').prop('disabled', false);
                    return;
                }

                // Load existing data if any
                $.get('?handler=PravnoEkonomskiOdnosi', { insurerId: selectedInsurer })
                    .done(function(data) {
                        if (data && data.pravnoEkonomskiOdnosi) {
                            $('#pravnoEkonomskiText').val(data.pravnoEkonomskiOdnosi);
                            $('#pravnoEkonomskiText').prop('readonly', true);
                            $('#savePravnoEkonomski').prop('disabled', true);
                        } else {
                            $('#pravnoEkonomskiText').val('');
                            $('#pravnoEkonomskiText').prop('readonly', false);
                            $('#savePravnoEkonomski').prop('disabled', false);
                        }
                    })
                    .fail(function(xhr, status, error) {
                        console.error('Error loading data:', error);
                        alert('Грешка при вчитување на податоците. Ве молиме обидете се повторно.');
                    });
            });

            // Handle save button click for new rizik
            $('#saveNewRizik').on('click', function() {
                var naziv = $('#newRizikNaziv').val().trim();
                var tip = $('#newRizikTip').val();
                var klasaId = $('#newRizikKlasa').val();
                var produktId = $('#newRizikProdukt').val();
                var produktText = $('#newRizikProdukt option:selected').text();

                if (!naziv) {
                    alert('Внесете назив на ризик');
                    return;
                }

                if (!tip) {
                    alert('Изберете тип на ризик');
                    return;
                }

                if (!klasaId) {
                    alert('Изберете класа');
                    return;
                }

                if (!produktId) {
                    alert('Изберете продукт');
                    return;
                }

                // Disable the save button to prevent double submission
                $(this).prop('disabled', true);

                $.ajax({
                    url: '?handler=AddRizik',
                    type: 'POST',
                    data: JSON.stringify({
                        naziv: naziv,
                        tip: tip,
                        klasaId: klasaId,
                        produktId: produktId
                    }),
                    contentType: 'application/json',
                    headers: {
                        "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            // Store the state we want to restore after reload
                            localStorage.setItem('showRiziciList', 'true');
                            
                            // Show success message
                            alert('Ризикот е успешно додаден');
                            
                            // Reload the page to refresh the table
                            location.reload();
                        } else {
                            alert(response.message || 'Грешка при зачувување на ризик');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error:', error);
                        console.error('Status:', status);
                        console.error('Response:', xhr.responseText);
                        alert('Грешка при зачувување на ризик');
                    },
                    complete: function() {
                        // Re-enable the save button
                        $('#saveNewRizik').prop('disabled', false);
                    }
                });
            });

            // Check for stored states on page load
            $(document).ready(function() {
                // Check for rizici list state
                if (localStorage.getItem('showRiziciList') === 'true') {
                    // Remove the stored state
                    localStorage.removeItem('showRiziciList');
                    
                    // Open the main Rizici section
                    $('#collapseRizici').collapse('show');
                    
                    // Close the "Dodaj nov rizik" section
                    $('#collapseDodajRizik').collapse('hide');
                    
                    // Open the "Lista rizici" section
                    $('#collapseListaRizici').collapse('show');
                }

                // Check for pravno ekonomski list state
                if (localStorage.getItem('showPravnoEkonomskiList') === 'true') {
                    // Remove the stored state
                    localStorage.removeItem('showPravnoEkonomskiList');
                    
                    // Open the main Pravno-ekonomski odnosi section
                    $('#collapsePravnoEkonomskiMain').collapse('show');
                    
                    // Close the "Dodaj" section
                    $('#collapseDodajPravnoEkonomski').collapse('hide');
                    
                    // Open the "Lista" section
                    $('#collapseListaPravnoEkonomski').collapse('show');
                }

                // Check for rizici po osiguritel list state
                if (localStorage.getItem('showRiziciPoOsiguritelList') === 'true') {
                    // Remove the stored state
                    localStorage.removeItem('showRiziciPoOsiguritelList');
                    
                    // Open the main Rizici po osiguritel section
                    $('#collapseRiziciPoOsiguritel').collapse('show');
                    
                    // Close the "Dodaj" section
                    $('#collapseDodajRizikPoOsiguritel').collapse('hide');
                    
                    // Open the "Lista" section
                    $('#collapseListaRiziciPoOsiguritel').collapse('show');
                }
            });

            // Handle save button click for pravno ekonomski
            $('#savePravnoEkonomski').on('click', function() {
                const insurerId = $('#osiguritelSelect').val();
                const pravnoEkonomskiText = $('#pravnoEkonomskiText').val().trim();
                const osiguritelText = $('#osiguritelSelect option:selected').text();

                if (!insurerId) {
                    alert('Ве молиме изберете осигурител');
                    return;
                }

                if (!pravnoEkonomskiText) {
                    alert('Ве молиме внесете податок за правно-економски односи');
                    return;
                }

                // Disable the save button to prevent double submission
                $(this).prop('disabled', true);

                const requestData = {
                    insurerId: parseInt(insurerId),
                    pravnoEkonomskiOdnosi: pravnoEkonomskiText
                };

                $.ajax({
                    url: '?handler=SavePravnoEkonomskiOdnosi',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(requestData),
                    headers: {
                        "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            // Store the state we want to restore after reload
                            localStorage.setItem('showPravnoEkonomskiList', 'true');
                            
                            // Show success message
                            alert('Правно-економскиот однос е успешно додаден');
                            
                            // Reload the page to refresh the table
                            location.reload();
                        } else {
                            alert(response.message || 'Грешка при зачувување на податокот');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', {
                            status: status,
                            error: error,
                            responseText: xhr.responseText,
                            statusText: xhr.statusText
                        });
                        alert('Грешка при зачувување на податокот. Ве молиме обидете се повторно.');
                    },
                    complete: function() {
                        // Re-enable the save button
                        $('#savePravnoEkonomski').prop('disabled', false);
                    }
                });
            });

            // Initialize DataTable for Pravno Ekonomski
            var pravnoEkonomskiTable = $('#pravnoEkonomskiTable').DataTable({
                dom: 'Bfrtip',
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel"></i> Excel',
                        className: 'btn btn-success',
                        exportOptions: {
                            columns: [0, 1, 2]
                        }
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="fas fa-file-pdf"></i> PDF',
                        className: 'btn btn-danger',
                        exportOptions: {
                            columns: [0, 1, 2]
                        }
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print"></i> Print',
                        className: 'btn btn-info',
                        exportOptions: {
                            columns: [0, 1, 2]
                        }
                    }
                ],
                pageLength: 25,
                order: [[0, 'desc']],
                language: {
                    url: '/lib/datatables/macedonian.json'
                },
                columnDefs: [
                    { orderable: false, targets: 3 }
                ]
            });

            // Handle search boxes
            $('#searchBoxPravnoEkonomski').on('keyup', function() {
                pravnoEkonomskiTable.search(this.value).draw();
            });

            // Add drag scroll functionality
            $('.table-responsive').each(function() {
                const container = this;
                let isDown = false;
                let startX;
                let scrollLeft;

                container.addEventListener('mousedown', (e) => {
                    isDown = true;
                    container.classList.add('grabbing');
                    startX = e.pageX - container.offsetLeft;
                    scrollLeft = container.scrollLeft;
                });

                container.addEventListener('mouseleave', () => {
                    isDown = false;
                    container.classList.remove('grabbing');
                });

                container.addEventListener('mouseup', () => {
                    isDown = false;
                    container.classList.remove('grabbing');
                });

                container.addEventListener('mousemove', (e) => {
                    if (!isDown) return;
                    e.preventDefault();
                    const x = e.pageX - container.offsetLeft;
                    const walk = (x - startX) * 2;
                    container.scrollLeft = scrollLeft - walk;
                });
            });

            var editPravnoEkonomskiModal = new bootstrap.Modal(document.getElementById('editPravnoEkonomskiModal'));

            // Handle edit button click for Pravno Ekonomski Odnosi
            $(document).on('click', '.edit-pravno-ekonomski', function() {
                var id = $(this).data('id');
                
                // Fetch record data
                $.ajax({
                    url: '?handler=PravnoEkonomskiOdnosiById&id=' + id,
                    type: 'GET',
                    success: function(response) {
                        if (response.success) {
                            $('#editPravnoEkonomskiId').val(response.data.id);
                            $('#editPravnoEkonomskiOsiguritel').val(response.data.klientiIdAnalizaDogovoruvac).trigger('change');
                            $('#editPravnoEkonomskiText').val(response.data.pravnoEkonomskiOdnosi);
                            editPravnoEkonomskiModal.show();
                        } else {
                            alert(response.message || 'Грешка при вчитување на записот');
                        }
                    },
                    error: function() {
                        alert('Грешка при вчитување на записот');
                    }
                });
            });

            // Handle save edit button click for Pravno Ekonomski Odnosi
            $('#saveEditPravnoEkonomski').on('click', function() {
                var id = $('#editPravnoEkonomskiId').val();
                var klientiId = $('#editPravnoEkonomskiOsiguritel').val();
                var pravnoEkonomskiOdnosi = $('#editPravnoEkonomskiText').val().trim();
                var osiguritelText = $('#editPravnoEkonomskiOsiguritel option:selected').text();

                if (!klientiId) {
                    alert('Изберете осигурител');
                    return;
                }

                if (!pravnoEkonomskiOdnosi) {
                    alert('Внесете правно-економски односи');
                    return;
                }

                // Disable the save button to prevent double submission
                $(this).prop('disabled', true);

                $.ajax({
                    url: '?handler=UpdatePravnoEkonomskiOdnosi',
                    type: 'POST',
                    data: JSON.stringify({
                        id: id,
                        klientiIdAnalizaDogovoruvac: klientiId,
                        pravnoEkonomskiOdnosi: pravnoEkonomskiOdnosi
                    }),
                    contentType: 'application/json',
                    headers: {
                        "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            editPravnoEkonomskiModal.hide();
                            // Update the row in the DataTable
                            var row = pravnoEkonomskiTable.row($('button[data-id="' + id + '"]').closest('tr'));
                            var data = row.data();
                            data[1] = osiguritelText; // Update Osiguritel
                            data[2] = pravnoEkonomskiOdnosi; // Update PravnoEkonomskiOdnosi
                            row.data(data).draw(false);
                            // Show success message
                            alert('Записот е успешно ажуриран');
                        } else {
                            alert(response.message || 'Грешка при зачувување на записот');
                        }
                    },
                    error: function() {
                        alert('Грешка при зачувување на записот');
                    },
                    complete: function() {
                        // Re-enable the save button
                        $('#saveEditPravnoEkonomski').prop('disabled', false);
                    }
                });
            });

            // Handle delete button click for Pravno Ekonomski
            $(document).on('click', '.delete-pravno-ekonomski', function() {
                var button = $(this);
                var id = button.data('id');
                var row = button.closest('tr');
                
                if (confirm('Дали сте сигурни дека сакате да го избришете овој запис?')) {
                    $.ajax({
                        url: '?handler=DeletePravnoEkonomskiOdnosi',
                        type: 'POST',
                        data: JSON.stringify({
                            id: parseInt(id)
                        }),
                        contentType: 'application/json',
                        headers: {
                            "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: function(response) {
                            if (response.success) {
                                // Remove the row from DataTable
                                pravnoEkonomskiTable.row(row).remove().draw(false);
                                // Show success message
                                alert(response.message);
                            } else {
                                alert(response.message || 'Грешка при бришење на записот');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Error:', error);
                            console.error('Status:', status);
                            console.error('Response:', xhr.responseText);
                            alert('Грешка при бришење на записот');
                        }
                    });
                }
            });

            // Initialize edit modal
            var editRizikPoOsiguritelModal = new bootstrap.Modal(document.getElementById('editRizikPoOsiguritelModal'));

            // Handle edit button click for Rizik Po Osiguritel
            $(document).on('click', '.edit-rizik-po-osiguritel', function() {
                var id = $(this).data('id');
                
                // Fetch record data
                $.ajax({
                    url: '?handler=RizikPoOsiguritelById&id=' + id,
                    type: 'GET',
                    success: function(response) {
                        if (response.success) {
                            $('#editRizikPoOsiguritelId').val(response.data.id);
                            $('#editRizikPoOsiguritelOsiguritel').val(response.data.klientiIdAnalizaDogovoruvac).trigger('change');
                            $('#editRizikPoOsiguritelRizik').val(response.data.rizikIdRizici);
                            $('#editRizikPoOsiguritelPokrieno').prop('checked', response.data.pokrieno);
                            $('#editRizikPoOsiguritelBeleshka').val(response.data.beleshka);
                            editRizikPoOsiguritelModal.show();
                        } else {
                            alert(response.message || 'Грешка при вчитување на записот');
                        }
                    },
                    error: function() {
                        alert('Грешка при вчитување на записот');
                    }
                });
            });

            // Handle save edit button click for Rizik Po Osiguritel
            $('#saveEditRizikPoOsiguritel').on('click', function() {
                var id = parseInt($('#editRizikPoOsiguritelId').val());
                var klientiId = parseInt($('#editRizikPoOsiguritelOsiguritel').val());
                var rizikId = parseInt($('#editRizikPoOsiguritelRizik').val());
                var pokrieno = $('#editRizikPoOsiguritelPokrieno').is(':checked');
                var beleshka = $('#editRizikPoOsiguritelBeleshka').val();

                if (!klientiId) {
                    alert('Изберете осигурител');
                    return;
                }

                if (!rizikId) {
                    alert('Изберете ризик');
                    return;
                }

                // Disable the save button to prevent double submission
                $(this).prop('disabled', true);

                $.ajax({
                    url: '?handler=UpdateRizikPoOsiguritel',
                    type: 'POST',
                    data: JSON.stringify({
                        id: id,
                        klientiIdAnalizaDogovoruvac: klientiId,
                        rizikIdRizici: rizikId,
                        pokrieno: pokrieno,
                        beleshka: beleshka || null
                    }),
                    contentType: 'application/json',
                    headers: {
                        "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            editRizikPoOsiguritelModal.hide();
                            // Store the state we want to restore after reload
                            localStorage.setItem('showRiziciPoOsiguritelList', 'true');
                            
                            // Reload the page to refresh the table
                            location.reload();
                        } else {
                            alert(response.message || 'Грешка при зачувување на записот');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error:', error);
                        console.error('Status:', status);
                        console.error('Response:', xhr.responseText);
                        alert('Грешка при зачувување на записот');
                    },
                    complete: function() {
                        // Re-enable the save button
                        $('#saveEditRizikPoOsiguritel').prop('disabled', false);
                    }
                });
            });

            // Handle delete button click for Rizik Po Osiguritel
            $(document).on('click', '.delete-rizik-po-osiguritel', function() {
                var button = $(this);
                var id = button.data('id');
                var row = button.closest('tr');
                
                if (confirm('Дали сте сигурни дека сакате да го избришете овој запис?')) {
                    $.ajax({
                        url: '?handler=DeleteRizikPoOsiguritel',
                        type: 'POST',
                        data: JSON.stringify({
                            id: parseInt(id)
                        }),
                        contentType: 'application/json',
                        headers: {
                            "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: function(response) {
                            if (response.success) {
                                // Remove the row from DataTable
                                var table = $('#riziciPoOsiguritelTable').DataTable();
                                table.row(row).remove().draw(false);
                                // Show success message
                                alert('Записот е успешно избришан');
                            } else {
                                alert(response.message || 'Грешка при бришење на записот');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Error:', error);
                            console.error('Status:', status);
                            console.error('Response:', xhr.responseText);
                            alert('Грешка при бришење на записот');
                        }
                    });
                }
            });

            // Handle save button click for new rizik po osiguritel
            $('#saveNewRizikPoOsiguritel').on('click', function() {
                var osiguritelId = $('#newRizikPoOsiguritelOsiguritel').val();
                var rizikId = $('#newRizikPoOsiguritelRizik').val();
                var pokrieno = $('#newRizikPoOsiguritelPokrieno').is(':checked');
                var beleshka = $('#newRizikPoOsiguritelBeleshka').val().trim();

                if (!osiguritelId) {
                    alert('Изберете осигурител');
                    return;
                }

                if (!rizikId) {
                    alert('Изберете ризик');
                    return;
                }

                // Disable the save button to prevent double submission
                $(this).prop('disabled', true);

                $.ajax({
                    url: '?handler=AddRizikPoOsiguritel',
                    type: 'POST',
                    data: JSON.stringify({
                        klientiIdAnalizaDogovoruvac: osiguritelId,
                        rizikIdRizici: rizikId,
                        pokrieno: pokrieno,
                        beleshka: beleshka || null
                    }),
                    contentType: 'application/json',
                    headers: {
                        "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            // Store the state we want to restore after reload
                            localStorage.setItem('showRiziciPoOsiguritelList', 'true');
                            
                            // Show success message
                            alert('Ризикот по осигурител е успешно додаден');
                            
                            // Reload the page to refresh the table
                            location.reload();
                        } else {
                            alert(response.message || 'Грешка при зачувување на ризик');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error:', error);
                        console.error('Status:', status);
                        console.error('Response:', xhr.responseText);
                        alert('Грешка при зачувување на ризик');
                    },
                    complete: function() {
                        // Re-enable the save button
                        $('#saveNewRizikPoOsiguritel').prop('disabled', false);
                    }
                });
            });

            // Initialize Select2 for osiguritel filter
            $('#osiguritelFilterRiziciPoOsiguritel').select2({
                theme: 'bootstrap-5',
                width: '100%'
            });

            var riziciPoOsiguritelTable = $('#riziciPoOsiguritelTable').DataTable({
                dom: 'Bfrtip',
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel"></i> Excel',
                        className: 'btn btn-success',
                        exportOptions: {
                            columns: [0, 1, 2, 3, 4, 5, 6]
                        }
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="fas fa-file-pdf"></i> PDF',
                        className: 'btn btn-danger',
                        exportOptions: {
                            columns: [0, 1, 2, 3, 4, 5, 6]
                        }
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print"></i> Print',
                        className: 'btn btn-info',
                        exportOptions: {
                            columns: [0, 1, 2, 3, 4, 5, 6]
                        }
                    }
                ],
                pageLength: 25,
                order: [[0, 'desc']],
                language: {
                    url: '/lib/datatables/macedonian.json'
                },
                columnDefs: [
                    { orderable: false, targets: 7 }
                ]
            });

            // Function to apply all filters
            function applyAllFilters() {
                var selectedKlasaId = $('#klasaFilterRiziciPoOsiguritel').val();
                var selectedProduktId = $('#produktFilterRiziciPoOsiguritel').val();
                var selectedOsiguritelId = $('#osiguritelFilterRiziciPoOsiguritel').val();
                var searchText = $('#searchBoxRiziciPoOsiguritel').val();

                $.get('?handler=ListaRiziciPoOsiguritel', function(response) {
                    if (response && response.success) {
                        var filteredData = response.data;

                        // Apply class filter if selected
                        if (selectedKlasaId) {
                            $.get(`?handler=ProduktiByKlasa&klasaId=${selectedKlasaId}`, function(klasaResponse) {
                                if (klasaResponse && klasaResponse.success) {
                                    var produktIds = klasaResponse.data.map(p => p.id);
                                    filteredData = filteredData.filter(item => 
                                        item.produktId && produktIds.includes(item.produktId)
                                    );
                                    
                                    // Apply product filter if selected
                                    if (selectedProduktId) {
                                        filteredData = filteredData.filter(item => 
                                            item.produktId === parseInt(selectedProduktId)
                                        );
                                    }

                                    // Apply osiguritel filter if selected
                                    if (selectedOsiguritelId) {
                                        filteredData = filteredData.filter(item =>
                                            item.klientiIdAnalizaDogovoruvac === parseInt(selectedOsiguritelId)
                                        );
                                    }

                                    // Apply search filter
                                    if (searchText) {
                                        var searchLower = searchText.toLowerCase();
                                        filteredData = filteredData.filter(item =>
                                            (item.osiguritelNaziv && item.osiguritelNaziv.toLowerCase().includes(searchLower)) ||
                                            (item.rizikNaziv && item.rizikNaziv.toLowerCase().includes(searchLower)) ||
                                            (item.beleshka && item.beleshka.toLowerCase().includes(searchLower))
                                        );
                                    }
                                    
                                    updateRiziciTable(filteredData);
                                }
                            });
                        } else {
                            // Apply product filter if selected
                            if (selectedProduktId) {
                                filteredData = filteredData.filter(item => 
                                    item.produktId === parseInt(selectedProduktId)
                                );
                            }

                            // Apply osiguritel filter if selected
                            if (selectedOsiguritelId) {
                                filteredData = filteredData.filter(item =>
                                    item.klientiIdAnalizaDogovoruvac === parseInt(selectedOsiguritelId)
                                );
                            }

                            // Apply search filter
                            if (searchText) {
                                var searchLower = searchText.toLowerCase();
                                filteredData = filteredData.filter(item =>
                                    (item.osiguritelNaziv && item.osiguritelNaziv.toLowerCase().includes(searchLower)) ||
                                    (item.rizikNaziv && item.rizikNaziv.toLowerCase().includes(searchLower)) ||
                                    (item.beleshka && item.beleshka.toLowerCase().includes(searchLower))
                                );
                            }

                            updateRiziciTable(filteredData);
                        }
                    }
                });
            }

            // Update event handlers to use the new combined filter function
            $('#klasaFilterRiziciPoOsiguritel').on('change', function() {
                var selectedKlasaId = $(this).val();
                
                // Reset product filter when class changes
                $('#produktFilterRiziciPoOsiguritel').val('');
                
                if (!selectedKlasaId) {
                    // If no class selected, show all products
                    $('#produktFilterRiziciPoOsiguritel option').show();
                }

                // Get all products for the selected class
                if (selectedKlasaId) {
                    $.get(`?handler=ProduktiByKlasa&klasaId=${selectedKlasaId}`, function(response) {
                        if (response && response.success) {
                            var produktIds = response.data.map(p => p.id);
                            
                            // Hide/show product options based on class
                            $('#produktFilterRiziciPoOsiguritel option').each(function() {
                                var produktId = $(this).val();
                                if (!produktId || produktIds.includes(parseInt(produktId))) {
                                    $(this).show();
                                } else {
                                    $(this).hide();
                                }
                            });
                        }
                    });
                }

                applyAllFilters();
            });

            $('#produktFilterRiziciPoOsiguritel').on('change', applyAllFilters);
            $('#osiguritelFilterRiziciPoOsiguritel').on('change', applyAllFilters);
            $('#searchBoxRiziciPoOsiguritel').on('keyup', applyAllFilters);

            function updateRiziciTable(data) {
                var table = $('#riziciPoOsiguritelTable').DataTable();
                table.clear();
                data.forEach(function(item) {
                    table.row.add([
                        item.id,
                        item.osiguritelNaziv,
                        item.rizikNaziv,
                        item.klasaIme || '',
                        item.produktIme || '',
                        item.pokrieno ? '<span class="badge bg-success">Да</span>' : '<span class="badge bg-danger">Не</span>',
                        item.beleshka || '',
                        '<button class="btn btn-sm btn-light-primary edit-rizik-po-osiguritel" data-id="' + item.id + '"><i class="fas fa-edit"></i></button>' +
                        '<button class="btn btn-sm btn-light-danger delete-rizik-po-osiguritel" data-id="' + item.id + '"><i class="fas fa-trash"></i></button>'
                    ]);
                });
                table.draw(false);
            }

            // Add handler for class selection in Додај нов ризик section
            $('#newRizikKlasa').on('change', function() {
                var selectedKlasaId = $(this).val();
                var produktSelect = $('#newRizikProdukt');
                
                // Reset and disable product dropdown if no class selected
                if (!selectedKlasaId) {
                    produktSelect.val('');
                    produktSelect.prop('disabled', true);
                    return;
                }

                // Enable product dropdown and get products for selected class
                produktSelect.prop('disabled', false);
                $.get(`?handler=ProduktiByKlasa&klasaId=${selectedKlasaId}`, function(response) {
                    if (response && response.success) {
                        var produktIds = response.data.map(p => p.id);
                        
                        // Reset selection
                        produktSelect.val('');
                        
                        // Hide/show product options based on class
                        produktSelect.find('option').each(function() {
                            var produktId = $(this).val();
                            if (!produktId || produktIds.includes(parseInt(produktId))) {
                                $(this).show();
                            } else {
                                $(this).hide();
                            }
                        });
                    }
                });
            });

            // Add handler for class selection in Edit modal
            $('#editRizikKlasa').on('change', function() {
                var selectedKlasaId = $(this).val();
                var produktSelect = $('#editRizikProdukt');
                
                // Reset and disable product dropdown if no class selected
                if (!selectedKlasaId) {
                    produktSelect.val('');
                    produktSelect.prop('disabled', true);
                    return;
                }

                // Enable product dropdown and get products for selected class
                produktSelect.prop('disabled', false);
                $.get(`?handler=ProduktiByKlasa&klasaId=${selectedKlasaId}`, function(response) {
                    if (response && response.success) {
                        var produktIds = response.data.map(p => p.id);
                        
                        // Reset selection
                        produktSelect.val('');
                        
                        // Hide/show product options based on class
                        produktSelect.find('option').each(function() {
                            var produktId = $(this).val();
                            if (!produktId || produktIds.includes(parseInt(produktId))) {
                                $(this).show();
                            } else {
                                $(this).hide();
                            }
                        });
                    }
                });
            });

            // Initialize Select2 for osiguritel filter
            $('#osiguritelFilterRizici').select2({
                theme: 'bootstrap-5',
                width: '100%'
            });

            // Function to apply all filters for Rizici table
            function applyRiziciFilters() {
                var selectedKlasaId = $('#klasaFilterRizici').val();
                var selectedProduktId = $('#produktFilterRizici').val();
                var searchText = $('#searchBox').val().toLowerCase();

                var table = $('#riziciTable').DataTable();
                
                // Custom filter function
                $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
                    if (settings.nTable.id !== 'riziciTable') return true; // Only apply to riziciTable
                    
                    // Get the row data from the DataTable's internal data
                    var row = table.row(dataIndex).data();
                    var rowNode = table.row(dataIndex).node();
                    
                    // Get the KlasiOsiguruvanjeIdRizik from the data attribute
                    var rowKlasaId = $(rowNode).find('td:eq(3)').data('klasa-id');
                    
                    // Match based on the actual KlasiOsiguruvanjeIdRizik
                    var matchesKlasa = !selectedKlasaId || (rowKlasaId && rowKlasaId.toString() === selectedKlasaId);
                    
                    var matchesProdukt = !selectedProduktId || data[4].includes(
                        $('#produktFilterRizici option:selected').text().trim()
                    );
                    
                    // Apply search text filter
                    var matchesSearch = !searchText || 
                        data[0].toLowerCase().includes(searchText) ||
                        data[1].toLowerCase().includes(searchText) ||
                        data[2].toLowerCase().includes(searchText) ||
                        data[3].toLowerCase().includes(searchText) ||
                        data[4].toLowerCase().includes(searchText);

                    return matchesKlasa && matchesProdukt && matchesSearch;
                });

                table.draw();

                // Remove the custom filter after drawing
                $.fn.dataTable.ext.search.pop();
            }

            // Handle class filter change
            $('#klasaFilterRizici').on('change', function() {
                var selectedKlasaId = $(this).val();
                var produktSelect = $('#produktFilterRizici');
                
                // Reset product filter when class changes
                produktSelect.val('');
                
                if (!selectedKlasaId) {
                    // If no class selected, show all products
                    produktSelect.find('option').show();
                } else {
                    // Get products for the selected class
                    $.get(`?handler=ProduktiByKlasa&klasaId=${selectedKlasaId}`, function(response) {
                        if (response && response.success) {
                            var produktIds = response.data.map(p => p.id);
                            
                            // Hide/show product options based on class
                            produktSelect.find('option').each(function() {
                                var produktId = $(this).val();
                                if (!produktId || produktIds.includes(parseInt(produktId))) {
                                    $(this).show();
                                } else {
                                    $(this).hide();
                                }
                            });
                        }
                    });
                }

                applyRiziciFilters();
            });

            // Handle product filter change
            $('#produktFilterRizici').on('change', applyRiziciFilters);

            // Handle search box input
            $('#searchBox').on('keyup', applyRiziciFilters);
        });
    </script>
}
