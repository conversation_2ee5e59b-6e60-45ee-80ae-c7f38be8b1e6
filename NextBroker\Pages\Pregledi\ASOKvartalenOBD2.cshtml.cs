using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Linq;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Text.Json;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.IO;
using iText.Html2pdf;
using iText.Kernel.Pdf;
using System.Text;

namespace NextBroker.Pages.Pregledi
{
    // OBD2 Table Model
    public class OBD2TableModel
    {
        // Broj na dogovori
        public int? BrojDogovori_OsiguritelnoPokritie { get; set; }
        public int? BrojDogovori_ReosiguritelnoPokritie { get; set; }
        public int? BrojDogovori_ProdazhbaOsteteniPredmeti { get; set; }
        public int? BrojDogovori_PrezemanjeMerkiZaRizici { get; set; }
        public int? BrojDogovori_ProcenaNaRizik { get; set; }
        public int? BrojDogovori_IntelektualniTehnickiUslugi { get; set; }

        // Realizirana provizija
        public decimal? RealiziranaProvizija_OsiguritelnoPokritie { get; set; }
        public decimal? RealiziranaProvizija_ReosiguritelnoPokritie { get; set; }
        public decimal? RealiziranaProvizija_ProdazhbaOsteteniPredmeti { get; set; }
        public decimal? RealiziranaProvizija_PrezemanjeMerkiZaRizici { get; set; }
        public decimal? RealiziranaProvizija_ProcenaNaRizik { get; set; }
        public decimal? RealiziranaProvizija_IntelektualniTehnickiUslugi { get; set; }

        // Totals
        public int? VkupenBrojNaDogovori { get; set; }
        public decimal? VkupnaRealiziranaProvizija { get; set; }
    }

    public class ASOKvartalenOBD2Model : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public ASOKvartalenOBD2Model(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
            InitializeQuarterOptions();
            InitializeYearOptions();
        }

        [BindProperty]
        public OBD2TableModel TableData { get; set; } = new OBD2TableModel();

        [BindProperty(SupportsGet = true)]
        public int? SelectedYear { get; set; }
        [BindProperty(SupportsGet = true)]
        public string SelectedQuarter { get; set; }

        [BindProperty]
        public List<ActivityRowModel> ActivityRows { get; set; } = null;

        public SelectList YearOptions { get; set; }
        public SelectList QuarterOptions { get; set; }
        public bool IsConfirmed { get; set; }
        public bool IsPreview { get; set; }
        public bool IsEditMode { get; set; }
        public bool IsReadOnly { get; set; }
        public bool ShowTable { get; set; } = false;
        public string NoDataMessage { get; set; }

        public class ActivityRowModel
        {
            public string Description { get; set; }
            public int? BrojDogovori { get; set; }
            public decimal? RealiziranaProvizija { get; set; }
        }

        public override async Task OnPageHandlerExecutionAsync(PageHandlerExecutingContext context, PageHandlerExecutionDelegate next)
        {
            InitializeQuarterOptions();
            InitializeYearOptions();
            if (ActivityRows == null || ActivityRows.Count == 0)
            {
                ActivityRows = new List<ActivityRowModel>
                {
                    new ActivityRowModel { Description = "Посредување во договарање на осигурително покритие" },
                    new ActivityRowModel { Description = "Посредување во договарање на реосигурително покритие" },
                    new ActivityRowModel { Description = "Посредување во продажба и/или продажба на оштетени предмети кои за време на процесот на ликвидација на штетата станале сопственост на друштвото за осигурување" },
                    new ActivityRowModel { Description = "Преземање мерки за спречување или елиминирање ризици на кои се изложени осигурениот имот и лица" },
                    new ActivityRowModel { Description = "Процена и снимање на ризик кој се однесува на осигурениот објект" },
                    new ActivityRowModel { Description = "Извршување на други интелектуални и технички услуги кои се однесуваат на операциите на осигурување" }
                };
            }
            await base.OnPageHandlerExecutionAsync(context, next);
        }

        public async Task<IActionResult> OnGetAsync()
        {
            InitializeQuarterOptions();
            InitializeYearOptions();
            IsEditMode = false;
            IsReadOnly = false;
            ShowTable = false;

            // Ensure SelectedQuarter is set
            if (string.IsNullOrEmpty(SelectedQuarter))
                SelectedQuarter = "1";

            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear ?? DateTime.Now.Year);

            // First, check if OBD1 data exists for this period
            bool hasObd1Data = false;
            using (var conn = new Microsoft.Data.SqlClient.SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            using (var cmd = new Microsoft.Data.SqlClient.SqlCommand(@"SELECT TOP 1 * FROM ASOKvartalenIzvestajArhiva WHERE StartDate = @StartDate AND EndDate = @EndDate AND (UsernameCreated IS NULL OR UsernameCreated NOT LIKE '%_PREVIEW%') ORDER BY DateCreated DESC", conn))
            {
                cmd.Parameters.AddWithValue("@StartDate", startDate);
                cmd.Parameters.AddWithValue("@EndDate", endDate);
                await conn.OpenAsync();
                using (var reader = await cmd.ExecuteReaderAsync())
                {
                    if (await reader.ReadAsync())
                    {
                        hasObd1Data = true;
                    }
                }
            }

            // If no OBD1 data exists, show message and don't allow any operations
            if (!hasObd1Data)
            {
                NoDataMessage = "Нема потврдени податоци за ОБД1 за избраниот квартал. Мора прво да се генерира и потврди ОБД1 извештајот.";
                ShowTable = false;
                return Page();
            }

            // Check if OBD2 has confirmed data for this period
            bool hasConfirmedObd2Data = false;
            try
            {
                using (var conn = new Microsoft.Data.SqlClient.SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                using (var cmd = new Microsoft.Data.SqlClient.SqlCommand(@"SELECT TOP 1 * FROM ASOKvartalenIzvestajObd2Arhiva WHERE StartDate = @StartDate AND EndDate = @EndDate AND (UsernameCreated IS NULL OR UsernameCreated NOT LIKE '%_PREVIEW%') ORDER BY DateCreated DESC", conn))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    await conn.OpenAsync();
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            hasConfirmedObd2Data = true;
                            // Load the confirmed data into ActivityRows
                            ActivityRows = new List<ActivityRowModel>
                            {
                                new ActivityRowModel { 
                                    Description = "Посредување во договарање на осигурително покритие",
                                    BrojDogovori = reader["BrojDogovori_OsiguritelnoPokritie"] as int?,
                                    RealiziranaProvizija = reader["RealiziranaProvizija_OsiguritelnoPokritie"] as decimal?
                                },
                                new ActivityRowModel { 
                                    Description = "Посредување во договарање на реосигурително покритие",
                                    BrojDogovori = reader["BrojDogovori_ReosiguritelnoPokritie"] as int?,
                                    RealiziranaProvizija = reader["RealiziranaProvizija_ReosiguritelnoPokritie"] as decimal?
                                },
                                new ActivityRowModel { 
                                    Description = "Посредување во продажба и/или продажба на оштетени предмети кои за време на процесот на ликвидација на штетата станале сопственост на друштвото за осигурување",
                                    BrojDogovori = reader["BrojDogovori_ProdazhbaOsteteniPredmeti"] as int?,
                                    RealiziranaProvizija = reader["RealiziranaProvizija_ProdazhbaOsteteniPredmeti"] as decimal?
                                },
                                new ActivityRowModel { 
                                    Description = "Преземање мерки за спречување или елиминирање ризици на кои се изложени осигурениот имот и лица",
                                    BrojDogovori = reader["BrojDogovori_PrezemanjeMerkiZaRizici"] as int?,
                                    RealiziranaProvizija = reader["RealiziranaProvizija_PrezemanjeMerkiZaRizici"] as decimal?
                                },
                                new ActivityRowModel { 
                                    Description = "Процена и снимање на ризик кој се однесува на осигурениот објект",
                                    BrojDogovori = reader["BrojDogovori_ProcenaNaRizik"] as int?,
                                    RealiziranaProvizija = reader["RealiziranaProvizija_ProcenaNaRizik"] as decimal?
                                },
                                new ActivityRowModel { 
                                    Description = "Извршување на други интелектуални и технички услуги кои се однесуваат на операциите на осигурување",
                                    BrojDogovori = reader["BrojDogovori_IntelektualniTehnickiUslugi"] as int?,
                                    RealiziranaProvizija = reader["RealiziranaProvizija_IntelektualniTehnickiUslugi"] as decimal?
                                }
                            };
                            // Set flags for confirmed data
                            IsReadOnly = true;
                            IsConfirmed = true;
                            IsPreview = false;
                            ShowTable = true;
                        }
                    }
                }
            }
            catch (Microsoft.Data.SqlClient.SqlException ex) when (ex.Message.Contains("Invalid column name"))
            {
                // Table does not have StartDate/EndDate columns, so we cannot filter by period
                // Always fall back to OBD1 if no OBD2 data is found
            }

            // If no confirmed OBD2 data, try to load from OBD1 (confirmed only)
            if (!hasConfirmedObd2Data)
            {
                using (var conn = new Microsoft.Data.SqlClient.SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                using (var cmd = new Microsoft.Data.SqlClient.SqlCommand(@"SELECT TOP 1 * FROM ASOKvartalenIzvestajArhiva WHERE StartDate = @StartDate AND EndDate = @EndDate AND (UsernameCreated IS NULL OR UsernameCreated NOT LIKE '%_PREVIEW%') ORDER BY DateCreated DESC", conn))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    await conn.OpenAsync();
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            // Map OBD1 summary fields to OBD2 ActivityRows
                            ActivityRows[0].BrojDogovori = reader["VkupenBrojNaDogovori"] as int?;
                            ActivityRows[0].RealiziranaProvizija = reader["VkupnaRealiziranaProvizija"] as decimal?;
                            // Set flags for OBD1 fallback data
                            IsReadOnly = false;
                            IsConfirmed = false;
                            IsPreview = false;
                            ShowTable = true;
                        }
                    }
                }
            }
            return Page();
        }

        public async Task<IActionResult> OnPostLoadDataAsync()
        {
            System.Diagnostics.Debug.WriteLine("OBD2 OnPostLoadDataAsync: ActivityRows received from form:");
            if (ActivityRows != null)
            {
                for (int i = 0; i < ActivityRows.Count; i++)
                {
                    var row = ActivityRows[i];
                    System.Diagnostics.Debug.WriteLine($"Row {i}: Description={row.Description}, BrojDogovori={row.BrojDogovori}, RealiziranaProvizija={row.RealiziranaProvizija}");
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("ActivityRows is null");
            }
            InitializeQuarterOptions();
            InitializeYearOptions();
            ShowTable = false;
            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear ?? DateTime.Now.Year);
            
            // First, check if OBD1 data exists for this period
            bool hasObd1Data = false;
            using (var conn = new Microsoft.Data.SqlClient.SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            using (var cmd = new Microsoft.Data.SqlClient.SqlCommand(@"SELECT TOP 1 * FROM ASOKvartalenIzvestajArhiva WHERE StartDate = @StartDate AND EndDate = @EndDate AND (UsernameCreated IS NULL OR UsernameCreated NOT LIKE '%_PREVIEW%') ORDER BY DateCreated DESC", conn))
            {
                cmd.Parameters.AddWithValue("@StartDate", startDate);
                cmd.Parameters.AddWithValue("@EndDate", endDate);
                await conn.OpenAsync();
                using (var reader = await cmd.ExecuteReaderAsync())
                {
                    if (await reader.ReadAsync())
                    {
                        hasObd1Data = true;
                    }
                }
            }

            // If no OBD1 data exists, show message and don't allow any operations
            if (!hasObd1Data)
            {
                NoDataMessage = "Нема потврдени податоци за ОБД1 за избраниот квартал. Мора прво да се генерира и потврди ОБД1 извештајот.";
                ShowTable = false;
                return Page();
            }
            
            // Check if confirmed OBD2 data exists for this period
            bool hasConfirmedObd2Data = false;
            try
            {
                using (var conn = new Microsoft.Data.SqlClient.SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                using (var cmd = new Microsoft.Data.SqlClient.SqlCommand(@"SELECT TOP 1 * FROM ASOKvartalenIzvestajObd2Arhiva WHERE StartDate = @StartDate AND EndDate = @EndDate AND (UsernameCreated IS NULL OR UsernameCreated NOT LIKE '%_PREVIEW%') ORDER BY DateCreated DESC", conn))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    await conn.OpenAsync();
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            hasConfirmedObd2Data = true;
                        }
                    }
                }
            }
            catch (Microsoft.Data.SqlClient.SqlException ex) when (ex.Message.Contains("Invalid column name"))
            {
                // Table does not have StartDate/EndDate columns
            }

            if (hasConfirmedObd2Data)
            {
                // If confirmed data exists, load it and set read-only
                ActivityRows = await LoadExportRowsAsync(startDate, endDate);
                IsReadOnly = true;
                IsConfirmed = true;
                IsPreview = false;
                ShowTable = true;
            }
            else
            {
                // Only save preview if ActivityRows has real data and no confirmed data exists
                bool hasData = ActivityRows != null && ActivityRows.Any(r => (r.BrojDogovori ?? 0) != 0 || (r.RealiziranaProvizija ?? 0) != 0);
                await CleanupPreviewDataAsync(startDate, endDate);
                if (hasData)
                    await SaveToArchiveAsync(startDate, endDate, true);
                // Reload from DB (preview, confirmed, or OBD1 fallback)
                ActivityRows = await LoadExportRowsAsync(startDate, endDate);
                IsPreview = true;
                IsReadOnly = false;
                IsConfirmed = false;
                ShowTable = true;
            }
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            System.Diagnostics.Debug.WriteLine("OBD2 OnPostAsync: Method called!");
            InitializeQuarterOptions();
            InitializeYearOptions();
            ShowTable = true;
            return Page();
        }

        // === OBD2 Confirmation Helpers ===
        private string GetPreviewMarker() => (HttpContext.Session.GetString("Username") ?? "System") + "_PREVIEW";
        private string GetFinalMarker() => HttpContext.Session.GetString("Username") ?? "System";

        private async Task CleanupPreviewDataAsync(DateTime startDate, DateTime endDate)
        {
            using (var conn = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            using (var cmd = new SqlCommand("DELETE FROM ASOKvartalenIzvestajObd2Arhiva WHERE StartDate = @StartDate AND EndDate = @EndDate AND UsernameCreated LIKE @PreviewMarker", conn))
            {
                cmd.Parameters.AddWithValue("@StartDate", startDate);
                cmd.Parameters.AddWithValue("@EndDate", endDate);
                cmd.Parameters.AddWithValue("@PreviewMarker", GetPreviewMarker() + "%");
                await conn.OpenAsync();
                await cmd.ExecuteNonQueryAsync();
            }
        }

        private async Task DeleteAllRowsForQuarterAsync(DateTime startDate, DateTime endDate)
        {
            using (var conn = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            using (var cmd = new SqlCommand("DELETE FROM ASOKvartalenIzvestajObd2Arhiva WHERE StartDate = @StartDate AND EndDate = @EndDate", conn))
            {
                cmd.Parameters.AddWithValue("@StartDate", startDate);
                cmd.Parameters.AddWithValue("@EndDate", endDate);
                await conn.OpenAsync();
                await cmd.ExecuteNonQueryAsync();
            }
        }

        private async Task SaveToArchiveAsync(DateTime startDate, DateTime endDate, bool isPreview)
        {
            using (var conn = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            using (var cmd = new SqlCommand(@"
                INSERT INTO ASOKvartalenIzvestajObd2Arhiva (
                    BrojDogovori_OsiguritelnoPokritie, BrojDogovori_ReosiguritelnoPokritie, BrojDogovori_ProdazhbaOsteteniPredmeti, BrojDogovori_PrezemanjeMerkiZaRizici, BrojDogovori_ProcenaNaRizik, BrojDogovori_IntelektualniTehnickiUslugi,
                    RealiziranaProvizija_OsiguritelnoPokritie, RealiziranaProvizija_ReosiguritelnoPokritie, RealiziranaProvizija_ProdazhbaOsteteniPredmeti, RealiziranaProvizija_PrezemanjeMerkiZaRizici, RealiziranaProvizija_ProcenaNaRizik, RealiziranaProvizija_IntelektualniTehnickiUslugi,
                    VkupenBrojNaDogovori, VkupnaRealiziranaProvizija, StartDate, EndDate, UsernameCreated, DateCreated
                ) VALUES (
                    @BrojDogovori_OsiguritelnoPokritie, @BrojDogovori_ReosiguritelnoPokritie, @BrojDogovori_ProdazhbaOsteteniPredmeti, @BrojDogovori_PrezemanjeMerkiZaRizici, @BrojDogovori_ProcenaNaRizik, @BrojDogovori_IntelektualniTehnickiUslugi,
                    @RealiziranaProvizija_OsiguritelnoPokritie, @RealiziranaProvizija_ReosiguritelnoPokritie, @RealiziranaProvizija_ProdazhbaOsteteniPredmeti, @RealiziranaProvizija_PrezemanjeMerkiZaRizici, @RealiziranaProvizija_ProcenaNaRizik, @RealiziranaProvizija_IntelektualniTehnickiUslugi,
                    @VkupenBrojNaDogovori, @VkupnaRealiziranaProvizija, @StartDate, @EndDate, @UsernameCreated, @DateCreated
                )", conn))
            {
                cmd.Parameters.AddWithValue("@BrojDogovori_OsiguritelnoPokritie", ActivityRows[0].BrojDogovori ?? 0);
                cmd.Parameters.AddWithValue("@BrojDogovori_ReosiguritelnoPokritie", ActivityRows[1].BrojDogovori ?? 0);
                cmd.Parameters.AddWithValue("@BrojDogovori_ProdazhbaOsteteniPredmeti", ActivityRows[2].BrojDogovori ?? 0);
                cmd.Parameters.AddWithValue("@BrojDogovori_PrezemanjeMerkiZaRizici", ActivityRows[3].BrojDogovori ?? 0);
                cmd.Parameters.AddWithValue("@BrojDogovori_ProcenaNaRizik", ActivityRows[4].BrojDogovori ?? 0);
                cmd.Parameters.AddWithValue("@BrojDogovori_IntelektualniTehnickiUslugi", ActivityRows[5].BrojDogovori ?? 0);
                cmd.Parameters.AddWithValue("@RealiziranaProvizija_OsiguritelnoPokritie", ActivityRows[0].RealiziranaProvizija ?? 0m);
                cmd.Parameters.AddWithValue("@RealiziranaProvizija_ReosiguritelnoPokritie", ActivityRows[1].RealiziranaProvizija ?? 0m);
                cmd.Parameters.AddWithValue("@RealiziranaProvizija_ProdazhbaOsteteniPredmeti", ActivityRows[2].RealiziranaProvizija ?? 0m);
                cmd.Parameters.AddWithValue("@RealiziranaProvizija_PrezemanjeMerkiZaRizici", ActivityRows[3].RealiziranaProvizija ?? 0m);
                cmd.Parameters.AddWithValue("@RealiziranaProvizija_ProcenaNaRizik", ActivityRows[4].RealiziranaProvizija ?? 0m);
                cmd.Parameters.AddWithValue("@RealiziranaProvizija_IntelektualniTehnickiUslugi", ActivityRows[5].RealiziranaProvizija ?? 0m);
                cmd.Parameters.AddWithValue("@VkupenBrojNaDogovori", ActivityRows.Sum(x => x.BrojDogovori ?? 0));
                cmd.Parameters.AddWithValue("@VkupnaRealiziranaProvizija", ActivityRows.Sum(x => x.RealiziranaProvizija ?? 0));
                cmd.Parameters.AddWithValue("@StartDate", startDate);
                cmd.Parameters.AddWithValue("@EndDate", endDate);
                cmd.Parameters.AddWithValue("@UsernameCreated", isPreview ? GetPreviewMarker() : GetFinalMarker());
                cmd.Parameters.AddWithValue("@DateCreated", DateTime.Now);
                await conn.OpenAsync();
                await cmd.ExecuteNonQueryAsync();
            }
        }

        private async Task<List<ActivityRowModel>> LoadExportRowsAsync(DateTime startDate, DateTime endDate)
        {
            var rows = new List<ActivityRowModel>
            {
                new ActivityRowModel { Description = "Посредување во договарање на осигурително покритие" },
                new ActivityRowModel { Description = "Посредување во договарање на реосигурително покритие" },
                new ActivityRowModel { Description = "Посредување во продажба и/или продажба на оштетени предмети кои за време на процесот на ликвидација на штетата станале сопственост на друштвото за осигурување" },
                new ActivityRowModel { Description = "Преземање мерки за спречување или елиминирање ризици на кои се изложени осигурениот имот и лица" },
                new ActivityRowModel { Description = "Процена и снимање на ризик кој се однесува на осигурениот објект" },
                new ActivityRowModel { Description = "Извршување на други интелектуални и технички услуги кои се однесуваат на операциите на осигурување" }
            };
            // Try OBD2 preview
            using (var conn = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            using (var cmd = new SqlCommand(@"SELECT TOP 1 * FROM ASOKvartalenIzvestajObd2Arhiva WHERE StartDate = @StartDate AND EndDate = @EndDate AND UsernameCreated LIKE @PreviewMarker ORDER BY DateCreated DESC", conn))
            {
                cmd.Parameters.AddWithValue("@StartDate", startDate);
                cmd.Parameters.AddWithValue("@EndDate", endDate);
                cmd.Parameters.AddWithValue("@PreviewMarker", GetPreviewMarker() + "%");
                await conn.OpenAsync();
                using (var reader = await cmd.ExecuteReaderAsync())
                {
                    if (await reader.ReadAsync())
                    {
                        rows[0].BrojDogovori = reader["BrojDogovori_OsiguritelnoPokritie"] as int?;
                        rows[0].RealiziranaProvizija = reader["RealiziranaProvizija_OsiguritelnoPokritie"] as decimal?;
                        rows[1].BrojDogovori = reader["BrojDogovori_ReosiguritelnoPokritie"] as int?;
                        rows[1].RealiziranaProvizija = reader["RealiziranaProvizija_ReosiguritelnoPokritie"] as decimal?;
                        rows[2].BrojDogovori = reader["BrojDogovori_ProdazhbaOsteteniPredmeti"] as int?;
                        rows[2].RealiziranaProvizija = reader["RealiziranaProvizija_ProdazhbaOsteteniPredmeti"] as decimal?;
                        rows[3].BrojDogovori = reader["BrojDogovori_PrezemanjeMerkiZaRizici"] as int?;
                        rows[3].RealiziranaProvizija = reader["RealiziranaProvizija_PrezemanjeMerkiZaRizici"] as decimal?;
                        rows[4].BrojDogovori = reader["BrojDogovori_ProcenaNaRizik"] as int?;
                        rows[4].RealiziranaProvizija = reader["RealiziranaProvizija_ProcenaNaRizik"] as decimal?;
                        rows[5].BrojDogovori = reader["BrojDogovori_IntelektualniTehnickiUslugi"] as int?;
                        rows[5].RealiziranaProvizija = reader["RealiziranaProvizija_IntelektualniTehnickiUslugi"] as decimal?;
                        return rows;
                    }
                }
            }
            // Try OBD2 confirmed
            using (var conn = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            using (var cmd = new SqlCommand(@"SELECT TOP 1 * FROM ASOKvartalenIzvestajObd2Arhiva WHERE StartDate = @StartDate AND EndDate = @EndDate AND (UsernameCreated IS NULL OR UsernameCreated NOT LIKE '%_PREVIEW%') ORDER BY DateCreated DESC", conn))
            {
                cmd.Parameters.AddWithValue("@StartDate", startDate);
                cmd.Parameters.AddWithValue("@EndDate", endDate);
                await conn.OpenAsync();
                using (var reader = await cmd.ExecuteReaderAsync())
                {
                    if (await reader.ReadAsync())
                    {
                        rows[0].BrojDogovori = reader["BrojDogovori_OsiguritelnoPokritie"] as int?;
                        rows[0].RealiziranaProvizija = reader["RealiziranaProvizija_OsiguritelnoPokritie"] as decimal?;
                        rows[1].BrojDogovori = reader["BrojDogovori_ReosiguritelnoPokritie"] as int?;
                        rows[1].RealiziranaProvizija = reader["RealiziranaProvizija_ReosiguritelnoPokritie"] as decimal?;
                        rows[2].BrojDogovori = reader["BrojDogovori_ProdazhbaOsteteniPredmeti"] as int?;
                        rows[2].RealiziranaProvizija = reader["RealiziranaProvizija_ProdazhbaOsteteniPredmeti"] as decimal?;
                        rows[3].BrojDogovori = reader["BrojDogovori_PrezemanjeMerkiZaRizici"] as int?;
                        rows[3].RealiziranaProvizija = reader["RealiziranaProvizija_PrezemanjeMerkiZaRizici"] as decimal?;
                        rows[4].BrojDogovori = reader["BrojDogovori_ProcenaNaRizik"] as int?;
                        rows[4].RealiziranaProvizija = reader["RealiziranaProvizija_ProcenaNaRizik"] as decimal?;
                        rows[5].BrojDogovori = reader["BrojDogovori_IntelektualniTehnickiUslugi"] as int?;
                        rows[5].RealiziranaProvizija = reader["RealiziranaProvizija_IntelektualniTehnickiUslugi"] as decimal?;
                        return rows;
                    }
                }
            }
            // Fallback: Try OBD1 confirmed
            using (var conn = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            using (var cmd = new SqlCommand(@"SELECT TOP 1 * FROM ASOKvartalenIzvestajArhiva WHERE StartDate = @StartDate AND EndDate = @EndDate AND (UsernameCreated IS NULL OR UsernameCreated NOT LIKE '%_PREVIEW%') ORDER BY DateCreated DESC", conn))
            {
                cmd.Parameters.AddWithValue("@StartDate", startDate);
                cmd.Parameters.AddWithValue("@EndDate", endDate);
                await conn.OpenAsync();
                using (var reader = await cmd.ExecuteReaderAsync())
                {
                    if (await reader.ReadAsync())
                    {
                        rows[0].BrojDogovori = reader["VkupenBrojNaDogovori"] as int?;
                        rows[0].RealiziranaProvizija = reader["VkupnaRealiziranaProvizija"] as decimal?;
                    }
                }
            }
            return rows;
        }

        // === END OBD2 Confirmation Helpers ===

        public async Task<IActionResult> OnPostConfirmAsync()
        {
            System.Diagnostics.Debug.WriteLine("OBD2 OnPostConfirmAsync: ActivityRows received from form:");
            if (ActivityRows != null)
            {
                for (int i = 0; i < ActivityRows.Count; i++)
                {
                    var row = ActivityRows[i];
                    System.Diagnostics.Debug.WriteLine($"Row {i}: Description={row.Description}, BrojDogovori={row.BrojDogovori}, RealiziranaProvizija={row.RealiziranaProvizija}");
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("ActivityRows is null");
            }
            
            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear ?? DateTime.Now.Year);
            
            // First, check if OBD1 data exists for this period
            bool hasObd1Data = false;
            using (var conn = new Microsoft.Data.SqlClient.SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            using (var cmd = new Microsoft.Data.SqlClient.SqlCommand(@"SELECT TOP 1 * FROM ASOKvartalenIzvestajArhiva WHERE StartDate = @StartDate AND EndDate = @EndDate AND (UsernameCreated IS NULL OR UsernameCreated NOT LIKE '%_PREVIEW%') ORDER BY DateCreated DESC", conn))
            {
                cmd.Parameters.AddWithValue("@StartDate", startDate);
                cmd.Parameters.AddWithValue("@EndDate", endDate);
                await conn.OpenAsync();
                using (var reader = await cmd.ExecuteReaderAsync())
                {
                    if (await reader.ReadAsync())
                    {
                        hasObd1Data = true;
                    }
                }
            }

            // If no OBD1 data exists, show message and don't allow confirmation
            if (!hasObd1Data)
            {
                NoDataMessage = "Нема потврдени податоци за ОБД1 за избраниот квартал. Мора прво да се генерира и потврди ОБД1 извештајот.";
                ShowTable = false;
                return Page();
            }
            
            await DeleteAllRowsForQuarterAsync(startDate, endDate);
            // Only save if ActivityRows has real data
            bool hasData = ActivityRows != null && ActivityRows.Any(r => (r.BrojDogovori ?? 0) != 0 || (r.RealiziranaProvizija ?? 0) != 0);
            if (hasData)
                await SaveToArchiveAsync(startDate, endDate, false);
            ActivityRows = await LoadExportRowsAsync(startDate, endDate);
            IsConfirmed = true;
            IsPreview = false;
            IsReadOnly = true;
            ShowTable = true;
            return Page();
        }

        public async Task<IActionResult> OnPostCancelPreviewAsync()
        {
            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear ?? DateTime.Now.Year);
            await CleanupPreviewDataAsync(startDate, endDate);
            ActivityRows = await LoadExportRowsAsync(startDate, endDate);
            IsPreview = false;
            IsReadOnly = false;
            IsConfirmed = false;
            ShowTable = false;
            return Page();
        }

        private (DateTime, DateTime) GetQuarterDateRange(string quarter, int year)
        {
            switch (quarter)
            {
                case "1":
                case "Q1":
                    return (new DateTime(year, 1, 1), new DateTime(year, 3, 31));
                case "2":
                case "Q2":
                    return (new DateTime(year, 1, 1), new DateTime(year, 6, 30));
                case "3":
                case "Q3":
                    return (new DateTime(year, 1, 1), new DateTime(year, 9, 30));
                case "4":
                case "Q4":
                    return (new DateTime(year, 1, 1), new DateTime(year, 12, 31));
                default:
                    throw new ArgumentException("Invalid quarter");
            }
        }

        // Quarter and year options logic from OBD1
        public void InitializeQuarterOptions()
        {
            var quarters = new List<SelectListItem>
            {
                new SelectListItem { Value = "1", Text = "1-ви квартал (01.01-31.03)" },
                new SelectListItem { Value = "2", Text = "2-ри квартал (01.01-30.06)" },
                new SelectListItem { Value = "3", Text = "3-ти квартал (01.01-30.09)" },
                new SelectListItem { Value = "4", Text = "4-ти квартал (01.01-31.12)" }
            };
            QuarterOptions = new SelectList(quarters, "Value", "Text");
        }

        public void InitializeYearOptions()
        {
            int currentYear = DateTime.Now.Year;
            int startYear = 2025; // Adjust as needed
            var years = new List<SelectListItem>();
            for (int year = currentYear; year >= startYear; year--)
            {
                years.Add(new SelectListItem { Value = year.ToString(), Text = year.ToString() });
            }
            YearOptions = new SelectList(years, "Value", "Text");
            if (SelectedYear == null || SelectedYear == 0)
                SelectedYear = currentYear;
        }

        private bool ValidateTableData()
        {
            // Check all editable fields for null and non-negative values
            var errors = new List<string>();
            if (TableData.BrojDogovori_ReosiguritelnoPokritie == null || TableData.BrojDogovori_ReosiguritelnoPokritie < 0)
                errors.Add("Број на договори - Реосигурително покритие е задолжително и мора да биде >= 0.");
            if (TableData.BrojDogovori_ProdazhbaOsteteniPredmeti == null || TableData.BrojDogovori_ProdazhbaOsteteniPredmeti < 0)
                errors.Add("Број на договори - Продажба оштетени предмети е задолжително и мора да биде >= 0.");
            if (TableData.BrojDogovori_PrezemanjeMerkiZaRizici == null || TableData.BrojDogovori_PrezemanjeMerkiZaRizici < 0)
                errors.Add("Број на договори - Преземање мерки за ризици е задолжително и мора да биде >= 0.");
            if (TableData.BrojDogovori_ProcenaNaRizik == null || TableData.BrojDogovori_ProcenaNaRizik < 0)
                errors.Add("Број на договори - Процена на ризик е задолжително и мора да биде >= 0.");
            if (TableData.BrojDogovori_IntelektualniTehnickiUslugi == null || TableData.BrojDogovori_IntelektualniTehnickiUslugi < 0)
                errors.Add("Број на договори - Интелектуални и технички услуги е задолжително и мора да биде >= 0.");

            if (TableData.RealiziranaProvizija_ReosiguritelnoPokritie == null || TableData.RealiziranaProvizija_ReosiguritelnoPokritie < 0)
                errors.Add("Реализирана провизија - Реосигурително покритие е задолжително и мора да биде >= 0.");
            if (TableData.RealiziranaProvizija_ProdazhbaOsteteniPredmeti == null || TableData.RealiziranaProvizija_ProdazhbaOsteteniPredmeti < 0)
                errors.Add("Реализирана провизија - Продажба оштетени предмети е задолжително и мора да биде >= 0.");
            if (TableData.RealiziranaProvizija_PrezemanjeMerkiZaRizici == null || TableData.RealiziranaProvizija_PrezemanjeMerkiZaRizici < 0)
                errors.Add("Реализирана провизија - Преземање мерки за ризици е задолжително и мора да биде >= 0.");
            if (TableData.RealiziranaProvizija_ProcenaNaRizik == null || TableData.RealiziranaProvizija_ProcenaNaRizik < 0)
                errors.Add("Реализирана провизија - Процена на ризик е задолжително и мора да биде >= 0.");
            if (TableData.RealiziranaProvizija_IntelektualniTehnickiUslugi == null || TableData.RealiziranaProvizija_IntelektualniTehnickiUslugi < 0)
                errors.Add("Реализирана провизија - Интелектуални и технички услуги е задолжително и мора да биде >= 0.");

            foreach (var error in errors)
            {
                ModelState.AddModelError(string.Empty, error);
            }
            return errors.Count == 0;
        }

        public async Task<IActionResult> OnPostExportExcelAsync()
        {
            System.Diagnostics.Debug.WriteLine("OBD2 OnPostExportExcelAsync: ActivityRows received from form:");
            if (ActivityRows != null)
            {
                for (int i = 0; i < ActivityRows.Count; i++)
                {
                    var row = ActivityRows[i];
                    System.Diagnostics.Debug.WriteLine($"Row {i}: Description={row.Description}, BrojDogovori={row.BrojDogovori}, RealiziranaProvizija={row.RealiziranaProvizija}");
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("ActivityRows is null");
            }

            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear ?? DateTime.Now.Year);
            
            // First, check if OBD1 data exists for this period
            bool hasObd1Data = false;
            using (var conn = new Microsoft.Data.SqlClient.SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            using (var cmd = new Microsoft.Data.SqlClient.SqlCommand(@"SELECT TOP 1 * FROM ASOKvartalenIzvestajArhiva WHERE StartDate = @StartDate AND EndDate = @EndDate AND (UsernameCreated IS NULL OR UsernameCreated NOT LIKE '%_PREVIEW%') ORDER BY DateCreated DESC", conn))
            {
                cmd.Parameters.AddWithValue("@StartDate", startDate);
                cmd.Parameters.AddWithValue("@EndDate", endDate);
                await conn.OpenAsync();
                using (var reader = await cmd.ExecuteReaderAsync())
                {
                    if (await reader.ReadAsync())
                    {
                        hasObd1Data = true;
                    }
                }
            }

            // If no OBD1 data exists, return error message
            if (!hasObd1Data)
            {
                return Content("Нема потврдени податоци за ОБД1 за избраниот квартал. Мора прво да се генерира и потврди ОБД1 извештајот.", "text/plain");
            }
            
            // Check if confirmed data exists
            bool hasConfirmedData = false;
            try
            {
                using (var conn = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                using (var cmd = new SqlCommand(@"SELECT TOP 1 * FROM ASOKvartalenIzvestajObd2Arhiva WHERE StartDate = @StartDate AND EndDate = @EndDate AND (UsernameCreated IS NULL OR UsernameCreated NOT LIKE '%_PREVIEW%') ORDER BY DateCreated DESC", conn))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    await conn.OpenAsync();
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            hasConfirmedData = true;
                        }
                    }
                }
            }
            catch (SqlException ex) when (ex.Message.Contains("Invalid column name"))
            {
                // Table does not have StartDate/EndDate columns
            }

            List<ActivityRowModel> exportRows;
            
            if (hasConfirmedData)
            {
                // If confirmed data exists, export from confirmed data
                exportRows = await LoadExportRowsAsync(startDate, endDate);
            }
            else
            {
                // If no confirmed data, save current form data as preview and export from that
                if (ActivityRows != null && ActivityRows.Any(r => (r.BrojDogovori ?? 0) != 0 || (r.RealiziranaProvizija ?? 0) != 0))
                {
                    // Clean up any existing preview data for this period
                    await CleanupPreviewDataAsync(startDate, endDate);
                    // Save current form data as preview
                    await SaveToArchiveAsync(startDate, endDate, true);
                }
                // Load the preview data (which now includes the current form data)
                exportRows = await LoadExportRowsAsync(startDate, endDate);
            }

            // Ensure all values are set to 0 if null
            foreach (var row in exportRows)
            {
                row.BrojDogovori = row.BrojDogovori ?? 0;
                row.RealiziranaProvizija = row.RealiziranaProvizija ?? 0m;
            }

            if (exportRows == null || exportRows.Count == 0)
                return Content("Нема податоци за извоз.", "text/plain");

            using (var package = new ExcelPackage())
            {
                var ws = package.Workbook.Worksheets.Add("OBD2 Report");
                ws.Cells[1, 1, 1, 5].Merge = true;
                ws.Cells[1, 1].Value = "Образец: obd2";
                ws.Cells[1, 1].Style.Font.Bold = true;
                ws.Cells[1, 1].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                ws.Row(1).Height = 24;
                ws.Cells[2, 3].Value = "Број на договори";
                ws.Cells[2, 4].Value = "Реализирана провизија";
                ws.Cells[2, 3, 2, 4].Style.Font.Bold = true;
                ws.Cells[2, 3, 2, 4].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                ws.Cells[2, 3, 2, 4].Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
                ws.Cells[3, 1].Value = "";
                ws.Cells[3, 2].Value = "Опис";
                ws.Cells[3, 3].Value = "";
                ws.Cells[3, 4].Value = "";
                ws.Cells[3, 1, 3, 4].Style.Font.Bold = true;
                ws.Cells[3, 1, 3, 4].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                ws.Cells[3, 1, 3, 4].Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
                int dataStartRow = 4;
                for (int i = 0; i < exportRows.Count; i++)
                {
                    var r = exportRows[i];
                    ws.Cells[dataStartRow + i, 1].Value = (i + 1);
                    ws.Cells[dataStartRow + i, 2].Value = r.Description;
                    ws.Cells[dataStartRow + i, 3].Value = r.BrojDogovori ?? 0;
                    ws.Cells[dataStartRow + i, 4].Value = r.RealiziranaProvizija ?? 0m;
                    // Format as clean number if it's a whole number
                    if (r.RealiziranaProvizija.HasValue && r.RealiziranaProvizija.Value % 1 == 0)
                    {
                        ws.Cells[dataStartRow + i, 4].Style.Numberformat.Format = "0";
                    }
                    ws.Cells[dataStartRow + i, 1, dataStartRow + i, 4].Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
                    ws.Cells[dataStartRow + i, 1].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                    ws.Cells[dataStartRow + i, 3].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                    ws.Cells[dataStartRow + i, 4].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                    ws.Cells[dataStartRow + i, 2].Style.Font.Size = 9;
                    ws.Cells[dataStartRow + i, 2].Style.WrapText = true;
                    ws.Cells[dataStartRow + i, 2].Style.VerticalAlignment = OfficeOpenXml.Style.ExcelVerticalAlignment.Center;
                }
                int totalRow = dataStartRow + exportRows.Count;
                ws.Cells[totalRow, 1].Value = "";
                ws.Cells[totalRow, 2].Value = "ВКУПНО";
                ws.Cells[totalRow, 3].Value = exportRows.Sum(x => x.BrojDogovori ?? 0);
                var totalRealizirana = exportRows.Sum(x => x.RealiziranaProvizija ?? 0);
                ws.Cells[totalRow, 4].Value = totalRealizirana;
                if (totalRealizirana % 1 == 0)
                {
                    ws.Cells[totalRow, 4].Style.Numberformat.Format = "0";
                }
                ws.Cells[totalRow, 1, totalRow, 4].Style.Font.Bold = true;
                ws.Cells[totalRow, 1, totalRow, 4].Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
                ws.Cells[totalRow, 2].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                ws.Cells[totalRow, 3].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                ws.Cells[totalRow, 4].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                ws.Column(1).Width = 5;
                ws.Column(2).Width = 60;
                ws.Column(3).Width = 18;
                ws.Column(4).Width = 22;
                int lastRow = totalRow;
                ws.Cells[2, 1, lastRow, 4].Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                ws.Cells[2, 1, lastRow, 4].Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                ws.Cells[2, 1, lastRow, 4].Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                ws.Cells[2, 1, lastRow, 4].Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                var stream = new MemoryStream();
                package.SaveAs(stream);
                stream.Position = 0;
                string fileName = $"OBD2_{SelectedYear}_Q{SelectedQuarter}.xlsx";
                return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
        }

        private string GetQuarterDisplayName(string quarter)
        {
            return quarter switch
            {
                "1" => "1-ви квартал (01.01-31.03)",
                "2" => "2-ри квартал (01.01-30.06)",
                "3" => "3-ти квартал (01.01-30.09)",
                "4" => "4-ти квартал (01.01-31.12)",
                _ => ""
            };
        }

        public async Task<IActionResult> OnPostExportPdfAsync()
        {
            System.Diagnostics.Debug.WriteLine("OBD2 OnPostExportPdfAsync: ActivityRows received from form:");
            if (ActivityRows != null)
            {
                for (int i = 0; i < ActivityRows.Count; i++)
                {
                    var row = ActivityRows[i];
                    System.Diagnostics.Debug.WriteLine($"Row {i}: Description={row.Description}, BrojDogovori={row.BrojDogovori}, RealiziranaProvizija={row.RealiziranaProvizija}");
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("ActivityRows is null");
            }

            var (startDate, endDate) = GetQuarterDateRange(SelectedQuarter, SelectedYear ?? DateTime.Now.Year);
            
            // First, check if OBD1 data exists for this period
            bool hasObd1Data = false;
            using (var conn = new Microsoft.Data.SqlClient.SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            using (var cmd = new Microsoft.Data.SqlClient.SqlCommand(@"SELECT TOP 1 * FROM ASOKvartalenIzvestajArhiva WHERE StartDate = @StartDate AND EndDate = @EndDate AND (UsernameCreated IS NULL OR UsernameCreated NOT LIKE '%_PREVIEW%') ORDER BY DateCreated DESC", conn))
            {
                cmd.Parameters.AddWithValue("@StartDate", startDate);
                cmd.Parameters.AddWithValue("@EndDate", endDate);
                await conn.OpenAsync();
                using (var reader = await cmd.ExecuteReaderAsync())
                {
                    if (await reader.ReadAsync())
                    {
                        hasObd1Data = true;
                    }
                }
            }

            // If no OBD1 data exists, return error message
            if (!hasObd1Data)
            {
                return Content("Нема потврдени податоци за ОБД1 за избраниот квартал. Мора прво да се генерира и потврди ОБД1 извештајот.", "text/plain");
            }
            
            // Check if confirmed data exists
            bool hasConfirmedData = false;
            try
            {
                using (var conn = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                using (var cmd = new SqlCommand(@"SELECT TOP 1 * FROM ASOKvartalenIzvestajObd2Arhiva WHERE StartDate = @StartDate AND EndDate = @EndDate AND (UsernameCreated IS NULL OR UsernameCreated NOT LIKE '%_PREVIEW%') ORDER BY DateCreated DESC", conn))
                {
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    await conn.OpenAsync();
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            hasConfirmedData = true;
                        }
                    }
                }
            }
            catch (SqlException ex) when (ex.Message.Contains("Invalid column name"))
            {
                // Table does not have StartDate/EndDate columns
            }

            List<ActivityRowModel> exportRows;
            
            if (hasConfirmedData)
            {
                // If confirmed data exists, export from confirmed data
                exportRows = await LoadExportRowsAsync(startDate, endDate);
            }
            else
            {
                // If no confirmed data, save current form data as preview and export from that
                if (ActivityRows != null && ActivityRows.Any(r => (r.BrojDogovori ?? 0) != 0 || (r.RealiziranaProvizija ?? 0) != 0))
                {
                    // Clean up any existing preview data for this period
                    await CleanupPreviewDataAsync(startDate, endDate);
                    // Save current form data as preview
                    await SaveToArchiveAsync(startDate, endDate, true);
                }
                // Load the preview data (which now includes the current form data)
                exportRows = await LoadExportRowsAsync(startDate, endDate);
            }

            // Ensure all values are set to 0 if null
            foreach (var row in exportRows)
            {
                row.BrojDogovori = row.BrojDogovori ?? 0;
                row.RealiziranaProvizija = row.RealiziranaProvizija ?? 0m;
            }

            if (exportRows == null || exportRows.Count == 0)
                return Content("Нема податоци за извоз.", "text/plain");

            // Calculate totals
            var totalBrojDogovori = exportRows.Sum(x => x.BrojDogovori ?? 0);
            var totalRealizirana = exportRows.Sum(x => x.RealiziranaProvizija ?? 0);

            // Build HTML content
            var sb = new StringBuilder();
            sb.AppendLine("<!DOCTYPE html>");
            sb.AppendLine("<html>");
            sb.AppendLine("<head>");
            sb.AppendLine("<meta charset='utf-8'>");
            sb.AppendLine("<style>");
            sb.AppendLine("body { font-family: Arial, sans-serif; margin: 15px; font-size: 10px; }");
            sb.AppendLine(".header { margin-bottom: 15px; }");
            sb.AppendLine(".company-info { margin-bottom: 8px; }");
            sb.AppendLine(".company-name { font-weight: bold; font-size: 12px; }");
            sb.AppendLine(".title { text-align: center; font-weight: bold; font-size: 14px; margin: 15px 0; }");
            sb.AppendLine("table { width: 100%; border-collapse: collapse; margin-top: 15px; }");
            sb.AppendLine("th, td { border: 1px solid #000; padding: 4px; text-align: center; font-size: 9px; }");
            sb.AppendLine("th { background-color: #f0f0f0; font-weight: bold; }");
            sb.AppendLine(".totals-row { font-weight: bold; background-color: #f0f0f0; }");
            sb.AppendLine(".merged-cell { text-align: center; }");
            sb.AppendLine("</style>");
            sb.AppendLine("</head>");
            sb.AppendLine("<body>");
            
            // Header section
            sb.AppendLine("<div class='header'>");
            sb.AppendLine("<div class='company-info'>");
            sb.AppendLine("<span>Осигурително брокерско друштво: <span class='company-name'>ОБД ИНКО АД СКОПЈЕ</span></span>");
            sb.AppendLine("</div>");
            sb.AppendLine("<div class='company-info'>");
            sb.AppendLine($"<span>Година: {SelectedYear}</span>");
            sb.AppendLine("</div>");
            sb.AppendLine("<div class='company-info'>");
            sb.AppendLine($"<span>Период: {GetQuarterDisplayName(SelectedQuarter)}</span>");
            sb.AppendLine("</div>");
            sb.AppendLine("<div class='company-info'>");
            sb.AppendLine("<span>Друштво за осигурување:</span>");
            sb.AppendLine("</div>");
            sb.AppendLine("</div>");
            
            // Title
            sb.AppendLine("<div class='title'>Образец: obd2</div>");
            
            // Table
            sb.AppendLine("<table>");
            
            // First header row
            sb.AppendLine("<tr>");
            sb.AppendLine("<th colspan='2' style='background-color: #f0f0f0;'></th>");
            sb.AppendLine("<th>Број на договори</th>");
            sb.AppendLine("<th>Реализирана провизија</th>");
            sb.AppendLine("</tr>");
            
            // Second header row
            sb.AppendLine("<tr>");
            sb.AppendLine("<th>Вид на осигурување</th>");
            sb.AppendLine("<th>Опис</th>");
            sb.AppendLine("<th>100</th>");
            sb.AppendLine("<th>200</th>");
            sb.AppendLine("</tr>");
            
            // Data rows
            for (int i = 0; i < exportRows.Count; i++)
            {
                var row = exportRows[i];
                sb.AppendLine("<tr>");
                sb.AppendLine($"<td>{i + 1}</td>");
                sb.AppendLine($"<td>{row.Description}</td>");
                sb.AppendLine($"<td>{row.BrojDogovori?.ToString() ?? ""}</td>");
                sb.AppendLine($"<td>{(row.RealiziranaProvizija % 1 == 0 ? row.RealiziranaProvizija?.ToString("N0") : row.RealiziranaProvizija?.ToString("N2")) ?? ""}</td>");
                sb.AppendLine("</tr>");
            }
            
            // Totals row
            sb.AppendLine("<tr class='totals-row'>");
            sb.AppendLine("<td colspan='2'>ВКУПНО</td>");
            sb.AppendLine($"<td>{totalBrojDogovori}</td>");
            sb.AppendLine($"<td>{(totalRealizirana % 1 == 0 ? totalRealizirana.ToString("N0") : totalRealizirana.ToString("N2"))}</td>");
            sb.AppendLine("</tr>");
            
            sb.AppendLine("</table>");
            sb.AppendLine("</body>");
            sb.AppendLine("</html>");
            
            // Convert HTML to PDF using iText7
            using (var stream = new MemoryStream())
            {
                using (var writer = new PdfWriter(stream))
                {
                    HtmlConverter.ConvertToPdf(sb.ToString(), writer);
                }
                
                string fileName = $"OBD2_{SelectedYear}_Q{SelectedQuarter}.pdf";
                return File(stream.ToArray(), "application/pdf", fileName);
            }
        }
    }
}

