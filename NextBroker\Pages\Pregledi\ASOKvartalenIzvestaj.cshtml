@page
@model NextBroker.Pages.Pregledi.ASOKvartalenIzvestajModel
@{
    ViewData["Title"] = "АСО Квартален Извештај";
}

<div class="container-fluid">
    <h2>@ViewData["Title"]</h2>

    <form method="post" class="mb-4">
        <div class="row">
            <div class="col-md-3">
                <div class="form-group">
                    <label for="SelectedYear">Година</label>
                    <select asp-for="SelectedYear" asp-items="Model.YearOptions" class="form-control">
                        <option value="">Изберете година</option>
                    </select>
                    <span asp-validation-for="SelectedYear" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label for="SelectedQuarter">Квартал</label>
                    <select asp-for="SelectedQuarter" asp-items="Model.QuarterOptions" class="form-control">
                        <option value="">Изберете квартал</option>
                    </select>
                    <span asp-validation-for="SelectedQuarter" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-6 d-flex align-items-end">
                @if (Model.ReportData == null)
                {
                    <button type="submit" class="btn btn-primary me-2">Генерирај преглед</button>
                }
                @if (Model.ReportData != null && Model.ReportData.Rows.Count > 0 && !Model.IsConfirmed)
                {
                    <button type="submit" asp-page-handler="Confirm" class="btn btn-success me-2">
                        <i class="fas fa-check me-1"></i> Потврди
                    </button>
                    <button type="submit" asp-page-handler="CancelPreview" class="btn btn-secondary me-2">
                        <i class="fas fa-times me-1"></i> Откажи
                    </button>
                }
            </div>
        </div>
    </form>

    @if (Model.ReportData != null && Model.ReportData.Rows.Count > 0)
    {
        <div class="mb-4">
            <a href="?handler=ExportExcel&SelectedQuarter=@Model.SelectedQuarter&SelectedYear=@Model.SelectedYear" class="btn btn-success me-2">
                <i class="fas fa-file-excel me-1"></i> Export Excel
            </a>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ReportExistsMessage))
    {
        <div class="alert alert-info mt-3">
            <i class="fas fa-info-circle me-2"></i> @Model.ReportExistsMessage
        </div>
    }

    @if (Model.ReportData != null && Model.ReportData.Rows.Count > 0)
    {
        @if (!Model.IsConfirmed)
        {
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i> Ова е преглед на податоците. Кликнете "Потврди" за да ги зачувате во базата на податоци.
            </div>
        }
        else
        {
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i> Податоците се зачувани во базата на податоци. Можете да ги извезете во Excel.
            </div>
        }



        <div class="alert alert-info mb-3">
            <i class="fas fa-info-circle me-2"></i> 
            <strong>Напомена:</strong> Редовите со <span class="text-danger fw-bold">црвен текст</span> претставуваат сторно (откажани) полиси.
        </div>

        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        @foreach (System.Data.DataColumn column in Model.ReportData.Columns)
                        {
                            <th>@column.ColumnName</th>
                        }
                    </tr>
                </thead>
                <tbody>
                    @foreach (System.Data.DataRow row in Model.ReportData.Rows)
                    {
                        bool isStorno = false;
                        // Check if this row has Storno = "Да"
                        foreach (var item in row.ItemArray)
                        {
                            if (item != null && item.ToString().Trim().Equals("Да", StringComparison.OrdinalIgnoreCase))
                            {
                                isStorno = true;
                                break;
                            }
                        }
                        
                        <tr class="@(isStorno ? "text-danger" : "")">
                            @foreach (var item in row.ItemArray)
                            {
                                <td>
                                    @if (item is DateTime date)
                                    {
                                        @date.ToString("dd/MM/yyyy")
                                    }
                                    else if (item is decimal decimalValue)
                                    {
                                        @decimalValue.ToString("N2")
                                    }
                                    else if (item is double doubleValue)
                                    {
                                        @doubleValue.ToString("N2")
                                    }
                                    else if (item is float floatValue)
                                    {
                                        @floatValue.ToString("N2")
                                    }
                                    else
                                    {
                                        @item
                                    }
                                </td>
                            }
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
    else if (Model.ReportData != null)
    {
        <div class="alert alert-info">
            Нема податоци за избраниот период.
        </div>
    }
</div>
