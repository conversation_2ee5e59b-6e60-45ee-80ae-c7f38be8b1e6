using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using Microsoft.Data.SqlClient;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.Rendering;
using iText.Kernel.Pdf;
using iText.Layout;
using iText.Layout.Element;
using iText.Layout.Properties;
using iText.Kernel.Geom;
using iText.IO.Font;
using iText.Kernel.Font;
using iText.IO.Font.Constants;
using System.Text;
using System.IO;

namespace NextBroker.Pages.Finansii
{
    public class KasovIzvestajModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public bool HasAdminAccess { get; private set; }
        public List<KasovIzvestajView> KasovIzvestai { get; set; } = new();
        public List<SelectListItem> TipoviNaPlakanje { get; set; } = new();

        [BindProperty(SupportsGet = true)]
        public DateTime? DatumOd { get; set; } = DateTime.Today;

        [BindProperty(SupportsGet = true)]
        public DateTime? DatumDo { get; set; } = DateTime.Today;

        public class KasovIzvestajView
        {
            public long Id { get; set; }
            public DateTime? DateCreated { get; set; }
            public string UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string UsernameModified { get; set; }
            public long SifrarnikTipNaPlakanjeId { get; set; }
            public string TipNaPlakanje { get; set; }
            public decimal VkupenIznos { get; set; }
            public DateTime DatumNaKasovIzvestaj { get; set; }
            public string BrojNaKasovIzvestaj { get; set; }
            public decimal? IznosNaBankarskiIzvod { get; set; }
            public string BrojNaIzvod { get; set; }
            public long? StavkaPremijaId { get; set; }
            public bool Zatvoren { get; set; }
        }

        public class UplataView
        {
            public long Id { get; set; }
            public DateTime? DateCreated { get; set; }
            public string UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string UsernameModified { get; set; }
            public DateTime UplataDatum { get; set; }
            public string TipNaPlakanje { get; set; }
            public string BrojIzvod { get; set; }
            public decimal Iznos { get; set; }
            public string PolisaBroj { get; set; }
            public bool Neraspredelena { get; set; }
            public bool PovratNaSredstva { get; set; }
            public string BrojNaKasovIzvestaj { get; set; }
            public string BankaNaziv { get; set; }
            public bool KasaPrimi { get; set; }
            public string UplakjacNaziv { get; set; }
            public DateTime? PreraspredelbaDatum { get; set; }
        }

        public class IzvodPremijaSearchResult
        {
            public long Id { get; set; }
            public string BankaNaziv { get; set; }
            public string BrojNaIzvod { get; set; }
            public DateTime DatumNaIzvod { get; set; }
            public decimal Priliv { get; set; }
        }

        public class StavkaPremijaSearchResult
        {
            public long Id { get; set; }
            public string ReferencaUplakjac { get; set; }
            public string PovikBroj { get; set; }
            public string CelNaDoznaka { get; set; }
            public decimal Iznos { get; set; }
            public long? PolisaBroj { get; set; }
        }

        public KasovIzvestajModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public async Task<IActionResult> OnGetAsync()
        {
            if (!await HasPageAccess("KasovIzvestaj"))
            {
                return RedirectToAccessDenied();
            }

            if (!Request.Query.ContainsKey("DatumOd") && !Request.Query.ContainsKey("DatumDo"))
            {
                DatumOd = DateTime.Today;
                DatumDo = DateTime.Today;
            }

            HasAdminAccess = await HasPageAccess("KasovIzvestajAdmin");
            await LoadKasovIzvestai();
            await LoadTipoviNaPlakanje();
            return Page();
        }

        private async Task LoadKasovIzvestai()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                
                // Check if user has pregledsitekasovizvestaj privilege
                bool hasPregledsiteKasovIzvestajAccess = await HasPageAccess("pregledsitekasovizvestaj");
                
                string whereClause = @"WHERE (@DatumOd IS NULL OR k.DatumNaKasovIzvestaj >= @DatumOd)
                    AND (@DatumDo IS NULL OR k.DatumNaKasovIzvestaj <= @DatumDo)";
                
                // Add filtering if user doesn't have pregledsitekasovizvestaj privilege
                if (!hasPregledsiteKasovIzvestajAccess)
                {
                    whereClause += @"
                    AND t.TipNaPlakanje like '%' + (select dbo.VratiImeEkspozituraPoUsername(@Username)) + '%'";
                }
                
                string query = $@"
                    SELECT k.*, t.TipNaPlakanje
                    FROM KasovIzvestaj k
                    LEFT JOIN SifrarnikTipNaPlakanje t ON k.SifrarnikTipNaPlakanjeId = t.Id
                    {whereClause}
                    ORDER BY k.Id DESC";
                
                using (SqlCommand cmd = new SqlCommand(query, connection))
                {
                    cmd.Parameters.AddWithValue("@DatumOd", (object)DatumOd ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@DatumDo", (object)DatumDo ?? DBNull.Value);
                    
                    // Add username parameter if filtering is applied
                    if (!hasPregledsiteKasovIzvestajAccess)
                    {
                        cmd.Parameters.AddWithValue("@Username", HttpContext.Session.GetString("Username"));
                    }
                    
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        KasovIzvestai.Add(new KasovIzvestajView
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            DateModified = reader["DateModified"] as DateTime?,
                            UsernameModified = reader["UsernameModified"] as string,
                            SifrarnikTipNaPlakanjeId = reader.GetInt64(reader.GetOrdinal("SifrarnikTipNaPlakanjeId")),
                            TipNaPlakanje = reader["TipNaPlakanje"] as string,
                            VkupenIznos = reader.GetDecimal(reader.GetOrdinal("VkupenIznos")),
                            DatumNaKasovIzvestaj = reader.GetDateTime(reader.GetOrdinal("DatumNaKasovIzvestaj")),
                            BrojNaKasovIzvestaj = reader["BrojNaKasovIzvestaj"] as string,
                            IznosNaBankarskiIzvod = reader["IznosNaBankarskiIzvod"] as decimal?,
                            BrojNaIzvod = reader["BrojNaIzvod"] as string,
                            StavkaPremijaId = reader["StavkaPremijaId"] as long?,
                            Zatvoren = reader["Zatvoren"] != DBNull.Value && (bool)reader["Zatvoren"]
                        });
                    }
                }
            }
        }

        private async Task LoadTipoviNaPlakanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                
                // Check if user has pregledsitekasovizvestaj privilege
                bool hasPregledsiteKasovIzvestajAccess = await HasPageAccess("pregledsitekasovizvestaj");
                
                string whereClause = "WHERE Id not in (2,5)";
                
                // Add filtering if user doesn't have pregledsitekasovizvestaj privilege
                if (!hasPregledsiteKasovIzvestajAccess)
                {
                    whereClause += @"
                    AND TipNaPlakanje like '%' + (select dbo.VratiImeEkspozituraPoUsername(@Username)) + '%'";
                }
                
                string query = $@"
                    SELECT Id, TipNaPlakanje 
                    FROM SifrarnikTipNaPlakanje 
                    {whereClause}
                    ORDER BY TipNaPlakanje";
                
                using (SqlCommand cmd = new SqlCommand(query, connection))
                {
                    // Add username parameter if filtering is applied
                    if (!hasPregledsiteKasovIzvestajAccess)
                    {
                        cmd.Parameters.AddWithValue("@Username", HttpContext.Session.GetString("Username"));
                    }
                    
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        TipoviNaPlakanje.Add(new SelectListItem
                        {
                            Value = reader["Id"].ToString(),
                            Text = reader["TipNaPlakanje"].ToString()
                        });
                    }
                }
            }
        }

        public async Task<JsonResult> OnGetGetKasovIzvestajAsync(long id)
        {
            if (!await HasPageAccess("KasovIzvestaj"))
                return new JsonResult(new { success = false });

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, IznosNaBankarskiIzvod, BrojNaIzvod, StavkaPremijaId
                    FROM KasovIzvestaj 
                    WHERE Id = @Id", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", id);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    if (await reader.ReadAsync())
                    {
                        return new JsonResult(new
                        {
                            id = reader.GetInt64(0),
                            iznosNaBankarskiIzvod = reader["IznosNaBankarskiIzvod"] as decimal?,
                            brojNaIzvod = reader["BrojNaIzvod"] as string,
                            stavkaPremijaId = reader["StavkaPremijaId"] as long?
                        });
                    }
                }
            }
            return new JsonResult(new { success = false });
        }

        public async Task<JsonResult> OnPostUpdateIznosAsync([FromBody] UpdateIznosModel model)
        {
            if (!await HasPageAccess("KasovIzvestajAdmin"))
                return new JsonResult(new { success = false });

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    UPDATE KasovIzvestaj 
                    SET IznosNaBankarskiIzvod = @IznosNaBankarskiIzvod,
                        BrojNaIzvod = @BrojNaIzvod,
                        StavkaPremijaId = @StavkaPremijaId,
                        DateModified = GETDATE(),
                        UsernameModified = @UsernameModified
                    WHERE Id = @Id", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", model.Id);
                    cmd.Parameters.AddWithValue("@IznosNaBankarskiIzvod", 
                        string.IsNullOrEmpty(model.IznosNaBankarskiIzvod) ? DBNull.Value : decimal.Parse(model.IznosNaBankarskiIzvod));
                    cmd.Parameters.AddWithValue("@BrojNaIzvod", 
                        string.IsNullOrEmpty(model.BrojNaIzvod) ? DBNull.Value : model.BrojNaIzvod);
                    cmd.Parameters.AddWithValue("@StavkaPremijaId", 
                        string.IsNullOrEmpty(model.StavkaPremijaId) ? DBNull.Value : long.Parse(model.StavkaPremijaId));
                    cmd.Parameters.AddWithValue("@UsernameModified", HttpContext.Session.GetString("Username"));

                    await cmd.ExecuteNonQueryAsync();

                    // Call stored procedure if StavkaPremijaId is provided
                    if (!string.IsNullOrEmpty(model.StavkaPremijaId))
                    {
                        using (SqlCommand spCmd = new SqlCommand("StavkaPremijaNeraspredelenaUpdate", connection))
                        {
                            spCmd.CommandType = System.Data.CommandType.StoredProcedure;
                            spCmd.Parameters.AddWithValue("@StavkaPremijaId", long.Parse(model.StavkaPremijaId));
                            spCmd.Parameters.AddWithValue("@Parameter", "update");
                            await spCmd.ExecuteNonQueryAsync();
                        }
                    }

                    return new JsonResult(new { success = true });
                }
            }
        }

        public async Task<JsonResult> OnPostAddKasovIzvestajAsync([FromBody] AddKasovIzvestajModel model)
        {
            if (!await HasPageAccess("KasovIzvestajAdmin"))
                return new JsonResult(new { success = false });

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
               
                // Check if record already exists for this date and tip na plakanje
                using (SqlCommand checkCmd = new SqlCommand(@"
                    SELECT COUNT(1) 
                    FROM KasovIzvestaj 
                    WHERE DatumNaKasovIzvestaj = @DatumNaKasovIzvestaj 
                    AND SifrarnikTipNaPlakanjeId = @SifrarnikTipNaPlakanjeId", connection))
                {
                    checkCmd.Parameters.AddWithValue("@DatumNaKasovIzvestaj", model.DatumNaKasovIzvestaj);
                    checkCmd.Parameters.AddWithValue("@SifrarnikTipNaPlakanjeId", model.SifrarnikTipNaPlakanjeId);
                    
                    int existingCount = (int)await checkCmd.ExecuteScalarAsync();
                    if (existingCount > 0)
                    {
                        return new JsonResult(new { 
                            success = false, 
                            message = "Касов извештај за овој датум е веќе креиран." 
                        });
                    }
                }

                using (SqlCommand cmd = new SqlCommand(@"
                    INSERT INTO KasovIzvestaj (
                        SifrarnikTipNaPlakanjeId,
                        DatumNaKasovIzvestaj,
                        UsernameCreated
                    ) VALUES (
                        @SifrarnikTipNaPlakanjeId,
                        @DatumNaKasovIzvestaj,
                        @UsernameCreated
                    )", connection))
                {
                    cmd.Parameters.AddWithValue("@SifrarnikTipNaPlakanjeId", model.SifrarnikTipNaPlakanjeId);
                    cmd.Parameters.AddWithValue("@DatumNaKasovIzvestaj", model.DatumNaKasovIzvestaj);
                    cmd.Parameters.AddWithValue("@UsernameCreated", HttpContext.Session.GetString("Username"));

                    await cmd.ExecuteNonQueryAsync();
                    return new JsonResult(new { success = true });
                }
            }
        }

        public async Task<JsonResult> OnGetUplatiForKasovIzvestajAsync(string brojNaKasovIzvestaj)
        {
            if (!await HasPageAccess("KasovIzvestaj"))
                return new JsonResult(new { success = false });

            var uplati = new List<UplataView>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT u.Id, u.DateCreated, u.UsernameCreated, u.DateModified, u.UsernameModified,
                        u.UplataDatum, tp.TipNaPlakanje, kizv.BrojNaIzvod as [BrojIzvod], u.Iznos, u.PolisaBroj,
                        u.Neraspredelena, u.PovratNaSredstva, u.BrojNaKasovIzvestaj,                        
                        dbo.VratiBankaNazivPoBrojNaIzvod(kizv.BrojNaIzvod) as BankaNaziv, 
                        u.KasaPrimi,
                        CASE 
                            WHEN k.KlientFizickoPravnoLice = 'P' THEN k.Naziv
                            WHEN k.KlientFizickoPravnoLice = 'F' THEN CONCAT(k.Ime, ' ', k.Prezime)
                            ELSE NULL
                        END as UplakjacNaziv
                    FROM Uplati u
                    LEFT JOIN SifrarnikTipNaPlakanje tp ON u.SifrarnikTipNaPlakanjeId = tp.Id
                    LEFT JOIN SifrarnikBanki b ON u.SifrarnikBankiId = b.Id
                    LEFT JOIN Klienti k ON u.KlientIdUplakjac = k.Id
                    LEFT JOIN KasovIzvestaj kizv on kizv.BrojNaKasovIzvestaj = u.BrojNaKasovIzvestaj
                    
                    WHERE (u.Storno <> 1)          
                    AND u.BrojNaKasovIzvestaj = @BrojNaKasovIzvestaj
                    AND (u.PreraspredelenaUplata !=1 and u.SredstvaOdPreraspredelenaUplata !=1)
                    ORDER BY u.DateCreated DESC", connection))
                {
                    cmd.Parameters.AddWithValue("@BrojNaKasovIzvestaj", brojNaKasovIzvestaj);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        uplati.Add(new UplataView
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            DateModified = reader["DateModified"] as DateTime?,
                            UsernameModified = reader["UsernameModified"] as string,
                            UplataDatum = reader.GetDateTime(reader.GetOrdinal("UplataDatum")),
                            TipNaPlakanje = reader["TipNaPlakanje"] as string,
                            BrojIzvod = reader["BrojIzvod"] as string,
                            Iznos = reader.GetDecimal(reader.GetOrdinal("Iznos")),
                            PolisaBroj = reader["PolisaBroj"] as string,
                            Neraspredelena = reader["Neraspredelena"] != DBNull.Value && (bool)reader["Neraspredelena"],
                            PovratNaSredstva = reader["PovratNaSredstva"] != DBNull.Value && (bool)reader["PovratNaSredstva"],
                            BrojNaKasovIzvestaj = reader["BrojNaKasovIzvestaj"] as string,
                            BankaNaziv = reader["BankaNaziv"] as string,
                            KasaPrimi = reader["KasaPrimi"] != DBNull.Value && reader.GetInt64(reader.GetOrdinal("KasaPrimi")) == 1,
                            UplakjacNaziv = reader["UplakjacNaziv"] as string
                        });
                    }
                }
            }
            return new JsonResult(new { success = true, data = uplati });
        }

        public async Task<JsonResult> OnGetPreraspredeleniUplatiAsync(string brojNaKasovIzvestaj)
        {
            if (!await HasPageAccess("KasovIzvestaj"))
                return new JsonResult(new { success = false });

            var uplati = new List<UplataView>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                     SELECT u.Id, u.DateCreated, u.UsernameCreated, u.DateModified, u.UsernameModified,
                        u.UplataDatum, 
                        Convert(Date,uzp.datecreated) as [PreraspredelbaDatum],
                        
                        tp.TipNaPlakanje, kizvuzp.BrojNaIzvod as [BrojIzvod], u.Iznos, u.PolisaBroj,
                        u.Neraspredelena, u.PovratNaSredstva, uzp.BrojNaKasovIzvestaj,                        
                        dbo.VratiBankaNazivPoBrojNaIzvod(kizvuzp.BrojNaIzvod) as BankaNaziv, 
                        u.KasaPrimi,
                        CASE 
                            WHEN k.KlientFizickoPravnoLice = 'P' THEN k.Naziv
                            WHEN k.KlientFizickoPravnoLice = 'F' THEN CONCAT(k.Ime, ' ', k.Prezime)
                            ELSE NULL
                        END as UplakjacNaziv
                    FROM Uplati u
                    LEFT JOIN SifrarnikTipNaPlakanje tp ON u.SifrarnikTipNaPlakanjeId = tp.Id
                    LEFT JOIN SifrarnikBanki b ON u.SifrarnikBankiId = b.Id
                    LEFT JOIN Klienti k ON u.KlientIdUplakjac = k.Id
                    LEFT JOIN KasovIzvestaj kizv on kizv.BrojNaKasovIzvestaj = u.BrojNaKasovIzvestaj
                    LEFT JOIN UplatiZaPreraspredelba uzp on u.UplatiZaPreraspredelbaId = uzp.id
                    Left join KasovIzvestaj kizvuzp on kizvuzp.BrojNaKasovIzvestaj = uzp.brojnakasovizvestaj
                    
                    WHERE (u.Storno <> 1)    
                    AND u.UplatiZaPreraspredelbaId in (select id from uplatizapreraspredelba where UplataId in (select Id from Uplati u where  (u.Storno <> 1) and u.BrojNaKasovIzvestaj = @BrojNaKasovIzvestaj AND (u.PreraspredelenaUplata = 1 or u.SredstvaOdPreraspredelenaUplata = 1)))


                    UNION ALL


                    SELECT u.Id, u.DateCreated, u.UsernameCreated, u.DateModified, u.UsernameModified,
                        u.UplataDatum, 
                        Convert(Date,uzp.datecreated) as [PreraspredelbaDatum],
                        
                        tp.TipNaPlakanje, kizvuzp.BrojNaIzvod as [BrojIzvod], u.Iznos, u.PolisaBroj,
                        u.Neraspredelena, u.PovratNaSredstva, uzp.BrojNaKasovIzvestaj,                        
                        dbo.VratiBankaNazivPoBrojNaIzvod(kizvuzp.BrojNaIzvod) as BankaNaziv, 
                        u.KasaPrimi,
                        CASE 
                            WHEN k.KlientFizickoPravnoLice = 'P' THEN k.Naziv
                            WHEN k.KlientFizickoPravnoLice = 'F' THEN CONCAT(k.Ime, ' ', k.Prezime)
                            ELSE NULL
                        END as UplakjacNaziv
                    FROM Uplati u
                    LEFT JOIN SifrarnikTipNaPlakanje tp ON u.SifrarnikTipNaPlakanjeId = tp.Id
                    LEFT JOIN SifrarnikBanki b ON u.SifrarnikBankiId = b.Id
                    LEFT JOIN Klienti k ON u.KlientIdUplakjac = k.Id
                    LEFT JOIN KasovIzvestaj kizv on kizv.BrojNaKasovIzvestaj = u.BrojNaKasovIzvestaj
                    LEFT JOIN UplatiZaPreraspredelba uzp on u.UplatiZaPreraspredelbaId = uzp.id
                    Left join KasovIzvestaj kizvuzp on kizvuzp.BrojNaKasovIzvestaj = uzp.brojnakasovizvestaj
                    Left join UplatiZaPreraspredelba uzp2 on u.id = uzp2.UplataId
                    
                    WHERE (u.Storno <> 1)  
                     
                     AND u.BrojNaKasovIzvestaj = @BrojNaKasovIzvestaj
                    and u.UplatiZaPreraspredelbaid is null
                    and u.PreraspredelenaUplata =1
                    and uzp2.PreostanatIznos >0
                    
                    ORDER BY u.DateCreated DESC", connection))
                {
                    cmd.Parameters.AddWithValue("@BrojNaKasovIzvestaj", brojNaKasovIzvestaj);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        var uplata = new UplataView
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            DateModified = reader["DateModified"] as DateTime?,
                            UsernameModified = reader["UsernameModified"] as string,
                            UplataDatum = reader.GetDateTime(reader.GetOrdinal("UplataDatum")),
                            TipNaPlakanje = reader["TipNaPlakanje"] as string,
                            BrojIzvod = reader["BrojIzvod"] as string,
                            Iznos = reader.GetDecimal(reader.GetOrdinal("Iznos")),
                            PolisaBroj = reader["PolisaBroj"] as string,
                            Neraspredelena = reader["Neraspredelena"] != DBNull.Value && (bool)reader["Neraspredelena"],
                            PovratNaSredstva = reader["PovratNaSredstva"] != DBNull.Value && (bool)reader["PovratNaSredstva"],
                            BrojNaKasovIzvestaj = reader["BrojNaKasovIzvestaj"] as string,
                            BankaNaziv = reader["BankaNaziv"] as string,
                            KasaPrimi = reader["KasaPrimi"] != DBNull.Value && reader.GetInt64(reader.GetOrdinal("KasaPrimi")) == 1,
                            UplakjacNaziv = reader["UplakjacNaziv"] as string
                        };
                        
                        // Add PreraspredelbaDatum if it exists
                        if (reader["PreraspredelbaDatum"] != DBNull.Value)
                        {
                            uplata.PreraspredelbaDatum = reader.GetDateTime(reader.GetOrdinal("PreraspredelbaDatum"));
                        }
                        
                        uplati.Add(uplata);
                    }
                }
            }
            return new JsonResult(new { success = true, data = uplati });
        }

        [ValidateAntiForgeryToken]
        public async Task<JsonResult> OnGetSearchIzvodPremijaAsync(string term)
        {
            if (!await HasPageAccess("KasovIzvestaj"))
                return new JsonResult(new { success = false });

            if (string.IsNullOrWhiteSpace(term))
                return new JsonResult(new List<object>());

            var results = new List<IzvodPremijaSearchResult>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        i.Id,
                        b.Banka as BankaNaziv,
                        i.BrojNaIzvod,
                        i.DatumNaIzvod,
                        i.Priliv
                    FROM IzvodPremija i
                    LEFT JOIN SifrarnikBanki b ON i.SifrarnikBankiId = b.Id
                    WHERE i.BrojNaIzvod LIKE '%' + @Term + '%'
                        OR b.Banka LIKE '%' + @Term + '%'
                    ORDER BY i.DatumNaIzvod DESC", connection))
                {
                    cmd.Parameters.AddWithValue("@Term", term);
                    
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new IzvodPremijaSearchResult
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            BankaNaziv = reader["BankaNaziv"] as string,
                            BrojNaIzvod = reader["BrojNaIzvod"] as string,
                            DatumNaIzvod = reader.GetDateTime(reader.GetOrdinal("DatumNaIzvod")),
                            Priliv = reader.GetDecimal(reader.GetOrdinal("Priliv"))
                        });
                    }
                }
            }
            
            return new JsonResult(results);
        }

        [ValidateAntiForgeryToken]
        public async Task<JsonResult> OnGetSearchStavkaPremijaAsync(string brojNaIzvod)
        {
            if (!await HasPageAccess("KasovIzvestaj"))
                return new JsonResult(new { success = false });

            if (string.IsNullOrWhiteSpace(brojNaIzvod))
                return new JsonResult(new List<object>());

            var results = new List<StavkaPremijaSearchResult>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT 
                        s.Id,
                        s.ReferencaUplakjac,
                        s.PovikBroj,
                        s.CelNaDoznaka,
                        s.Iznos,
                        s.PolisaBroj
                    FROM StavkaPremija s
                    WHERE s.IzvodPremijaId = (
                        SELECT id 
                        FROM IzvodPremija 
                        WHERE BrojNaIzvod = @BrojNaIzvod
                    )
                    AND (s.Storno IS NULL OR s.Storno = 0)
                    ORDER BY s.Id", connection))
                {
                    cmd.Parameters.AddWithValue("@BrojNaIzvod", brojNaIzvod);
                    
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        var result = new StavkaPremijaSearchResult
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            ReferencaUplakjac = reader["ReferencaUplakjac"] as string,
                            PovikBroj = reader["PovikBroj"] as string,
                            CelNaDoznaka = reader["CelNaDoznaka"] as string,
                            Iznos = reader.GetDecimal(reader.GetOrdinal("Iznos")),
                            PolisaBroj = reader["PolisaBroj"] as long?
                        };
                        results.Add(result);
                    }
                }
            }
            
            return new JsonResult(results);
        }

        [ValidateAntiForgeryToken]
        public async Task<JsonResult> OnGetUplatiPreviewAsync(long tipNaPlakanjeId, DateTime datum)
        {
            if (!await HasPageAccess("KasovIzvestaj"))
                return new JsonResult(new { success = false });

            var uplati = new List<UplataView>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT 
                        u.*,
                        tp.TipNaPlakanje,
                        b.Banka as BankaNaziv,
                        CASE 
                            WHEN k.KlientFizickoPravnoLice = 'P' THEN k.Naziv
                            WHEN k.KlientFizickoPravnoLice = 'F' THEN CONCAT(k.Ime, ' ', k.Prezime)
                            ELSE NULL
                        END as UplakjacNaziv
                    FROM Uplati u
                    LEFT JOIN SifrarnikTipNaPlakanje tp ON u.SifrarnikTipNaPlakanjeId = tp.Id
                    LEFT JOIN SifrarnikBanki b ON u.SifrarnikBankiId = b.Id
                    LEFT JOIN Klienti k ON u.KlientIdUplakjac = k.Id
                    WHERE u.SifrarnikTipNaPlakanjeId = @TipNaPlakanjeId
                    AND u.UplataDatum = @Datum
                    AND (u.Storno IS NULL OR u.Storno = 0)
                    AND (u.SredstvaOdPreraspredelenaUplata != 1 and u.PreraspredelenaUplata !=1)
                    AND u.BrojNaKasovIzvestaj IS NULL
                    ORDER BY u.DateCreated DESC", connection))
                {
                    cmd.Parameters.AddWithValue("@TipNaPlakanjeId", tipNaPlakanjeId);
                    cmd.Parameters.AddWithValue("@Datum", datum);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        uplati.Add(new UplataView
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            UplataDatum = reader.GetDateTime(reader.GetOrdinal("UplataDatum")),
                            TipNaPlakanje = reader["TipNaPlakanje"] as string,
                            BrojIzvod = reader["BrojIzvod"] as string,
                            Iznos = reader.GetDecimal(reader.GetOrdinal("Iznos")),
                            PolisaBroj = reader["PolisaBroj"]?.ToString(),
                            Neraspredelena = reader["Neraspredelena"] != DBNull.Value && (bool)reader["Neraspredelena"],
                            PovratNaSredstva = reader["PovratNaSredstva"] != DBNull.Value && (bool)reader["PovratNaSredstva"],
                            KasaPrimi = reader["KasaPrimi"] != DBNull.Value && reader.GetInt64(reader.GetOrdinal("KasaPrimi")) == 1,
                            UplakjacNaziv = reader["UplakjacNaziv"] as string
                        });
                    }
                }
            }
            
            return new JsonResult(new { success = true, data = uplati });
        }

        public class UpdateIznosModel
        {
            public long Id { get; set; }
            public string IznosNaBankarskiIzvod { get; set; }
            public string BrojNaIzvod { get; set; }
            public string StavkaPremijaId { get; set; }
        }

        public class AddKasovIzvestajModel
        {
            public long SifrarnikTipNaPlakanjeId { get; set; }
            public DateTime DatumNaKasovIzvestaj { get; set; }
        }

        public class CloseKasovIzvestajModel
        {
            public long Id { get; set; }
        }

        [ValidateAntiForgeryToken]
        public async Task<JsonResult> OnPostCloseKasovIzvestajAsync([FromBody] CloseKasovIzvestajModel model)
        {
            if (!await HasPageAccess("KasovIzvestajAdmin"))
                return new JsonResult(new { success = false });

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    UPDATE KasovIzvestaj 
                    SET Zatvoren = 1,
                        DateModified = GETDATE(),
                        UsernameModified = @UsernameModified
                    WHERE Id = @Id", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", model.Id);
                    cmd.Parameters.AddWithValue("@UsernameModified", HttpContext.Session.GetString("Username"));

                    await cmd.ExecuteNonQueryAsync();
                    return new JsonResult(new { success = true });
                }
            }
        }

        public async Task<IActionResult> OnGetDownloadPdfAsync(string brojNaKasovIzvestaj)
        {
            if (!await HasPageAccess("KasovIzvestaj"))
                return RedirectToAccessDenied();

            var uplati = await GetUplatiForKasovIzvestajInternalAsync(brojNaKasovIzvestaj);
            var preraspredeleniUplati = await GetPreraspredeleniUplatiForPdfAsync(brojNaKasovIzvestaj);
            
            if (!uplati.Any() && !preraspredeleniUplati.Any())
                return NotFound();

            var memoryStream = new MemoryStream();
            var writer = new PdfWriter(memoryStream);
            var pdf = new PdfDocument(writer);
            var document = new Document(pdf, PageSize.A4.Rotate()); // Landscape orientation

            try
            {
                // Set up font for Cyrillic support
                var fontPath = System.IO.Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "fonts", "arial.ttf");
                PdfFont font = PdfFontFactory.CreateFont(fontPath, PdfEncodings.IDENTITY_H);
                document.SetFont(font);

                // Add title and captions
                var titleTable = new Table(1).UseAllAvailableWidth();
                titleTable.AddCell(new Cell()
                    .Add(new Paragraph($"Касов извештај")
                        .SetFont(font)
                        .SetTextAlignment(TextAlignment.CENTER)
                        .SetFontSize(16)
                        .SetBold())
                    .SetBorder(iText.Layout.Borders.Border.NO_BORDER));

                titleTable.AddCell(new Cell()
                    .Add(new Paragraph($"Број на касов извештај: {brojNaKasovIzvestaj}")
                        .SetFont(font)
                        .SetTextAlignment(TextAlignment.CENTER)
                        .SetFontSize(12))
                    .SetBorder(iText.Layout.Borders.Border.NO_BORDER));

                titleTable.AddCell(new Cell()
                    .Add(new Paragraph($"Датум и време на генерирање: {DateTime.Now:dd.MM.yyyy HH:mm}")
                        .SetFont(font)
                        .SetTextAlignment(TextAlignment.CENTER)
                        .SetFontSize(12))
                    .SetBorder(iText.Layout.Borders.Border.NO_BORDER)
                    .SetPaddingBottom(20));

                document.Add(titleTable);

                // Style for header cells
                Action<Cell> headerStyle = cell => {
                    cell.SetBackgroundColor(new iText.Kernel.Colors.DeviceRgb(240, 240, 240))
                        .SetFont(font)
                        .SetTextAlignment(TextAlignment.CENTER)
                        .SetBold()
                        .SetFontSize(10);
                };

                // Style for data cells
                Action<Cell> dataStyle = cell => {
                    cell.SetFont(font)
                        .SetTextAlignment(TextAlignment.CENTER)
                        .SetFontSize(9)
                        .SetPadding(5);
                };
                
                // Section title style
                Action<Cell> sectionTitleStyle = cell => {
                    cell.SetFont(font)
                        .SetTextAlignment(TextAlignment.LEFT)
                        .SetBold()
                        .SetFontSize(12)
                        .SetBorder(iText.Layout.Borders.Border.NO_BORDER)
                        .SetPaddingTop(10)
                        .SetPaddingBottom(5);
                };

                // First add the main table with regular payments if any exist
                if (uplati.Any())
                {
                    // Add section title
                    var mainTableTitle = new Table(1).UseAllAvailableWidth();
                    mainTableTitle.AddCell(new Cell()
                        .Add(new Paragraph("Основни уплати")
                            .SetFont(font))
                        .SetBorder(iText.Layout.Borders.Border.NO_BORDER)
                        .SetPaddingBottom(5));
                    document.Add(mainTableTitle);
                    
                    // Create main table
                    var table = new Table(new float[] { 
                        1.5f, // Креирано на
                        1.2f, // Креирано од
                        1.2f, // Датум на уплата
                        1.2f, // Тип на плаќање
                        1f,   // Број на извод
                        1f,   // Износ
                        1f,   // Број на полиса
                        0.8f, // Нераспределена
                        0.8f, // Поврат на средства
                        1.2f, // Банка
                        0.8f, // Каса прими
                        1.5f  // Уплаќач
                    }).UseAllAvailableWidth();

                    // Add headers
                    string[] headers = new[] { 
                        "Креирано на", "Креирано од", "Датум на уплата", "Тип на плаќање", 
                        "Број на извод", "Износ", "Број на полиса", "Нераспр.", 
                        "Поврат", "Банка", "Каса", "Уплаќач" 
                    };

                    foreach (var header in headers)
                    {
                        var cell = new Cell().Add(new Paragraph(header));
                        headerStyle(cell);
                        table.AddHeaderCell(cell);
                    }

                    // Add data rows
                    decimal totalRegular = 0;
                    bool alternateRow = false;
                    foreach (var uplata in uplati)
                    {
                        var rowColor = alternateRow ? 
                            new iText.Kernel.Colors.DeviceRgb(249, 249, 249) : 
                            new iText.Kernel.Colors.DeviceRgb(255, 255, 255);

                        Action<Cell> rowStyle = cell => {
                            dataStyle(cell);
                            cell.SetBackgroundColor(rowColor);
                        };

                        var cells = new Cell[] {
                            new Cell().Add(new Paragraph(uplata.DateCreated?.ToString("dd.MM.yyyy HH:mm") ?? "")),
                            new Cell().Add(new Paragraph(uplata.UsernameCreated ?? "")),
                            new Cell().Add(new Paragraph(uplata.UplataDatum.ToString("dd.MM.yyyy"))),
                            new Cell().Add(new Paragraph(uplata.TipNaPlakanje ?? "")),
                            new Cell().Add(new Paragraph(uplata.BrojIzvod ?? "")),
                            new Cell().Add(new Paragraph(uplata.Iznos.ToString("N2"))).SetTextAlignment(TextAlignment.RIGHT),
                            new Cell().Add(new Paragraph(uplata.PolisaBroj ?? "")),
                            new Cell().Add(new Paragraph(uplata.Neraspredelena ? "Да" : "Не")),
                            new Cell().Add(new Paragraph(uplata.PovratNaSredstva ? "Да" : "Не")),
                            new Cell().Add(new Paragraph(uplata.BankaNaziv ?? "")),
                            new Cell().Add(new Paragraph(uplata.KasaPrimi ? "Да" : "Не")),
                            new Cell().Add(new Paragraph(uplata.UplakjacNaziv ?? ""))
                        };

                        foreach (var cell in cells)
                        {
                            rowStyle(cell);
                            table.AddCell(cell);
                        }

                        totalRegular += uplata.Iznos;
                        alternateRow = !alternateRow;
                    }

                    document.Add(table);

                    // Add total with line above
                    var totalTable = new Table(1).UseAllAvailableWidth();
                    totalTable.AddCell(new Cell()
                        .Add(new Paragraph($"Вкупно: {totalRegular:N2} ден.")
                            .SetFont(font)
                            .SetTextAlignment(TextAlignment.RIGHT)
                            .SetBold()
                            .SetFontSize(11))
                        .SetBorder(iText.Layout.Borders.Border.NO_BORDER)
                        .SetPaddingTop(10));

                    document.Add(totalTable);
                }
                
                // Now add the preraspredeleni table if any exist
                if (preraspredeleniUplati.Any())
                {
                    // Add section title with spacing
                    var preraspredeleniTitle = new Table(1).UseAllAvailableWidth();
                    preraspredeleniTitle.AddCell(new Cell()
                        .Add(new Paragraph("Прераспределени уплати")
                            .SetFont(font))
                        .SetBorder(iText.Layout.Borders.Border.NO_BORDER)
                        .SetPaddingTop(20)
                        .SetPaddingBottom(5));
                    document.Add(preraspredeleniTitle);
                    
                    // Create preraspredeleni table
                    var preraspredeleniTable = new Table(new float[] { 
                        1.5f, // Креирано на
                        1.2f, // Креирано од
                        1.2f, // Датум на уплата
                        1.2f, // Датум на прераспределба
                        1.2f, // Тип на плаќање
                        1f,   // Број на извод
                        1f,   // Износ
                        1f,   // Број на полиса
                        0.8f, // Нераспределена
                        0.8f, // Поврат на средства
                        1.2f, // Банка
                        0.8f, // Каса прими
                        1.5f  // Уплаќач
                    }).UseAllAvailableWidth();

                    // Add headers
                    string[] preraspredeleniHeaders = new[] { 
                        "Креирано на", "Креирано од", "Датум на уплата", "Датум на прераспределба", "Тип на плаќање", 
                        "Број на извод", "Износ", "Број на полиса", "Нераспр.", 
                        "Поврат", "Банка", "Каса", "Уплаќач" 
                    };

                    foreach (var header in preraspredeleniHeaders)
                    {
                        var cell = new Cell().Add(new Paragraph(header));
                        headerStyle(cell);
                        preraspredeleniTable.AddHeaderCell(cell);
                    }

                    // Add data rows
                    decimal totalPreraspredeleni = 0;
                    bool alternateRow = false;
                    foreach (var uplata in preraspredeleniUplati)
                    {
                        var rowColor = alternateRow ? 
                            new iText.Kernel.Colors.DeviceRgb(249, 249, 249) : 
                            new iText.Kernel.Colors.DeviceRgb(255, 255, 255);

                        Action<Cell> rowStyle = cell => {
                            dataStyle(cell);
                            cell.SetBackgroundColor(rowColor);
                        };

                        var cells = new Cell[] {
                            new Cell().Add(new Paragraph(uplata.DateCreated?.ToString("dd.MM.yyyy HH:mm") ?? "")),
                            new Cell().Add(new Paragraph(uplata.UsernameCreated ?? "")),
                            new Cell().Add(new Paragraph(uplata.UplataDatum.ToString("dd.MM.yyyy"))),
                            new Cell().Add(new Paragraph(uplata.PreraspredelbaDatum?.ToString("dd.MM.yyyy") ?? "")),
                            new Cell().Add(new Paragraph(uplata.TipNaPlakanje ?? "")),
                            new Cell().Add(new Paragraph(uplata.BrojIzvod ?? "")),
                            new Cell().Add(new Paragraph(uplata.Iznos.ToString("N2"))).SetTextAlignment(TextAlignment.RIGHT),
                            new Cell().Add(new Paragraph(uplata.PolisaBroj ?? "")),
                            new Cell().Add(new Paragraph(uplata.Neraspredelena ? "Да" : "Не")),
                            new Cell().Add(new Paragraph(uplata.PovratNaSredstva ? "Да" : "Не")),
                            new Cell().Add(new Paragraph(uplata.BankaNaziv ?? "")),
                            new Cell().Add(new Paragraph(uplata.KasaPrimi ? "Да" : "Не")),
                            new Cell().Add(new Paragraph(uplata.UplakjacNaziv ?? ""))
                        };

                        foreach (var cell in cells)
                        {
                            rowStyle(cell);
                            preraspredeleniTable.AddCell(cell);
                        }

                        totalPreraspredeleni += uplata.Iznos;
                        alternateRow = !alternateRow;
                    }

                    document.Add(preraspredeleniTable);

                    // Add total with line above
                    var totalPreraspredeleniTable = new Table(1).UseAllAvailableWidth();
                    totalPreraspredeleniTable.AddCell(new Cell()
                        .Add(new Paragraph($"Вкупно прераспределени: {totalPreraspredeleni:N2} ден.")
                            .SetFont(font)
                            .SetTextAlignment(TextAlignment.RIGHT)
                            .SetBold()
                            .SetFontSize(11))
                        .SetBorder(iText.Layout.Borders.Border.NO_BORDER)
                        .SetPaddingTop(10));

                    document.Add(totalPreraspredeleniTable);
                }

                document.Close();

                return File(memoryStream.ToArray(), "application/pdf", $"kasov_izvestaj_{brojNaKasovIzvestaj}.pdf");
            }
            catch (Exception)
            {
                document.Close();
                return BadRequest("Грешка при генерирање на PDF документот.");
            }
        }

        private async Task<List<UplataView>> GetUplatiForKasovIzvestajInternalAsync(string brojNaKasovIzvestaj)
        {
            var uplati = new List<UplataView>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                     SELECT u.Id, u.DateCreated, u.UsernameCreated, u.DateModified, u.UsernameModified,
                        u.UplataDatum, tp.TipNaPlakanje, kizv.BrojNaIzvod as [BrojIzvod], u.Iznos, u.PolisaBroj,
                        u.Neraspredelena, u.PovratNaSredstva, u.BrojNaKasovIzvestaj,                        
                        dbo.VratiBankaNazivPoBrojNaIzvod(kizv.BrojNaIzvod) as BankaNaziv,
                        u.KasaPrimi,
                        CASE 
                            WHEN k.KlientFizickoPravnoLice = 'P' THEN k.Naziv
                            WHEN k.KlientFizickoPravnoLice = 'F' THEN CONCAT(k.Ime, ' ', k.Prezime)
                            ELSE NULL
                        END as UplakjacNaziv
                    FROM Uplati u
                    LEFT JOIN SifrarnikTipNaPlakanje tp ON u.SifrarnikTipNaPlakanjeId = tp.Id
                    LEFT JOIN SifrarnikBanki b ON u.SifrarnikBankiId = b.Id
                    LEFT JOIN Klienti k ON u.KlientIdUplakjac = k.Id
                    LEFT JOIN KasovIzvestaj kizv on kizv.BrojNaKasovIzvestaj = u.BrojNaKasovIzvestaj
                    
                    WHERE (u.Storno <> 1)          
                    AND u.BrojNaKasovIzvestaj = @BrojNaKasovIzvestaj
                    AND (u.PreraspredelenaUplata !=1 and u.SredstvaOdPreraspredelenaUplata !=1)
                    ORDER BY u.DateCreated DESC", connection))
                {
                    cmd.Parameters.AddWithValue("@BrojNaKasovIzvestaj", brojNaKasovIzvestaj);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    
                    while (await reader.ReadAsync())
                    {
                        uplati.Add(new UplataView
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            DateModified = reader["DateModified"] as DateTime?,
                            UsernameModified = reader["UsernameModified"] as string,
                            UplataDatum = reader.GetDateTime(reader.GetOrdinal("UplataDatum")),
                            TipNaPlakanje = reader["TipNaPlakanje"] as string,
                            BrojIzvod = reader["BrojIzvod"] as string,
                            Iznos = reader.GetDecimal(reader.GetOrdinal("Iznos")),
                            PolisaBroj = reader["PolisaBroj"] as string,
                            Neraspredelena = reader["Neraspredelena"] != DBNull.Value && (bool)reader["Neraspredelena"],
                            PovratNaSredstva = reader["PovratNaSredstva"] != DBNull.Value && (bool)reader["PovratNaSredstva"],
                            BrojNaKasovIzvestaj = reader["BrojNaKasovIzvestaj"] as string,
                            BankaNaziv = reader["BankaNaziv"] as string,
                            KasaPrimi = reader["KasaPrimi"] != DBNull.Value && reader.GetInt64(reader.GetOrdinal("KasaPrimi")) == 1,
                            UplakjacNaziv = reader["UplakjacNaziv"] as string
                        });
                    }
                }
            }
            
            return uplati;
        }
        
        private async Task<List<UplataView>> GetPreraspredeleniUplatiForPdfAsync(string brojNaKasovIzvestaj)
        {
            var uplati = new List<UplataView>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT u.Id, u.DateCreated, u.UsernameCreated, u.DateModified, u.UsernameModified,
                        u.UplataDatum, 
                        Convert(Date,uzp.datecreated) as [PreraspredelbaDatum],
                        
                        tp.TipNaPlakanje, kizvuzp.BrojNaIzvod as [BrojIzvod], u.Iznos, u.PolisaBroj,
                        u.Neraspredelena, u.PovratNaSredstva, uzp.BrojNaKasovIzvestaj,                        
                        dbo.VratiBankaNazivPoBrojNaIzvod(kizvuzp.BrojNaIzvod) as BankaNaziv, 
                        u.KasaPrimi,
                        CASE 
                            WHEN k.KlientFizickoPravnoLice = 'P' THEN k.Naziv
                            WHEN k.KlientFizickoPravnoLice = 'F' THEN CONCAT(k.Ime, ' ', k.Prezime)
                            ELSE NULL
                        END as UplakjacNaziv
                    FROM Uplati u
                    LEFT JOIN SifrarnikTipNaPlakanje tp ON u.SifrarnikTipNaPlakanjeId = tp.Id
                    LEFT JOIN SifrarnikBanki b ON u.SifrarnikBankiId = b.Id
                    LEFT JOIN Klienti k ON u.KlientIdUplakjac = k.Id
                    LEFT JOIN KasovIzvestaj kizv on kizv.BrojNaKasovIzvestaj = u.BrojNaKasovIzvestaj
                    LEFT JOIN UplatiZaPreraspredelba uzp on u.UplatiZaPreraspredelbaId = uzp.id
                    Left join KasovIzvestaj kizvuzp on kizvuzp.BrojNaKasovIzvestaj = uzp.brojnakasovizvestaj
                    
                    WHERE (u.Storno <> 1)    
                    AND u.UplatiZaPreraspredelbaId in (select id from uplatizapreraspredelba where UplataId in (select Id from Uplati u where  (u.Storno <> 1) and u.BrojNaKasovIzvestaj = @BrojNaKasovIzvestaj AND (u.PreraspredelenaUplata = 1 or u.SredstvaOdPreraspredelenaUplata = 1)))


                    UNION ALL


                    SELECT u.Id, u.DateCreated, u.UsernameCreated, u.DateModified, u.UsernameModified,
                        u.UplataDatum, 
                        Convert(Date,uzp.datecreated) as [PreraspredelbaDatum],
                        
                        tp.TipNaPlakanje, kizvuzp.BrojNaIzvod as [BrojIzvod], u.Iznos, u.PolisaBroj,
                        u.Neraspredelena, u.PovratNaSredstva, uzp.BrojNaKasovIzvestaj,                        
                        dbo.VratiBankaNazivPoBrojNaIzvod(kizvuzp.BrojNaIzvod) as BankaNaziv, 
                        u.KasaPrimi,
                        CASE 
                            WHEN k.KlientFizickoPravnoLice = 'P' THEN k.Naziv
                            WHEN k.KlientFizickoPravnoLice = 'F' THEN CONCAT(k.Ime, ' ', k.Prezime)
                            ELSE NULL
                        END as UplakjacNaziv
                    FROM Uplati u
                    LEFT JOIN SifrarnikTipNaPlakanje tp ON u.SifrarnikTipNaPlakanjeId = tp.Id
                    LEFT JOIN SifrarnikBanki b ON u.SifrarnikBankiId = b.Id
                    LEFT JOIN Klienti k ON u.KlientIdUplakjac = k.Id
                    LEFT JOIN KasovIzvestaj kizv on kizv.BrojNaKasovIzvestaj = u.BrojNaKasovIzvestaj
                    LEFT JOIN UplatiZaPreraspredelba uzp on u.UplatiZaPreraspredelbaId = uzp.id
                    Left join KasovIzvestaj kizvuzp on kizvuzp.BrojNaKasovIzvestaj = uzp.brojnakasovizvestaj
                    Left join UplatiZaPreraspredelba uzp2 on u.id = uzp2.UplataId
                    
                    WHERE (u.Storno <> 1)  
                     
                     AND u.BrojNaKasovIzvestaj = @BrojNaKasovIzvestaj
                    and u.UplatiZaPreraspredelbaid is null
                    and u.PreraspredelenaUplata =1
                    and uzp2.PreostanatIznos >0
                    
                    ORDER BY u.DateCreated DESC", connection))
                {
                    cmd.Parameters.AddWithValue("@BrojNaKasovIzvestaj", brojNaKasovIzvestaj);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    
                    while (await reader.ReadAsync())
                    {
                        var uplata = new UplataView
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            DateModified = reader["DateModified"] as DateTime?,
                            UsernameModified = reader["UsernameModified"] as string,
                            UplataDatum = reader.GetDateTime(reader.GetOrdinal("UplataDatum")),
                            TipNaPlakanje = reader["TipNaPlakanje"] as string,
                            BrojIzvod = reader["BrojIzvod"] as string,
                            Iznos = reader.GetDecimal(reader.GetOrdinal("Iznos")),
                            PolisaBroj = reader["PolisaBroj"] as string,
                            Neraspredelena = reader["Neraspredelena"] != DBNull.Value && (bool)reader["Neraspredelena"],
                            PovratNaSredstva = reader["PovratNaSredstva"] != DBNull.Value && (bool)reader["PovratNaSredstva"],
                            BrojNaKasovIzvestaj = reader["BrojNaKasovIzvestaj"] as string,
                            BankaNaziv = reader["BankaNaziv"] as string,
                            KasaPrimi = reader["KasaPrimi"] != DBNull.Value && reader.GetInt64(reader.GetOrdinal("KasaPrimi")) == 1,
                            UplakjacNaziv = reader["UplakjacNaziv"] as string
                        };
                        
                        // Add PreraspredelbaDatum if it exists
                        if (reader["PreraspredelbaDatum"] != DBNull.Value)
                        {
                            uplata.PreraspredelbaDatum = reader.GetDateTime(reader.GetOrdinal("PreraspredelbaDatum"));
                        }
                        
                        uplati.Add(uplata);
                    }
                }
            }
            
            return uplati;
        }
    }
} 