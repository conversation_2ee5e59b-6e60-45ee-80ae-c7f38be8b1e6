﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.Data.SqlClient;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;

public class ResetRequestModel : PageModel
{
    private readonly IConfiguration _configuration;
    private readonly MailerService _mailerService;

    public ResetRequestModel(IConfiguration configuration, MailerService mailerService)
    {
        _configuration = configuration;
        _mailerService = mailerService;
    }

    [BindProperty]
    [Required(ErrorMessage = "Задолжително поле!")]
    public string EMB { get; set; }

    public string ErrorMessage { get; set; }
    public string SuccessMessage { get; set; }

    public async Task<IActionResult> OnPostAsync()
    {
        if (string.IsNullOrWhiteSpace(EMB))
        {
            ErrorMessage = "Мора да внесете единствен матичен број.";
            return Page();
        }

        // Generate a random key
        var resetKey = GenerateRandomKey(8);

        // Update the key in the database and fetch the user's email
        var connectionString = _configuration.GetConnectionString("DefaultConnection");

        try
        {
            string userEmail = null;

            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // Fetch the user's email based on the EMB
                using (var selectCommand = new SqlCommand("SELECT email FROM users WHERE emb = @emb", connection))
                {
                    selectCommand.Parameters.AddWithValue("@emb", EMB);

                    var result = await selectCommand.ExecuteScalarAsync();
                    if (result == null)
                    {
                        ErrorMessage = "Корисникот со дадениот матичен број не постои.";
                        return Page();
                    }

                    userEmail = result.ToString();
                }

                // Update the reset key in the database
                using (var updateCommand = new SqlCommand("UPDATE users SET resetrequest = @resetrequest WHERE emb = @emb", connection))
                {
                    updateCommand.Parameters.AddWithValue("@resetrequest", resetKey);
                    updateCommand.Parameters.AddWithValue("@emb", EMB);

                    await updateCommand.ExecuteNonQueryAsync();
                }
            }

            // Send an email with the reset key to the user's email
            var subject = "Промена на лозинка - NextBroker Core";
            var messageBody = $@"
<!DOCTYPE html>
<html lang=""mk"">
<head>
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <title>Ресетирање на лозинка</title>
</head>
<body style=""font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f0f2f5;"">
    <div style=""max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 16px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,51,102,0.15);"">
        <div style=""background-color: #003366; padding: 30px; text-align: center; box-shadow: 0 4px 6px rgba(0,51,102,0.2);"">
            <h1 style=""color: white; margin: 0; font-size: 28px; text-shadow: 0 2px 4px rgba(0,0,0,0.1);"">Ресетирање на лозинка 🔐</h1>
        </div>
        
        <div style=""padding: 40px 30px;"">
            <p style=""font-size: 16px; line-height: 1.6; color: #333; margin-bottom: 30px;"">
                Здраво,<br><br>
                Добивме барање за промена на вашата лозинка. 
                Ве молиме користете го следниот код за да ја ресетирате вашата лозинка:
            </p>
            
            <div style=""background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 12px; margin: 25px 0; 
                      box-shadow: 0 2px 8px rgba(0,51,102,0.08); border: 2px dashed #003366;"">
                <span style=""font-size: 32px; font-weight: bold; color: #003366; letter-spacing: 3px; text-shadow: 0 1px 2px rgba(0,51,102,0.1);
                           font-family: monospace;"">
                    {resetKey}
                </span>
            </div>
            
            <div style=""background-color: #f8f9fa; padding: 20px; border-radius: 12px; margin-top: 30px; box-shadow: 0 2px 8px rgba(0,51,102,0.08);"">
                <p style=""margin: 0; color: #666; font-size: 15px;"">
                    <span style=""color: #003366; font-weight: bold;"">🔒 За ваша безбедност:</span>
                </p>
                <ul style=""list-style: none; padding: 10px 0; margin: 0;"">
                    <li style=""margin: 8px 0; color: #666; font-size: 14px;"">
                        <span style=""color: #4CAF50;"">•</span> Кодот е валиден 24 часа
                    </li>
                    <li style=""margin: 8px 0; color: #666; font-size: 14px;"">
                        <span style=""color: #4CAF50;"">•</span> Никогаш не го споделувајте вашиот код со други
                    </li>
                    <li style=""margin: 8px 0; color: #666; font-size: 14px;"">
                        <span style=""color: #4CAF50;"">•</span> Ако не сте побарале промена на лозинка, игнорирајте ја оваа порака
                    </li>
                </ul>
            </div>
            
            <p style=""margin-top: 30px; font-size: 15px; color: #666; text-align: center; padding: 0 20px;"">
                Ви треба помош? <span style=""color: #003366; text-decoration: none; font-weight: 500;"">Слободно контактирајте нѐ!</span>
            </p>
        </div>
        
        <div style=""background-color: #f8f9fa; padding: 25px; text-align: center; border-top: 1px solid #eee;"">
            <p style=""margin: 0; color: #666; font-size: 13px; line-height: 1.6;"">
                © {DateTime.Now.Year} NextBroker Core. All rights reserved.<br>
                <span style=""color: #999;"">Ова е автоматска порака, ве молиме не одговарајте на овој емаил.</span>
            </p>
        </div>
    </div>
</body>
</html>";
            await _mailerService.SendEmailAsync(userEmail, subject, messageBody);

            // Set the success message and redirect
            SuccessMessage = "Кодот за промена на лозинка е испратен на вашиот емаил.";
            return RedirectToPage("/ResetPassword/ResetConfirmation");
        }
        catch (Exception ex)
        {
            ErrorMessage = $"Настана грешка: {ex.Message}";
            return Page();
        }
    }

    private string GenerateRandomKey(int length)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        var random = new RNGCryptoServiceProvider();
        var data = new byte[length];
        var result = new StringBuilder(length);

        random.GetBytes(data);
        foreach (var byteValue in data)
        {
            result.Append(chars[byteValue % chars.Length]);
        }

        return result.ToString();
    }
}