@page
@model NextBroker.Pages.Finansii.PrenosNaNaplatenaPremijaSpecifikaciiModel
@{
    ViewData["Title"] = "Пренос на премија - Спецификации";
}


<h1 style='text-align: center;'>@ViewData["Title"]</h1>


<div class="container-fluid mt-4 px-4">
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
            @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Филтрирај полиси за спецификација</h5>
        </div>
        <div class="card-body">
            <form method="post" asp-page-handler="FilterPolisi">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label asp-for="Filter.KlientiIdOsiguritel" class="form-label"></label>
                        <select asp-for="Filter.KlientiIdOsiguritel" asp-items="Model.Osiguriteli" class="form-select">
                            <option value="">-- Изберете осигурител --</option>
                        </select>
                        <span asp-validation-for="Filter.KlientiIdOsiguritel" class="text-danger"></span>
                    </div>
                    <div class="col-md-4">
                        <label asp-for="Filter.KlasiOsiguruvanjeIdKlasa" class="form-label"></label>
                        <select asp-for="Filter.KlasiOsiguruvanjeIdKlasa" asp-items="Model.KlasiOsiguruvanje" class="form-select">
                        </select>
                        <span asp-validation-for="Filter.KlasiOsiguruvanjeIdKlasa" class="text-danger"></span>
                    </div>
                    <div class="col-md-4">
                        <label asp-for="Filter.TipNaFaktura" class="form-label"></label>
                        <select asp-for="Filter.TipNaFaktura" asp-items="Model.TipoviFakturi" class="form-select">
                            <option value="">-- Изберете тип на фактура --</option>
                        </select>
                        <span asp-validation-for="Filter.TipNaFaktura" class="text-danger"></span>
                    </div>
                </div>
                <div class="row g-3 mt-2">
                    <div class="col-md-4">
                        <label asp-for="Filter.DatumNaIzdavanjeOd" class="form-label"></label>
                        <input type="date" class="form-control" asp-for="Filter.DatumNaIzdavanjeOd" />
                        <span asp-validation-for="Filter.DatumNaIzdavanjeOd" class="text-danger"></span>
                    </div>
                    <div class="col-md-4">
                        <label asp-for="Filter.DatumNaIzdavanjeDo" class="form-label"></label>
                        <input type="date" class="form-control" asp-for="Filter.DatumNaIzdavanjeDo" />
                        <span asp-validation-for="Filter.DatumNaIzdavanjeDo" class="text-danger"></span>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i> Филтрирај
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    @if (Model.FilteredPolisi.Any())
    {
        <div class="card mt-4">
            <div class="card-body">
                <form method="post" asp-page-handler="SaveSpecifikacija" id="specifikacijaForm">
                    <div class="row g-3 mb-4">
                        <div class="col-md-4">
                            <label class="form-label">Број на фактура</label>
                            <input type="text" class="form-control" id="BrojNaFakturaVlezna" name="BrojNaFakturaVlezna" required />
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Датум на фактура</label>
                            <input type="date" class="form-control" id="DatumNaFakturaVlezna" name="DatumNaFakturaVlezna" required />
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Рок на плаќање</label>
                            <input type="date" class="form-control" id="RokNaPlakjanjeFakturaVlezna" name="RokNaPlakjanjeFakturaVlezna" required />
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Износ на фактура во рок</label>
                            <input type="number" step="0.0001" class="form-control" id="IznosNaFakturaVoRok" name="IznosNaFakturaVoRok" />
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col">
                            <button type="button" class="btn btn-success me-2" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-1"></i> Генерирај Excel
                            </button>
                            <button type="submit" class="btn btn-primary" id="saveButton" disabled>
                                <i class="fas fa-save me-1"></i> Зачувај
                            </button>
                        </div>
                    </div>
                    <input type="hidden" name="SelectedPolisiIds" id="selectedPolisiIds" />
                </form>

                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" class="form-check-input" checked />
                                </th>
                                <th>Класа</th>
                                <th>Број на полиса</th>
                                <th>Премија за плаќање</th>
                                <th>Платено од договорувач</th>
                                <th>Износ за плаќање во рок</th>
                                <th>Должи договорувач</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var polisa in Model.FilteredPolisi)
                            {
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input polisa-checkbox" 
                                               value="@polisa.Id" 
                                               data-premium="@polisa.VkupnaPremijaZaPlakanje"
                                               checked />
                                    </td>
                                    <td>@polisa.KlasaIme</td>
                                    <td>@polisa.BrojNaPolisa</td>
                                    <td>@polisa.VkupnaPremijaZaPlakanje</td>
                                    <td>@polisa.PlatenoOdDogovoruvac</td>
                                    <td>@polisa.IznosZaPlakanjeVoRok</td>
                                    <td>@polisa.DolziDogovoruvac</td>                                   
                                    

                                </tr>
                            }
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="3" class="text-end fw-bold">Вкупно:</td>
                                <td class="text-end fw-bold" id="totalPremium">0.00</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);

            function updateTotalPremium() {
                let total = 0;
                $(".polisa-checkbox:checked").each(function() {
                    total += parseFloat($(this).data('premium') || 0);
                });
                $("#totalPremium").text(total.toLocaleString('mk-MK', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
            }

            // Handle "Select All" checkbox
            $("#selectAll").change(function () {
                $(".polisa-checkbox").prop('checked', $(this).prop('checked'));
                updateTotalPremium();
                // Disable save button when "Select All" is changed
                document.getElementById('saveButton').setAttribute('disabled', 'disabled');
            });

            // Update "Select All" when individual checkboxes change
            $(".polisa-checkbox").change(function () {
                if ($(".polisa-checkbox:checked").length === $(".polisa-checkbox").length) {
                    $("#selectAll").prop('checked', true);
                } else {
                    $("#selectAll").prop('checked', false);
                }
                updateTotalPremium();
                // Disable save button when any checkbox is changed
                document.getElementById('saveButton').setAttribute('disabled', 'disabled');
            });

            // Function to get selected polisa IDs
            window.getSelectedPolisaIds = function() {
                return $(".polisa-checkbox:checked").map(function() {
                    return parseInt($(this).val());
                }).get();
            };

            // Calculate initial total on page load
            updateTotalPremium();

            // Handle form submission
            $("#specifikacijaForm").on("submit", function(e) {
                e.preventDefault();
                var selectedIds = window.getSelectedPolisaIds();
                if (selectedIds.length === 0) {
                    alert("Ве молиме изберете барем една полиса.");
                    return false;
                }

                var brojNaFakturaVlezna = document.getElementById('BrojNaFakturaVlezna').value;
                var datumNaFakturaVlezna = document.getElementById('DatumNaFakturaVlezna').value;
                var rokNaPlakjanjeFakturaVlezna = document.getElementById('RokNaPlakjanjeFakturaVlezna').value;

                if (!brojNaFakturaVlezna || !datumNaFakturaVlezna || !rokNaPlakjanjeFakturaVlezna) {
                    alert('Ве молиме пополнете ги сите полиња (број на фактура, датум и рок)');
                    return false;
                }

                $("#selectedPolisiIds").val(JSON.stringify(selectedIds));
                this.submit();
            });

            // Add new code for Excel export tracking
            window.exportToExcel = function() {
                console.log('Export to Excel function called');
                
                var selectedPolisiIds = getSelectedPolisaIds();
                console.log('Selected Polisi IDs:', selectedPolisiIds);
                
                if (selectedPolisiIds.length === 0) {
                    alert('Не се избрани полиси за експорт.');
                    return;
                }

                var brojNaFakturaVlezna = document.getElementById('BrojNaFakturaVlezna').value;
                var datumNaFakturaVlezna = document.getElementById('DatumNaFakturaVlezna').value;
                var rokNaPlakjanjeFakturaVlezna = document.getElementById('RokNaPlakjanjeFakturaVlezna').value;
                var iznosNaFakturaVoRok = document.getElementById('IznosNaFakturaVoRok').value;

                console.log('Form values:', {
                    brojNaFakturaVlezna,
                    datumNaFakturaVlezna,
                    rokNaPlakjanjeFakturaVlezna,
                    iznosNaFakturaVoRok
                });

                if (!brojNaFakturaVlezna || !datumNaFakturaVlezna || !rokNaPlakjanjeFakturaVlezna) {
                    alert('Ве молиме пополнете ги сите задолжителни полиња (број на фактура, датум и рок)');
                    return;
                }

                var url = `/Finansii/PrenosNaNaplatenaPremijaSpecifikacii?handler=ExportToExcel&SelectedPolisiIds=${encodeURIComponent(JSON.stringify(selectedPolisiIds))}&BrojNaFakturaVlezna=${encodeURIComponent(brojNaFakturaVlezna)}&DatumNaFakturaVlezna=${encodeURIComponent(datumNaFakturaVlezna)}&RokNaPlakjanjeFakturaVlezna=${encodeURIComponent(rokNaPlakjanjeFakturaVlezna)}&IznosNaFakturaVoRok=${encodeURIComponent(iznosNaFakturaVoRok)}`;
                
                console.log('Request URL:', url);
                
                try {
                    // Enable the save button after successful Excel generation
                    window.location.href = url;
                    document.getElementById('saveButton').removeAttribute('disabled');
                    console.log('Navigation initiated and save button enabled');
                } catch (error) {
                    console.error('Error during export:', error);
                    alert('Грешка при експорт: ' + error.message);
                }
            };

            // Remove the old change handlers since we've incorporated them above
            // Reset save button state when form fields change
            $('#BrojNaFakturaVlezna, #DatumNaFakturaVlezna, #RokNaPlakjanjeFakturaVlezna').on('change', function() {
                document.getElementById('saveButton').setAttribute('disabled', 'disabled');
            });
        });
    </script>
}
