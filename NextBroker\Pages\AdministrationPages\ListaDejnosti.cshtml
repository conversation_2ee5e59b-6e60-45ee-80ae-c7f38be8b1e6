@page
@model NextBroker.Pages.AdministrationPages.ListaDejnostiModel
@{
    ViewData["Title"] = "Листа на дејности";
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container">
    <div class="mb-3">
        <form method="post" asp-page-handler="ToggleEdit" class="d-flex gap-2">
            <button type="submit" class="btn @(Model.IsEditing ? "btn-warning" : "btn-primary")">
                @(Model.IsEditing ? "Назад" : "Измени")
            </button>
            @if (Model.IsEditing)
            {                
                <button type="button" class="btn btn-info" id="saveChangesBtn">
                    Зачувај промени
                </button>
                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addDejnostModal">
                    Додади нов
                </button>
            }
        </form>
    </div>

    <div class="mb-3">
        <input type="text" id="searchBox" class="form-control" placeholder="Пребарувај..." />
    </div>

    <div class="table-responsive">
        <table id="dejnostiTable" class="table table-striped table-bordered">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Шифра на дејност</th>
                    <th>Назив на дејност</th>
                    <th>Датум на креирање</th>
                    <th>Датум на промена</th>
                    @if (Model.IsEditing)
                    {
                        <th>Акции</th>
                    }
                </tr>
            </thead>
            <tbody>
                @foreach (var dejnost in Model.DejnostiList)
                {
                    <tr data-id="@dejnost.Id">
                        <td>@dejnost.Id</td>
                        <td>
                            @if (Model.IsEditing)
                            {
                                <input type="text" class="form-control" value="@dejnost.SifraDejnost" data-field="SifraDejnost" />
                            }
                            else
                            {
                                @dejnost.SifraDejnost
                            }
                        </td>
                        <td>
                            @if (Model.IsEditing)
                            {
                                <input type="text" class="form-control" value="@dejnost.NazivDejnost" data-field="NazivDejnost" />
                            }
                            else
                            {
                                @dejnost.NazivDejnost
                            }
                        </td>
                        <td>@dejnost.DateCreated.ToString("dd.MM.yyyy HH:mm")</td>
                        <td>@(dejnost.DateModified == DateTime.MinValue ? "" : dejnost.DateModified.ToString("dd.MM.yyyy HH:mm"))</td>
                        @if (Model.IsEditing)
                        {
                            <td>
                                <button type="button" class="btn btn-danger btn-sm delete-dejnost" data-id="@dejnost.Id">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        }
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>

<!-- Add Dejnost Modal -->
<div class="modal fade" id="addDejnostModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Додади нова дејност</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" asp-page-handler="AddDejnost">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Шифра на дејност</label>
                        <input type="text" class="form-control" name="sifraDejnost" required />
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Назив на дејност</label>
                        <input type="text" class="form-control" name="nazivDejnost" required />
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Откажи</button>
                    <button type="submit" class="btn btn-primary">Додади</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            var table = $('#dejnostiTable').DataTable({
                "dom": "t",
                "language": {
                    "emptyTable": "Нема пронајдени записи",
                    "zeroRecords": "Нема пронајдени записи"
                },
                "ordering": false,
                "paging": false
            });

            $('#searchBox').on('keyup', function() {
                table.search(this.value).draw();
            });

            $('.delete-dejnost').click(function () {
                var id = $(this).data('id');
                if (confirm('Дали сте сигурни дека сакате да ја избришете оваа дејност?')) {
                    $.ajax({
                        url: '?handler=DeleteDejnost',
                        type: 'POST',
                        data: JSON.stringify({ id: id }),
                        contentType: 'application/json',
                        headers: {
                            "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: function () {
                            location.reload();
                        }
                    });
                }
            });

            $('#saveChangesBtn').click(function () {
                var updates = [];
                $('#dejnostiTable tbody tr').each(function () {
                    var row = $(this);
                    updates.push({
                        id: row.data('id'),
                        sifraDejnost: row.find('input[data-field="SifraDejnost"]').val(),
                        nazivDejnost: row.find('input[data-field="NazivDejnost"]').val()
                    });
                });

                $.ajax({
                    url: '?handler=SaveChanges',
                    type: 'POST',
                    data: JSON.stringify(updates),
                    contentType: 'application/json',
                    headers: {
                        "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function () {
                        location.reload();
                    }
                });
            });
        });
    </script>
} 