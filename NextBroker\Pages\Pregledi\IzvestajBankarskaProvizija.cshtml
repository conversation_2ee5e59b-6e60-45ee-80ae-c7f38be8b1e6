@page
@model NextBroker.Pages.Pregledi.IzvestajBankarskaProvizijaModel
@using System.Data
@{
    ViewData["Title"] = "Извештај Банкарска Провизија";
    // Mapping from SQL column names to Macedonian
    var mkHeaders = new Dictionary<string, string> {
        {"Banka", "Банка"},
        {"BrojNaIzvod", "Број на извод"},
        {"BrojNaSmetka", "Број на сметка"},
        {"DatumNaIzvod", "Датум на извод"},
        {"CelNaDoznaka", "Цел на дознака"},
        {"BrojNaKasovIzvestaj", "Број на касов извештај"},
        {"IznosOdKasovIzvestaj", "Износ од касов извештај"},
        {"IzvodIznosPriliv", "Износ прилив (извод)"},
        {"IzvodIznosOdliv", "Износ одлив (извод)"},
        {"Iz<PERSON>", "Износ"}
    };

    // Prepare sums for numeric columns
    var sums = new Dictionary<string, decimal>();
    if (Model.ReportData != null)
    {
        foreach (DataColumn column in Model.ReportData.Columns)
        {
            if (column.DataType == typeof(decimal) || column.DataType == typeof(double) || column.DataType == typeof(float) || column.DataType == typeof(int) || column.DataType == typeof(long))
            {
                decimal sum = 0;
                foreach (DataRow row in Model.ReportData.Rows)
                {
                    if (row[column] != DBNull.Value)
                    {
                        try { sum += Convert.ToDecimal(row[column]); } catch { }
                    }
                }
                sums[column.ColumnName] = sum;
            }
        }
    }
}

<div class="container-fluid">
    <h1 class="h3 mb-4">Извештај Банкарска Провизија</h1>
    
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Филтри за извештај</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="DatumOd">Датум од:</label>
                                    <input type="date" class="form-control" id="DatumOd" name="DatumOd" value="@Model.DatumOd.ToString("yyyy-MM-dd")" required />
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="DatumDo">Датум до:</label>
                                    <input type="date" class="form-control" id="DatumDo" name="DatumDo" value="@Model.DatumDo.ToString("yyyy-MM-dd")" required />
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary mr-2">Генерирај извештај</button>
                                    <button type="submit" formaction="?handler=ExportExcel" formmethod="post" class="btn btn-success">Export to Excel</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @if (Model.ReportData != null && Model.ReportData.Rows.Count > 0)
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Резултати од извештајот</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered">
                                <thead>
                                    <tr>
                                        @foreach (DataColumn column in Model.ReportData.Columns)
                                        {
                                            <th>@(mkHeaders.ContainsKey(column.ColumnName) ? mkHeaders[column.ColumnName] : column.ColumnName)</th>
                                        }
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (DataRow row in Model.ReportData.Rows)
                                    {
                                        <tr>
                                            @foreach (DataColumn column in Model.ReportData.Columns)
                                            {
                                                var value = row[column];
                                                if (value is DateTime dtValue)
                                                {
                                                    <td>@dtValue.ToString("dd.MM.yyyy")</td>
                                                }
                                                else
                                                {
                                                    <td>@value</td>
                                                }
                                            }
                                        </tr>
                                    }
                                    <tr style="font-weight:bold; background:#f2f2f2;">
                                        @foreach (DataColumn column in Model.ReportData.Columns)
                                        {
                                            if (column.ColumnName == "Iznos")
                                            {
                                                <td>@sums["Iznos"].ToString("N2")</td>
                                            }
                                            else
                                            {
                                                <td></td>
                                            }
                                        }
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
    else if (Model.ReportData != null)
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-info">
                    Нема податоци за избраниот период.
                </div>
            </div>
        </div>
    }
</div>
