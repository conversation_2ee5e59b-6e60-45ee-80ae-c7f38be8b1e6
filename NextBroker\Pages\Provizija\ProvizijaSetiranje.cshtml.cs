using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using OfficeOpenXml;
using System.IO;

namespace NextBroker.Pages.Provizija
{
    public class ProvizijaSetiranjeModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public List<ProvizijaSetiranjeViewModel> ProvizijaList { get; set; }

        [BindProperty(SupportsGet = true)]
        public string StatusFilter { get; set; } = "Сите";

        [BindProperty(SupportsGet = true)]
        public string OsiguritelFilter { get; set; } = "Сите";

        [BindProperty(SupportsGet = true)]
        public List<string> SelectedOsiguriteli { get; set; } = new List<string>();

        public List<string> OsiguritelOptions { get; set; } = new List<string>();

        public ProvizijaSetiranjeModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
            ProvizijaList = new List<ProvizijaSetiranjeViewModel>();
        }

        public class ProvizijaSetiranjeViewModel
        {
            public long Id { get; set; }
            public DateTime? DateCreated { get; set; }
            public string UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string UsernameModified { get; set; }
            public string Naziv { get; set; }
            public string KlientProvizija { get; set; }
            public string KlasaIme { get; set; }
            public string Ime { get; set; }
            public decimal? ProcentNaProvizijaZaFizickiLica { get; set; }
            public decimal? ProcentNaProvizijaZaPravniLica { get; set; }
            public string Nacin { get; set; }
            public string TipNaProvizija { get; set; }
            public DateTime? DatumVaziOd { get; set; }
            public DateTime? DatumVaziDo { get; set; }
            public string PrimacProvizija { get; set; }
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("ProvizijaSetiranje"))
            {
                return RedirectToAccessDenied();
            }

            // Initialize SelectedOsiguriteli if empty
            if (SelectedOsiguriteli == null || SelectedOsiguriteli.Count == 0)
            {
                SelectedOsiguriteli = new List<string> { "Сите" };
            }

            // Debug: Log the received values
            System.Diagnostics.Debug.WriteLine($"StatusFilter: {StatusFilter}");
            System.Diagnostics.Debug.WriteLine($"SelectedOsiguriteli Count: {SelectedOsiguriteli?.Count ?? 0}");
            if (SelectedOsiguriteli != null)
            {
                foreach (var item in SelectedOsiguriteli)
                {
                    System.Diagnostics.Debug.WriteLine($"  - {item}");
                }
            }

            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();

                // Populate Osiguritel options
                await PopulateOsiguritelOptions(connection);

                // Build the base query
                string baseQuery = @"
                    select
                    provset.Id,
                    provset.DateCreated,
                    provset.UsernameCreated,
                    provset.DateModified,
                    provset.UsernameModified,
                    klnt.Naziv,
                    klntprov.KlientProvizija,
                    klsosig.KlasaIme,
                    prod.Ime,
                    provset.ProcentNaProvizijaZaFizickiLica,
                    provset.ProcentNaProvizijaZaPravniLica,
                    provnacinpresmetka.Nacin,
                    brutonetoprov.TipNaProvizija,
                    provset.DatumVaziOd,
                    provset.DatumVaziDo,
                    CAST(ISNULL(klntprimac.Ime, '') + ' ' + ISNULL(klntprimac.Prezime, '') + ' ' + ISNULL(klntprimac.Naziv, '') AS NVARCHAR(255)) AS PrimacProvizija
                    from ProvizijaSetiranje provset
                    left join Klienti klnt on provset.KlientiIdOsiguritel = klnt.Id
                    left join KlientProvizija klntprov on provset.KlientProvizijaId = klntprov.Id
                    left join KlasiOsiguruvanje klsosig on provset.KlasiOsiguruvanjeIdKlasa = klsosig.Id
                    left join Produkti prod on provset.ProduktiIdProizvod = prod.Id
                    left join SifrarnikNacinNaPresmetkaProvizija provnacinpresmetka on provset.SifrarnikNacinNaPresmetkaProvizijaId = provnacinpresmetka.Id
                    left join SifrarnikBrutoNetoProvizija brutonetoprov on provset.SifrarnikBrutoNetoProvizija = brutonetoprov.Id
                    left join Klienti klntprimac on provset.KlientiIdPrimacProvizija = klntprimac.Id";

                // Build WHERE clause based on filters
                List<string> whereConditions = new List<string>();

                // Add StatusFilter condition
                switch (StatusFilter)
                {
                    case "Активни":
                        whereConditions.Add("(provset.DatumVaziDo IS NULL OR provset.DatumVaziDo >= CONVERT(DATE, GETDATE()))");
                        break;
                    case "Неактивни":
                        whereConditions.Add("provset.DatumVaziDo < CONVERT(DATE, GETDATE())");
                        break;
                    case "Сите":
                    default:
                        // No condition for "Сите"
                        break;
                }

                // Add OsiguritelFilter condition (multiple selections)
                if (SelectedOsiguriteli != null && SelectedOsiguriteli.Count > 0 && !SelectedOsiguriteli.Contains("Сите"))
                {
                    var osiguritelParams = new List<string>();
                    for (int i = 0; i < SelectedOsiguriteli.Count; i++)
                    {
                        osiguritelParams.Add($"@Osiguritel{i}");
                    }
                    whereConditions.Add($"klnt.Naziv IN ({string.Join(", ", osiguritelParams)})");
                }

                // Combine WHERE conditions
                string whereClause = "";
                if (whereConditions.Count > 0)
                {
                    whereClause = " WHERE " + string.Join(" AND ", whereConditions);
                }

                string fullQuery = baseQuery + whereClause;
                var command = new SqlCommand(fullQuery, connection);

                // Add parameters for selected Osiguriteli if needed
                if (SelectedOsiguriteli != null && SelectedOsiguriteli.Count > 0 && !SelectedOsiguriteli.Contains("Сите"))
                {
                    for (int i = 0; i < SelectedOsiguriteli.Count; i++)
                    {
                        command.Parameters.AddWithValue($"@Osiguritel{i}", SelectedOsiguriteli[i]);
                    }
                }

                using (var reader = await command.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        ProvizijaList.Add(new ProvizijaSetiranjeViewModel
                        {
                            Id = reader.GetInt64(0),
                            DateCreated = reader.IsDBNull(1) ? null : reader.GetDateTime(1),
                            UsernameCreated = reader.IsDBNull(2) ? null : reader.GetString(2),
                            DateModified = reader.IsDBNull(3) ? null : reader.GetDateTime(3),
                            UsernameModified = reader.IsDBNull(4) ? null : reader.GetString(4),
                            Naziv = reader.IsDBNull(5) ? null : reader.GetString(5),
                            KlientProvizija = reader.IsDBNull(6) ? null : reader.GetString(6),
                            KlasaIme = reader.IsDBNull(7) ? null : reader.GetString(7),
                            Ime = reader.IsDBNull(8) ? null : reader.GetString(8),
                            ProcentNaProvizijaZaFizickiLica = reader.IsDBNull(9) ? null : reader.GetDecimal(9),
                            ProcentNaProvizijaZaPravniLica = reader.IsDBNull(10) ? null : reader.GetDecimal(10),
                            Nacin = reader.IsDBNull(11) ? null : reader.GetString(11),
                            TipNaProvizija = reader.IsDBNull(12) ? null : reader.GetString(12),
                            DatumVaziOd = reader.IsDBNull(13) ? null : reader.GetDateTime(13),
                            DatumVaziDo = reader.IsDBNull(14) ? null : reader.GetDateTime(14),
                            PrimacProvizija = reader.IsDBNull(15) ? null : reader.GetString(15)
                        });
                    }
                }
            }

            return Page();
        }
        
        public class UpdateDatumVaziDoRequest
        {
            public long Id { get; set; }
            public DateTime DatumVaziDo { get; set; }
        }
        
        public class UpdateDatumVaziDoResponse
        {
            public bool Success { get; set; }
            public string Message { get; set; }
        }
        
        public async Task<IActionResult> OnPostUpdateDatumVaziDoAsync([FromBody] UpdateDatumVaziDoRequest request)
        {
            if (!await HasPageAccess("ProvizijaSetiranje"))
            {
                return new JsonResult(new UpdateDatumVaziDoResponse 
                { 
                    Success = false, 
                    Message = "Немате пристап за уредување." 
                });
            }
            
            try
            {
                string username = HttpContext.Session.GetString("Username") ?? "Unknown";
                
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    
                    // First check if DatumVaziDo is null for this record
                    var checkCommand = new SqlCommand(
                        "SELECT COUNT(*) FROM ProvizijaSetiranje WHERE Id = @Id AND DatumVaziDo IS NULL", 
                        connection);
                    checkCommand.Parameters.AddWithValue("@Id", request.Id);
                    
                    int count = (int)await checkCommand.ExecuteScalarAsync();
                    if (count == 0)
                    {
                        return new JsonResult(new UpdateDatumVaziDoResponse 
                        { 
                            Success = false, 
                            Message = "Записот не постои или веќе има поставено датум." 
                        });
                    }
                    
                    // Update the DatumVaziDo field
                    var updateCommand = new SqlCommand(
                        "UPDATE ProvizijaSetiranje SET DatumVaziDo = @DatumVaziDo, DateModified = @DateModified, UsernameModified = @UsernameModified WHERE Id = @Id", 
                        connection);
                    updateCommand.Parameters.AddWithValue("@Id", request.Id);
                    updateCommand.Parameters.AddWithValue("@DatumVaziDo", request.DatumVaziDo);
                    updateCommand.Parameters.AddWithValue("@DateModified", DateTime.Now);
                    updateCommand.Parameters.AddWithValue("@UsernameModified", username);
                    
                    int rowsAffected = await updateCommand.ExecuteNonQueryAsync();
                    
                    if (rowsAffected > 0)
                    {
                        return new JsonResult(new UpdateDatumVaziDoResponse 
                        { 
                            Success = true, 
                            Message = "Датумот е успешно ажуриран." 
                        });
                    }
                    else
                    {
                        return new JsonResult(new UpdateDatumVaziDoResponse 
                        { 
                            Success = false, 
                            Message = "Не беше можно да се ажурира датумот." 
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new UpdateDatumVaziDoResponse 
                { 
                    Success = false, 
                    Message = "Грешка: " + ex.Message 
                });
            }
        }

        public async Task<IActionResult> OnPostExportToExcelAsync()
        {
            if (!await HasPageAccess("ProvizijaSetiranje"))
            {
                return RedirectToAccessDenied();
            }

            // Initialize SelectedOsiguriteli if empty
            if (SelectedOsiguriteli == null || SelectedOsiguriteli.Count == 0)
            {
                SelectedOsiguriteli = new List<string> { "Сите" };
            }

            var exportData = new List<ProvizijaSetiranjeViewModel>();

            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();

                // Build the same query as in OnGet method
                string baseQuery = @"
                    select
                    provset.Id,
                    provset.DateCreated,
                    provset.UsernameCreated,
                    provset.DateModified,
                    provset.UsernameModified,
                    klnt.Naziv,
                    klntprov.KlientProvizija,
                    klsosig.KlasaIme,
                    prod.Ime,
                    provset.ProcentNaProvizijaZaFizickiLica,
                    provset.ProcentNaProvizijaZaPravniLica,
                    provnacinpresmetka.Nacin,
                    brutonetoprov.TipNaProvizija,
                    provset.DatumVaziOd,
                    provset.DatumVaziDo,
                    CAST(ISNULL(klntprimac.Ime, '') + ' ' + ISNULL(klntprimac.Prezime, '') + ' ' + ISNULL(klntprimac.Naziv, '') AS NVARCHAR(255)) AS PrimacProvizija
                    from ProvizijaSetiranje provset
                    left join Klienti klnt on provset.KlientiIdOsiguritel = klnt.Id
                    left join KlientProvizija klntprov on provset.KlientProvizijaId = klntprov.Id
                    left join KlasiOsiguruvanje klsosig on provset.KlasiOsiguruvanjeIdKlasa = klsosig.Id
                    left join Produkti prod on provset.ProduktiIdProizvod = prod.Id
                    left join SifrarnikNacinNaPresmetkaProvizija provnacinpresmetka on provset.SifrarnikNacinNaPresmetkaProvizijaId = provnacinpresmetka.Id
                    left join SifrarnikBrutoNetoProvizija brutonetoprov on provset.SifrarnikBrutoNetoProvizija = brutonetoprov.Id
                    left join Klienti klntprimac on provset.KlientiIdPrimacProvizija = klntprimac.Id";

                // Build WHERE clause based on filters
                List<string> whereConditions = new List<string>();

                // Add StatusFilter condition
                switch (StatusFilter)
                {
                    case "Активни":
                        whereConditions.Add("(provset.DatumVaziDo IS NULL OR provset.DatumVaziDo >= CONVERT(DATE, GETDATE()))");
                        break;
                    case "Неактивни":
                        whereConditions.Add("provset.DatumVaziDo < CONVERT(DATE, GETDATE())");
                        break;
                    case "Сите":
                    default:
                        // No condition for "Сите"
                        break;
                }

                // Add OsiguritelFilter condition (multiple selections)
                if (SelectedOsiguriteli != null && SelectedOsiguriteli.Count > 0 && !SelectedOsiguriteli.Contains("Сите"))
                {
                    var osiguritelParams = new List<string>();
                    for (int i = 0; i < SelectedOsiguriteli.Count; i++)
                    {
                        osiguritelParams.Add($"@Osiguritel{i}");
                    }
                    whereConditions.Add($"klnt.Naziv IN ({string.Join(", ", osiguritelParams)})");
                }

                // Combine WHERE conditions
                string whereClause = "";
                if (whereConditions.Count > 0)
                {
                    whereClause = " WHERE " + string.Join(" AND ", whereConditions);
                }

                string fullQuery = baseQuery + whereClause;
                var command = new SqlCommand(fullQuery, connection);

                // Add parameters for selected Osiguriteli if needed
                if (SelectedOsiguriteli != null && SelectedOsiguriteli.Count > 0 && !SelectedOsiguriteli.Contains("Сите"))
                {
                    for (int i = 0; i < SelectedOsiguriteli.Count; i++)
                    {
                        command.Parameters.AddWithValue($"@Osiguritel{i}", SelectedOsiguriteli[i]);
                    }
                }

                using (var reader = await command.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        exportData.Add(new ProvizijaSetiranjeViewModel
                        {
                            Id = reader.GetInt64(0),
                            DateCreated = reader.IsDBNull(1) ? null : reader.GetDateTime(1),
                            UsernameCreated = reader.IsDBNull(2) ? null : reader.GetString(2),
                            DateModified = reader.IsDBNull(3) ? null : reader.GetDateTime(3),
                            UsernameModified = reader.IsDBNull(4) ? null : reader.GetString(4),
                            Naziv = reader.IsDBNull(5) ? null : reader.GetString(5),
                            KlientProvizija = reader.IsDBNull(6) ? null : reader.GetString(6),
                            KlasaIme = reader.IsDBNull(7) ? null : reader.GetString(7),
                            Ime = reader.IsDBNull(8) ? null : reader.GetString(8),
                            ProcentNaProvizijaZaFizickiLica = reader.IsDBNull(9) ? null : reader.GetDecimal(9),
                            ProcentNaProvizijaZaPravniLica = reader.IsDBNull(10) ? null : reader.GetDecimal(10),
                            Nacin = reader.IsDBNull(11) ? null : reader.GetString(11),
                            TipNaProvizija = reader.IsDBNull(12) ? null : reader.GetString(12),
                            DatumVaziOd = reader.IsDBNull(13) ? null : reader.GetDateTime(13),
                            DatumVaziDo = reader.IsDBNull(14) ? null : reader.GetDateTime(14),
                            PrimacProvizija = reader.IsDBNull(15) ? null : reader.GetString(15)
                        });
                    }
                }
            }

            // Create Excel file using EPPlus
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("Провизија Сетирање");

                // Add headers
                worksheet.Cells[1, 1].Value = "ID";
                worksheet.Cells[1, 2].Value = "Датум креирано";
                worksheet.Cells[1, 3].Value = "Креирано од";
                worksheet.Cells[1, 4].Value = "Датум модифицирано";
                worksheet.Cells[1, 5].Value = "Модифицирано од";
                worksheet.Cells[1, 6].Value = "Назив";
                worksheet.Cells[1, 7].Value = "Клиент провизија";
                worksheet.Cells[1, 8].Value = "Класа име";
                worksheet.Cells[1, 9].Value = "Име";
                worksheet.Cells[1, 10].Value = "% Провизија (физички)";
                worksheet.Cells[1, 11].Value = "% Провизија (правни)";
                worksheet.Cells[1, 12].Value = "Начин";
                worksheet.Cells[1, 13].Value = "Тип на провизија";
                worksheet.Cells[1, 14].Value = "Важи од";
                worksheet.Cells[1, 15].Value = "Важи до";
                worksheet.Cells[1, 16].Value = "Примач на провизија";

                // Style headers
                using (var range = worksheet.Cells[1, 1, 1, 16])
                {
                    range.Style.Font.Bold = true;
                    range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                    range.Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
                }

                // Add data
                for (int i = 0; i < exportData.Count; i++)
                {
                    var item = exportData[i];
                    int row = i + 2;

                    worksheet.Cells[row, 1].Value = item.Id;
                    worksheet.Cells[row, 2].Value = item.DateCreated?.ToString("dd.MM.yyyy");
                    worksheet.Cells[row, 3].Value = item.UsernameCreated;
                    worksheet.Cells[row, 4].Value = item.DateModified?.ToString("dd.MM.yyyy");
                    worksheet.Cells[row, 5].Value = item.UsernameModified;
                    worksheet.Cells[row, 6].Value = item.Naziv;
                    worksheet.Cells[row, 7].Value = item.KlientProvizija;
                    worksheet.Cells[row, 8].Value = item.KlasaIme;
                    worksheet.Cells[row, 9].Value = item.Ime;
                    worksheet.Cells[row, 10].Value = item.ProcentNaProvizijaZaFizickiLica;
                    worksheet.Cells[row, 11].Value = item.ProcentNaProvizijaZaPravniLica;
                    worksheet.Cells[row, 12].Value = item.Nacin;
                    worksheet.Cells[row, 13].Value = item.TipNaProvizija;
                    worksheet.Cells[row, 14].Value = item.DatumVaziOd?.ToString("dd.MM.yyyy");
                    worksheet.Cells[row, 15].Value = item.DatumVaziDo?.ToString("dd.MM.yyyy");
                    worksheet.Cells[row, 16].Value = item.PrimacProvizija;
                }

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                // Generate file
                var stream = new MemoryStream();
                package.SaveAs(stream);
                stream.Position = 0;

                string fileName = $"ProvizijaSetiranje_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                return File(stream.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
        }

        private async Task PopulateOsiguritelOptions(SqlConnection connection)
        {
            OsiguritelOptions.Clear();
            OsiguritelOptions.Add("Сите");

            var osiguritelCommand = new SqlCommand("SELECT Naziv FROM Klienti WHERE Osiguritel = 1 ORDER BY Naziv", connection);
            using (var reader = await osiguritelCommand.ExecuteReaderAsync())
            {
                while (await reader.ReadAsync())
                {
                    string naziv = reader.GetString(0);
                    if (!string.IsNullOrEmpty(naziv))
                    {
                        OsiguritelOptions.Add(naziv);
                    }
                }
            }
        }
    }
}
