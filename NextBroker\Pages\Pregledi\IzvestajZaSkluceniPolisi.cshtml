@page
@model NextBroker.Pages.Pregledi.IzvestajZaSkluceniPolisiModel
@{
    ViewData["Title"] = "Извештај за склучени полиси";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2>@ViewData["Title"]</h2>
        </div>
    </div>

    <form method="post" class="mb-4" id="polisiFilterForm">
        <div class="card mb-3">
            <div class="card-header">
                <h5>Филтри</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="IzdavanjeStartDate">Датум на издавање од</label>
                            <div class="input-group">
                                <input asp-for="IzdavanjeStartDate" type="date" class="form-control date-from required-date-from" id="IzdavanjeStartDate" />
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-secondary clear-date" data-target="IzdavanjeStartDate">
                                        <i class="fas fa-eraser"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="IzdavanjeEndDate">до</label>
                            <div class="input-group">
                                <input asp-for="IzdavanjeEndDate" type="date" class="form-control date-to" id="IzdavanjeEndDate" data-from-date="IzdavanjeStartDate" data-required="true" />
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-secondary clear-date" data-target="IzdavanjeEndDate">
                                        <i class="fas fa-eraser"></i>
                                    </button>
                                </div>
                            </div>
                            <span class="text-danger date-error" id="IzdavanjeEndDateError" style="display:none;">Датумот 'до' не може да биде пред датумот 'од'</span>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="VaziOdStartDate">Датум важи од</label>
                            <div class="input-group">
                                <input asp-for="VaziOdStartDate" type="date" class="form-control date-from" id="VaziOdStartDate" />
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-secondary clear-date" data-target="VaziOdStartDate">
                                        <i class="fas fa-eraser"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label asp-for="VaziOdEndDate">до</label>
                            <div class="input-group">
                                <input asp-for="VaziOdEndDate" type="date" class="form-control date-to" id="VaziOdEndDate" data-from-date="VaziOdStartDate" data-required="false" />
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-secondary clear-date" data-target="VaziOdEndDate">
                                        <i class="fas fa-eraser"></i>
                                    </button>
                                </div>
                            </div>
                            <span class="text-danger date-error" id="VaziOdEndDateError" style="display:none;">Датумот 'до' не може да биде пред датумот 'од'</span>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8 d-flex">
                        <button type="submit" asp-page-handler="Filter" class="btn btn-primary me-2" id="filterButton">
                            <i class="fas fa-filter me-1"></i> Филтрирај
                        </button>
                        <button type="submit" asp-page-handler="Clear" class="btn btn-outline-secondary me-2" id="clearButton">
                            <i class="fas fa-eraser me-1"></i> Исчисти
                        </button>
                        <button type="submit" asp-page-handler="SitePolisi" class="btn btn-secondary me-2">Извештај за сите полиси</button>
                        
                        @if (Model.ReportData != null && Model.ReportData.Rows.Count > 0)
                        {
                            <button type="submit" asp-page-handler="ExportExcel" class="btn btn-success">
                                <i class="fas fa-file-excel me-1"></i> Export Excel
                            </button>
                        }
                    </div>
                </div>
            </div>
        </div>
    </form>

    @if (Model.ReportData != null && Model.ReportData.Rows.Count > 0)
    {
        <div class="table-responsive">
            <table class="table table-striped table-bordered" id="polisiTable">
                <thead>
                    <tr>
                        @foreach (System.Data.DataColumn column in Model.ReportData.Columns)
                        {
                            <th>@column.ColumnName</th>
                        }
                    </tr>
                </thead>
                <tbody>
                    @foreach (System.Data.DataRow row in Model.ReportData.Rows)
                    {
                        <tr>
                            @foreach (var item in row.ItemArray)
                            {
                                <td>
                                    @if (item is DateTime date)
                                    {
                                        @date.ToString("dd/MM/yyyy")
                                    }
                                    else if (item is decimal decimalValue)
                                    {
                                        @decimalValue.ToString("N2")
                                    }
                                    else if (item is double doubleValue)
                                    {
                                        @doubleValue.ToString("N2")
                                    }
                                    else if (item is float floatValue)
                                    {
                                        @floatValue.ToString("N2")
                                    }
                                    else
                                    {
                                        @item
                                    }
                                </td>
                            }
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
    else if (Model.ReportData != null)
    {
        <div class="alert alert-info mt-3">
            Нема податоци за избраниот период.
        </div>
    }
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            $('#polisiTable').DataTable({
                "pageLength": 25,
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Macedonian.json"
                }
            });
            
            // Clear individual date fields
            $('.clear-date').on('click', function() {
                var targetId = $(this).data('target');
                $('#' + targetId).val('').trigger('change');
                // Clear any error messages
                $('#' + targetId + 'Error').hide();
                $('#' + targetId).removeClass('is-invalid');
            });
            
            // Ensure Clear button resets all form elements
            $('#clearButton').on('click', function() {
                // The form will be submitted to the Clear handler
                // This is just to ensure any client-side state is also reset
                setTimeout(function() {
                    $('input[type="date"]').val('');
                    $('.date-error').hide();
                    $('.is-invalid').removeClass('is-invalid');
                }, 0);
            });
            
            // Date validation
            $('.date-to').on('change', function() {
                var toDateValue = $(this).val();
                var fromDateId = $(this).data('from-date');
                var fromDateValue = $('#' + fromDateId).val();
                var errorId = $(this).attr('id') + 'Error';
                var isRequired = $(this).data('required') === true;
                
                // If 'to' date is provided, check if 'from' date is required
                if (toDateValue && fromDateValue === "" && isRequired) {
                    $('#' + fromDateId).addClass('is-invalid');
                    $('#' + errorId).text('Мора да внесете датум "од" пред да внесете датум "до"').show();
                    return;
                }
                
                // If both dates are provided, check that 'to' is not before 'from'
                if (toDateValue && fromDateValue) {
                    var toDate = new Date(toDateValue);
                    var fromDate = new Date(fromDateValue);
                    
                    if (toDate < fromDate) {
                        $(this).addClass('is-invalid');
                        $('#' + errorId).text('Датумот "до" не може да биде пред датумот "од"').show();
                    } else {
                        $(this).removeClass('is-invalid');
                        $('#' + fromDateId).removeClass('is-invalid');
                        $('#' + errorId).hide();
                    }
                }
            });
            
            // Form validation before submit
            $('#polisiFilterForm').on('submit', function(e) {
                // Skip validation if this is the clear button
                if (e.originalEvent && e.originalEvent.submitter && e.originalEvent.submitter.id === 'clearButton') {
                    return true;
                }
                
                var isValid = true;
                
                // Check each 'to' date
                $('.date-to').each(function() {
                    var toDateValue = $(this).val();
                    var fromDateId = $(this).data('from-date');
                    var fromDateValue = $('#' + fromDateId).val();
                    var errorId = $(this).attr('id') + 'Error';
                    var isRequired = $(this).data('required') === true;
                    
                    // Skip if empty
                    if (!toDateValue) return;
                    
                    // If 'to' date is provided, check if 'from' date is required
                    if (!fromDateValue && isRequired) {
                        $('#' + fromDateId).addClass('is-invalid');
                        $('#' + errorId).text('Мора да внесете датум "од" пред да внесете датум "до"').show();
                        isValid = false;
                        return;
                    }
                    
                    // Check that 'to' is not before 'from' (only if both are provided)
                    if (toDateValue && fromDateValue) {
                        var toDate = new Date(toDateValue);
                        var fromDate = new Date(fromDateValue);
                        
                        if (toDate < fromDate) {
                            $(this).addClass('is-invalid');
                            $('#' + errorId).text('Датумот "до" не може да биде пред датумот "од"').show();
                            isValid = false;
                        }
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                }
            });
        });
    </script>
}
