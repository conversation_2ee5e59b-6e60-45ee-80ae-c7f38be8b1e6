@page
@model NextBroker.Pages.Polisi.ListaPolisiModel
@{
    ViewData["Title"] = "Листа на полиси";
}

<div class="container-fluid mt-4">
    @Html.AntiForgeryToken()
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>@ViewData["Title"]</h1>
<!--
        <a href="/Polisi/DodajPolisaAO" class="btn btn-primary">
            <i class="fas fa-plus"></i> Додај полиса
        </a>
-->
    </div>

    @if (!string.IsNullOrEmpty(TempData["SuccessMessage"]?.ToString()))
    {
        <div class="alert alert-success" id="successMessage">
            @TempData["SuccessMessage"]
        </div>
    }

    <div class="card">
        <div class="card-body">
            <div class="mb-3">
                <input type="text" id="searchBox" class="form-control" placeholder="Пребарувај..." />
            </div>
            <div class="mb-3">
                <button class="btn btn-secondary w-100 text-start" type="button" data-bs-toggle="collapse" data-bs-target="#columnTogglers" aria-expanded="false" aria-controls="columnTogglers">
                    <i class="fas fa-cog me-2"></i> Додатни информации
                    <i class="fas fa-chevron-down float-end mt-1"></i>
                </button>
            </div>
            <div class="collapse mb-3" id="columnTogglers">
                <div class="row g-3">
                    <div class="col-md-4">
                        <strong>Основни информации</strong>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="3" id="toggle-modified-date">
                            <label class="form-check-label" for="toggle-modified-date">Изменето на</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="4" id="toggle-modified-by">
                            <label class="form-check-label" for="toggle-modified-by">Изменето од</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="9" id="toggle-proizvod">
                            <label class="form-check-label" for="toggle-proizvod">Производ</label>
                        </div>

                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="16" id="toggle-osigurenik">
                            <label class="form-check-label" for="toggle-osigurenik">Осигуреник</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="17" id="toggle-kolektivna">
                            <label class="form-check-label" for="toggle-kolektivna">Колективна</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="27" id="toggle-sorabotnik">
                            <label class="form-check-label" for="toggle-sorabotnik">Соработник</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="28" id="toggle-maticen-broj">
                            <label class="form-check-label" for="toggle-maticen-broj">Матичен број</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="29" id="toggle-brokerska-provizija" checked>
                            <label class="form-check-label" for="toggle-brokerska-provizija">Сетирање за провизија за брокер</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <strong>Датуми и статус</strong>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="18" id="toggle-datum-vazi-od">
                            <label class="form-check-label" for="toggle-datum-vazi-od">Важи од</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="19" id="toggle-datum-vazi-do">
                            <label class="form-check-label" for="toggle-datum-vazi-do">Важи до</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="20" id="toggle-datum-izdavanje">
                            <label class="form-check-label" for="toggle-datum-izdavanje">Датум на издавање</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="24" id="toggle-storno">
                            <label class="form-check-label" for="toggle-storno">Сторно</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="25" id="toggle-pricina-storno">
                            <label class="form-check-label" for="toggle-pricina-storno">Причина за сторно</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table id="polisiTable" class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Креирано на</th>
                            <th>Креирано од</th>
                            <th>Изменето на</th>
                            <th>Изменето од</th>
                            <th>Осигурител</th>
                            <th>Класа ID</th>
                            <th>Класа на осигурување</th>
                            <th>Производ ID</th>
                            <th>Производ</th>
                            <th>Број на полиса</th>
                            <th>Број на понуда</th>
                            <th>Договорувач ID</th>
                            <th>Договорувач</th>
                            <th>Вкупна Премија</th>
                            <th>Осигуреник ID</th>
                            <th>Осигуреник</th>
                            <th>Колективна</th>
                            <th>Важи од</th>
                            <th>Важи до</th>
                            <th>Датум на издавање</th>
                            <th>Премија за наплата</th>
                            <th>Уплатено</th>
                            <th>Должна премија</th>
                            <th>Сторно</th>
                            <th>Причина за сторно</th>
                            <th>Соработник ID</th>
                            <th>Соработник</th>
                            <th>Матичен број</th>
                            <th>Сетирање за провизија за брокер</th>
                            <th>Акции</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var polisa in Model.Polisi)
                        {
                            <tr class="@(polisa.Storno ? "text-danger" : "")">
                                <td>@polisa.Id</td>
                                <td>@(polisa.DateCreated?.ToString("dd.MM.yyyy HH:mm"))</td>
                                <td>@polisa.UsernameCreated</td>
                                <td>@(polisa.DateModified?.ToString("dd.MM.yyyy HH:mm"))</td>
                                <td>@polisa.UsernameModified</td>
                                <td>@polisa.OsiguritelNaziv</td>
                                <td>@polisa.KlasiOsiguruvanjeIdKlasa</td>
                                <td>@polisa.KlasaOsiguruvanje</td>
                                <td>@polisa.ProduktiIdProizvod</td>
                                <td>@polisa.ProizvodIme</td>
                                <td>@polisa.BrojNaPolisa</td>
                                <td>@polisa.BrojNaPonuda</td>
                                <td>@polisa.KlientiIdDogovoruvac</td>
                                <td>@polisa.DogovoruvacNaziv</td>
                                <td>@polisa.VkupnaPremija?.ToString("N2")</td>
                                <td>@polisa.KlientiIdOsigurenik</td>
                                <td>@polisa.OsigurenikNaziv</td>
                                <td>@(polisa.Kolektivna ? "Да" : "Не")</td>
                                <td>@(polisa.DatumVaziOd?.ToString("dd.MM.yyyy"))</td>
                                <td>@(polisa.DatumVaziDo?.ToString("dd.MM.yyyy"))</td>
                                <td>@(polisa.DatumNaIzdavanje?.ToString("dd.MM.yyyy"))</td>
                                <td>@polisa.PremijaZaNaplata?.ToString("N2")</td>
                                <td>@polisa.Uplateno?.ToString("N2")</td>
                                <td>@polisa.DolznaPremija?.ToString("N2")</td>
                                <td>@(polisa.Storno ? "Да" : "Не")</td>
                                <td>@polisa.PricinaZaStorno</td>
                                <td>@polisa.KlientiIdSorabotnik</td>
                                <td>@polisa.SorabotnikNaziv</td>
                                <td>@polisa.MaticenBroj</td>
                                <td class="text-center">
                                    @if (polisa.BrokerskaProvizijaSetting == 0)
                                    {
                                        <i class="fas fa-times text-danger" title="Нема сетирање"></i>
                                    }
                                    else if (polisa.BrokerskaProvizijaSetting >= 1)
                                    {
                                        <i class="fas fa-check text-success" title="Има сетирање"></i>
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>
                                <td class="actions-column">
                                    @if (polisa.KlasiOsiguruvanjeIdKlasa == 10 && polisa.ProduktiIdProizvod == 15)
                                    {
                                        <a href="/Polisi/ViewEditPolisaAO/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                    else if (polisa.KlasiOsiguruvanjeIdKlasa == 10 && polisa.ProduktiIdProizvod == 16)
                                    {
                                        <a href="/Polisi/ViewEditPolisaZK/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                     else if (polisa.KlasiOsiguruvanjeIdKlasa == 10 && polisa.ProduktiIdProizvod == 17)
                                    {
                                        <a href="/Polisi/ViewEditPolisaGR/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                     else if (polisa.KlasiOsiguruvanjeIdKlasa == 10 && polisa.ProduktiIdProizvod == 42)
                                    {
                                        <a href="/Polisi/ViewEditPolisaCMR/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                       else if (polisa.KlasiOsiguruvanjeIdKlasa == 10 && polisa.ProduktiIdProizvod == 43)
                                    {
                                        <a href="/Polisi/ViewEditPolisaAsistencija/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                     else if (polisa.KlasiOsiguruvanjeIdKlasa == 3)
                                    {
                                        <a href="/Polisi/ViewEditPolisaKasko/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                      else if (polisa.KlasiOsiguruvanjeIdKlasa == 1)
                                    {
                                        <a href="/Polisi/ViewEditPolisaKlasa1/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                      else if (polisa.KlasiOsiguruvanjeIdKlasa == 2)
                                    {
                                        <a href="/Polisi/ViewEditPolisaKlasa2/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                      else if (polisa.KlasiOsiguruvanjeIdKlasa == 8)
                                    {
                                        <a href="/Polisi/ViewEditPolisaKlasa8/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                      else if (polisa.KlasiOsiguruvanjeIdKlasa == 9)
                                    {
                                        <a href="/Polisi/ViewEditPolisaKlasa9/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                      else if (polisa.KlasiOsiguruvanjeIdKlasa == 10)
                                    {
                                        <a href="/Polisi/ViewEditPolisaAO/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                      else if (polisa.KlasiOsiguruvanjeIdKlasa == 18)
                                    {
                                        <a href="/Polisi/ViewEditPolisaKlasa18/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                      else if (polisa.KlasiOsiguruvanjeIdKlasa == 19)
                                    {
                                        <a href="/Polisi/ViewEditPolisaKlasa19/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                      else if (polisa.KlasiOsiguruvanjeIdKlasa == 4)
                                    {
                                        <a href="/Polisi/ViewEditPolisaKlasa4/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                      else if (polisa.KlasiOsiguruvanjeIdKlasa == 5)
                                    {
                                        <a href="/Polisi/ViewEditPolisaKlasa5/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                      else if (polisa.KlasiOsiguruvanjeIdKlasa == 6)
                                    {
                                        <a href="/Polisi/ViewEditPolisaKlasa6/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                      else if (polisa.KlasiOsiguruvanjeIdKlasa == 7)
                                    {
                                        <a href="/Polisi/ViewEditPolisaKlasa7/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                      else if (polisa.KlasiOsiguruvanjeIdKlasa == 13)
                                    {
                                        <a href="/Polisi/ViewEditPolisaKlasa13/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                      else if (polisa.KlasiOsiguruvanjeIdKlasa == 14)
                                    {
                                        <a href="/Polisi/ViewEditPolisaKlasa14/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                      else if (polisa.KlasiOsiguruvanjeIdKlasa == 15)
                                    {
                                        <a href="/Polisi/ViewEditPolisaKlasa15/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                      else if (polisa.KlasiOsiguruvanjeIdKlasa == 16)
                                    {
                                        <a href="/Polisi/ViewEditPolisaKlasa16/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                      else if (polisa.KlasiOsiguruvanjeIdKlasa == 17)
                                    {
                                        <a href="/Polisi/ViewEditPolisaKlasa17/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                      else if (polisa.KlasiOsiguruvanjeIdKlasa == 21)
                                    {
                                        <a href="/Polisi/ViewEditPolisaKlasa21/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }                                
                                    else
                                    {
                                        <a href="/Polisi/ViewPolisa/@polisa.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    }
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" />
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css" />
    <style>
        /* Remove both Bootstrap 5 sort indicators */
        table.dataTable thead th.sorting:before,
        table.dataTable thead th.sorting:after,
        table.dataTable thead th.sorting_asc:before,
        table.dataTable thead th.sorting_asc:after,
        table.dataTable thead th.sorting_desc:before,
        table.dataTable thead th.sorting_desc:after {
            display: none !important;
        }
        
        .rotate-180 {
            transform: rotate(180deg);
            transition: transform 0.3s ease;
        }

        .fa-chevron-down {
            transition: transform 0.3s ease;
        }

        #columnTogglers {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.25rem;
        }

        #columnTogglers .form-check {
            margin-bottom: 0.5rem;
        }

        .btn-light-primary {
            background-color: rgba(13, 110, 253, 0.2) !important;
            border-color: rgba(13, 110, 253, 0.2) !important;
            color: #0d6efd !important;
        }

        .btn-light-primary:hover {
            background-color: rgba(13, 110, 253, 0.4) !important;
            border-color: rgba(13, 110, 253, 0.4) !important;
            color: #0d6efd !important;
        }

        .actions-column {
            width: 120px !important;
            min-width: 120px !important;
            white-space: nowrap;
        }

        .actions-column .btn {
            padding: 0.25rem 0.5rem;
            margin-right: 0.25rem;
        }

        .actions-column .btn:last-child {
            margin-right: 0;
        }

        .table-responsive {
            cursor: grab;
            user-select: none;
        }
        
        .table-responsive.grabbing {
            cursor: grabbing;
        }

        #polisiTable {
            min-width: 100%;
            width: max-content;
        }

        #polisiTable th {
            white-space: nowrap;
        }

        /* Styling for broker commission column */
        #polisiTable td:nth-child(30) {
            text-align: center;
            width: 80px;
            min-width: 80px;
        }

        #polisiTable th:nth-child(30) {
            text-align: center;
            width: 80px;
            min-width: 80px;
        }
    </style>

    <script>
        $(document).ready(function() {
            // Handle collapse icon rotation
            $('#columnTogglers').on('show.bs.collapse', function () {
                $('.fa-chevron-down').addClass('rotate-180');
            }).on('hide.bs.collapse', function () {
                $('.fa-chevron-down').removeClass('rotate-180');
            });

            // Initialize DataTable
            var table = $('#polisiTable').DataTable({
                dom: 'Bfrtip',
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel"></i> Excel',
                        className: 'btn btn-success',
                        exportOptions: {
                            columns: ':visible'
                        }
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="fas fa-file-pdf"></i> PDF',
                        className: 'btn btn-danger',
                        exportOptions: {
                            columns: ':visible'
                        }
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print"></i> Print',
                        className: 'btn btn-info',
                        exportOptions: {
                            columns: ':visible'
                        }
                    }
                ],
                pageLength: 25,
                order: [[0, 'desc']],
                language: {
                    url: '/lib/datatables/macedonian.json'
                }
            });

            // Handle search box
            $('#searchBox').on('keyup', function() {
                table.search(this.value).draw();
            });

            // Handle column toggles
            $('.toggle-column').on('change', function() {
                var column = table.column($(this).data('column'));
                column.visible(!column.visible());
            });

            // Initially hide columns (including IDs and financial data)
            table.columns([3,4,6,7,8,9,11,12,15,16,17,18,19,20,21,22,23,24,25,26,27,28]).visible(false);

            // Add drag scroll functionality
            const tableContainer = document.querySelector('.table-responsive');
            let isDown = false;
            let startX;
            let scrollLeft;

            tableContainer.addEventListener('mousedown', (e) => {
                isDown = true;
                tableContainer.classList.add('grabbing');
                startX = e.pageX - tableContainer.offsetLeft;
                scrollLeft = tableContainer.scrollLeft;
            });

            tableContainer.addEventListener('mouseleave', () => {
                isDown = false;
                tableContainer.classList.remove('grabbing');
            });

            tableContainer.addEventListener('mouseup', () => {
                isDown = false;
                tableContainer.classList.remove('grabbing');
            });

            tableContainer.addEventListener('mousemove', (e) => {
                if (!isDown) return;
                e.preventDefault();
                const x = e.pageX - tableContainer.offsetLeft;
                const walk = (x - startX) * 2; // Adjust scrolling speed
                tableContainer.scrollLeft = scrollLeft - walk;
            });

            // Handle success message fade out
            setTimeout(function() {
                $("#successMessage").fadeOut('slow');
            }, 3000);
        });
    </script>
}
