@page
@model NextBroker.Pages.Polisi.DodajPolisaZKModel
@{
    ViewData["Title"] = "Додај полиса - Зелен Картон";
}

<h1 style='text-align: center;'>Додај полиса - Зелен Картон</h1>

<div class="container-fluid mt-4 px-4">
    <!-- Success Message -->
    <div id="successMessage" class="alert alert-success alert-dismissible fade" role="alert" style="display: none;">
        <strong>Успешно!</strong> Полисата е успешно зачувана.
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <form method="post" id="polisaForm" class="dodaj-polisa-form">
        @Html.AntiForgeryToken()
        
        <!-- Add validation summary -->
        <div asp-validation-summary="All" class="text-danger"></div>

        <!-- Basic Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Основни информации</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Осигурител</label>
                        <select asp-for="Input.KlientiIdOsiguritel" 
                                asp-items="Model.Osiguriteli" 
                                class="form-select">
                            <option value="">-- Избери осигурител --</option>
                        </select>
                        <span asp-validation-for="Input.KlientiIdOsiguritel" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Класа на осигурување</label>
                        <select asp-for="Input.KlasiOsiguruvanjeIdKlasa" 
                                asp-items="Model.KlasiOsiguruvanje" 
                                class="form-select">
                            <option value="">-- Избери класа --</option>
                        </select>
                        <span asp-validation-for="Input.KlasiOsiguruvanjeIdKlasa" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Продукт</label>
                        <select asp-for="Input.ProduktiIdProizvod" 
                                asp-items="Model.Produkti" 
                                class="form-select">
                            <option value="">-- Избери продукт --</option>
                        </select>
                        <span asp-validation-for="Input.ProduktiIdProizvod" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.BrojNaPolisa" class="form-label">Број на полиса</label>
                        <input asp-for="Input.BrojNaPolisa" class="form-control" type="text" required />
                        <span asp-validation-for="Input.BrojNaPolisa" class="text-danger"></span>
                    </div>
                    <!-- Commented out Broj na Ponuda
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.BrojNaPonuda" class="form-label">Број на понуда</label>
                        <input asp-for="Input.BrojNaPonuda" class="form-control" type="number" />
                        <span asp-validation-for="Input.BrojNaPonuda" class="text-danger"></span>
                    </div>
                    -->
                    <div class="col-md-9 mb-3">
                        <label class="form-label">Полиса Авто Одговорност (пребарувај по број на полиса)</label>
                        <div class="input-group">
                            <input type="text" id="polisaAOSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по број на полиса или осигуреник..." />
                            <input type="hidden" asp-for="Input.BrojNaAOPolisa" id="BrojNaAOPolisa" />
                        </div>
                        <div id="polisaAOSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Договорувач (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" data-target="dogovoruvac" style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="dogovoruvacMBSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                            <input type="hidden" asp-for="Input.KlientiIdDogovoruvac" id="KlientiIdDogovoruvac" />
                        </div>
                        <div id="dogovoruvacSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Осигуреник (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" data-target="osigurenik" style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="osigurenikMBSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                            <input type="hidden" asp-for="Input.KlientiIdOsigurenik" id="KlientiIdOsigurenik" />
                        </div>
                        <div id="osigurenikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Соработник/вработен (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" data-target="sorabotnik" style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="sorabotnikMBSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                            <input type="hidden" asp-for="Input.KlientiIdSorabotnik" id="KlientiIdSorabotnik" />
                        </div>
                        <div id="sorabotnikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <!-- Keep existing checkboxes -->
                        <div class="form-check mb-3">
                            <input type="checkbox" class="form-check-input" asp-for="Input.Faktoring" id="faktoring">
                            <label class="form-check-label" asp-for="Input.Faktoring">Факторинг</label>
                        </div>
                        <div class="form-check mb-3" style="display: none;">
                            <input type="checkbox" class="form-check-input" asp-for="Input.GeneriranaFakturaIzlezna" id="generiranaFakturaIzlezna">
                            <label class="form-check-label" asp-for="Input.GeneriranaFakturaIzlezna">Генерирана излезна фактура</label>
                        </div>
                        <!-- Commented out Storno field
                        <div class="form-check mb-3">
                            <input type="checkbox" class="form-check-input" asp-for="Input.Storno" id="storno">
                            <label class="form-check-label" asp-for="Input.Storno">Сторно</label>
                        </div>
                        <div id="pricinaZaStornoContainer" class="mb-3" style="display: none;">
                            <label asp-for="Input.SifrarnikPricinaZaStornoId" class="form-label">Причина за сторно</label>
                            <select asp-for="Input.SifrarnikPricinaZaStornoId" 
                                    asp-items="Model.PriciniZaStorno" 
                                    class="form-select">
                                <option value="">-- Избери причина за сторно --</option>
                            </select>
                            <span asp-validation-for="Input.SifrarnikPricinaZaStornoId" class="text-danger"></span>
                        </div>
                        -->
                        <!-- Add hidden fields to ensure values are still submitted -->
                        <div style="display: none;">
                            <input asp-for="Input.Storno" type="hidden" value="false" />
                            <input asp-for="Input.SifrarnikPricinaZaStornoId" type="hidden" />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.DatumVaziOd" class="form-label"></label>
                        <input asp-for="Input.DatumVaziOd" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.DatumVaziOd" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.DatumVaziDo" class="form-label"></label>
                        <input asp-for="Input.DatumVaziDo" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.DatumVaziDo" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.DatumNaIzdavanje" class="form-label"></label>
                        <input asp-for="Input.DatumNaIzdavanje" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.DatumNaIzdavanje" class="text-danger"></span>
                    </div>
                </div>
                <!-- Commented out Vremetraenje and Period fields
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.VremetraenjeNaPolisa" class="form-label"></label>
                        <input asp-for="Input.VremetraenjeNaPolisa" class="form-control" type="number" min="0" />
                        <span asp-validation-for="Input.VremetraenjeNaPolisa" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.PeriodNaUplata" class="form-label"></label>
                        <input asp-for="Input.PeriodNaUplata" class="form-control" type="number" min="0" />
                        <span asp-validation-for="Input.PeriodNaUplata" class="text-danger"></span>
                    </div>
                </div>
                -->
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.SifrarnikValutiIdValuta" class="form-label">Валута</label>
                        <select asp-for="Input.SifrarnikValutiIdValuta" 
                                asp-items="Model.Valuti" 
                                class="form-select">
                            <option value="">-- Избери валута --</option>
                        </select>
                        <span asp-validation-for="Input.SifrarnikValutiIdValuta" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.SifrarnikNacinNaPlakjanjeId" class="form-label">Начин на плаќање</label>
                        <select asp-for="Input.SifrarnikNacinNaPlakjanjeId" 
                                asp-items="Model.NaciniNaPlakanje" 
                                class="form-select">
                            <option value="">-- Избери начин на плаќање --</option>
                        </select>
                        <span asp-validation-for="Input.SifrarnikNacinNaPlakjanjeId" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <!-- Commented out Тип на плаќање field
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.SifrarnikTipNaPlakanjeId" class="form-label">Тип на плаќање</label>
                        <select asp-for="Input.SifrarnikTipNaPlakanjeId" 
                                asp-items="Model.TipoviNaPlakanje" 
                                class="form-select">
                            <option value="">-- Избери тип на плаќање --</option>
                        </select>
                        <span asp-validation-for="Input.SifrarnikTipNaPlakanjeId" class="text-danger"></span>
                    </div>
                    -->
                    <!-- Commented out Банка field
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.SifrarnikBankiIdBanka" class="form-label">Банка</label>
                        <select asp-for="Input.SifrarnikBankiIdBanka" 
                                asp-items="Model.Banki" 
                                class="form-select">
                            <option value="">-- Избери банка --</option>
                        </select>
                        <span asp-validation-for="Input.SifrarnikBankiIdBanka" class="text-danger"></span>
                    </div>
                    -->
                    <!-- Add hidden fields to ensure values are still submitted -->
                    <div style="display: none;">
                        <input asp-for="Input.SifrarnikTipNaPlakanjeId" type="hidden" value="1" />
                        <input asp-for="Input.SifrarnikBankiIdBanka" type="hidden" />
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.TipNaFaktura" class="form-label">Тип на фактура</label>
                        <select asp-for="Input.TipNaFaktura" 
                                asp-items="Model.TipoviNaFaktura" 
                                class="form-select"
                                required>
                            <option value="">-- Избери тип на фактура --</option>
                        </select>
                        <span asp-validation-for="Input.TipNaFaktura" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3" style="display: none;">
                        <label asp-for="Input.BrojNaFakturaVlezna" class="form-label"></label>
                        <input asp-for="Input.BrojNaFakturaVlezna" class="form-control" type="text" />
                        <span asp-validation-for="Input.BrojNaFakturaVlezna" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3" style="display: none;">
                        <label asp-for="Input.BrojNaFakturaIzlezna" class="form-label"></label>
                        <input asp-for="Input.BrojNaFakturaIzlezna" class="form-control" type="text" />
                        <span asp-validation-for="Input.BrojNaFakturaIzlezna" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3" style="display: none;">
                        <label asp-for="Input.DatumNaFakturaVlezna" class="form-label"></label>
                        <input asp-for="Input.DatumNaFakturaVlezna" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.DatumNaFakturaVlezna" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3" style="display: none;">
                        <label asp-for="Input.DatumNaIzleznaFaktura" class="form-label"></label>
                        <input asp-for="Input.DatumNaIzleznaFaktura" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.DatumNaIzleznaFaktura" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3" style="display: none;">
                        <label asp-for="Input.RokNaPlakjanjeFakturaIzlezna" class="form-label"></label>
                        <input asp-for="Input.RokNaPlakjanjeFakturaIzlezna" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.RokNaPlakjanjeFakturaIzlezna" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3" style="display: none;">
                        <label asp-for="Input.RokNaPlakjanjeFakturaVlezna" class="form-label"></label>
                        <input asp-for="Input.RokNaPlakjanjeFakturaVlezna" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.RokNaPlakjanjeFakturaVlezna" class="text-danger"></span>
                    </div>
                    <div style="display: none;">
                        
                        <input asp-for="Input.ProcentNaPopustZaFakturaVoRok" type="hidden" />
                        <input asp-for="Input.IznosZaPlakjanjeVoRok" type="hidden" />
                        <input asp-for="Input.ProcentKomercijalenPopust" type="hidden" />
                        <input asp-for="Input.ProcentFinansiski" type="hidden" />
                        
                        <input asp-for="Input.PremijaZaNaplata" type="hidden" />
                        <input asp-for="Input.Uplateno" type="hidden" />
                        <input asp-for="Input.DolznaPremija" type="hidden" />
                    </div>
                </div>
                <div class="row">
                <!--
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentKomercijalenPopust" class="form-label"></label>
                        <div class="input-group">
                            <input asp-for="Input.ProcentKomercijalenPopust" class="form-control decimal-input" type="number" step="0.0001" min="0" max="100" />
                            <span class="input-group-text">%</span>
                        </div>
                        <span asp-validation-for="Input.ProcentKomercijalenPopust" class="text-danger"></span>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.ProcentFinansiski" class="form-label"></label>
                        <div class="input-group">
                            <input asp-for="Input.ProcentFinansiski" class="form-control decimal-input" type="number" step="0.0001" min="0" max="100" />
                            <span class="input-group-text">%</span>
                        </div>
                        <span asp-validation-for="Input.ProcentFinansiski" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                -->
<!--
                    <div class="col-md-3 mb-3">
                      <label asp-for="Input.KoregiranaStapkaNaProvizija" class="form-label"></label> 
                        <div class="input-group">
                            <input asp-for="Input.KoregiranaStapkaNaProvizija" class="form-control decimal-input" type="number" step="0.0001" min="0" max="100" />
                            <span class="input-group-text">%</span>
                        </div>
                        <span asp-validation-for="Input.KoregiranaStapkaNaProvizija" class="text-danger"></span>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.PremijaZaNaplata" class="form-label"></label>
                        <input asp-for="Input.PremijaZaNaplata" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.PremijaZaNaplata" class="text-danger"></span>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.Uplateno" class="form-label"></label>
                        <input asp-for="Input.Uplateno" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.Uplateno" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.DolznaPremija" class="form-label"></label>
                        <input asp-for="Input.DolznaPremija" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.DolznaPremija" class="text-danger"></span>
                    </div>
                </div>
-->
            </div>
        </div>
        <!-- Financial Information -->

  <div class="row">
                   <div class="col-md-3 mb-3 ms-4">
                        <div class="form-group">
                            <label asp-for="Input.SifrarnikValutiIdFranshizaValuta" class="control-label"></label>
                            <select asp-for="Input.SifrarnikValutiIdFranshizaValuta" class="form-select"
                                    asp-items="Model.Valuti">
                                <option value="">-- Изберете валута --</option>
                            </select>
                            <span asp-validation-for="Input.SifrarnikValutiIdFranshizaValuta" class="text-danger"></span>
                        </div>
                    </div>                   
                <div class="row">

                    </div>
        </div>
            
            <div class="col-md-3 mb-3">
                <label asp-for="Input.FranshizaIznos" class="form-label"></label>
                <div class="input-group">
                    <input asp-for="Input.FranshizaIznos" 
                           class="form-control decimal-input" 
                           type="number" 
                           step="0.0001" />
                </div>
                <span asp-validation-for="Input.FranshizaIznos" class="text-danger"></span>
            </div>

        <div class="row">
            <div class="col-md-3 mb-3 ms-4">
                <label asp-for="Input.ProcentFranshiza" class="form-label"></label>
                <div class="input-group">
                    <input asp-for="Input.ProcentFranshiza" 
                           class="form-control decimal-input" 
                           type="number" 
                           step="0.01" 
                           min="0" 
                           max="100"
                           onchange="console.log('ProcentFranshiza changed:', this.value)" />
                    <span class="input-group-text">%</span>
                </div>
                <span asp-validation-for="Input.ProcentFranshiza" class="text-danger"></span>
            </div>
             <!--
            <div class="col-md-3 mb-3">
                <label asp-for="Input.ProcentNaPopustZaFakturaVoRok" class="form-label"></label>
                <div class="input-group">
                    <input asp-for="Input.ProcentNaPopustZaFakturaVoRok" class="form-control decimal-input" type="number" step="0.0001" min="0" max="100" />
                    <span class="input-group-text">%</span>
                </div>
                <span asp-validation-for="Input.ProcentNaPopustZaFakturaVoRok" class="text-danger"></span>
            </div>
            
            <div class="col-md-3 mb-3">
                <label asp-for="Input.IznosZaPlakjanjeVoRok" class="form-label"></label>
                <input asp-for="Input.IznosZaPlakjanjeVoRok" class="form-control decimal-input" type="number" step="0.0001" />
                <span asp-validation-for="Input.IznosZaPlakjanjeVoRok" class="text-danger"></span>
            </div>
            -->
        </div>
        <div class="row">
         <!--
            <div class="col-md-3 mb-3">
                <label asp-for="Input.ProcentKomercijalenPopust" class="form-label"></label>
                <div class="input-group">
                    <input asp-for="Input.ProcentKomercijalenPopust" class="form-control decimal-input" type="number" step="0.0001" min="0" max="100" />
                    <span class="input-group-text">%</span>
                </div>
                <span asp-validation-for="Input.ProcentKomercijalenPopust" class="text-danger"></span>
            </div>
            <div class="col-md-3 mb-3">
                <label asp-for="Input.ProcentFinansiski" class="form-label"></label>
                <div class="input-group">
                    <input asp-for="Input.ProcentFinansiski" class="form-control decimal-input" type="number" step="0.0001" min="0" max="100" />
                    <span class="input-group-text">%</span>
                </div>
                <span asp-validation-for="Input.ProcentFinansiski" class="text-danger"></span>
            </div>
            -->
            <div class="col-md-3 mb-3 ms-4">
                <label asp-for="Input.KoregiranaStapkaNaProvizija" class="form-label"></label>
                <div class="input-group">
                    <input asp-for="Input.KoregiranaStapkaNaProvizija" 
                           class="form-control decimal-input" 
                           type="number" 
                           step="0.0001" 
                           min="0" 
                           max="100"
                           onchange="console.log('KoregiranaStapkaNaProvizija changed:', this.value)" />
                    <span class="input-group-text">%</span>
                </div>
                <span asp-validation-for="Input.KoregiranaStapkaNaProvizija" class="text-danger"></span>
            </div>


        </div>
         <!--
        <div class="row">
            <div class="col-md-3 mb-3">
                <label asp-for="Input.PremijaZaNaplata" class="form-label"></label>
                <input asp-for="Input.PremijaZaNaplata" class="form-control decimal-input" type="number" step="0.0001" />
                <span asp-validation-for="Input.PremijaZaNaplata" class="text-danger"></span>
            </div>
            <div class="col-md-3 mb-3">
                <label asp-for="Input.Uplateno" class="form-label"></label>
                <input asp-for="Input.Uplateno" class="form-control decimal-input" type="number" step="0.0001" />
                <span asp-validation-for="Input.Uplateno" class="text-danger"></span>
            </div>
            <div class="col-md-3 mb-3">
                <label asp-for="Input.DolznaPremija" class="form-label"></label>
                <input asp-for="Input.DolznaPremija" class="form-control decimal-input" type="number" step="0.0001" />
                <span asp-validation-for="Input.DolznaPremija" class="text-danger"></span>
            </div>
        </div>
        -->
        <!-- Auto Odgovornost -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center" role="button" data-bs-toggle="collapse" data-bs-target="#autoOdgovornostCollapse" aria-expanded="false" aria-controls="autoOdgovornostCollapse">
                <h5 class="mb-0">Авто Одговорност Зелен Картон</h5>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div id="autoOdgovornostCollapse" class="collapse">
                <div class="card-body">
                    <div class="row mb-3 d-none">
                        <div class="col-12">
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" asp-for="Input.KlasiOsiguruvanjeKlasa1" id="klasaOsiguruvanje1">
                                <label class="form-check-label" asp-for="Input.KlasiOsiguruvanjeKlasa1">Осигурување на патници</label>
                            </div>
                            <div class="form-check mb-3">
                                <input type="checkbox" class="form-check-input" asp-for="Input.KlasiOsiguruvanjeKlasa8" id="klasaOsiguruvanje8">
                                <label class="form-check-label" asp-for="Input.KlasiOsiguruvanjeKlasa8">Осигурување на стакла</label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.OsnovnaPremija" class="form-label">Основна премија ЗК</label>
                            <input asp-for="Input.OsnovnaPremija" class="form-control decimal-input" type="number" step="0.0001" />
                            <span asp-validation-for="Input.OsnovnaPremija" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3 d-none">
                            <label asp-for="Input.Bonus" class="form-label">Бонус ЗК %</label>
                            <div class="input-group">
                                <input asp-for="Input.Bonus" class="form-control decimal-input" type="number" step="0.0001" />
                                <span class="input-group-text">%</span>
                            </div>
                            <span asp-validation-for="Input.Bonus" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3 d-none">
                            <label asp-for="Input.Malus" class="form-label">Малус ЗК %</label>
                            <div class="input-group">
                                <input asp-for="Input.Malus" class="form-control decimal-input" type="number" step="0.0001" />
                                <span class="input-group-text">%</span>
                            </div>
                            <span asp-validation-for="Input.Malus" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3 d-none">
                            <label asp-for="Input.Doplatoci" class="form-label">Доплатоци</label>
                            <input asp-for="Input.Doplatoci" class="form-control decimal-input" type="number" step="0.0001" />
                            <span asp-validation-for="Input.Doplatoci" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3 d-none">
                            <label asp-for="Input.Popusti" class="form-label">Попусти</label>
                            <input asp-for="Input.Popusti" class="form-control decimal-input" type="number" step="0.0001" />
                            <span asp-validation-for="Input.Popusti" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3 d-none">
                            <label asp-for="Input.VkupnoOsnovnaPremijaAO" class="form-label">Вкупно основна премија ЗК</label>
                            <input asp-for="Input.VkupnoOsnovnaPremijaAO" class="form-control decimal-input" type="number" step="0.0001" readonly />
                            <span asp-validation-for="Input.VkupnoOsnovnaPremijaAO" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3 d-none">
                            <label asp-for="Input.OsigurenaSumaNezgodaAO" class="form-label">Осигурена сума за незгода</label>
                            <input asp-for="Input.OsigurenaSumaNezgodaAO" class="form-control decimal-input" type="number" step="0.0001" />
                            <span asp-validation-for="Input.OsigurenaSumaNezgodaAO" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3 d-none">
                            <label asp-for="Input.PremijaZaNezgodaAO" class="form-label">Премија за незгода ЗК</label>
                            <input asp-for="Input.PremijaZaNezgodaAO" class="form-control decimal-input" type="number" step="0.0001" />
                            <span asp-validation-for="Input.PremijaZaNezgodaAO" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3 d-none">
                            <label asp-for="Input.PremijaKrsenjeStakloAO" class="form-label">Премија за кршење на стакло</label>
                            <input asp-for="Input.PremijaKrsenjeStakloAO" class="form-control decimal-input" type="number" step="0.0001" />
                            <span asp-validation-for="Input.PremijaKrsenjeStakloAO" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.VkupnaPremija" class="form-label">Вкупна премија ЗК</label>
                            <input asp-for="Input.VkupnaPremija" class="form-control decimal-input" type="number" step="0.0001" readonly style="background-color: #e9ecef;" />
                            <span asp-validation-for="Input.VkupnaPremija" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3 d-none">
                      
                        </div>
                        <div class="col-md-3 mb-3 d-none">
                       
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3 d-none">                         
       
                        </div>
                        <div class="col-md-3 mb-3 d-none">
                 
                        </div>
                        <div class="col-md-3 mb-3 d-none">

                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3 d-none">
                            <label asp-for="Input.UplatenoAO" class="form-label">Уплатено ЗК</label>
                            <input asp-for="Input.UplatenoAO" class="form-control decimal-input" type="number" step="0.0001" />
                            <span asp-validation-for="Input.UplatenoAO" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3 d-none">
                            <label asp-for="Input.DolznaPremijaAO" class="form-label">Должна премија ЗК</label>
                            <input asp-for="Input.DolznaPremijaAO" class="form-control decimal-input" type="number" step="0.0001" readonly />
                            <span asp-validation-for="Input.DolznaPremijaAO" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!-- Попусти -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center" role="button" data-bs-toggle="collapse" data-bs-target="#popustiCollapse" aria-expanded="false" aria-controls="popustiCollapse">
                <h5 class="mb-0">Попусти</h5>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div id="popustiCollapse" class="collapse">
                <div class="card-body">
                    
                    <div class="col-md-3 mb-3">
                            <label asp-for="Input.ProcentNaPopustZaFakturaVoRokAO" class="form-label">Процент на попуст за фактура во рок ЗК</label>
                            <div class="input-group">
                                <input asp-for="Input.ProcentNaPopustZaFakturaVoRokAO" class="form-control decimal-input" type="number" step="0.0001" />
                                <span class="input-group-text">%</span>
                            </div>
                            <span asp-validation-for="Input.ProcentNaPopustZaFakturaVoRokAO" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.IznosZaPlakjanjeVoRokAO" class="form-label">Износ за плаќање во рок ЗК</label>
                            <input asp-for="Input.IznosZaPlakjanjeVoRokAO" class="form-control decimal-input" type="number" step="0.0001" readonly style="background-color: #e9ecef;" />
                            <span asp-validation-for="Input.IznosZaPlakjanjeVoRokAO" class="text-danger"></span>
                        </div>
                   
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.ProcentKomercijalenPopustAO" class="form-label">Процент на комерцијален попуст ЗК</label>
                            <div class="input-group">
                                <input asp-for="Input.ProcentKomercijalenPopustAO" class="form-control decimal-input" type="number" step="0.0001" />
                                <span class="input-group-text">%</span>
                            </div>
                            <span asp-validation-for="Input.ProcentKomercijalenPopustAO" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.ProcentFinansiskiPopustAO" class="form-label">Процент на финансиски попуст ЗК</label>
                            <div class="input-group">
                                <input asp-for="Input.ProcentFinansiskiPopustAO" class="form-control decimal-input" type="number" step="0.0001" />
                                <span class="input-group-text">%</span>
                            </div>
                            <span asp-validation-for="Input.ProcentFinansiskiPopustAO" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.PremijaZaNaplataAO" class="form-label">Премија за наплата ЗК</label>
                            <input asp-for="Input.PremijaZaNaplataAO" class="form-control decimal-input" type="number" step="0.0001" readonly style="background-color: #e9ecef;" />
                            <span asp-validation-for="Input.PremijaZaNaplataAO" class="text-danger"></span>
                        </div>
                    </div>
                 </div>
                </div>
            </div>
        </div>


        <!-- Soobrakjajna -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center" role="button" data-bs-toggle="collapse" data-bs-target="#soobrakjajnaCollapse" aria-expanded="false" aria-controls="soobrakjajnaCollapse">
                <h5 class="mb-0">Сообраќајна</h5>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div id="soobrakjajnaCollapse" class="collapse">
                <div class="card-body">

<h5 class="mb-3">Група 1 - Основни податоци</h5>

                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.SifrarnikTipNaVoziloId" class="form-label">Тип на возило</label>
                            <select asp-for="Input.SifrarnikTipNaVoziloId" 
                                    asp-items="Model.TipoviNaVozilo" 
                                    class="form-select">
                                <option value="">-- Избери тип на возило --</option>
                            </select>
                            <span asp-validation-for="Input.SifrarnikTipNaVoziloId" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.RegisterskaOznaka" class="form-label">Регистарска ознака</label>
                            <input asp-for="Input.RegisterskaOznaka" class="form-control" maxlength="255" id="registerskaOznakaInput" />
                            <span asp-validation-for="Input.RegisterskaOznaka" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.Marka" class="form-label">Марка</label>
                            <input asp-for="Input.Marka" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.Marka" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.KomercijalnaOznaka" class="form-label">Комерцијална ознака</label>
                            <input asp-for="Input.KomercijalnaOznaka" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.KomercijalnaOznaka" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.Shasija" class="form-label">Број на шасија</label>
                            <input asp-for="Input.Shasija" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.Shasija" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.GodinaNaProizvodstvo" class="form-label">Година на производство</label>
                            <input asp-for="Input.GodinaNaProizvodstvo" class="form-control" type="number" min="1900" max="2100" />
                            <span asp-validation-for="Input.GodinaNaProizvodstvo" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.ZafatninaNaMotorotcm3" class="form-label">Зафатнина на моторот (cm³)</label>
                            <input asp-for="Input.ZafatninaNaMotorotcm3" class="form-control" type="number" min="0" />
                            <span asp-validation-for="Input.ZafatninaNaMotorotcm3" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.SilinaNaMotorotKW" class="form-label">Силина на моторот (kW)</label>
                            <input asp-for="Input.SilinaNaMotorotKW" class="form-control" type="number" min="0" />
                            <span asp-validation-for="Input.SilinaNaMotorotKW" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.BrojNaSedista" class="form-label">Број на седишта</label>
                            <input asp-for="Input.BrojNaSedista" class="form-control" type="number" min="0" />
                            <span asp-validation-for="Input.BrojNaSedista" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.NosivostKG" class="form-label">Носивост (kg)</label>
                            <input asp-for="Input.NosivostKG" class="form-control" type="number" min="0" />
                            <span asp-validation-for="Input.NosivostKG" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label asp-for="Input.BojaNaVoziloto" class="form-label">Боја на возилото</label>
                            <input asp-for="Input.BojaNaVoziloto" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.BojaNaVoziloto" class="text-danger"></span>
                        </div>
                    </div>

<h5 class="mb-3">Група 2 - Дополнителни податоци</h5>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.DatumNaRegistracija" class="form-label">Датум на регистрација</label>
                            <input asp-for="Input.DatumNaRegistracija" class="form-control datepicker" type="date" />
                            <span asp-validation-for="Input.DatumNaRegistracija" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.DatumNaPrvataRegistracija" class="form-label">Датум на првата регистрација</label>
                            <input asp-for="Input.DatumNaPrvataRegistracija" class="form-control datepicker" type="date" />
                            <span asp-validation-for="Input.DatumNaPrvataRegistracija" class="text-danger"></span>
                        </div>
                    </div>
                    <!-- Податоци за корисник -->
                    <div class="row mt-4">
                        <h6 class="mb-3">Податоци за корисник</h6>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.BrojNaVpisot" class="form-label">Број на впис</label>
                            <input asp-for="Input.BrojNaVpisot" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.BrojNaVpisot" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.PrezimeNazivNaKorisnikot" class="form-label">Презиме/Назив на корисникот</label>
                            <input asp-for="Input.PrezimeNazivNaKorisnikot" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.PrezimeNazivNaKorisnikot" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.Ime" class="form-label">Име</label>
                            <input asp-for="Input.Ime" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.Ime" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label asp-for="Input.AdresaNaPostojanoZivealiste" class="form-label">Адреса на постојано живеалиште</label>
                            <input asp-for="Input.AdresaNaPostojanoZivealiste" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.AdresaNaPostojanoZivealiste" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.EMBNaKorisnikot" class="form-label">ЕМБ на корисникот</label>
                            <input asp-for="Input.EMBNaKorisnikot" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.EMBNaKorisnikot" class="text-danger"></span>
                        </div>
                    </div>
                    <!-- Податоци за регистрација -->
                    <div class="row mt-4">
                        <h6 class="mb-3">Податоци за регистрација</h6>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.DatumNaPrvaRegistracijaVoRSM" class="form-label">Датум на прва регистрација во РСМ</label>
                            <input asp-for="Input.DatumNaPrvaRegistracijaVoRSM" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.DatumNaPrvaRegistracijaVoRSM" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.DozvolataJaIzdal" class="form-label">Дозволата ја издал</label>
                            <input asp-for="Input.DozvolataJaIzdal" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.DozvolataJaIzdal" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.OznakaNaOdobrenie" class="form-label">Ознака на одобрение</label>
                            <input asp-for="Input.OznakaNaOdobrenie" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.OznakaNaOdobrenie" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.BrojNAEUPotvrdaZaSoobraznost" class="form-label">Број на ЕУ потврда за сообразност</label>
                            <input asp-for="Input.BrojNAEUPotvrdaZaSoobraznost" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.BrojNAEUPotvrdaZaSoobraznost" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.PrezimeNazivNaSopstvenikot" class="form-label">Презиме/Назив на сопственикот</label>
                            <input asp-for="Input.PrezimeNazivNaSopstvenikot" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.PrezimeNazivNaSopstvenikot" class="text-danger"></span>
                        </div>
                    </div>
                    <!-- Податоци за сопственик -->
                    <div class="row mt-4">
                        <h6 class="mb-3">Податоци за сопственик</h6>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.ImeSopstvenik" class="form-label">Име</label>
                            <input asp-for="Input.ImeSopstvenik" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.ImeSopstvenik" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.EMBNaFizickoLiceEMBNaPravnoLice" class="form-label">ЕМБ на физичко/правно лице</label>
                            <input asp-for="Input.EMBNaFizickoLiceEMBNaPravnoLice" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.EMBNaFizickoLiceEMBNaPravnoLice" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label asp-for="Input.AdresaNaPostojanoZivealisteSediste" class="form-label">Адреса на постојано живеалиште/седиште</label>
                            <input asp-for="Input.AdresaNaPostojanoZivealisteSediste" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.AdresaNaPostojanoZivealisteSediste" class="text-danger"></span>
                        </div>
                    </div>
                    <!-- Технички податоци за возилото -->
                    <div class="row mt-4">
                        <h6 class="mb-3">Технички податоци за возилото</h6>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.KategorijaIVidNaVoziloto" class="form-label">Категорија и вид на возилото</label>
                            <input asp-for="Input.KategorijaIVidNaVoziloto" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.KategorijaIVidNaVoziloto" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.OblikINamenaNaKaroserijata" class="form-label">Облик и намена на каросеријата</label>
                            <input asp-for="Input.OblikINamenaNaKaroserijata" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.OblikINamenaNaKaroserijata" class="text-danger"></span>
                        </div>
                    </div>
                    <!-- Податоци за моторот -->
                    <div class="row mt-4">
                        <h6 class="mb-3">Податоци за моторот</h6>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.TipNaMotorot" class="form-label">Тип на моторот</label>
                            <input asp-for="Input.TipNaMotorot" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.TipNaMotorot" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.VidNaGorivo" class="form-label">Вид на гориво</label>
                            <input asp-for="Input.VidNaGorivo" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.VidNaGorivo" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.BrojNaVrtezi" class="form-label">Број на вртежи</label>
                            <input asp-for="Input.BrojNaVrtezi" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.BrojNaVrtezi" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label asp-for="Input.IdentifikacionenBrojNaMotorot" class="form-label">Идентификационен број на моторот</label>
                            <input asp-for="Input.IdentifikacionenBrojNaMotorot" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.IdentifikacionenBrojNaMotorot" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.MaksimalnaBrzinaKM" class="form-label">Максимална брзина (km/h)</label>
                            <input asp-for="Input.MaksimalnaBrzinaKM" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.MaksimalnaBrzinaKM" class="text-danger"></span>
                        </div>
                    </div>
                    <!-- Податоци за маса -->
                    <div class="row mt-4">
                        <h6 class="mb-3">Податоци за маса</h6>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.OdnosSilinaMasa" class="form-label">Однос сила/маса</label>
                            <input asp-for="Input.OdnosSilinaMasa" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.OdnosSilinaMasa" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.MasaNaVoziloto" class="form-label">Маса на возилото</label>
                            <input asp-for="Input.MasaNaVoziloto" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.MasaNaVoziloto" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG" class="form-label">Најголема конструктивна маса (kg)</label>
                            <input asp-for="Input.NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG" class="form-label">Најголема легална маса (kg)</label>
                            <input asp-for="Input.NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG" class="form-label">Најголема легална маса на група (kg)</label>
                            <input asp-for="Input.NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Забелешка field at the bottom -->
        <div class="row mt-4">
            <div class="col-12">
                <label asp-for="Input.Zabeleska" class="form-label">Забелешка</label>
                <textarea asp-for="Input.Zabeleska" 
                          class="form-control" 
                          rows="3"
                          placeholder="Внесете забелешка..."></textarea>
                <span asp-validation-for="Input.Zabeleska" class="text-danger"></span>
            </div>
        </div>
        <!-- Save Button -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-save me-2"></i>Зачувај
                </button>
            </div>
        </div>
    </form>
</div>

@section Scripts {
    <script>
        // Define the function globally
        function openAddClientWindow(sourceField) {
            const width = 800;
            const height = 600;
            const left = (window.screen.width - width) / 2;
            const top = (window.screen.height - height) / 2;
            
            const popup = window.open(`/Klienti/DodajKlient?fromPolisa=true&source=${sourceField}`, 'DodajKlient', 
                `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`);
            
            // Listen for messages from the popup
            window.addEventListener('message', function(event) {
                if (event.data.type === 'clientAdded') {
                    // Clear the search field based on the source
                    if (event.data.source === 'dogovoruvac') {
                        $('#dogovoruvacMBSearch').val('');
                        $('#KlientiIdDogovoruvac').val('');
                        $('#dogovoruvacSearchResults').hide();
                    } else if (event.data.source === 'osigurenik') {
                        $('#osigurenikMBSearch').val('');
                        $('#KlientiIdOsigurenik').val('');
                        $('#osigurenikSearchResults').hide();
                    } else if (event.data.source === 'sorabotnik') {
                        $('#sorabotnikMBSearch').val('');
                        $('#KlientiIdSorabotnik').val('');
                        $('#sorabotnikSearchResults').hide();
                    }
                }
            });
        }
    </script>

    <style>
        #dogovoruvacSearchResults .list-group-item.text-center {
            padding: 1rem;
        }
        #dogovoruvacSearchResults .list-group-item.text-center p {
            color: #6c757d;
            margin-bottom: 0.75rem;
        }
        #dogovoruvacSearchResults .btn-primary {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }
    </style>
    <script>
        $(document).ready(function() {
            // Function to create search functionality
            function createSearchFunctionality(searchInputId, resultsContainerId, hiddenInputId, searchHandler = 'SearchKlienti') {
                let searchTimeout;
                const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();

                $(`#${searchInputId}`).on('input', function() {
                    clearTimeout(searchTimeout);
                    const searchTerm = $(this).val();
                    const resultsDiv = $(`#${resultsContainerId}`);

                    if (searchTerm.length < 1) {
                        resultsDiv.hide();
                        return;
                    }

                    searchTimeout = setTimeout(function() {
                        $.ajax({
                            url: `?handler=${searchHandler}`,
                            type: 'GET',
                            data: { mb: searchTerm },
                            headers: {
                                "RequestVerificationToken": antiForgeryToken
                            },
                            success: function(data) {
                                if (data && data.length > 0) {
                                    let html = '<div class="list-group">';
                                    data.forEach(item => {
                                        let displayText = '';
                                        if (item.tip === 'P') {
                                            displayText = `${item.naziv}`;
                                            let identifiers = [];
                                            if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                            if (item.edb) identifiers.push(`ЕДБ: ${item.edb}`);
                                            if (identifiers.length > 0) {
                                                displayText += ` (${identifiers.join(', ')})`;
                                            }
                                        } else {
                                            displayText = `${item.ime} ${item.prezime}`;
                                            let identifiers = [];
                                            if (item.embg) identifiers.push(`ЕМБГ: ${item.embg}`);
                                            if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                            if (identifiers.length > 0) {
                                                displayText += ` (${identifiers.join(', ')})`;
                                            }
                                        }
                                        html += `<a href="#" class="list-group-item list-group-item-action" data-id="${item.id}">
                                                  ${displayText}
                                               </a>`;
                                    });
                                    html += '</div>';
                                    resultsDiv.html(html).show();
                                } else {
                                    resultsDiv.html(`
                                        <div class="list-group">
                                            <div class="list-group-item text-center">
                                                <p class="mb-2">Нема пронајдени резултати</p>
                                                <button type="button" class="btn btn-primary" onclick="openAddClientWindow('${
                                                    searchInputId === 'dogovoruvacMBSearch' ? 'dogovoruvac' : 
                                                    searchInputId === 'osigurenikMBSearch' ? 'osigurenik' : 'sorabotnik'
                                                }')">
                                                    <i class="fas fa-plus"></i> Додај клиент
                                                </button>
                                            </div>
                                        </div>
                                    `).show();
                                }
                            },
                            error: function() {
                                resultsDiv.html(`
                                    <div class="list-group">
                                        <div class="list-group-item text-center">
                                            <p class="text-danger mb-2">Грешка при пребарување</p>
                                            <button type="button" class="btn btn-primary" onclick="openAddClientWindow('${
                                                searchInputId === 'dogovoruvacMBSearch' ? 'dogovoruvac' : 
                                                searchInputId === 'osigurenikMBSearch' ? 'osigurenik' : 'sorabotnik'
                                            }')">
                                                <i class="fas fa-plus"></i> Додај клиент
                                            </button>
                                        </div>
                                    </div>
                                `).show();
                            }
                        });
                    }, 300);
                });

                // Handle selection
                $(document).on('click', `#${resultsContainerId} .list-group-item`, function(e) {
                    e.preventDefault();
                    const id = $(this).data('id');
                    const displayText = $(this).text();
                    $(`#${searchInputId}`).val(displayText.trim());
                    $(`#${hiddenInputId}`).val(id);
                    $(`#${resultsContainerId}`).hide();
                });

                // Hide results when clicking outside
                $(document).on('click', function(e) {
                    if (!$(e.target).closest(`#${searchInputId}, #${resultsContainerId}`).length) {
                        $(`#${resultsContainerId}`).hide();
                    }
                });
            }

            // Initialize search for all fields
            createSearchFunctionality('dogovoruvacMBSearch', 'dogovoruvacSearchResults', 'KlientiIdDogovoruvac');
            createSearchFunctionality('osigurenikMBSearch', 'osigurenikSearchResults', 'KlientiIdOsigurenik');
            createSearchFunctionality('sorabotnikMBSearch', 'sorabotnikSearchResults', 'KlientiIdSorabotnik', 'SearchSorabotnici');

            // Handle decimal input formatting
            $('.decimal-input').on('input', function() {
                let value = $(this).val();
                if (value !== '') {
                    // Ensure we don't exceed 4 decimal places
                    let parts = value.split('.');
                    if (parts.length > 1 && parts[1].length > 4) {
                        $(this).val(Number(value).toFixed(4));
                    }
                }
            });

            // Show/hide zabeleska field based on checkbox
            $('#kolektivnaNeodredena').change(function() {
                $('#neodredenBrZabeleskaContainer').toggle(this.checked);
            });

            /* Commented out because the Storno checkbox has been hidden
            // Show/hide pricina za storno field based on checkbox
            $('#storno').change(function() {
                $('#pricinaZaStornoContainer').toggle(this.checked);
            });
            */

            // Handle form submission
            $('#polisaForm').on('submit', function(e) {
                e.preventDefault();
                
                $.ajax({
                    url: window.location.pathname,
                    type: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        if (response.success) {
                            // Show success message
                            $('#successMessage').addClass('show').show();
                            
                            // Wait briefly to show the success message before redirecting
                            setTimeout(function() {
                                window.location.href = '/Polisi/ListaPolisi';
                            }, 1000);
                        } else {
                            // Handle validation errors
                            // Create or update error message div
                            if ($('#errorMessage').length === 0) {
                                $('<div id="errorMessage" class="alert alert-danger" role="alert">' +
                                    '<pre style="white-space: pre-wrap;"></pre>' +
                                    '</div>').insertBefore('#polisaForm');
                            }
                            $('#errorMessage pre').html(response.errorMessage);
                            $('#errorMessage').show();

                            // Scroll to error message
                            $('html, body').animate({
                                scrollTop: $('#errorMessage').offset().top - 100
                            }, 200);
                        }
                    },
                    error: function(xhr, status, error) {
                        if ($('#errorMessage').length === 0) {
                            $('<div id="errorMessage" class="alert alert-danger" role="alert">' +
                                '<pre style="white-space: pre-wrap;"></pre>' +
                                '</div>').insertBefore('#polisaForm');
                        }
                        $('#errorMessage pre').html('Ajax Error: ' + error + '<br>Status: ' + status);
                        $('#errorMessage').show();

                        // Scroll to error message
                        $('html, body').animate({
                            scrollTop: $('#errorMessage').offset().top - 100
                        }, 200);
                    }
                });
            });

            // Function to calculate VkupnoOsnovnaPremijaAO
            function calculateVkupnoOsnovnaPremija() {
                let osnovnaPremija = parseFloat($('#Input_OsnovnaPremija').val()) || 0;
                let bonus = parseFloat($('#Input_Bonus').val()) || 0;
                let malus = parseFloat($('#Input_Malus').val()) || 0;
                let doplatoci = parseFloat($('#Input_Doplatoci').val()) || 0;
                let popusti = parseFloat($('#Input_Popusti').val()) || 0;

                // Apply bonus/malus percentages
                let bonusAmount = osnovnaPremija * (bonus / 100);
                let malusAmount = osnovnaPremija * (malus / 100);

                // Calculate total
                let vkupno = osnovnaPremija + malusAmount - bonusAmount + doplatoci - popusti;
                
                // Update the field, limiting to 4 decimal places
                $('#Input_VkupnoOsnovnaPremijaAO').val(vkupno.toFixed(4));
                
                // Trigger VkupnaPremija calculation after updating VkupnoOsnovnaPremija
                calculateVkupnaPremija();
            }

            // Function to calculate VkupnaPremija
            function calculateVkupnaPremija() {
                let vkupnoOsnovnaPremija = parseFloat($('#Input_VkupnoOsnovnaPremijaAO').val()) || 0;
                let premijaZaNezgoda = parseFloat($('#Input_PremijaZaNezgodaAO').val()) || 0;
                let premijaKrsenjeStaklo = parseFloat($('#Input_PremijaKrsenjeStakloAO').val()) || 0;

                let vkupnaPremija = vkupnoOsnovnaPremija + premijaZaNezgoda + premijaKrsenjeStaklo;
                $('#Input_VkupnaPremija').val(vkupnaPremija.toFixed(4));

                // Trigger calculation of IznosZaPlakjanjeVoRok
                calculateIznosZaPlakjanjeVoRok();
            }

            // Function to calculate IznosZaPlakjanjeVoRok
            function calculateIznosZaPlakjanjeVoRok() {
                let vkupnaPremija = parseFloat($('#Input_VkupnaPremija').val()) || 0;
                let procentPopust = parseFloat($('#Input_ProcentNaPopustZaFakturaVoRokAO').val()) || 0;

                let popustAmount = vkupnaPremija * (procentPopust / 100);
                let iznos = vkupnaPremija - popustAmount;
                $('#Input_IznosZaPlakjanjeVoRokAO').val(iznos.toFixed(4));

                // Trigger calculation of PremijaZaNaplata
                calculatePremijaZaNaplata();
            }

            // Function to calculate PremijaZaNaplata
            function calculatePremijaZaNaplata() {
                let iznosZaPlakjanjeVoRok = parseFloat($('#Input_IznosZaPlakjanjeVoRokAO').val()) || 0;
                let procentKomercijalen = parseFloat($('#Input_ProcentKomercijalenPopustAO').val()) || 0;
                let procentFinansiski = parseFloat($('#Input_ProcentFinansiskiPopustAO').val()) || 0;

                let komercijalenPopust = iznosZaPlakjanjeVoRok * (procentKomercijalen / 100);
                let finansiskiPopust = iznosZaPlakjanjeVoRok * (procentFinansiski / 100);
                
                let premija = iznosZaPlakjanjeVoRok - komercijalenPopust - finansiskiPopust;
                $('#Input_PremijaZaNaplataAO').val(premija.toFixed(4));

                // Trigger calculation of DolznaPremija
                calculateDolznaPremija();
            }

            // Function to calculate DolznaPremija
            function calculateDolznaPremija() {
                let premijaZaNaplata = parseFloat($('#Input_PremijaZaNaplataAO').val()) || 0;
                let uplateno = parseFloat($('#Input_UplatenoAO').val()) || 0;

                let dolznaPremija = premijaZaNaplata - uplateno;
                $('#Input_DolznaPremijaAO').val(dolznaPremija.toFixed(4));
            }

            // Attach the calculation to all relevant input changes
            $('#Input_OsnovnaPremija, #Input_Bonus, #Input_Malus, #Input_Doplatoci, #Input_Popusti')
                .on('input', calculateVkupnoOsnovnaPremija);

            // Attach new calculations to their respective inputs
            $('#Input_PremijaZaNezgodaAO, #Input_PremijaKrsenjeStakloAO')
                .on('input', calculateVkupnaPremija);

            $('#Input_ProcentNaPopustZaFakturaVoRokAO')
                .on('input', calculateIznosZaPlakjanjeVoRok);

            $('#Input_ProcentKomercijalenPopustAO, #Input_ProcentFinansiskiPopustAO')
                .on('input', calculatePremijaZaNaplata);

            $('#Input_UplatenoAO')
                .on('input', calculateDolznaPremija);

            // Handle collapse icon rotation
            $('.collapse').on('show.bs.collapse hide.bs.collapse', function() {
                $(this)
                    .parent()
                    .find('.card-header')
                    .find('.fas')
                    .toggleClass('rotated');
            });

            // Initialize search for Polisa AO
            let polisaAOTimeout;
            $('#polisaAOSearch').on('input', function() {
                clearTimeout(polisaAOTimeout);
                const searchTerm = $(this).val();
                const resultsContainer = $('#polisaAOSearchResults');

                if (searchTerm.length < 1) {
                    resultsContainer.hide();
                    return;
                }

                polisaAOTimeout = setTimeout(() => {
                    $.get(`?handler=SearchPolisiAO&searchTerm=${encodeURIComponent(searchTerm)}`, function(data) {
                        resultsContainer.empty();
                        if (data.length > 0) {
                            data.forEach(item => {
                                resultsContainer.append(`
                                    <div class="list-group-item list-group-item-action" 
                                         data-id="${item.id}" 
                                         style="cursor: pointer;">
                                        <div class="d-flex justify-content-between">
                                            <strong>Полиса бр. ${item.brojPolisa}</strong>
                                            <small>${item.datumOd} - ${item.datumDo}</small>
                                        </div>
                                        <div class="small">${item.osigurenik}</div>
                                    </div>
                                `);
                            });
                            resultsContainer.show();
                        } else {
                            resultsContainer.hide();
                        }
                    });
                }, 300);
            });

            // Handle selection of Polisa AO
            $(document).on('click', '#polisaAOSearchResults .list-group-item', function() {
                const id = $(this).data('id');
                const displayText = $(this).find('strong').text();
                $('#polisaAOSearch').val(displayText);
                $('#BrojNaAOPolisa').val(id);
                $('#polisaAOSearchResults').hide();
            });

            // Hide results when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('#polisaAOSearch, #polisaAOSearchResults').length) {
                    $('#polisaAOSearchResults').hide();
                }
            });

            // Add click handler for clear field buttons
            $('.clear-field').click(function() {
                const target = $(this).data('target');
                switch(target) {
                    case 'dogovoruvac':
                        $('#dogovoruvacMBSearch').val('');
                        $('#KlientiIdDogovoruvac').val('');
                        $('#dogovoruvacSearchResults').hide();
                        break;
                    case 'osigurenik':
                        $('#osigurenikMBSearch').val('');
                        $('#KlientiIdOsigurenik').val('');
                        $('#osigurenikSearchResults').hide();
                        break;
                    case 'sorabotnik':
                        $('#sorabotnikMBSearch').val('');
                        $('#KlientiIdSorabotnik').val('');
                        $('#sorabotnikSearchResults').hide();
                        break;
                }
            });
            // Add policy number check
            $('#Input_BrojNaPolisa').on('change', function() {
                const brojNaPolisa = $(this).val();
                const errorDiv = $('#brojNaPolisa-error');
                if (!errorDiv.length) {
                    $('<div id="brojNaPolisa-error" class="text-danger" style="margin-bottom: 0.5rem;"></div>').insertBefore($(this));
                }
                
                if (brojNaPolisa) {
                    $.get('?handler=CheckBrojNaPolisa', { brojNaPolisa: brojNaPolisa })
                        .done(function(data) {
                            if (data.exists) {
                                $('#brojNaPolisa-error').text('Веќе постои полиса со овој број која не е сторнирана!');
                                $('#Input_BrojNaPolisa').addClass('is-invalid');
                                // Add a custom validation attribute to prevent form submission
                                $('#Input_BrojNaPolisa').attr('data-val-duplicate', 'true');
                            } else {
                                $('#brojNaPolisa-error').text('');
                                $('#Input_BrojNaPolisa').removeClass('is-invalid');
                                $('#Input_BrojNaPolisa').removeAttr('data-val-duplicate');
                            }
                        });
                } else {
                    $('#brojNaPolisa-error').text('');
                    $('#Input_BrojNaPolisa').removeClass('is-invalid');
                    $('#Input_BrojNaPolisa').removeAttr('data-val-duplicate');
                }
            });

            // Add form submission check
            $('form').on('submit', function(e) {
                if ($('#Input_BrojNaPolisa').attr('data-val-duplicate') === 'true') {
                    e.preventDefault();
                    return false;
                }
            });

            // Add date validation for DatumVaziOd and DatumVaziDo
            function validateDates() {
                const vaziOd = $('#Input_DatumVaziOd').val();
                const vaziDo = $('#Input_DatumVaziDo').val();
                
                if (vaziOd && vaziDo) {
                    const dateVaziOd = new Date(vaziOd);
                    const dateVaziDo = new Date(vaziDo);
                    
                    if (dateVaziDo < dateVaziOd) {
                        // Add error message if it doesn't exist
                        if (!$('#vaziDoError').length) {
                            $('#Input_DatumVaziDo').after('<span id="vaziDoError" class="text-danger">Датумот "Важи до" не може да биде пред "Важи од"</span>');
                        }
                        // Set VaziDo to VaziOd
                        $('#Input_DatumVaziDo').val(vaziOd);
                        return false;
                    } else {
                        // Remove error message if exists
                        $('#vaziDoError').remove();
                        return true;
                    }
                }
                return true;
            }

            // Attach the event handler after page load
            $(function() {
                $('#Input_DatumVaziOd, #Input_DatumVaziDo').on('blur change', validateDates);
                
                // Run initial validation
                validateDates();
            });

            // Remove any existing input event handlers for these fields since they're now read-only
            $('#Input_VkupnaPremija, #Input_IznosZaPlakjanjeVoRok, #Input_PremijaZaNaplata').off('input');

            // Add new function to calculate months between dates
            function getMonthsBetweenDates(startDate, endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);
                
                // Calculate the difference in months
                return (end.getFullYear() - start.getFullYear()) * 12 + 
                       (end.getMonth() - start.getMonth()) + 
                       (end.getDate() >= start.getDate() ? 1 : 0);
            }

            // Add function to update payment type dropdown
            function updatePaymentTypeOptions() {
                const vaziOd = $('#Input_DatumVaziOd').val();
                const vaziDo = $('#Input_DatumVaziDo').val();
                
                if (!vaziOd || !vaziDo) return;

                const monthsDiff = getMonthsBetweenDates(vaziOd, vaziDo);
                const paymentTypeSelect = $('#Input_SifrarnikNacinNaPlakjanjeId');
                
                // Store the current selection if any
                const currentSelection = paymentTypeSelect.val();

                // Hide all options first
                paymentTypeSelect.find('option').each(function() {
                    const optionValue = $(this).val();
                    if (optionValue) { // Skip the empty/default option
                        const rateNumber = parseInt($(this).text().match(/\d+/)?.[0] || 1);
                        $(this).prop('disabled', rateNumber > monthsDiff);
                        
                        // Hide disabled options
                        if (rateNumber > monthsDiff) {
                            $(this).hide();
                        } else {
                            $(this).show();
                        }
                    }
                });

                // If the current selection is now disabled, reset to default
                if (currentSelection && paymentTypeSelect.find(`option[value="${currentSelection}"]`).prop('disabled')) {
                    paymentTypeSelect.val('');
                }
            }

            // Attach the update function to date changes
            $('#Input_DatumVaziOd, #Input_DatumVaziDo').on('change', function() {
                updatePaymentTypeOptions();
            });

            // Run initial update if dates are pre-filled
            updatePaymentTypeOptions();

            // Cyrillic to Latin conversion function for Macedonian
            function convertCyrillicToLatin(text) {
                const cyrillicToLatinMap = {
                    'а': 'a', 'А': 'A',
                    'б': 'b', 'Б': 'B',
                    'в': 'v', 'В': 'V',
                    'г': 'g', 'Г': 'G',
                    'д': 'd', 'Д': 'D',
                    'ѓ': 'gj', 'Ѓ': 'Gj',
                    'е': 'e', 'Е': 'E',
                    'ж': 'zh', 'Ж': 'Zh',
                    'з': 'z', 'З': 'Z',
                    'ѕ': 'dz', 'Ѕ': 'Dz',
                    'и': 'i', 'И': 'I',
                    'ј': 'j', 'Ј': 'J',
                    'к': 'k', 'К': 'K',
                    'л': 'l', 'Л': 'L',
                    'љ': 'lj', 'Љ': 'Lj',
                    'м': 'm', 'М': 'M',
                    'н': 'n', 'Н': 'N',
                    'њ': 'nj', 'Њ': 'Nj',
                    'о': 'o', 'О': 'O',
                    'п': 'p', 'П': 'P',
                    'р': 'r', 'Р': 'R',
                    'с': 's', 'С': 'S',
                    'т': 't', 'Т': 'T',
                    'ќ': 'kj', 'Ќ': 'Kj',
                    'у': 'u', 'У': 'U',
                    'ф': 'f', 'Ф': 'F',
                    'х': 'h', 'Х': 'H',
                    'ц': 'c', 'Ц': 'C',
                    'ч': 'ch', 'Ч': 'Ch',
                    'џ': 'dj', 'Џ': 'Dj',
                    'ш': 'sh', 'Ш': 'Sh'
                };

                return text.replace(/[а-яёѐѓѕјљњќџА-ЯЁЀЃЅЈЉЊЌЏ]/g, function(match) {
                    return cyrillicToLatinMap[match] || match;
                });
            }

            // Add event listener for Registarska oznaka input to convert Cyrillic to Latin
            $('#registerskaOznakaInput').on('input', function() {
                const currentValue = $(this).val();
                const convertedValue = convertCyrillicToLatin(currentValue);
                
                // Only update if there's a change to avoid cursor position issues
                if (currentValue !== convertedValue) {
                    const cursorPosition = this.selectionStart;
                    $(this).val(convertedValue);
                    
                    // Restore cursor position, accounting for possible length changes
                    const lengthDifference = convertedValue.length - currentValue.length;
                    const newCursorPosition = cursorPosition + lengthDifference;
                    this.setSelectionRange(newCursorPosition, newCursorPosition);
                }
            });
        });
    </script>
}

@section Styles {
    <style>
        .card-header[data-bs-toggle="collapse"] {
            cursor: pointer;
        }
        
        .card-header[data-bs-toggle="collapse"]:hover {
            background-color: rgba(0,0,0,.03);
        }
        
        .fas {
            transition: transform 0.3s ease-in-out;
        }
        
        /* Rotate when section is expanded */
        .rotated {
            transform: rotate(180deg);
        }
    </style>
}