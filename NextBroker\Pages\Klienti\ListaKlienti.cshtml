@page
@model NextBroker.Pages.Klienti.ListaKlientiModel
@{
    ViewData["Title"] = "Листа на клиенти";
}

<div class="container-fluid mt-4">
    @Html.AntiForgeryToken()
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>@ViewData["Title"]</h1>
        <a href="/Klienti/DodajKlient" class="btn btn-primary">
            <i class="fas fa-plus"></i> Додај клиент
        </a>
    </div>

    @if (!string.IsNullOrEmpty(TempData["SuccessMessage"]?.ToString()))
    {
        <div class="alert alert-success" id="successMessage">
            @TempData["SuccessMessage"]
        </div>
    }

    <div class="card">
        <div class="card-body">
            <div class="mb-3">
                <input type="text" id="searchBox" class="form-control" placeholder="Пребарувај..." />
            </div>
            <div class="mb-3">
                <button class="btn btn-secondary w-100 text-start" type="button" data-bs-toggle="collapse" data-bs-target="#columnTogglers" aria-expanded="false" aria-controls="columnTogglers">
                    <i class="fas fa-cog me-2"></i> Додатни информации
                    <i class="fas fa-chevron-down float-end mt-1"></i>
                </button>
            </div>
            <div class="collapse mb-3" id="columnTogglers">
                <div class="row g-3">
                    <div class="col-md-3">
                        <strong>Основни информации</strong>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="55" id="toggle-vraboten">
                            <label class="form-check-label" for="toggle-vraboten">Вработен</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="56" id="toggle-sorabotnik">
                            <label class="form-check-label" for="toggle-sorabotnik">Соработник</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="57" id="toggle-brokersko">
                            <label class="form-check-label" for="toggle-brokersko">Брокерско друштво</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="58" id="toggle-osiguritel">
                            <label class="form-check-label" for="toggle-osiguritel">Осигурител</label>
                        </div>
                        
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="8" id="toggle-edb">
                            <label class="form-check-label" for="toggle-edb">ЕДБ</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="9" id="toggle-mb">
                            <label class="form-check-label" for="toggle-mb">МБ</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="10" id="toggle-embg">
                            <label class="form-check-label" for="toggle-embg">ЕМБГ</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="24" id="toggle-webstrana">
                            <label class="form-check-label" for="toggle-webstrana">Веб страна</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="54" id="toggle-pol">
                            <label class="form-check-label" for="toggle-pol">Пол</label>
                        </div>
                        <strong class="mt-3">Документи</strong>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="59" id="toggle-datum-ovlastuvanje">
                            <label class="form-check-label" for="toggle-datum-ovlastuvanje">Датум на овластување</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="17" id="toggle-datum-tekovna">
                            <label class="form-check-label" for="toggle-datum-tekovna">Датум на тековна</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="18" id="toggle-pasos">
                            <label class="form-check-label" for="toggle-pasos">Број на пасош/ЛК</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="19" id="toggle-vazi-od">
                            <label class="form-check-label" for="toggle-vazi-od">Важи од</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="20" id="toggle-vazi-do">
                            <label class="form-check-label" for="toggle-vazi-do">Важи до</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <strong>Адресни податоци</strong>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="11" id="toggle-opstina-id">
                            <label class="form-check-label" for="toggle-opstina-id">Општина (Идент.)</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="12" id="toggle-ulica-id">
                            <label class="form-check-label" for="toggle-ulica-id">Улица (Идент.)</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="13" id="toggle-broj-id">
                            <label class="form-check-label" for="toggle-broj-id">Број (Идент.)</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="14" id="toggle-opstina-kom">
                            <label class="form-check-label" for="toggle-opstina-kom">Општина (Комун.)</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="15" id="toggle-ulica-kom">
                            <label class="form-check-label" for="toggle-ulica-kom">Улица (Комун.)</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="16" id="toggle-broj-kom">
                            <label class="form-check-label" for="toggle-broj-kom">Број (Комун.)</label>
                        </div>
                        <strong class="mt-3">Дејност и Согласности</strong>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="21" id="toggle-dejnost">
                            <label class="form-check-label" for="toggle-dejnost">Дејност</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="25" id="toggle-sogl-marketing">
                            <label class="form-check-label" for="toggle-sogl-marketing">Согл. директен маркетинг</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <strong>Согласности и Сопственост</strong>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="26" id="toggle-sogl-email">
                            <label class="form-check-label" for="toggle-sogl-email">Согл. email комуникација</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="27" id="toggle-sogl-tel">
                            <label class="form-check-label" for="toggle-sogl-tel">Согл. тел. комуникација</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="28" id="toggle-datum-sogl">
                            <label class="form-check-label" for="toggle-datum-sogl">Датум повлечена согласност</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="29" id="toggle-vistinski-sopstvenik">
                            <label class="form-check-label" for="toggle-vistinski-sopstvenik">Вистински сопственик</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="30" id="toggle-vs-ime">
                            <label class="form-check-label" for="toggle-vs-ime">Име на вист. сопственик</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="31" id="toggle-vs-prezime">
                            <label class="form-check-label" for="toggle-vs-prezime">Презиме на вист. сопственик</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <strong>Договор и Лиценца</strong>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="36" id="toggle-datum-dogovor">
                            <label class="form-check-label" for="toggle-datum-dogovor">Датум на договор</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="37" id="toggle-dogovor-vazi">
                            <label class="form-check-label" for="toggle-dogovor-vazi">Договор важи до</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="38" id="toggle-broj-dogovor">
                            <label class="form-check-label" for="toggle-broj-dogovor">Број на договор</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="39" id="toggle-dogovor-tip">
                            <label class="form-check-label" for="toggle-dogovor-tip">Договор определено/неопределено</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input toggle-column" data-column="53" id="toggle-zivot-nezivot">
                            <label class="form-check-label" for="toggle-zivot-nezivot">Живот/Неживот</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table id="klientiTable" class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Датум на креирање</th>
                            <th>Креирано од</th>
                            <th>Датум на промена</th>
                            <th>Променето од</th>
                            <th>Тип</th>
                            <th>Име/Назив</th>
                            <th>Презиме</th>
                            <th>ЕДБ</th>
                            <th>МБ</th>
                            <th>ЕМБГ</th>
                            <th>Општина (Идент.)</th>
                            <th>Улица (Идент.)</th>
                            <th>Број (Идент.)</th>
                            <th>Општина (Комун.)</th>
                            <th>Улица (Комун.)</th>
                            <th>Број (Комун.)</th>
                            <th>Датум на тековна</th>
                            <th>Број на пасош/ЛК</th>
                            <th>Важи од</th>
                            <th>Важи до</th>
                            <th>Дејност</th>
                            <th>Email</th>
                            <th>Телефон</th>
                            <th>Веб страна</th>
                            <th>Согл. директен маркетинг</th>
                            <th>Согл. email комуникација</th>
                            <th>Согл. тел. комуникација</th>
                            <th>Датум повлечена согласност</th>
                            <th>Вистински сопственик</th>
                            <th>Име на вист. сопственик</th>
                            <th>Презиме на вист. сопственик</th>
                            <th>Носител на ЈФ</th>
                            <th>Основ за носител на ЈФ</th>
                            <th>Засилена анализа</th>
                            <th>Ниво на ризик</th>
                            <th>Датум на договор</th>
                            <th>Договор важи до</th>
                            <th>Број на договор</th>
                            <th>Договор определено/неопределено</th>
                            <th>Платежна сметка</th>
                            <th>Банка</th>
                            <th>Работна позиција</th>
                            <th>Организациона единица</th>
                            <th>Надреден</th>
                            <th>Надреден важи од</th>
                            <th>Надреден до</th>
                            <th>Задолжително лиценца</th>
                            <th>Број на решение од АСО</th>
                            <th>Датум на решение од АСО</th>
                            <th>Датум на одземена лиценца</th>
                            <th>Број на дозвола за брокер</th>
                            <th>Датум на дозвола за брокер</th>
                            <th>Живот/Неживот</th>
                            <th>Пол</th>
                            <th>Вработен</th>
                            <th>Соработник</th>
                            <th>Брокерско друштво</th>
                            <th>Осигурител</th>  
                            <th>Датум на овластување</th>
                            <th class="actions-column">Акции</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var klient in Model.Klienti)
                        {
                            <tr>
                                <td>@klient.Id</td>
                                <td>@(klient.DateCreated?.ToString("dd.MM.yyyy HH:mm"))</td>
                                <td>@klient.UsernameCreated</td>
                                <td>@(klient.DateModified?.ToString("dd.MM.yyyy HH:mm"))</td>
                                <td>@klient.UsernameModified</td>
                                <td>@klient.KlientTip</td>
                                <td>@(klient.KlientTip == "Физичко лице" ? klient.Ime : klient.Naziv)</td>
                                <td>@(klient.KlientTip == "Физичко лице" ? klient.Prezime : "")</td>
                                <td>@klient.EDB</td>
                                <td>@klient.MB</td>
                                <td>@klient.EMBG</td>
                                <td>@klient.ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija</td>
                                <td>@klient.UlicaOdDokumentZaIdentifikacija</td>
                                <td>@klient.BrojOdDokumentZaIdentifikacija</td>
                                <td>@klient.ListaOpstiniIdOpstinaZaKomunikacija</td>
                                <td>@klient.UlicaZaKomunikacija</td>
                                <td>@klient.BrojZaKomunikacija</td>
                                <td>@(klient.DatumNaTekovnaSostojba?.ToString("dd.MM.yyyy"))</td>
                                <td>@klient.BrojPasosLicnaKarta</td>
                                <td>@(klient.DatumVaziOdPasosLicnaKarta?.ToString("dd.MM.yyyy"))</td>
                                <td>@(klient.DatumVaziDoPasosLicnaKarta?.ToString("dd.MM.yyyy"))</td>
                                <td>@klient.ListaDejnostiIdDejnost</td>
                                <td>@klient.Email</td>
                                <td>@klient.Tel</td>
                                <td>@klient.Webstrana</td>
                                <td>@(klient.SoglasnostZaDirektenMarketing ? "Да" : "Не")</td>
                                <td>@(klient.SoglasnostZaEmailKomunikacija ? "Да" : "Не")</td>
                                <td>@(klient.SoglasnostZaTelKomunikacija ? "Да" : "Не")</td>
                                <td>@(klient.DatumNaPovlecenaSoglasnostZaDirektenMarketing?.ToString("dd.MM.yyyy"))</td>
                                <td>@klient.VistinskiSopstvenik</td>
                                <td>@klient.VistinskiSopstvenikIme</td>
                                <td>@klient.VistinskiSopstvenikPrezime</td>
                                <td>@(klient.NositelNaJF ? "Да" : "Не")</td>
                                <td>@klient.OsnovZaNositelNaJF</td>
                                <td>@(klient.ZasilenaAnaliza ? "Да" : "Не")</td>
                                <td>@klient.NivoaNaRizikIdNivoNaRizik</td>
                                <td>@(klient.DaumNaDogovor?.ToString("dd.MM.yyyy"))</td>
                                <td>@(klient.DogovorVaziDo?.ToString("dd.MM.yyyy"))</td>
                                <td>@klient.BrojNaDogovor</td>
                                <td>@klient.DogovorOpredelenoNeopredeleno</td>
                                <td>@klient.PlateznaSmetka</td>
                                <td>@klient.SifrarnikBankiIdBanka</td>
                                <td>@klient.SifrarnikRabotniPoziciiIdRabotnaPozicija</td>
                                <td>@klient.SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica</td>
                                <td>@klient.SifrarnikRabotniPoziciiIdNadreden</td>
                                <td>@(klient.NadredenVaziOd?.ToString("dd.MM.yyyy"))</td>
                                <td>@(klient.NadredenDo?.ToString("dd.MM.yyyy"))</td>
                                <td>@(klient.ZadolzitelnoLicenca ? "Да" : "Не")</td>
                                <td>@klient.BrojNaResenieOdASOZaLicenca</td>
                                <td>@(klient.DatumNaResenieOdASOZaLicenca?.ToString("dd.MM.yyyy"))</td>
                                <td>@(klient.DatumNaOdzemenaLicenca?.ToString("dd.MM.yyyy"))</td>
                                <td>@klient.BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti</td>
                                <td>@(klient.DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti?.ToString("dd.MM.yyyy"))</td>
                                <td>@klient.ZivotNezivot</td>
                                <td>@klient.KlientPol</td>
                                <td>@(klient.KlientVraboten ? "Да" : "Не")</td>
                                <td>@(klient.KlientSorabotnik ? "Да" : "Не")</td>
                                <td>@(klient.BrokerskoDrustvo ? "Да" : "Не")</td>
                                <td>@(klient.Osiguritel ? "Да" : "Не")</td>
                                <td>@(klient.DatumNaOvlastuvanje?.ToString("dd.MM.yyyy"))</td>
                                <td class="actions-column">
                                    @if (klient.KlientFizickoPravnoLice == "P")
                                    {
                                        <a href="/Klienti/ViewPravnoLice/@klient.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-search"></i>
                                        </a>
                                    }
                                    @if (klient.KlientFizickoPravnoLice == "F")
                                    {
                                        <a href="/Klienti/ViewFizickoLice/@klient.Id" class="btn btn-sm btn-light-primary">
                                            <i class="fas fa-search"></i>
                                        </a>
                                    }
                                    <a href="/Klienti/@(klient.KlientFizickoPravnoLice == "F" ? "EditFizickoLice" : "EditPravnoLice")/@klient.Id" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="/Klienti/KlientiLOG/@klient.Id" class="btn btn-sm btn-info" title="Историјат на промени">
                                        <i class="fas fa-history"></i>
                                    </a>
                                    <button class="btn btn-sm btn-danger" onclick="deleteKlient(@klient.Id)" style="display: none;">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" />
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css" />
    <style>
        .rotate-180 {
            transform: rotate(180deg);
            transition: transform 0.3s ease;
        }
        .fa-chevron-down {
            transition: transform 0.3s ease;
        }
        #columnTogglers {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.25rem;
        }
        .btn-light-primary {
            background-color: rgba(13, 110, 253, 0.2) !important;
            border-color: rgba(13, 110, 253, 0.2) !important;
            color: #0d6efd !important;
        }
        .btn-light-primary:hover {
            background-color: rgba(13, 110, 253, 0.4) !important;
            border-color: rgba(13, 110, 253, 0.4) !important;
            color: #0d6efd !important;
        }
        .actions-column {
            width: 150px !important;
            min-width: 150px !important;
            white-space: nowrap;
        }
        .actions-column .btn {
            padding: 0.25rem 0.5rem;
            margin-right: 0.25rem;
        }
        .actions-column .btn:last-child {
            margin-right: 0;
        }
        .table-responsive {
            cursor: grab;
            user-select: none;
        }
        
        .table-responsive.grabbing {
            cursor: grabbing;
        }
    </style>

    <script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>

    <script>
        $(document).ready(function() {
            // Handle collapse icon rotation
            $('#columnTogglers').on('show.bs.collapse', function () {
                $('.fa-chevron-down').addClass('rotate-180');
            }).on('hide.bs.collapse', function () {
                $('.fa-chevron-down').removeClass('rotate-180');
            });

            var table = $('#klientiTable').DataTable({
                "dom": "t",
                "language": {
                    "emptyTable": "Нема пронајдени записи",
                    "zeroRecords": "Нема пронајдени записи"
                },
                "ordering": false,
                "paging": false,
                "pageLength": -1,
                "columnDefs": [
                    // Show only these columns by default
                    { "targets": [0,5,6,7,22,23,1,2,60], "visible": true },
                    // Hide all other columns
                    { "targets": "_all", "visible": false }
                ]
            });

            $('#searchBox').on('keyup', function() {
                table.search(this.value).draw();
            });

            // Handle column visibility toggles
            $('.toggle-column').on('change', function() {
                var column = table.column($(this).attr('data-column'));
                column.visible($(this).is(':checked'));
            });

            // Set initial checkbox states based on visible columns
            table.columns().every(function(index) {
                var isVisible = this.visible();
                $('.toggle-column[data-column="' + index + '"]').prop('checked', isVisible);
            });

            // Add drag scroll functionality
            const tableContainer = document.querySelector('.table-responsive');
            let isDown = false;
            let startX;
            let scrollLeft;

            tableContainer.addEventListener('mousedown', (e) => {
                isDown = true;
                tableContainer.classList.add('grabbing');
                startX = e.pageX - tableContainer.offsetLeft;
                scrollLeft = tableContainer.scrollLeft;
            });

            tableContainer.addEventListener('mouseleave', () => {
                isDown = false;
                tableContainer.classList.remove('grabbing');
            });

            tableContainer.addEventListener('mouseup', () => {
                isDown = false;
                tableContainer.classList.remove('grabbing');
            });

            tableContainer.addEventListener('mousemove', (e) => {
                if (!isDown) return;
                e.preventDefault();
                const x = e.pageX - tableContainer.offsetLeft;
                const walk = (x - startX) * 2; // Adjust scrolling speed
                tableContainer.scrollLeft = scrollLeft - walk;
            });
        });

        function deleteKlient(id) {
            if (confirm('Дали сте сигурни дека сакате да го избришете овој клиент?')) {
                fetch('?handler=DeleteKlient', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    }
                })
                .catch(error => console.error('Error:', error));
            }
        }

        // Remove success message after 10 seconds
        document.addEventListener('DOMContentLoaded', function () {
            var successMessage = document.getElementById('successMessage');
            if (successMessage) {
                setTimeout(function () {
                    successMessage.style.transition = 'opacity 1s';
                    successMessage.style.opacity = '0';
                    setTimeout(function () {
                        successMessage.remove();
                    }, 1000);
                }, 10000);
            }
        });
    </script>
} 